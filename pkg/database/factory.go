package database

import (
	"fmt"
)

// DriverType represents the type of database driver
type DriverType string

const (
	// MySQL driver type
	MySQL DriverType = "mysql"
)

// NewDatabase creates a new database instance based on the driver type
func NewDatabase(driver DriverType, config *Config) (Database, error) {
	switch driver {
	case MySQL:
		return NewMySQLDatabase(config), nil
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", driver)
	}
}

// DefaultConfig returns a default database configuration
func DefaultConfig() *Config {
	return &Config{
		Host:            "localhost",
		Port:            3306,
		Database:        "blog_api_v3",
		Username:        "root",
		Password:        "",
		Charset:         "utf8mb4",
		MaxOpenConns:    25,
		MaxIdleConns:    5,
		ConnMaxLifetime: 0, // no limit
		ConnMaxIdleTime: 0, // no limit
		ParseTime:       true,
		Loc:             "Local",
		MigrationsPath:  "pkg/database/migrations",
	}
}