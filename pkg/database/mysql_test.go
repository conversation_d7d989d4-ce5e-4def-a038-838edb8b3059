package database

import (
	"context"
	"database/sql"
	"testing"
	"time"
)

func TestMySQLDatabase_BuildDSN(t *testing.T) {
	tests := []struct {
		name   string
		config *Config
		want   string
	}{
		{
			name: "basic config",
			config: &Config{
				Host:     "localhost",
				Port:     3306,
				Database: "testdb",
				Username: "root",
				Password: "password",
			},
			want: "root:password@tcp(localhost:3306)/testdb",
		},
		{
			name: "with charset and parseTime",
			config: &Config{
				Host:      "localhost",
				Port:      3306,
				Database:  "testdb",
				Username:  "root",
				Password:  "password",
				Charset:   "utf8mb4",
				ParseTime: true,
			},
			want: "root:password@tcp(localhost:3306)/testdb?charset=utf8mb4&parseTime=true",
		},
		{
			name: "with all options",
			config: &Config{
				Host:      "localhost",
				Port:      3306,
				Database:  "testdb",
				Username:  "root",
				Password:  "password",
				Charset:   "utf8mb4",
				ParseTime: true,
				Loc:       "UTC",
				Timeout:   5 * time.Second,
			},
			want: "root:password@tcp(localhost:3306)/testdb?charset=utf8mb4&parseTime=true&loc=UTC&timeout=5s",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MySQLDatabase{config: tt.config}
			if got := m.buildDSN(); got != tt.want {
				t.Errorf("buildDSN() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewDatabase(t *testing.T) {
	tests := []struct {
		name    string
		driver  DriverType
		config  *Config
		wantErr bool
	}{
		{
			name:    "MySQL driver",
			driver:  MySQL,
			config:  DefaultConfig(),
			wantErr: false,
		},
		{
			name:    "PostgreSQL driver (not implemented)",
			driver:  PostgreSQL,
			config:  DefaultConfig(),
			wantErr: true,
		},
		{
			name:    "Unknown driver",
			driver:  "unknown",
			config:  DefaultConfig(),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewDatabase(tt.driver, tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewDatabase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMySQLTransaction(t *testing.T) {
	// This is a mock test for transaction interface
	tx := &mysqlTransaction{tx: &sql.Tx{}}
	
	// Test that transaction implements the interface
	var _ Transaction = tx
	
	// Methods exist
	_ = tx.Commit
	_ = tx.Rollback
	_ = tx.Exec
	_ = tx.Query
	_ = tx.QueryRow
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	if config.Host != "localhost" {
		t.Errorf("DefaultConfig().Host = %v, want %v", config.Host, "localhost")
	}
	
	if config.Port != 3306 {
		t.Errorf("DefaultConfig().Port = %v, want %v", config.Port, 3306)
	}
	
	if config.MaxOpenConns != 25 {
		t.Errorf("DefaultConfig().MaxOpenConns = %v, want %v", config.MaxOpenConns, 25)
	}
	
	if config.MaxIdleConns != 5 {
		t.Errorf("DefaultConfig().MaxIdleConns = %v, want %v", config.MaxIdleConns, 5)
	}
	
	if !config.ParseTime {
		t.Error("DefaultConfig().ParseTime should be true")
	}
}

// MockDatabase implements Database interface for testing
type MockDatabase struct {
	connected bool
	pingErr   error
	beginErr  error
}

func (m *MockDatabase) Connect(ctx context.Context) error {
	m.connected = true
	return nil
}

func (m *MockDatabase) Close() error {
	m.connected = false
	return nil
}

func (m *MockDatabase) Ping(ctx context.Context) error {
	if !m.connected {
		return sql.ErrConnDone
	}
	return m.pingErr
}

func (m *MockDatabase) DB() *sql.DB {
	return nil
}

func (m *MockDatabase) BeginTx(ctx context.Context, opts *sql.TxOptions) (Transaction, error) {
	return nil, m.beginErr
}

func (m *MockDatabase) Migrate(ctx context.Context) error {
	return nil
}

func (m *MockDatabase) MigrateDown(ctx context.Context, steps int) error {
	return nil
}

func (m *MockDatabase) Health(ctx context.Context) error {
	return m.Ping(ctx)
}

func TestDatabaseInterface(t *testing.T) {
	// Test that MockDatabase implements Database interface
	var _ Database = &MockDatabase{}
	
	ctx := context.Background()
	db := &MockDatabase{}
	
	// Test Connect
	if err := db.Connect(ctx); err != nil {
		t.Errorf("Connect() error = %v", err)
	}
	
	if !db.connected {
		t.Error("Connect() should set connected to true")
	}
	
	// Test Close
	if err := db.Close(); err != nil {
		t.Errorf("Close() error = %v", err)
	}
	
	if db.connected {
		t.Error("Close() should set connected to false")
	}
}