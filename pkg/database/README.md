# Database Package

Package database cung cấp interface và implementation cho các database operations trong Blog API v3.

## Features

- Database connection interface với support cho multiple drivers
- MySQL driver implementation với GORM integration
- Connection pooling với cấu hình linh hoạt
- Transaction support
- Migration integration với golang-migrate
- Health check functionality

## Installation

```bash
go get github.com/blog-api-v3/blog-api-v3/pkg/database
```

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "log"
    
    "github.com/blog-api-v3/blog-api-v3/pkg/database"
)

func main() {
    // Create configuration
    config := &database.Config{
        Host:     "localhost",
        Port:     3306,
        Database: "blog_api_v3",
        Username: "root",
        Password: "password",
        Charset:  "utf8mb4",
        
        // Connection pool settings
        MaxOpenConns:    25,
        MaxIdleConns:    5,
        
        // Migration path
        MigrationsPath: "pkg/database/migrations",
    }
    
    // Create database instance
    db, err := database.NewDatabase(database.MySQL, config)
    if err != nil {
        log.Fatal(err)
    }
    
    // Connect to database
    ctx := context.Background()
    if err := db.Connect(ctx); err != nil {
        log.Fatal(err)
    }
    defer db.Close()
    
    // Run migrations
    if err := db.Migrate(ctx); err != nil {
        log.Fatal(err)
    }
    
    // Use database
    if err := db.Ping(ctx); err != nil {
        log.Fatal(err)
    }
}
```

### Transaction Usage

```go
// Start transaction
tx, err := db.BeginTx(ctx, nil)
if err != nil {
    return err
}

// Execute queries in transaction
_, err = tx.Exec("INSERT INTO users (name, email) VALUES (?, ?)", "John", "<EMAIL>")
if err != nil {
    tx.Rollback()
    return err
}

// Commit transaction
if err := tx.Commit(); err != nil {
    return err
}
```

### With GORM

```go
// Get GORM instance
mysqlDB, ok := db.(*database.MySQLDatabase)
if !ok {
    return errors.New("not a MySQL database")
}

gormDB := mysqlDB.GORM()

// Use GORM for ORM operations
var users []User
gormDB.Find(&users)
```

## Configuration

### Config struct

```go
type Config struct {
    // Connection settings
    Host     string
    Port     int
    Database string
    Username string
    Password string
    Charset  string
    
    // Connection pool settings
    MaxOpenConns    int           // Maximum number of open connections
    MaxIdleConns    int           // Maximum number of idle connections
    ConnMaxLifetime time.Duration // Maximum connection lifetime
    ConnMaxIdleTime time.Duration // Maximum idle time
    
    // Additional options
    ParseTime bool          // Parse MySQL DATE/DATETIME to Go time.Time
    Loc       string        // Location for time parsing
    Timeout   time.Duration // Connection timeout
    
    // Migration settings
    MigrationsPath string // Path to migration files
    
    // SSL/TLS settings
    SSLMode string // SSL mode (future implementation)
}
```

### Environment Variables

Bạn có thể load configuration từ environment variables:

```go
config := &database.Config{
    Host:     os.Getenv("DB_HOST"),
    Port:     3306, // Parse from DB_PORT
    Database: os.Getenv("DB_NAME"),
    Username: os.Getenv("DB_USER"),
    Password: os.Getenv("DB_PASSWORD"),
}
```

## Migrations

Package này tích hợp với golang-migrate để quản lý database schema:

```go
// Run all pending migrations
err := db.Migrate(ctx)

// Rollback last 2 migrations
err := db.MigrateDown(ctx, 2)
```

Migration files nên được đặt trong thư mục được chỉ định bởi `MigrationsPath` với format:
- `{version}_{description}.up.sql`
- `{version}_{description}.down.sql`

## Health Checks

```go
// Simple ping check
err := db.Ping(ctx)

// Full health check (includes query execution)
err := db.Health(ctx)
```

## Error Handling

Package trả về wrapped errors với context:

```go
if err := db.Connect(ctx); err != nil {
    // Error will include context like:
    // "failed to open database connection: dial tcp: ..."
    log.Printf("Database connection failed: %v", err)
}
```

## Best Practices

1. **Always close connections**: Sử dụng `defer db.Close()` sau khi connect
2. **Use context**: Pass context cho timeout control
3. **Configure connection pool**: Tune MaxOpenConns và MaxIdleConns theo workload
4. **Handle transactions properly**: Always rollback on error
5. **Run migrations separately**: Migrations nên chạy riêng trong deployment process

## Future Enhancements

- [ ] PostgreSQL driver implementation
- [ ] Redis integration
- [ ] Query logging and metrics
- [ ] Prepared statements caching
- [ ] Read/Write splitting for replicas