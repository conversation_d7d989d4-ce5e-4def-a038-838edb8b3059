package database

import (
	"database/sql"
	"fmt"
	"strings"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

// MigrationManager handles database migrations
type MigrationManager struct {
	migrate *migrate.Migrate
	db      *sql.DB
}

// NewMigrationManager creates a new migration manager instance
func NewMigrationManager(db *sql.DB, migrationsPath string) (*MigrationManager, error) {
	driver, err := mysql.WithInstance(db, &mysql.Config{})
	if err != nil {
		return nil, fmt.Errorf("creating mysql driver: %w", err)
	}

	// Ensure path starts with file://
	if !strings.HasPrefix(migrationsPath, "file://") {
		migrationsPath = fmt.Sprintf("file://%s", migrationsPath)
	}

	m, err := migrate.NewWithDatabaseInstance(
		migrationsPath,
		"mysql",
		driver,
	)
	if err != nil {
		return nil, fmt.Errorf("creating migration instance: %w", err)
	}

	return &MigrationManager{
		migrate: m,
		db:      db,
	}, nil
}

// Up runs all pending migrations
func (mm *MigrationManager) Up() error {
	if err := mm.migrate.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("running migrations up: %w", err)
	}
	return nil
}

// Down rolls back migrations by the specified number of steps
func (mm *MigrationManager) Down(steps int) error {
	if err := mm.migrate.Steps(-steps); err != nil {
		return fmt.Errorf("rolling back %d migrations: %w", steps, err)
	}
	return nil
}

// Version returns the current migration version and dirty state
func (mm *MigrationManager) Version() (uint, bool, error) {
	return mm.migrate.Version()
}

// Force sets the migration version without running any migrations
// This is useful for fixing dirty database states
func (mm *MigrationManager) Force(version int) error {
	return mm.migrate.Force(version)
}

// Drop removes all migrations (DANGEROUS!)
func (mm *MigrationManager) Drop() error {
	return mm.migrate.Drop()
}

// Close releases resources
func (mm *MigrationManager) Close() error {
	sourceErr, dbErr := mm.migrate.Close()
	if sourceErr != nil {
		return fmt.Errorf("closing source: %w", sourceErr)
	}
	if dbErr != nil {
		return fmt.Errorf("closing database: %w", dbErr)
	}
	return nil
}

// GetMigrationStatus returns information about migration status
func (mm *MigrationManager) GetMigrationStatus() (map[string]interface{}, error) {
	version, dirty, err := mm.Version()
	if err != nil && err != migrate.ErrNilVersion {
		return nil, err
	}

	status := map[string]interface{}{
		"current_version": version,
		"is_dirty":        dirty,
	}

	if err == migrate.ErrNilVersion {
		status["current_version"] = 0
		status["message"] = "No migrations have been applied yet"
	}

	return status, nil
}