package database

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// CursorQuery helps build cursor-based pagination queries
type CursorQuery struct {
	db       *gorm.DB
	tableName string
	sortBy   string
	sortOrder string
	conditions []string
	args      []interface{}
}

// NewCursorQuery creates a new cursor query builder
func NewCursorQuery(db *gorm.DB, tableName string) *CursorQuery {
	return &CursorQuery{
		db:        db,
		tableName: tableName,
		sortBy:    "created_at",
		sortOrder: "desc",
	}
}

// WithSort sets the sort field and order
func (cq *CursorQuery) WithSort(sortBy, sortOrder string) *CursorQuery {
	if sortBy != "" {
		cq.sortBy = sortBy
	}
	if sortOrder != "" {
		cq.sortOrder = sortOrder
	}
	return cq
}

// WithCondition adds a WHERE condition
func (cq *CursorQuery) WithCondition(condition string, args ...interface{}) *CursorQuery {
	cq.conditions = append(cq.conditions, condition)
	cq.args = append(cq.args, args...)
	return cq
}

// BuildQuery builds the GORM query with cursor pagination
func (cq *CursorQuery) BuildQuery(cursor string, limit int) *gorm.DB {
	query := cq.db.Table(cq.tableName)
	
	// Add WHERE conditions
	for i, condition := range cq.conditions {
		if i == 0 {
			query = query.Where(condition, cq.args[i])
		} else {
			query = query.Where(condition, cq.args[i])
		}
	}
	
	// Add cursor condition if provided
	if cursor != "" {
		decodedCursor, err := pagination.DecodeCursor(cursor)
		if err == nil && decodedCursor != nil {
			if cq.sortOrder == "desc" {
				// For descending order, we want records older than cursor
				switch cq.sortBy {
				case "created_at", "updated_at":
					query = query.Where(fmt.Sprintf("%s < ?", cq.sortBy), decodedCursor.Time)
				case "id":
					query = query.Where("id < ?", decodedCursor.ID)
				default:
					query = query.Where(fmt.Sprintf("%s < ? OR (%s = ? AND id < ?)", 
						cq.sortBy, cq.sortBy), 
						decodedCursor.Time, decodedCursor.Time, decodedCursor.ID)
				}
			} else {
				// For ascending order, we want records newer than cursor
				switch cq.sortBy {
				case "created_at", "updated_at":
					query = query.Where(fmt.Sprintf("%s > ?", cq.sortBy), decodedCursor.Time)
				case "id":
					query = query.Where("id > ?", decodedCursor.ID)
				default:
					query = query.Where(fmt.Sprintf("%s > ? OR (%s = ? AND id > ?)", 
						cq.sortBy, cq.sortBy), 
						decodedCursor.Time, decodedCursor.Time, decodedCursor.ID)
				}
			}
		}
	}
	
	// Add ordering
	query = query.Order(fmt.Sprintf("%s %s, id %s", cq.sortBy, cq.sortOrder, cq.sortOrder))
	
	// Add limit (fetch one extra to check if there are more records)
	query = query.Limit(limit + 1)
	
	return query
}

// CreateCursorResponse creates pagination response with next and previous cursors
func CreateCursorResponse(results interface{}, limit int, currentCursor string, getFirstRecord, getLastRecord func() (int64, time.Time, *float64)) *pagination.CursorResponse {
	response := &pagination.CursorResponse{
		HasMore:     false,
		HasPrevious: currentCursor != "",
		Limit:       limit,
	}
	
	// Use reflection to get slice length
	switch v := results.(type) {
	case []interface{}:
		if len(v) > limit {
			response.HasMore = true
			// Remove the extra record
			v = v[:limit]
		}
		
		// Set next cursor if there are more records
		if len(v) > 0 && response.HasMore {
			id, time, score := getLastRecord()
			cursor := pagination.NewCursorFromEntity(id, time, score)
			if encodedCursor, err := cursor.String(); err == nil {
				response.NextCursor = encodedCursor
			}
		}
		
		// Set previous cursor if we have results and there was a current cursor
		if len(v) > 0 && response.HasPrevious {
			id, time, score := getFirstRecord()
			cursor := pagination.NewCursorFromEntity(id, time, score)
			if encodedCursor, err := cursor.String(); err == nil {
				response.PreviousCursor = encodedCursor
			}
		}
	}
	
	return response
}

// Generic helper for creating cursor from any model with ID and CreatedAt
func CreateCursorFromModel(id int64, createdAt time.Time) *pagination.Cursor {
	return pagination.NewCursorFromEntity(id, createdAt, nil)
}

// Helper to parse string ID to int64 (for ULIDs or similar)
func ParseStringID(stringID string) int64 {
	// For ULID or similar string-based IDs, we can use hash or timestamp extraction
	// This is a simplified implementation - in practice you might want to use
	// the timestamp component of ULIDs or a hash function
	if len(stringID) > 0 {
		hash := int64(0)
		for _, r := range stringID {
			hash = hash*31 + int64(r)
		}
		if hash < 0 {
			hash = -hash
		}
		return hash
	}
	return 0
}