package database

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// CustomMigrationManager handles database migrations with filename-based tracking
type CustomMigrationManager struct {
	db             *sql.DB
	migrationsPath string
}

// Migration represents a database migration
type Migration struct {
	Name      string
	UpSQL     string
	DownSQL   string
	AppliedAt *time.Time
}

// NewCustomMigrationManager creates a new custom migration manager instance
func NewCustomMigrationManager(db *sql.DB, migrationsPath string) (*CustomMigrationManager, error) {
	manager := &CustomMigrationManager{
		db:             db,
		migrationsPath: migrationsPath,
	}

	// Create schema_migrations table if it doesn't exist
	if err := manager.createSchemaMigrationsTable(); err != nil {
		return nil, fmt.Errorf("creating schema_migrations table: %w", err)
	}

	return manager, nil
}

// createSchemaMigrationsTable creates the schema_migrations table
func (cm *CustomMigrationManager) createSchemaMigrationsTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS schema_migrations (
		id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
		migration_name VARCHAR(255) NOT NULL UNIQUE,
		applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		rollback_sql TEXT,
		INDEX idx_migration_name (migration_name)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`

	_, err := cm.db.Exec(query)
	return err
}

// Up runs all pending migrations
func (cm *CustomMigrationManager) Up() error {
	migrations, err := cm.loadMigrations()
	if err != nil {
		return fmt.Errorf("loading migrations: %w", err)
	}

	appliedMigrations, err := cm.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("getting applied migrations: %w", err)
	}

	// Filter out already applied migrations
	var pendingMigrations []Migration
	for _, migration := range migrations {
		if !cm.isMigrationApplied(migration.Name, appliedMigrations) {
			pendingMigrations = append(pendingMigrations, migration)
		}
	}

	if len(pendingMigrations) == 0 {
		fmt.Println("No pending migrations to apply")
		return nil
	}

	// Apply pending migrations
	for _, migration := range pendingMigrations {
		if err := cm.applyMigration(migration); err != nil {
			return fmt.Errorf("applying migration %s: %w", migration.Name, err)
		}
		fmt.Printf("Applied migration: %s\n", migration.Name)
	}

	return nil
}

// Down rolls back the last applied migration
func (cm *CustomMigrationManager) Down() error {
	appliedMigrations, err := cm.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("getting applied migrations: %w", err)
	}

	if len(appliedMigrations) == 0 {
		return fmt.Errorf("no migrations to roll back")
	}

	// Get the last applied migration
	lastMigration := appliedMigrations[len(appliedMigrations)-1]

	// Load the migration to get rollback SQL
	migration, err := cm.loadMigration(lastMigration.Name)
	if err != nil {
		return fmt.Errorf("loading migration %s: %w", lastMigration.Name, err)
	}

	// Apply rollback
	if err := cm.rollbackMigration(migration); err != nil {
		return fmt.Errorf("rolling back migration %s: %w", migration.Name, err)
	}

	fmt.Printf("Rolled back migration: %s\n", migration.Name)
	return nil
}

// loadMigrations loads all migration files from the migrations directory
func (cm *CustomMigrationManager) loadMigrations() ([]Migration, error) {
	var migrations []Migration

	err := filepath.Walk(cm.migrationsPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !strings.HasSuffix(path, ".up.sql") {
			return nil
		}

		// Extract migration name from filename only (without directory path)
		migrationName := strings.TrimSuffix(filepath.Base(path), ".up.sql")

		// Load up SQL
		upSQL, err := ioutil.ReadFile(path)
		if err != nil {
			return fmt.Errorf("reading up file %s: %w", path, err)
		}

		// Load down SQL
		downPath := strings.Replace(path, ".up.sql", ".down.sql", 1)
		downSQL, err := ioutil.ReadFile(downPath)
		if err != nil {
			return fmt.Errorf("reading down file %s: %w", downPath, err)
		}

		migrations = append(migrations, Migration{
			Name:    migrationName,
			UpSQL:   string(upSQL),
			DownSQL: string(downSQL),
		})

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Sort migrations by name to ensure consistent order
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Name < migrations[j].Name
	})

	return migrations, nil
}

// loadMigration loads a specific migration by name
func (cm *CustomMigrationManager) loadMigration(name string) (Migration, error) {
	var upPath, downPath string

	// Find the migration files by walking through all subdirectories
	err := filepath.Walk(cm.migrationsPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if filepath.Base(path) == name+".up.sql" {
			upPath = path
		}
		if filepath.Base(path) == name+".down.sql" {
			downPath = path
		}
		return nil
	})

	if err != nil {
		return Migration{}, fmt.Errorf("walking migration path: %w", err)
	}

	// Check if files were found
	if upPath == "" {
		return Migration{}, fmt.Errorf("migration up file not found: %s.up.sql", name)
	}
	if downPath == "" {
		return Migration{}, fmt.Errorf("migration down file not found: %s.down.sql", name)
	}

	// Read files
	upSQL, err := ioutil.ReadFile(upPath)
	if err != nil {
		return Migration{}, fmt.Errorf("reading up file: %w", err)
	}

	downSQL, err := ioutil.ReadFile(downPath)
	if err != nil {
		return Migration{}, fmt.Errorf("reading down file: %w", err)
	}

	return Migration{
		Name:    name,
		UpSQL:   string(upSQL),
		DownSQL: string(downSQL),
	}, nil
}

// getAppliedMigrations returns a list of applied migrations
func (cm *CustomMigrationManager) getAppliedMigrations() ([]Migration, error) {
	query := `
	SELECT migration_name, applied_at, rollback_sql
	FROM schema_migrations
	ORDER BY applied_at ASC
	`

	rows, err := cm.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var migrations []Migration
	for rows.Next() {
		var migration Migration
		var appliedAt time.Time
		var rollbackSQL sql.NullString

		if err := rows.Scan(&migration.Name, &appliedAt, &rollbackSQL); err != nil {
			return nil, err
		}

		migration.AppliedAt = &appliedAt
		if rollbackSQL.Valid {
			migration.DownSQL = rollbackSQL.String
		}

		migrations = append(migrations, migration)
	}

	return migrations, rows.Err()
}

// isMigrationApplied checks if a migration has been applied
func (cm *CustomMigrationManager) isMigrationApplied(name string, appliedMigrations []Migration) bool {
	for _, migration := range appliedMigrations {
		if migration.Name == name {
			return true
		}
	}
	return false
}

// applyMigration applies a migration
func (cm *CustomMigrationManager) applyMigration(migration Migration) error {
	tx, err := cm.db.Begin()
	if err != nil {
		return fmt.Errorf("beginning transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute migration SQL
	if _, err := tx.Exec(migration.UpSQL); err != nil {
		return fmt.Errorf("executing migration SQL: %w", err)
	}

	// Record migration in schema_migrations table
	_, err = tx.Exec(`
		INSERT INTO schema_migrations (migration_name, rollback_sql)
		VALUES (?, ?)
	`, migration.Name, migration.DownSQL)
	if err != nil {
		return fmt.Errorf("recording migration: %w", err)
	}

	return tx.Commit()
}

// rollbackMigration rolls back a migration
func (cm *CustomMigrationManager) rollbackMigration(migration Migration) error {
	tx, err := cm.db.Begin()
	if err != nil {
		return fmt.Errorf("beginning transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute rollback SQL
	if _, err := tx.Exec(migration.DownSQL); err != nil {
		return fmt.Errorf("executing rollback SQL: %w", err)
	}

	// Remove migration from schema_migrations table
	_, err = tx.Exec(`
		DELETE FROM schema_migrations
		WHERE migration_name = ?
	`, migration.Name)
	if err != nil {
		return fmt.Errorf("removing migration record: %w", err)
	}

	return tx.Commit()
}

// GetMigrationStatus returns information about migration status
func (cm *CustomMigrationManager) GetMigrationStatus() (map[string]interface{}, error) {
	appliedMigrations, err := cm.getAppliedMigrations()
	if err != nil {
		return nil, err
	}

	allMigrations, err := cm.loadMigrations()
	if err != nil {
		return nil, err
	}

	var pendingMigrations []string
	for _, migration := range allMigrations {
		if !cm.isMigrationApplied(migration.Name, appliedMigrations) {
			pendingMigrations = append(pendingMigrations, migration.Name)
		}
	}

	var appliedMigrationNames []string
	for _, migration := range appliedMigrations {
		appliedMigrationNames = append(appliedMigrationNames, migration.Name)
	}

	status := map[string]interface{}{
		"applied_migrations": appliedMigrationNames,
		"pending_migrations": pendingMigrations,
		"total_migrations":   len(allMigrations),
		"applied_count":      len(appliedMigrations),
		"pending_count":      len(pendingMigrations),
	}

	if len(appliedMigrations) > 0 {
		lastApplied := appliedMigrations[len(appliedMigrations)-1]
		status["last_applied"] = lastApplied.Name
		status["last_applied_at"] = lastApplied.AppliedAt
	}

	return status, nil
}

// Close releases resources
func (cm *CustomMigrationManager) Close() error {
	// Custom migration manager doesn't need special cleanup
	return nil
}