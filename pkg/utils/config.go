package utils

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config provides configuration management
type Config struct {
	v *viper.Viper
}

// NewConfig creates a new configuration instance
func NewConfig() *Config {
	v := viper.New()

	// Set defaults
	v.SetDefault("app.name", "blog-api-v3")
	v.<PERSON><PERSON><PERSON>ault("app.env", "development")
	v.<PERSON><PERSON>ault("app.port", 9077)
	v.<PERSON>ef<PERSON>("app.host", "0.0.0.0")

	// Enable environment variable reading
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	return &Config{v: v}
}

// LoadFromFile loads configuration from a file
func (c *Config) LoadFromFile(filename string) error {
	// Set config file
	c.v.SetConfigFile(filename)

	// Read config
	if err := c.v.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	return nil
}

// LoadFromPath loads configuration from a path
func (c *Config) LoadFromPath(path string, name string, configType string) error {
	c.v.AddConfigPath(path)
	c.v.SetConfigName(name)
	c.v.SetConfigType(configType)

	if err := c.v.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config: %w", err)
	}

	return nil
}

// LoadFromEnv loads configuration from environment variables
func (c *Config) LoadFromEnv(prefix string) {
	if prefix != "" {
		c.v.SetEnvPrefix(prefix)
	}
}

// Get returns a value by key
func (c *Config) Get(key string) interface{} {
	return c.v.Get(key)
}

// GetString returns a string value
func (c *Config) GetString(key string) string {
	return c.v.GetString(key)
}

// GetInt returns an integer value
func (c *Config) GetInt(key string) int {
	return c.v.GetInt(key)
}

// GetInt64 returns an int64 value
func (c *Config) GetInt64(key string) int64 {
	return c.v.GetInt64(key)
}

// GetBool returns a boolean value
func (c *Config) GetBool(key string) bool {
	return c.v.GetBool(key)
}

// GetFloat64 returns a float64 value
func (c *Config) GetFloat64(key string) float64 {
	return c.v.GetFloat64(key)
}

// GetStringSlice returns a string slice
func (c *Config) GetStringSlice(key string) []string {
	return c.v.GetStringSlice(key)
}

// GetStringMap returns a string map
func (c *Config) GetStringMap(key string) map[string]interface{} {
	return c.v.GetStringMap(key)
}

// IsSet checks if a key is set
func (c *Config) IsSet(key string) bool {
	return c.v.IsSet(key)
}

// Set sets a value for a key
func (c *Config) Set(key string, value interface{}) {
	c.v.Set(key, value)
}

// AllSettings returns all settings
func (c *Config) AllSettings() map[string]interface{} {
	return c.v.AllSettings()
}

// Unmarshal unmarshals config into a struct
func (c *Config) Unmarshal(rawVal interface{}) error {
	return c.v.Unmarshal(rawVal)
}

// UnmarshalKey unmarshals a specific key into a struct
func (c *Config) UnmarshalKey(key string, rawVal interface{}) error {
	return c.v.UnmarshalKey(key, rawVal)
}

// LoadConfig loads configuration from multiple sources
func LoadConfig(configFile string) (*Config, error) {
	config := NewConfig()

	// Load from environment first
	config.LoadFromEnv("")

	// If config file is provided, load it
	if configFile != "" {
		if err := config.LoadFromFile(configFile); err != nil {
			// If file doesn't exist in production, that's an error
			if config.GetString("app.env") == "production" {
				return nil, err
			}
			// In development, just log warning
			fmt.Printf("Warning: config file not found: %s\n", configFile)
		}
	} else {
		// Try to find config file in standard locations
		locations := []string{
			"config.yaml",
			"config.yml",
			"config/config.yaml",
			"config/config.yml",
			".env.yaml",
			".env.yml",
		}

		for _, loc := range locations {
			if _, err := os.Stat(loc); err == nil {
				if err := config.LoadFromFile(loc); err == nil {
					fmt.Printf("Loaded config from: %s\n", loc)
					break
				}
			}
		}
	}

	return config, nil
}

// MustLoadConfig loads configuration or panics
func MustLoadConfig(configFile string) *Config {
	config, err := LoadConfig(configFile)
	if err != nil {
		panic(fmt.Sprintf("Failed to load config: %v", err))
	}
	return config
}

// GetConfigPath returns the configuration file path based on environment
func GetConfigPath(env string) string {
	if env == "" {
		env = os.Getenv("APP_ENV")
		if env == "" {
			env = "development"
		}
	}

	// Check for environment-specific config
	envConfig := fmt.Sprintf("config/config.%s.yaml", env)
	if _, err := os.Stat(envConfig); err == nil {
		return envConfig
	}

	// Fall back to default config
	return "config/config.yaml"
}

// WatchConfig watches for config file changes
func (c *Config) WatchConfig(onChange func()) {
	// Note: OnConfigChange requires fsnotify.Event parameter
	// For simplicity, we'll just watch without the callback for now
	c.v.WatchConfig()
}
