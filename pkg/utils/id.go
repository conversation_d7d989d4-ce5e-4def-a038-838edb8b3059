package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/oklog/ulid/v2"
)

// IDGenerator interface for generating IDs
type IDGenerator interface {
	Generate() string
	GenerateWithPrefix(prefix string) string
}

// UUIDGenerator generates UUID v4
type UUIDGenerator struct{}

// NewUUIDGenerator creates a new UUID generator
func NewUUIDGenerator() IDGenerator {
	return &UUIDGenerator{}
}

// Generate generates a new UUID
func (g *UUIDGenerator) Generate() string {
	return uuid.New().String()
}

// GenerateWithPrefix generates a UUID with prefix
func (g *UUIDGenerator) GenerateWithPrefix(prefix string) string {
	return fmt.Sprintf("%s_%s", prefix, g.Generate())
}

// ULIDGenerator generates ULID (Universally Unique Lexicographically Sortable Identifier)
type ULIDGenerator struct{}

// NewULIDGenerator creates a new ULID generator
func NewULIDGenerator() IDGenerator {
	return &ULIDGenerator{}
}

// Generate generates a new ULID
func (g *ULIDGenerator) Generate() string {
	return ulid.Make().String()
}

// GenerateWithPrefix generates a ULID with prefix
func (g *ULIDGenerator) GenerateWithPrefix(prefix string) string {
	return fmt.Sprintf("%s_%s", prefix, g.Generate())
}

// NanoIDGenerator generates Nano ID
type NanoIDGenerator struct {
	alphabet string
	size     int
}

// NewNanoIDGenerator creates a new Nano ID generator
func NewNanoIDGenerator(size int) IDGenerator {
	return &NanoIDGenerator{
		alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
		size:     size,
	}
}

// Generate generates a new Nano ID
func (g *NanoIDGenerator) Generate() string {
	id := make([]byte, g.size)
	for i := 0; i < g.size; i++ {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(g.alphabet))))
		id[i] = g.alphabet[num.Int64()]
	}
	return string(id)
}

// GenerateWithPrefix generates a Nano ID with prefix
func (g *NanoIDGenerator) GenerateWithPrefix(prefix string) string {
	return fmt.Sprintf("%s_%s", prefix, g.Generate())
}

// ID generation helper functions

// GenerateUUID generates a UUID v4
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateULID generates a ULID
func GenerateULID() string {
	return ulid.Make().String()
}

// GenerateNanoID generates a Nano ID with default size of 21
func GenerateNanoID(size ...int) string {
	s := 21
	if len(size) > 0 && size[0] > 0 {
		s = size[0]
	}
	gen := NewNanoIDGenerator(s)
	return gen.Generate()
}

// GenerateRandomHex generates a random hex string
func GenerateRandomHex(n int) (string, error) {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GenerateRandomString generates a random string with given length
func GenerateRandomString(n int) (string, error) {
	const letters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	bytes := make([]byte, n)
	for i := 0; i < n; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			return "", err
		}
		bytes[i] = letters[num.Int64()]
	}
	return string(bytes), nil
}

// GenerateToken generates a secure random token
func GenerateToken(length int) (string, error) {
	return GenerateRandomHex(length)
}

// GenerateShortCode generates a short code (6 characters by default)
func GenerateShortCode(length ...int) string {
	l := 6
	if len(length) > 0 && length[0] > 0 {
		l = length[0]
	}
	
	// Use uppercase letters and numbers for better readability
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	code := make([]byte, l)
	for i := 0; i < l; i++ {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		code[i] = charset[num.Int64()]
	}
	return string(code)
}

// GenerateOrderID generates an order ID with timestamp
func GenerateOrderID() string {
	timestamp := time.Now().Format("20060102150405")
	random, _ := GenerateRandomString(6)
	return fmt.Sprintf("ORD%s%s", timestamp, strings.ToUpper(random))
}

// GenerateTransactionID generates a transaction ID
func GenerateTransactionID() string {
	timestamp := time.Now().Format("20060102150405")
	random, _ := GenerateRandomString(8)
	return fmt.Sprintf("TXN%s%s", timestamp, strings.ToUpper(random))
}

// GenerateSessionID generates a session ID
func GenerateSessionID() string {
	return GenerateULID()
}

// GenerateAPIKey generates an API key
func GenerateAPIKey() (string, error) {
	// Generate 32 bytes (256 bits) of random data
	token, err := GenerateToken(32)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("sk_%s", token), nil
}

// ValidateUUID validates if a string is a valid UUID
func ValidateUUID(s string) bool {
	_, err := uuid.Parse(s)
	return err == nil
}

// ValidateULID validates if a string is a valid ULID
func ValidateULID(s string) bool {
	_, err := ulid.Parse(s)
	return err == nil
}