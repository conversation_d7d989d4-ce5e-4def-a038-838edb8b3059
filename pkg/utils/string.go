package utils

import (
	"crypto/md5"
	"encoding/hex"
	"regexp"
	"strings"
	"unicode"
)

// Slugify converts a string to a URL-friendly slug
func Slugify(s string) string {
	// Convert to lowercase
	s = strings.ToLower(s)
	
	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	s = reg.ReplaceAllString(s, "-")
	
	// Remove leading and trailing hyphens
	s = strings.Trim(s, "-")
	
	// Replace multiple hyphens with single hyphen
	reg = regexp.MustCompile(`-+`)
	s = reg.ReplaceAllString(s, "-")
	
	return s
}

// Truncate truncates a string to a specified length
func Truncate(s string, length int) string {
	if length <= 0 {
		return ""
	}
	
	runes := []rune(s)
	if len(runes) <= length {
		return s
	}
	
	return string(runes[:length])
}

// TruncateWithSuffix truncates a string and adds a suffix
func TruncateWithSuffix(s string, length int, suffix string) string {
	if length <= 0 {
		return ""
	}
	
	runes := []rune(s)
	if len(runes) <= length {
		return s
	}
	
	truncateLength := length - len([]rune(suffix))
	if truncateLength <= 0 {
		return suffix
	}
	
	return string(runes[:truncateLength]) + suffix
}

// Contains checks if a string contains a substring
func Contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// ContainsAny checks if a string contains any of the substrings
func ContainsAny(s string, substrs []string) bool {
	for _, substr := range substrs {
		if strings.Contains(s, substr) {
			return true
		}
	}
	return false
}

// IsEmpty checks if a string is empty or contains only whitespace
func IsEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}

// IsNotEmpty checks if a string is not empty
func IsNotEmpty(s string) bool {
	return !IsEmpty(s)
}

// Capitalize capitalizes the first letter of a string
func Capitalize(s string) string {
	if s == "" {
		return s
	}
	
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// CapitalizeWords capitalizes the first letter of each word
func CapitalizeWords(s string) string {
	words := strings.Fields(s)
	for i, word := range words {
		words[i] = Capitalize(word)
	}
	return strings.Join(words, " ")
}

// CamelCase converts a string to camelCase
func CamelCase(s string) string {
	words := strings.FieldsFunc(s, func(r rune) bool {
		return !unicode.IsLetter(r) && !unicode.IsDigit(r)
	})
	
	if len(words) == 0 {
		return ""
	}
	
	result := strings.ToLower(words[0])
	for i := 1; i < len(words); i++ {
		result += Capitalize(strings.ToLower(words[i]))
	}
	
	return result
}

// SnakeCase converts a string to snake_case
func SnakeCase(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && unicode.IsUpper(r) {
			result.WriteRune('_')
		}
		result.WriteRune(unicode.ToLower(r))
	}
	return result.String()
}

// KebabCase converts a string to kebab-case
func KebabCase(s string) string {
	return strings.ReplaceAll(SnakeCase(s), "_", "-")
}

// RemoveAccents removes accents from a string
func RemoveAccents(s string) string {
	// Simple accent removal for common Latin characters
	replacer := strings.NewReplacer(
		"à", "a", "á", "a", "ä", "a", "â", "a", "ã", "a", "å", "a",
		"è", "e", "é", "e", "ë", "e", "ê", "e",
		"ì", "i", "í", "i", "ï", "i", "î", "i",
		"ò", "o", "ó", "o", "ö", "o", "ô", "o", "õ", "o",
		"ù", "u", "ú", "u", "ü", "u", "û", "u",
		"ñ", "n", "ç", "c",
		"À", "A", "Á", "A", "Ä", "A", "Â", "A", "Ã", "A", "Å", "A",
		"È", "E", "É", "E", "Ë", "E", "Ê", "E",
		"Ì", "I", "Í", "I", "Ï", "I", "Î", "I",
		"Ò", "O", "Ó", "O", "Ö", "O", "Ô", "O", "Õ", "O",
		"Ù", "U", "Ú", "U", "Ü", "U", "Û", "U",
		"Ñ", "N", "Ç", "C",
	)
	return replacer.Replace(s)
}

// Reverse reverses a string
func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// Repeat repeats a string n times
func Repeat(s string, n int) string {
	return strings.Repeat(s, n)
}

// Pad pads a string to a specified length
func Pad(s string, length int, padStr string) string {
	if len(s) >= length {
		return s
	}
	
	if padStr == "" {
		padStr = " "
	}
	
	padCount := length - len(s)
	pad := strings.Repeat(padStr, (padCount/len(padStr))+1)
	return s + pad[:padCount]
}

// PadLeft pads a string on the left
func PadLeft(s string, length int, padStr string) string {
	if len(s) >= length {
		return s
	}
	
	if padStr == "" {
		padStr = " "
	}
	
	padCount := length - len(s)
	pad := strings.Repeat(padStr, (padCount/len(padStr))+1)
	return pad[:padCount] + s
}

// MD5Hash generates MD5 hash of a string
func MD5Hash(s string) string {
	hash := md5.Sum([]byte(s))
	return hex.EncodeToString(hash[:])
}

// IsEmail checks if a string is a valid email format
func IsEmail(s string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(s)
}

// IsURL checks if a string is a valid URL
func IsURL(s string) bool {
	urlRegex := regexp.MustCompile(`^(https?|ftp)://[^\s/$.?#].[^\s]*$`)
	return urlRegex.MatchString(s)
}

// IsAlphanumeric checks if a string contains only letters and numbers
func IsAlphanumeric(s string) bool {
	for _, r := range s {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// RemoveNonAlphanumeric removes all non-alphanumeric characters
func RemoveNonAlphanumeric(s string) string {
	reg := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	return reg.ReplaceAllString(s, "")
}

// ExtractNumbers extracts all numbers from a string
func ExtractNumbers(s string) string {
	reg := regexp.MustCompile(`[^0-9]+`)
	return reg.ReplaceAllString(s, "")
}

// CountWords counts the number of words in a string
func CountWords(s string) int {
	return len(strings.Fields(s))
}

// SplitLines splits a string into lines
func SplitLines(s string) []string {
	return strings.Split(strings.ReplaceAll(s, "\r\n", "\n"), "\n")
}

// JoinLines joins lines into a single string
func JoinLines(lines []string) string {
	return strings.Join(lines, "\n")
}

// ToVietnameseUnsigned converts Vietnamese text to unsigned
func ToVietnameseUnsigned(s string) string {
	vietnameseMap := map[string]string{
		"à": "a", "á": "a", "ạ": "a", "ả": "a", "ã": "a",
		"â": "a", "ầ": "a", "ấ": "a", "ậ": "a", "ẩ": "a", "ẫ": "a",
		"ă": "a", "ằ": "a", "ắ": "a", "ặ": "a", "ẳ": "a", "ẵ": "a",
		"è": "e", "é": "e", "ẹ": "e", "ẻ": "e", "ẽ": "e",
		"ê": "e", "ề": "e", "ế": "e", "ệ": "e", "ể": "e", "ễ": "e",
		"ì": "i", "í": "i", "ị": "i", "ỉ": "i", "ĩ": "i",
		"ò": "o", "ó": "o", "ọ": "o", "ỏ": "o", "õ": "o",
		"ô": "o", "ồ": "o", "ố": "o", "ộ": "o", "ổ": "o", "ỗ": "o",
		"ơ": "o", "ờ": "o", "ớ": "o", "ợ": "o", "ở": "o", "ỡ": "o",
		"ù": "u", "ú": "u", "ụ": "u", "ủ": "u", "ũ": "u",
		"ư": "u", "ừ": "u", "ứ": "u", "ự": "u", "ử": "u", "ữ": "u",
		"ỳ": "y", "ý": "y", "ỵ": "y", "ỷ": "y", "ỹ": "y",
		"đ": "d",
		"À": "A", "Á": "A", "Ạ": "A", "Ả": "A", "Ã": "A",
		"Â": "A", "Ầ": "A", "Ấ": "A", "Ậ": "A", "Ẩ": "A", "Ẫ": "A",
		"Ă": "A", "Ằ": "A", "Ắ": "A", "Ặ": "A", "Ẳ": "A", "Ẵ": "A",
		"È": "E", "É": "E", "Ẹ": "E", "Ẻ": "E", "Ẽ": "E",
		"Ê": "E", "Ề": "E", "Ế": "E", "Ệ": "E", "Ể": "E", "Ễ": "E",
		"Ì": "I", "Í": "I", "Ị": "I", "Ỉ": "I", "Ĩ": "I",
		"Ò": "O", "Ó": "O", "Ọ": "O", "Ỏ": "O", "Õ": "O",
		"Ô": "O", "Ồ": "O", "Ố": "O", "Ộ": "O", "Ổ": "O", "Ỗ": "O",
		"Ơ": "O", "Ờ": "O", "Ớ": "O", "Ợ": "O", "Ở": "O", "Ỡ": "O",
		"Ù": "U", "Ú": "U", "Ụ": "U", "Ủ": "U", "Ũ": "U",
		"Ư": "U", "Ừ": "U", "Ứ": "U", "Ự": "U", "Ử": "U", "Ữ": "U",
		"Ỳ": "Y", "Ý": "Y", "Ỵ": "Y", "Ỷ": "Y", "Ỹ": "Y",
		"Đ": "D",
	}
	
	for vn, en := range vietnameseMap {
		s = strings.ReplaceAll(s, vn, en)
	}
	
	return s
}