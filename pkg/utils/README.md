# Utils Package

Package utils cung cấp các utility functions và helpers phổ biến cho Blog API v3.

## Features

- **Config Loader**: Load configuration từ files và environment variables
- **Structured Logging**: Logging interface với multiple adapters
- **ID Generation**: UUID, ULID, NanoID và custom ID generators
- **String Utilities**: Slugify, case conversion, validation, v.v.
- **Time Utilities**: Time manipulation, formatting, parsing
- **Error Handling**: Structured errors với context và stack traces  
- **Environment Helpers**: Environment variable utilities

## Installation

```bash
go get github.com/blog-api-v3/blog-api-v3/pkg/utils
```

## Usage

### Configuration

```go
import "github.com/blog-api-v3/blog-api-v3/pkg/utils"

// Load configuration
config, err := utils.LoadConfig("config.yaml")
if err != nil {
    log.Fatal(err)
}

// Get values
dbHost := config.GetString("database.host")
dbPort := config.GetInt("database.port")
debug := config.GetBool("app.debug")

// Load from environment
config.LoadFromEnv("APP")
// Now APP_DATABASE_HOST maps to database.host

// Watch for changes
config.WatchConfig(func() {
    log.Println("Config reloaded")
})
```

### Logging

```go
// Use default logger
utils.Info("Application started")
utils.WithField("user_id", "123").Info("User logged in")
utils.WithFields(utils.Fields{
    "method": "POST",
    "path":   "/api/users",
    "status": 201,
}).Info("Request completed")

// Create custom logger
logger := utils.NewLoggerWithConfig(utils.LoggerConfig{
    Level:        "debug",
    Format:       "json",
    ReportCaller: true,
})

// With context
ctx := context.WithValue(context.Background(), "requestID", "abc123")
logger.WithContext(ctx).Info("Processing request")
```

### ID Generation

```go
// UUID v4
uuid := utils.GenerateUUID()

// ULID (sortable)
ulid := utils.GenerateULID()

// NanoID
nanoID := utils.GenerateNanoID() // 21 chars
shortID := utils.GenerateNanoID(8) // 8 chars

// Short code
code := utils.GenerateShortCode() // 6 chars uppercase + numbers

// API key
apiKey, _ := utils.GenerateAPIKey() // sk_xxxx...

// Order/Transaction IDs
orderID := utils.GenerateOrderID() // ORD20231225150423ABCDEF
txnID := utils.GenerateTransactionID() // TXN20231225150423ABCDEFGH

// Using generators
uuidGen := utils.NewUUIDGenerator()
id := uuidGen.Generate()
prefixedID := uuidGen.GenerateWithPrefix("user") // user_uuid...
```

### String Utilities

```go
// Slugify
slug := utils.Slugify("Hello World!") // "hello-world"

// Case conversion
camel := utils.CamelCase("hello world") // "helloWorld"
snake := utils.SnakeCase("helloWorld") // "hello_world"
kebab := utils.KebabCase("helloWorld") // "hello-world"

// Validation
if utils.IsEmail("<EMAIL>") {
    // Valid email
}

if utils.IsURL("https://example.com") {
    // Valid URL
}

// String manipulation
truncated := utils.TruncateWithSuffix("Long text here", 10, "...") // "Long te..."
words := utils.CountWords("Hello world test") // 3

// Vietnamese support
unsigned := utils.ToVietnameseUnsigned("Xin chào Việt Nam") // "Xin chao Viet Nam"
```

### Time Utilities  

```go
// Time helpers
today := utils.Today() // Today at 00:00:00
tomorrow := utils.Tomorrow()
yesterday := utils.Yesterday()

// Start/End of period
startOfDay := utils.StartOfDay(time.Now())
endOfMonth := utils.EndOfMonth(time.Now())
startOfWeek := utils.StartOfWeek(time.Now())

// Formatting
dateStr := utils.FormatDate(time.Now()) // "2023-12-25"
dateTimeStr := utils.FormatDateTime(time.Now()) // "2023-12-25 15:04:05"
iso8601 := utils.FormatISO8601(time.Now()) // "2023-12-25T15:04:05+07:00"

// Parsing
date, _ := utils.ParseDate("2023-12-25")
dateTime, _ := utils.ParseDateTime("2023-12-25 15:04:05")

// Calculations
days := utils.DaysBetween(from, to)
age := utils.Age(birthDate)

// Relative time
relative := utils.FormatRelativeTime(pastTime) // "2 hours ago"

// Unix timestamps
unix := utils.UnixTimestamp(time.Now())
millis := utils.UnixMillisTimestamp(time.Now())
```

### Error Handling

```go
// Create errors
err := utils.NewError("Something went wrong", originalErr)
err = utils.NewErrorf("Failed to process user %s", userID)

// Wrap errors
if err := doSomething(); err != nil {
    return utils.WrapError(err, "failed to do something")
}

// Structured errors
err := utils.ValidationError("Validation failed", map[string]string{
    "email": "Invalid email format",
    "name":  "Name is required",
})

err = utils.NotFoundError("User")
err = utils.UnauthorizedError("Invalid token")
err = utils.ForbiddenError("Access denied")

// Check error types
if utils.IsNotFound(err) {
    // Handle not found
}

if utils.IsValidationError(err) {
    // Handle validation error
}

// Get error details
if appErr := utils.GetAppError(err); appErr != nil {
    fmt.Println(appErr.Code)
    fmt.Println(appErr.StatusCode)
    fmt.Println(appErr.Details)
}
```

### Environment Helpers

```go
// Get environment variables
port := utils.GetEnvAsInt("APP_PORT", 9077)
debug := utils.GetEnvAsBool("DEBUG", false)
hosts := utils.GetEnvAsSlice("ALLOWED_HOSTS", []string{"localhost"}, ",")

// Required env vars
err := utils.RequiredEnvVars([]string{"DATABASE_URL", "JWT_SECRET"})

// Environment detection
if utils.IsProduction() {
    // Production specific code
}

if utils.IsDevelopment() {
    // Development specific code
}

// Get all env vars with prefix
dbConfig := utils.GetEnvWithPrefix("DB_") // DB_HOST -> HOST
```

## Common Patterns

### Application Bootstrap

```go
func main() {
    // Load config
    config := utils.MustLoadConfig("")
    
    // Setup logging
    logger := utils.NewLoggerWithConfig(utils.LoggerConfig{
        Level:  config.GetString("log.level"),
        Format: config.GetString("log.format"),
    })
    utils.SetDefaultLogger(logger)
    
    // Check required env vars
    if err := utils.RequiredEnvVars([]string{
        "DATABASE_URL",
        "JWT_SECRET",
    }); err != nil {
        utils.Fatal(err)
    }
    
    // Log startup info
    utils.WithFields(utils.Fields{
        "app":         utils.GetAppName(),
        "version":     utils.GetAppVersion(),
        "environment": utils.GetEnvironment(),
    }).Info("Application starting")
}
```

### Request Handling

```go
func handleRequest(w http.ResponseWriter, r *http.Request) {
    // Generate request ID
    requestID := utils.GenerateULID()
    
    // Create logger with context
    logger := utils.WithFields(utils.Fields{
        "request_id": requestID,
        "method":     r.Method,
        "path":       r.URL.Path,
    })
    
    logger.Info("Request started")
    
    // ... handle request ...
    
    logger.WithField("duration", time.Since(start)).Info("Request completed")
}
```

### Data Processing

```go
func processUser(user *User) error {
    // Generate user ID
    user.ID = utils.GenerateULID()
    
    // Slugify username
    user.Slug = utils.Slugify(user.Username)
    
    // Validate email
    if !utils.IsEmail(user.Email) {
        return utils.ValidationError("Invalid input", map[string]string{
            "email": "Invalid email format",
        })
    }
    
    // Set timestamps
    now := utils.Now()
    user.CreatedAt = now
    user.UpdatedAt = now
    
    return nil
}
```

## Best Practices

1. **Use structured logging**: Always use WithFields for context
2. **Wrap errors**: Add context when propagating errors
3. **Use appropriate ID types**: UUID for random, ULID for sortable
4. **Validate environment early**: Check required vars at startup
5. **Format times consistently**: Use the provided format constants