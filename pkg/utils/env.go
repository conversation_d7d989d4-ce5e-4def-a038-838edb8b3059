package utils

import (
	"os"
	"strconv"
	"strings"
	"time"
)

// Environment types
const (
	Development = "development"
	Testing     = "testing"
	Staging     = "staging"
	Production  = "production"
)

// GetEnv gets an environment variable with a default value
func GetEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// GetEnvAsInt gets an environment variable as integer
func GetEnvAsInt(key string, defaultValue int) int {
	valueStr := os.Getenv(key)
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// GetEnvAsInt64 gets an environment variable as int64
func GetEnvAsInt64(key string, defaultValue int64) int64 {
	valueStr := os.Getenv(key)
	if value, err := strconv.ParseInt(valueStr, 10, 64); err == nil {
		return value
	}
	return defaultValue
}

// GetEnvAsBool gets an environment variable as boolean
func GetEnvAsBool(key string, defaultValue bool) bool {
	valueStr := os.Getenv(key)
	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// GetEnvAsFloat64 gets an environment variable as float64
func GetEnvAsFloat64(key string, defaultValue float64) float64 {
	valueStr := os.Getenv(key)
	if value, err := strconv.ParseFloat(valueStr, 64); err == nil {
		return value
	}
	return defaultValue
}

// GetEnvAsSlice gets an environment variable as string slice
func GetEnvAsSlice(key string, defaultValue []string, separator string) []string {
	valueStr := os.Getenv(key)
	if valueStr == "" {
		return defaultValue
	}

	if separator == "" {
		separator = ","
	}

	values := strings.Split(valueStr, separator)
	result := make([]string, 0, len(values))
	for _, v := range values {
		if trimmed := strings.TrimSpace(v); trimmed != "" {
			result = append(result, trimmed)
		}
	}

	if len(result) == 0 {
		return defaultValue
	}

	return result
}

// GetEnvAsDuration gets an environment variable as duration
func GetEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	valueStr := os.Getenv(key)
	if value, err := time.ParseDuration(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// MustGetEnv gets an environment variable or panics
func MustGetEnv(key string) string {
	value := os.Getenv(key)
	if value == "" {
		panic("Environment variable " + key + " is required")
	}
	return value
}

// IsEnvSet checks if an environment variable is set
func IsEnvSet(key string) bool {
	_, exists := os.LookupEnv(key)
	return exists
}

// GetEnvironment gets the current environment
func GetEnvironment() string {
	env := GetEnv("APP_ENV", Development)
	// Normalize environment name
	switch strings.ToLower(env) {
	case "dev", "development":
		return Development
	case "test", "testing":
		return Testing
	case "stage", "staging":
		return Staging
	case "prod", "production":
		return Production
	default:
		return Development
	}
}

// IsDevelopment checks if current environment is development
func IsDevelopment() bool {
	return GetEnvironment() == Development
}

// IsTesting checks if current environment is testing
func IsTesting() bool {
	return GetEnvironment() == Testing
}

// IsStaging checks if current environment is staging
func IsStaging() bool {
	return GetEnvironment() == Staging
}

// IsProduction checks if current environment is production
func IsProduction() bool {
	return GetEnvironment() == Production
}

// IsDebug checks if debug mode is enabled
func IsDebug() bool {
	return GetEnvAsBool("DEBUG", false) || IsDevelopment()
}

// GetAppName gets the application name
func GetAppName() string {
	return GetEnv("APP_NAME", "blog-api-v3")
}

// GetAppVersion gets the application version
func GetAppVersion() string {
	return GetEnv("APP_VERSION", "0.0.0")
}

// GetPort gets the application port
func GetPort() int {
	return GetEnvAsInt("APP_PORT", 9077)
}

// GetHost gets the application host
func GetHost() string {
	return GetEnv("APP_HOST", "0.0.0.0")
}

// LoadEnvFile loads environment variables from a file
func LoadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	return nil // Actual implementation would parse and set env vars
}

// EnvConfig represents environment configuration
type EnvConfig struct {
	Environment string
	Debug       bool
	AppName     string
	AppVersion  string
	Host        string
	Port        int
}

// GetEnvConfig gets the current environment configuration
func GetEnvConfig() *EnvConfig {
	return &EnvConfig{
		Environment: GetEnvironment(),
		Debug:       IsDebug(),
		AppName:     GetAppName(),
		AppVersion:  GetAppVersion(),
		Host:        GetHost(),
		Port:        GetPort(),
	}
}

// RequiredEnvVars checks if all required environment variables are set
func RequiredEnvVars(vars []string) error {
	missing := make([]string, 0)

	for _, v := range vars {
		if !IsEnvSet(v) {
			missing = append(missing, v)
		}
	}

	if len(missing) > 0 {
		return NewErrorf("Missing required environment variables: %s", strings.Join(missing, ", "))
	}

	return nil
}

// SetEnv sets an environment variable
func SetEnv(key, value string) error {
	return os.Setenv(key, value)
}

// UnsetEnv unsets an environment variable
func UnsetEnv(key string) error {
	return os.Unsetenv(key)
}

// ClearEnv clears all environment variables
func ClearEnv() {
	os.Clearenv()
}

// GetEnvMap gets all environment variables as a map
func GetEnvMap() map[string]string {
	envMap := make(map[string]string)
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 {
			envMap[parts[0]] = parts[1]
		}
	}
	return envMap
}

// GetEnvWithPrefix gets all environment variables with a specific prefix
func GetEnvWithPrefix(prefix string) map[string]string {
	envMap := make(map[string]string)
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 && strings.HasPrefix(parts[0], prefix) {
			key := strings.TrimPrefix(parts[0], prefix)
			envMap[key] = parts[1]
		}
	}
	return envMap
}
