package utils

import (
	"testing"
	"time"
)

func TestStartOfDay(t *testing.T) {
	tm := time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC)
	start := StartOfDay(tm)
	
	if start.Hour() != 0 || start.Minute() != 0 || start.Second() != 0 {
		t.<PERSON><PERSON><PERSON>("StartOfDay failed: got %v", start)
	}
	
	if start.Year() != 2023 || start.Month() != 12 || start.Day() != 25 {
		t.<PERSON><PERSON>("StartOfDay changed date: got %v", start)
	}
}

func TestEndOfDay(t *testing.T) {
	tm := time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC)
	end := EndOfDay(tm)
	
	if end.Hour() != 23 || end.Minute() != 59 || end.Second() != 59 {
		t.<PERSON><PERSON>rf("EndOfDay failed: got %v", end)
	}
}

func TestDaysBetween(t *testing.T) {
	from := time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC)
	to := time.Date(2023, 12, 28, 0, 0, 0, 0, time.UTC)
	
	days := DaysBetween(from, to)
	if days != 3 {
		t.Errorf("DaysBetween = %d; want 3", days)
	}
	
	// Test with different times on same days
	from = time.Date(2023, 12, 25, 23, 59, 59, 0, time.UTC)
	to = time.Date(2023, 12, 28, 0, 0, 1, 0, time.UTC)
	
	days = DaysBetween(from, to)
	if days != 3 {
		t.Errorf("DaysBetween with different times = %d; want 3", days)
	}
}

func TestIsWeekend(t *testing.T) {
	tests := []struct {
		date     time.Time
		expected bool
	}{
		{time.Date(2023, 12, 23, 0, 0, 0, 0, time.UTC), true},  // Saturday
		{time.Date(2023, 12, 24, 0, 0, 0, 0, time.UTC), true},  // Sunday
		{time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC), false}, // Monday
		{time.Date(2023, 12, 29, 0, 0, 0, 0, time.UTC), false}, // Friday
	}
	
	for _, tt := range tests {
		t.Run(tt.date.Weekday().String(), func(t *testing.T) {
			result := IsWeekend(tt.date)
			if result != tt.expected {
				t.Errorf("IsWeekend(%v) = %v; want %v", tt.date.Weekday(), result, tt.expected)
			}
		})
	}
}

func TestAge(t *testing.T) {
	now := time.Now()
	
	tests := []struct {
		birthDate   time.Time
		expectedAge int
	}{
		{now.AddDate(-25, 0, 0), 25},
		{now.AddDate(-25, 0, -1), 25},   // Birthday tomorrow
		{now.AddDate(-25, 0, 1), 24},    // Birthday was yesterday
		{now.AddDate(-1, 0, 0), 1},
	}
	
	for _, tt := range tests {
		age := Age(tt.birthDate)
		if age != tt.expectedAge {
			t.Errorf("Age(%v) = %d; want %d", tt.birthDate.Format("2006-01-02"), age, tt.expectedAge)
		}
	}
}

func TestFormatDuration(t *testing.T) {
	tests := []struct {
		duration time.Duration
		expected string
	}{
		{30 * time.Second, "30 seconds"},
		{5 * time.Minute, "5 minutes"},
		{2 * time.Hour, "2 hours"},
		{25 * time.Hour, "1 day"},
		{48 * time.Hour, "2 days"},
	}
	
	for _, tt := range tests {
		result := FormatDuration(tt.duration)
		if result != tt.expected {
			t.Errorf("FormatDuration(%v) = %q; want %q", tt.duration, result, tt.expected)
		}
	}
}

func TestUnixTimestamps(t *testing.T) {
	tm := time.Date(2023, 12, 25, 15, 30, 45, 123456789, time.UTC)
	
	// Test Unix timestamp
	unix := UnixTimestamp(tm)
	recovered := FromUnixTimestamp(unix)
	
	if !recovered.Equal(tm.Truncate(time.Second)) {
		t.Errorf("Unix timestamp conversion failed: original=%v, recovered=%v", tm, recovered)
	}
	
	// Test Unix millisecond timestamp
	unixMs := UnixMillisTimestamp(tm)
	recoveredMs := FromUnixMillisTimestamp(unixMs)
	
	if !recoveredMs.Equal(tm.Truncate(time.Millisecond)) {
		t.Errorf("Unix millis timestamp conversion failed: original=%v, recovered=%v", tm, recoveredMs)
	}
}