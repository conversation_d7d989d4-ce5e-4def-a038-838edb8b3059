package utils

import (
	"fmt"
	"time"
)

// Common time formats
const (
	DateFormat     = "2006-01-02"
	TimeFormat     = "15:04:05"
	DateTimeFormat = "2006-01-02 15:04:05"
	RFC3339Format  = time.RFC3339
	ISO8601Format  = "2006-01-02T15:04:05Z07:00"
)

// TimeUtil provides time-related utilities
type TimeUtil struct {
	location *time.Location
}

// NewTimeUtil creates a new TimeUtil instance
func NewTimeUtil(location *time.Location) *TimeUtil {
	if location == nil {
		location = time.Local
	}
	return &TimeUtil{location: location}
}

// Now returns current time in the configured location
func (t *TimeUtil) Now() time.Time {
	return time.Now().In(t.location)
}

// Parse parses a time string with the given format
func (t *TimeUtil) Parse(value, format string) (time.Time, error) {
	return time.ParseInLocation(format, value, t.location)
}

// Format formats a time with the given format
func (t *TimeUtil) Format(tm time.Time, format string) string {
	return tm.In(t.location).Format(format)
}

// Package-level functions

// Now returns current time
func Now() time.Time {
	return time.Now()
}

// NowUTC returns current time in UTC
func NowUTC() time.Time {
	return time.Now().UTC()
}

// Today returns today's date at 00:00:00
func Today() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
}

// Tomorrow returns tomorrow's date at 00:00:00
func Tomorrow() time.Time {
	return Today().AddDate(0, 0, 1)
}

// Yesterday returns yesterday's date at 00:00:00
func Yesterday() time.Time {
	return Today().AddDate(0, 0, -1)
}

// StartOfDay returns the start of the day for the given time
func StartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// EndOfDay returns the end of the day for the given time
func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// StartOfWeek returns the start of the week (Monday) for the given time
func StartOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	return StartOfDay(t.AddDate(0, 0, -weekday+1))
}

// EndOfWeek returns the end of the week (Sunday) for the given time
func EndOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	return EndOfDay(t.AddDate(0, 0, 7-weekday))
}

// StartOfMonth returns the start of the month for the given time
func StartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// EndOfMonth returns the end of the month for the given time
func EndOfMonth(t time.Time) time.Time {
	return StartOfMonth(t).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// StartOfYear returns the start of the year for the given time
func StartOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
}

// EndOfYear returns the end of the year for the given time
func EndOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 12, 31, 23, 59, 59, 999999999, t.Location())
}

// FormatDate formats a time as date
func FormatDate(t time.Time) string {
	return t.Format(DateFormat)
}

// FormatTime formats a time as time
func FormatTime(t time.Time) string {
	return t.Format(TimeFormat)
}

// FormatDateTime formats a time as datetime
func FormatDateTime(t time.Time) string {
	return t.Format(DateTimeFormat)
}

// FormatISO8601 formats a time as ISO8601
func FormatISO8601(t time.Time) string {
	return t.Format(ISO8601Format)
}

// ParseDate parses a date string
func ParseDate(s string) (time.Time, error) {
	return time.Parse(DateFormat, s)
}

// ParseDateTime parses a datetime string
func ParseDateTime(s string) (time.Time, error) {
	return time.Parse(DateTimeFormat, s)
}

// ParseISO8601 parses an ISO8601 string
func ParseISO8601(s string) (time.Time, error) {
	return time.Parse(ISO8601Format, s)
}

// DaysBetween calculates the number of days between two times
func DaysBetween(from, to time.Time) int {
	from = StartOfDay(from)
	to = StartOfDay(to)
	return int(to.Sub(from).Hours() / 24)
}

// HoursBetween calculates the number of hours between two times
func HoursBetween(from, to time.Time) float64 {
	return to.Sub(from).Hours()
}

// MinutesBetween calculates the number of minutes between two times
func MinutesBetween(from, to time.Time) float64 {
	return to.Sub(from).Minutes()
}

// SecondsBetween calculates the number of seconds between two times
func SecondsBetween(from, to time.Time) float64 {
	return to.Sub(from).Seconds()
}

// IsToday checks if a time is today
func IsToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.YearDay() == now.YearDay()
}

// IsTomorrow checks if a time is tomorrow
func IsTomorrow(t time.Time) bool {
	tomorrow := Tomorrow()
	return t.Year() == tomorrow.Year() && t.YearDay() == tomorrow.YearDay()
}

// IsYesterday checks if a time is yesterday
func IsYesterday(t time.Time) bool {
	yesterday := Yesterday()
	return t.Year() == yesterday.Year() && t.YearDay() == yesterday.YearDay()
}

// IsFuture checks if a time is in the future
func IsFuture(t time.Time) bool {
	return t.After(time.Now())
}

// IsPast checks if a time is in the past
func IsPast(t time.Time) bool {
	return t.Before(time.Now())
}

// IsWeekend checks if a time is on weekend
func IsWeekend(t time.Time) bool {
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

// IsWeekday checks if a time is on weekday
func IsWeekday(t time.Time) bool {
	return !IsWeekend(t)
}

// Age calculates age from birth date
func Age(birthDate time.Time) int {
	now := time.Now()
	years := now.Year() - birthDate.Year()
	
	// Adjust if birthday hasn't occurred this year
	if now.YearDay() < birthDate.YearDay() {
		years--
	}
	
	return years
}

// FormatDuration formats a duration in human-readable format
func FormatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%d seconds", int(d.Seconds()))
	}
	if d < time.Hour {
		return fmt.Sprintf("%d minutes", int(d.Minutes()))
	}
	if d < 24*time.Hour {
		return fmt.Sprintf("%d hours", int(d.Hours()))
	}
	days := int(d.Hours() / 24)
	if days == 1 {
		return "1 day"
	}
	return fmt.Sprintf("%d days", days)
}

// FormatRelativeTime formats time relative to now
func FormatRelativeTime(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)
	
	if diff < 0 {
		diff = -diff
		if diff < time.Minute {
			return "in a few seconds"
		}
		if diff < time.Hour {
			minutes := int(diff.Minutes())
			if minutes == 1 {
				return "in 1 minute"
			}
			return fmt.Sprintf("in %d minutes", minutes)
		}
		if diff < 24*time.Hour {
			hours := int(diff.Hours())
			if hours == 1 {
				return "in 1 hour"
			}
			return fmt.Sprintf("in %d hours", hours)
		}
		days := int(diff.Hours() / 24)
		if days == 1 {
			return "tomorrow"
		}
		return fmt.Sprintf("in %d days", days)
	}
	
	if diff < time.Minute {
		return "just now"
	}
	if diff < time.Hour {
		minutes := int(diff.Minutes())
		if minutes == 1 {
			return "1 minute ago"
		}
		return fmt.Sprintf("%d minutes ago", minutes)
	}
	if diff < 24*time.Hour {
		hours := int(diff.Hours())
		if hours == 1 {
			return "1 hour ago"
		}
		return fmt.Sprintf("%d hours ago", hours)
	}
	days := int(diff.Hours() / 24)
	if days == 1 {
		return "yesterday"
	}
	if days < 30 {
		return fmt.Sprintf("%d days ago", days)
	}
	if days < 365 {
		months := days / 30
		if months == 1 {
			return "1 month ago"
		}
		return fmt.Sprintf("%d months ago", months)
	}
	years := days / 365
	if years == 1 {
		return "1 year ago"
	}
	return fmt.Sprintf("%d years ago", years)
}

// UnixTimestamp returns Unix timestamp
func UnixTimestamp(t time.Time) int64 {
	return t.Unix()
}

// UnixMillisTimestamp returns Unix timestamp in milliseconds
func UnixMillisTimestamp(t time.Time) int64 {
	return t.UnixNano() / 1e6
}

// FromUnixTimestamp creates time from Unix timestamp
func FromUnixTimestamp(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// FromUnixMillisTimestamp creates time from Unix timestamp in milliseconds
func FromUnixMillisTimestamp(timestamp int64) time.Time {
	return time.Unix(timestamp/1000, (timestamp%1000)*1e6)
}