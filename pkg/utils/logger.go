package utils

import (
	"context"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"

	"github.com/sirupsen/logrus"
)

// Logger interface defines logging methods
type Logger interface {
	// Basic logging methods
	Debug(args ...interface{})
	Info(args ...interface{})
	Warn(args ...interface{})
	Error(args ...interface{})
	Fatal(args ...interface{})
	Panic(args ...interface{})
	
	// Formatted logging methods
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
	Fatalf(format string, args ...interface{})
	Panicf(format string, args ...interface{})
	
	// With fields
	WithField(key string, value interface{}) Logger
	WithFields(fields Fields) Logger
	WithError(err error) Logger
	WithContext(ctx context.Context) Logger
	
	// Get underlying logger (for advanced usage)
	GetLogger() interface{}
}

// Fields type for structured logging
type Fields map[string]interface{}

// Level represents log level
type Level string

const (
	DebugLevel Level = "debug"
	InfoLevel  Level = "info"
	WarnLevel  Level = "warn"
	ErrorLevel Level = "error"
	FatalLevel Level = "fatal"
	PanicLevel Level = "panic"
)

// LogrusLogger wraps logrus logger
type LogrusLogger struct {
	logger *logrus.Entry
}

// NewLogger creates a new logger instance
func NewLogger() Logger {
	logger := logrus.New()
	
	// Set default formatter
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			filename := strings.TrimPrefix(f.File, os.Getenv("GOPATH")+"/src/")
			return fmt.Sprintf("%s()", f.Function), fmt.Sprintf("%s:%d", filename, f.Line)
		},
	})
	
	// Set output
	logger.SetOutput(os.Stdout)
	
	// Set level from environment
	level := os.Getenv("LOG_LEVEL")
	if level == "" {
		level = "info"
	}
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	logger.SetLevel(logLevel)
	
	// Enable caller reporting
	logger.SetReportCaller(true)
	
	return &LogrusLogger{logger: logrus.NewEntry(logger)}
}

// NewLoggerWithConfig creates a logger with configuration
func NewLoggerWithConfig(config LoggerConfig) Logger {
	logger := logrus.New()
	
	// Set formatter
	if config.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			PrettyPrint:     true, // Enable pretty JSON formatting
		})
	} else if config.Format == "pretty" {
		// Beautiful text format with colors
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
			ForceColors:     true,
			DisableQuote:    true,
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
		})
	}
	
	// Set output
	if config.Output != nil {
		logger.SetOutput(config.Output)
	} else {
		logger.SetOutput(os.Stdout)
	}
	
	// Set level
	logLevel, err := logrus.ParseLevel(config.Level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	logger.SetLevel(logLevel)
	
	// Set caller reporting
	logger.SetReportCaller(config.ReportCaller)
	
	// Create entry with default fields
	entry := logrus.NewEntry(logger)
	if len(config.DefaultFields) > 0 {
		entry = entry.WithFields(logrus.Fields(config.DefaultFields))
	}
	
	return &LogrusLogger{logger: entry}
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level         string
	Format        string // "json" or "text"
	Output        io.Writer
	ReportCaller  bool
	DefaultFields Fields
}

// Implementation of Logger interface

func (l *LogrusLogger) Debug(args ...interface{}) {
	l.logger.Debug(args...)
}

func (l *LogrusLogger) Info(args ...interface{}) {
	l.logger.Info(args...)
}

func (l *LogrusLogger) Warn(args ...interface{}) {
	l.logger.Warn(args...)
}

func (l *LogrusLogger) Error(args ...interface{}) {
	l.logger.Error(args...)
}

func (l *LogrusLogger) Fatal(args ...interface{}) {
	l.logger.Fatal(args...)
}

func (l *LogrusLogger) Panic(args ...interface{}) {
	l.logger.Panic(args...)
}

func (l *LogrusLogger) Debugf(format string, args ...interface{}) {
	l.logger.Debugf(format, args...)
}

func (l *LogrusLogger) Infof(format string, args ...interface{}) {
	l.logger.Infof(format, args...)
}

func (l *LogrusLogger) Warnf(format string, args ...interface{}) {
	l.logger.Warnf(format, args...)
}

func (l *LogrusLogger) Errorf(format string, args ...interface{}) {
	l.logger.Errorf(format, args...)
}

func (l *LogrusLogger) Fatalf(format string, args ...interface{}) {
	l.logger.Fatalf(format, args...)
}

func (l *LogrusLogger) Panicf(format string, args ...interface{}) {
	l.logger.Panicf(format, args...)
}

func (l *LogrusLogger) WithField(key string, value interface{}) Logger {
	return &LogrusLogger{logger: l.logger.WithField(key, value)}
}

func (l *LogrusLogger) WithFields(fields Fields) Logger {
	return &LogrusLogger{logger: l.logger.WithFields(logrus.Fields(fields))}
}

func (l *LogrusLogger) WithError(err error) Logger {
	return &LogrusLogger{logger: l.logger.WithError(err)}
}

func (l *LogrusLogger) WithContext(ctx context.Context) Logger {
	// Extract request ID from context if available
	if requestID, ok := ctx.Value("requestID").(string); ok {
		return l.WithField("request_id", requestID)
	}
	return l
}

func (l *LogrusLogger) GetLogger() interface{} {
	return l.logger
}

// Global logger instance
var defaultLogger Logger

func init() {
	defaultLogger = NewLogger()
}

// SetDefaultLogger sets the default logger
func SetDefaultLogger(logger Logger) {
	defaultLogger = logger
}

// GetDefaultLogger returns the default logger
func GetDefaultLogger() Logger {
	return defaultLogger
}

// Package-level logging functions using default logger

func Debug(args ...interface{}) {
	defaultLogger.Debug(args...)
}

func Info(args ...interface{}) {
	defaultLogger.Info(args...)
}

func Warn(args ...interface{}) {
	defaultLogger.Warn(args...)
}

func Error(args ...interface{}) {
	defaultLogger.Error(args...)
}

func Fatal(args ...interface{}) {
	defaultLogger.Fatal(args...)
}

func Panic(args ...interface{}) {
	defaultLogger.Panic(args...)
}

func Debugf(format string, args ...interface{}) {
	defaultLogger.Debugf(format, args...)
}

func Infof(format string, args ...interface{}) {
	defaultLogger.Infof(format, args...)
}

func Warnf(format string, args ...interface{}) {
	defaultLogger.Warnf(format, args...)
}

func Errorf(format string, args ...interface{}) {
	defaultLogger.Errorf(format, args...)
}

func Fatalf(format string, args ...interface{}) {
	defaultLogger.Fatalf(format, args...)
}

func Panicf(format string, args ...interface{}) {
	defaultLogger.Panicf(format, args...)
}

func WithField(key string, value interface{}) Logger {
	return defaultLogger.WithField(key, value)
}

func WithFields(fields Fields) Logger {
	return defaultLogger.WithFields(fields)
}

func WithError(err error) Logger {
	return defaultLogger.WithError(err)
}

func WithContext(ctx context.Context) Logger {
	return defaultLogger.WithContext(ctx)
}