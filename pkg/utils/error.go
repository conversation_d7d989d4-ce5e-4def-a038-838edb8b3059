package utils

import (
	"errors"
	"fmt"
	"runtime"
	"strings"
)

// AppError represents an application error with additional context
type AppError struct {
	Code       string                 `json:"code,omitempty"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details,omitempty"`
	StatusCode int                    `json:"-"`
	Err        error                  `json:"-"`
	Stack      []string               `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

// Unwrap returns the wrapped error
func (e *AppError) Unwrap() error {
	return e.Err
}

// WithCode sets the error code
func (e *AppError) WithCode(code string) *AppError {
	e.Code = code
	return e
}

// WithStatusCode sets the HTTP status code
func (e *AppError) WithStatusCode(code int) *AppError {
	e.StatusCode = code
	return e
}

// WithDetails adds details to the error
func (e *AppError) WithDetails(details map[string]interface{}) *AppError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	for k, v := range details {
		e.Details[k] = v
	}
	return e
}

// WithDetail adds a single detail to the error
func (e *AppError) WithDetail(key string, value interface{}) *AppError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	e.Details[key] = value
	return e
}

// NewError creates a new AppError
func NewError(message string, err error) *AppError {
	appErr := &AppError{
		Message: message,
		Err:     err,
	}
	appErr.captureStack()
	return appErr
}

// NewErrorf creates a new AppError with formatted message
func NewErrorf(format string, args ...interface{}) *AppError {
	appErr := &AppError{
		Message: fmt.Sprintf(format, args...),
	}
	appErr.captureStack()
	return appErr
}

// WrapError wraps an error with additional context
func WrapError(err error, message string) *AppError {
	if err == nil {
		return nil
	}
	
	// If it's already an AppError, add context
	if appErr, ok := err.(*AppError); ok {
		appErr.Message = fmt.Sprintf("%s: %s", message, appErr.Message)
		return appErr
	}
	
	return NewError(message, err)
}

// WrapErrorf wraps an error with formatted message
func WrapErrorf(err error, format string, args ...interface{}) *AppError {
	if err == nil {
		return nil
	}
	return WrapError(err, fmt.Sprintf(format, args...))
}

// captureStack captures the current stack trace
func (e *AppError) captureStack() {
	const maxStackDepth = 32
	pcs := make([]uintptr, maxStackDepth)
	n := runtime.Callers(3, pcs)
	
	frames := runtime.CallersFrames(pcs[:n])
	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			e.Stack = append(e.Stack, fmt.Sprintf("%s:%d %s", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}
}

// Common error types

// ValidationError creates a validation error
func ValidationError(message string, fields map[string]string) *AppError {
	details := make(map[string]interface{})
	for k, v := range fields {
		details[k] = v
	}
	
	return NewError(message, nil).
		WithCode("VALIDATION_ERROR").
		WithStatusCode(422).
		WithDetails(details)
}

// NotFoundError creates a not found error
func NotFoundError(resource string) *AppError {
	return NewErrorf("%s not found", resource).
		WithCode("NOT_FOUND").
		WithStatusCode(404)
}

// UnauthorizedError creates an unauthorized error
func UnauthorizedError(message string) *AppError {
	if message == "" {
		message = "Unauthorized"
	}
	return NewError(message, nil).
		WithCode("UNAUTHORIZED").
		WithStatusCode(401)
}

// ForbiddenError creates a forbidden error
func ForbiddenError(message string) *AppError {
	if message == "" {
		message = "Forbidden"
	}
	return NewError(message, nil).
		WithCode("FORBIDDEN").
		WithStatusCode(403)
}

// BadRequestError creates a bad request error
func BadRequestError(message string) *AppError {
	return NewError(message, nil).
		WithCode("BAD_REQUEST").
		WithStatusCode(400)
}

// InternalError creates an internal server error
func InternalError(err error) *AppError {
	return NewError("Internal server error", err).
		WithCode("INTERNAL_ERROR").
		WithStatusCode(500)
}

// ConflictError creates a conflict error
func ConflictError(message string) *AppError {
	return NewError(message, nil).
		WithCode("CONFLICT").
		WithStatusCode(409)
}

// Error checking functions

// IsAppError checks if an error is an AppError
func IsAppError(err error) bool {
	var appErr *AppError
	return errors.As(err, &appErr)
}

// GetAppError extracts AppError from an error
func GetAppError(err error) *AppError {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr
	}
	return nil
}

// IsNotFound checks if an error is a not found error
func IsNotFound(err error) bool {
	appErr := GetAppError(err)
	return appErr != nil && appErr.Code == "NOT_FOUND"
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	appErr := GetAppError(err)
	return appErr != nil && appErr.Code == "VALIDATION_ERROR"
}

// IsUnauthorized checks if an error is an unauthorized error
func IsUnauthorized(err error) bool {
	appErr := GetAppError(err)
	return appErr != nil && appErr.Code == "UNAUTHORIZED"
}

// IsForbidden checks if an error is a forbidden error
func IsForbidden(err error) bool {
	appErr := GetAppError(err)
	return appErr != nil && appErr.Code == "FORBIDDEN"
}

// ErrorList represents a list of errors
type ErrorList struct {
	Errors []*AppError `json:"errors"`
}

// Add adds an error to the list
func (el *ErrorList) Add(err *AppError) {
	el.Errors = append(el.Errors, err)
}

// HasErrors checks if there are any errors
func (el *ErrorList) HasErrors() bool {
	return len(el.Errors) > 0
}

// Error implements the error interface
func (el *ErrorList) Error() string {
	if len(el.Errors) == 0 {
		return ""
	}
	
	messages := make([]string, len(el.Errors))
	for i, err := range el.Errors {
		messages[i] = err.Error()
	}
	return strings.Join(messages, "; ")
}

// FirstError returns the first error or nil
func (el *ErrorList) FirstError() error {
	if len(el.Errors) == 0 {
		return nil
	}
	return el.Errors[0]
}

// NewErrorList creates a new error list
func NewErrorList() *ErrorList {
	return &ErrorList{
		Errors: make([]*AppError, 0),
	}
}