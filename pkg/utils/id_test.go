package utils

import (
	"strings"
	"testing"
)

func TestGenerateUUID(t *testing.T) {
	uuid := GenerateUUID()
	
	if len(uuid) != 36 {
		t.<PERSON><PERSON><PERSON>("UUID length = %d; want 36", len(uuid))
	}
	
	if !ValidateUUID(uuid) {
		t.<PERSON><PERSON>("Generated UUID is not valid: %s", uuid)
	}
}

func TestGenerateULID(t *testing.T) {
	ulid := GenerateULID()
	
	if len(ulid) != 26 {
		t.<PERSON><PERSON>("ULID length = %d; want 26", len(ulid))
	}
	
	if !ValidateULID(ulid) {
		t.<PERSON><PERSON>rf("Generated ULID is not valid: %s", ulid)
	}
}

func TestGenerateNanoID(t *testing.T) {
	// Test default size
	id := GenerateNanoID()
	if len(id) != 21 {
		t.<PERSON><PERSON>rf("NanoID length = %d; want 21", len(id))
	}
	
	// Test custom size
	customID := GenerateNanoID(10)
	if len(customID) != 10 {
		t.<PERSON><PERSON><PERSON>("Custom NanoID length = %d; want 10", len(customID))
	}
}

func TestGenerateRandomHex(t *testing.T) {
	hex, err := GenerateRandomHex(16)
	if err != nil {
		t.Fatalf("GenerateRandomHex error: %v", err)
	}
	
	if len(hex) != 32 { // 16 bytes = 32 hex characters
		t.Errorf("Hex length = %d; want 32", len(hex))
	}
}

func TestGenerateShortCode(t *testing.T) {
	// Test default length
	code := GenerateShortCode()
	if len(code) != 6 {
		t.Errorf("ShortCode length = %d; want 6", len(code))
	}
	
	// Test custom length
	customCode := GenerateShortCode(8)
	if len(customCode) != 8 {
		t.Errorf("Custom ShortCode length = %d; want 8", len(customCode))
	}
	
	// Should only contain uppercase letters and numbers
	for _, r := range code {
		if !((r >= '0' && r <= '9') || (r >= 'A' && r <= 'Z')) {
			t.Errorf("ShortCode contains invalid character: %c", r)
		}
	}
}

func TestGenerateAPIKey(t *testing.T) {
	key, err := GenerateAPIKey()
	if err != nil {
		t.Fatalf("GenerateAPIKey error: %v", err)
	}
	
	if !strings.HasPrefix(key, "sk_") {
		t.Errorf("API key should start with 'sk_', got: %s", key)
	}
	
	if len(key) != 67 { // "sk_" (3) + 64 hex characters
		t.Errorf("API key length = %d; want 67", len(key))
	}
}

func TestUUIDGenerator(t *testing.T) {
	gen := NewUUIDGenerator()
	
	// Test Generate
	id1 := gen.Generate()
	id2 := gen.Generate()
	
	if id1 == id2 {
		t.Error("UUIDs should be unique")
	}
	
	// Test GenerateWithPrefix
	prefixed := gen.GenerateWithPrefix("user")
	if !strings.HasPrefix(prefixed, "user_") {
		t.Errorf("Prefixed UUID should start with 'user_', got: %s", prefixed)
	}
}