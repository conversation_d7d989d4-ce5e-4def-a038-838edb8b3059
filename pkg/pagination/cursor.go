package pagination

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"
)

type Cursor struct {
	ID    int64     `json:"id"`
	Time  time.Time `json:"time"`
	Score *float64  `json:"score,omitempty"`
}

type CursorPagination struct {
	Cursor string `json:"cursor,omitempty"`
	Limit  int    `json:"limit"`
}

type CursorResponse struct {
	NextCursor     string `json:"next_cursor,omitempty"`
	PreviousCursor string `json:"previous_cursor,omitempty"`
	Has<PERSON>ore        bool   `json:"has_more"`
	HasPrevious    bool   `json:"has_previous"`
	Limit          int    `json:"limit"`
}

func EncodeCursor(cursor Cursor) (string, error) {
	data, err := json.Marshal(cursor)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor: %w", err)
	}
	return base64.URLEncoding.EncodeToString(data), nil
}

func DecodeCursor(encoded string) (*Cursor, error) {
	if encoded == "" {
		return nil, nil
	}

	data, err := base64.URLEncoding.DecodeString(encoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	var cursor Cursor
	if err := json.Unmarshal(data, &cursor); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cursor: %w", err)
	}

	return &cursor, nil
}

func NewCursorFromEntity(id int64, createdAt time.Time, score *float64) *Cursor {
	return &Cursor{
		ID:    id,
		Time:  createdAt,
		Score: score,
	}
}

func (c *Cursor) String() (string, error) {
	return EncodeCursor(*c)
}

func ValidateLimit(limit int) int {
	const (
		minLimit     = 1
		maxLimit     = 100
		defaultLimit = 20
	)

	if limit <= 0 {
		return defaultLimit
	}
	if limit > maxLimit {
		return maxLimit
	}
	return limit
}