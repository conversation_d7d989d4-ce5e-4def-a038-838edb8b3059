package http

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// MaxBodySize is the maximum allowed request body size (10MB)
const MaxBodySize = 10 * 1024 * 1024

// Request provides helper methods for HTTP requests
type Request struct {
	*http.Request
}

// NewRequest wraps an http.Request with helper methods
func NewRequest(r *http.Request) *Request {
	return &Request{Request: r}
}

// DecodeJSON decodes JSON request body into the given interface
func (r *Request) DecodeJSON(v interface{}) error {
	// Limit request body size
	r.Body = http.MaxBytesReader(nil, r.Body, MaxBodySize)
	
	// Decode JSON
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields()
	
	if err := decoder.Decode(v); err != nil {
		if err == io.EOF {
			return fmt.Errorf("request body is empty")
		}
		return fmt.Errorf("invalid JSON: %w", err)
	}
	
	// Check for extra data
	if decoder.More() {
		return fmt.Errorf("request body contains extra data")
	}
	
	return nil
}

// PathParam returns a path parameter by name
func (r *Request) PathParam(name string) string {
	vars := mux.Vars(r.Request)
	return vars[name]
}

// QueryParam returns a query parameter by name
func (r *Request) QueryParam(name string) string {
	return r.URL.Query().Get(name)
}

// QueryParamInt returns a query parameter as integer
func (r *Request) QueryParamInt(name string, defaultValue int) int {
	param := r.QueryParam(name)
	if param == "" {
		return defaultValue
	}
	
	value, err := strconv.Atoi(param)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// QueryParamBool returns a query parameter as boolean
func (r *Request) QueryParamBool(name string, defaultValue bool) bool {
	param := r.QueryParam(name)
	if param == "" {
		return defaultValue
	}
	
	value, err := strconv.ParseBool(param)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// QueryParams returns all query parameters
func (r *Request) QueryParams() map[string][]string {
	return r.URL.Query()
}

// HeaderValue returns a header value by name
func (r *Request) HeaderValue(name string) string {
	return r.Header.Get(name)
}

// BearerToken extracts bearer token from Authorization header
func (r *Request) BearerToken() string {
	auth := r.Header.Get("Authorization")
	if auth == "" {
		return ""
	}
	
	parts := strings.Split(auth, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return ""
	}
	
	return parts[1]
}

// RequestID returns the request ID from context
func (r *Request) RequestID() string {
	if id, ok := r.Context().Value("requestID").(string); ok {
		return id
	}
	return ""
}

// RemoteIP returns the remote IP address
func (r *Request) RemoteIP() string {
	// Check X-Forwarded-For header first
	xff := r.Header.Get("X-Forwarded-For")
	if xff != "" {
		// Take the first IP in the chain
		parts := strings.Split(xff, ",")
		return strings.TrimSpace(parts[0])
	}
	
	// Check X-Real-IP header
	xri := r.Header.Get("X-Real-IP")
	if xri != "" {
		return xri
	}
	
	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}
	
	return ip
}

// Pagination represents offset-based pagination parameters (deprecated)
type Pagination struct {
	Page     int
	PageSize int
	Offset   int
	Limit    int
}

// GetPagination extracts offset-based pagination parameters from query string (deprecated)
func (r *Request) GetPagination(defaultPageSize, maxPageSize int) *Pagination {
	page := r.QueryParamInt("page", 1)
	if page < 1 {
		page = 1
	}
	
	pageSize := r.QueryParamInt("page_size", defaultPageSize)
	if pageSize < 1 {
		pageSize = defaultPageSize
	}
	if pageSize > maxPageSize {
		pageSize = maxPageSize
	}
	
	offset := (page - 1) * pageSize
	
	return &Pagination{
		Page:     page,
		PageSize: pageSize,
		Offset:   offset,
		Limit:    pageSize,
	}
}

// GetCursorPagination extracts cursor-based pagination parameters from query string
func (r *Request) GetCursorPagination(defaultLimit int) (*pagination.CursorPagination, error) {
	cursor := r.QueryParam("cursor")
	limit := r.QueryParamInt("limit", defaultLimit)
	limit = pagination.ValidateLimit(limit)
	
	return &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}, nil
}

// SortOrder represents sort parameters
type SortOrder struct {
	Field     string
	Direction string // "asc" or "desc"
}

// GetSortOrder extracts sort parameters from query string
func (r *Request) GetSortOrder(defaultField, defaultDirection string) *SortOrder {
	field := r.QueryParam("sort_by")
	if field == "" {
		field = defaultField
	}
	
	direction := strings.ToLower(r.QueryParam("sort_order"))
	if direction != "asc" && direction != "desc" {
		direction = defaultDirection
	}
	
	return &SortOrder{
		Field:     field,
		Direction: direction,
	}
}