package http

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
)

func TestChain(t *testing.T) {
	var order []string
	
	middleware1 := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			order = append(order, "m1-before")
			next.ServeHTTP(w, r)
			order = append(order, "m1-after")
		})
	}
	
	middleware2 := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			order = append(order, "m2-before")
			next.ServeHTTP(w, r)
			order = append(order, "m2-after")
		})
	}
	
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		order = append(order, "handler")
	})
	
	chain := Chain(middleware1, middleware2)
	finalHandler := chain(handler)
	
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	
	finalHandler.ServeHTTP(rr, req)
	
	expected := []string{"m1-before", "m2-before", "handler", "m2-after", "m1-after"}
	if len(order) != len(expected) {
		t.Fatalf("Expected order length %d, got %d", len(expected), len(order))
	}
	
	for i, v := range expected {
		if order[i] != v {
			t.Errorf("Expected order[%d] = %s, got %s", i, v, order[i])
		}
	}
}

func TestWithLogging(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusCreated)
		w.Write([]byte("test"))
	})
	
	logging := WithLogging()
	wrappedHandler := logging(handler)
	
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()
	
	// Capture log output would require redirecting stdout
	// For now, just verify it doesn't panic
	wrappedHandler.ServeHTTP(rr, req)
	
	if rr.Code != http.StatusCreated {
		t.Errorf("Expected status %d, got %d", http.StatusCreated, rr.Code)
	}
}

func TestWithRecovery(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		panic("test panic")
	})
	
	recovery := WithRecovery()
	wrappedHandler := recovery(handler)
	
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	
	// Should not panic
	wrappedHandler.ServeHTTP(rr, req)
	
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, rr.Code)
	}
	
	if !strings.Contains(rr.Body.String(), "Internal Server Error") {
		t.Error("Expected error message in response")
	}
}

func TestWithTimeout(t *testing.T) {
	// Test timeout occurs
	slowHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
	})
	
	timeout := WithTimeout(50 * time.Millisecond)
	wrappedHandler := timeout(slowHandler)
	
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	
	wrappedHandler.ServeHTTP(rr, req)
	
	// Give some time for the handler to complete
	time.Sleep(150 * time.Millisecond)
	
	if rr.Code != http.StatusRequestTimeout {
		t.Errorf("Expected status %d, got %d", http.StatusRequestTimeout, rr.Code)
	}
	
	// Test no timeout
	fastHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	
	timeout = WithTimeout(100 * time.Millisecond)
	wrappedHandler = timeout(fastHandler)
	
	req = httptest.NewRequest("GET", "/", nil)
	rr = httptest.NewRecorder()
	
	wrappedHandler.ServeHTTP(rr, req)
	
	if rr.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, rr.Code)
	}
}

func TestWithRateLimit(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	
	// Allow 2 requests per second with burst of 2
	rateLimit := WithRateLimit(2, 2)
	wrappedHandler := rateLimit(handler)
	
	// First 2 requests should succeed
	for i := 0; i < 2; i++ {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(rr, req)
		
		if rr.Code != http.StatusOK {
			t.Errorf("Request %d: Expected status %d, got %d", i+1, http.StatusOK, rr.Code)
		}
	}
	
	// Third request should be rate limited
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	wrappedHandler.ServeHTTP(rr, req)
	
	if rr.Code != http.StatusTooManyRequests {
		t.Errorf("Expected status %d, got %d", http.StatusTooManyRequests, rr.Code)
	}
}

func TestWithRequestID(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		id := r.Context().Value("requestID")
		if id == nil {
			t.Error("Request ID not found in context")
		}
		w.WriteHeader(http.StatusOK)
	})
	
	requestID := WithRequestID()
	wrappedHandler := requestID(handler)
	
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	
	wrappedHandler.ServeHTTP(rr, req)
	
	headerID := rr.Header().Get("X-Request-ID")
	if headerID == "" {
		t.Error("X-Request-ID header not set")
	}
}

func TestWithContentType(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	
	contentType := WithContentType("application/json")
	wrappedHandler := contentType(handler)
	
	req := httptest.NewRequest("GET", "/", nil)
	rr := httptest.NewRecorder()
	
	wrappedHandler.ServeHTTP(rr, req)
	
	ct := rr.Header().Get("Content-Type")
	if ct != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got '%s'", ct)
	}
}