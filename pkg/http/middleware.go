package http

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/rs/cors"
	"golang.org/x/time/rate"
)

// Middleware is a function that wraps an http.Handler
type Middleware func(http.Handler) http.Handler

// Chain creates a new middleware chain
func Chain(middlewares ...Middleware) Middleware {
	return func(next http.Handler) http.Handler {
		for i := len(middlewares) - 1; i >= 0; i-- {
			next = middlewares[i](next)
		}
		return next
	}
}

// WithLogging adds request logging middleware
func WithLogging() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Create a response writer wrapper to capture status code
			rw := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}
			
			// Call the next handler
			next.ServeHTTP(rw, r)
			
			// Log request details
			duration := time.Since(start)
			fmt.Printf("[%s] %s %s %d %v\n",
				start.Format("2006-01-02 15:04:05"),
				r.<PERSON>,
				r.<PERSON>,
				rw.statusCode,
				duration,
			)
		})
	}
}

// WithRecovery adds panic recovery middleware
func WithRecovery() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					// Log the panic
					fmt.Printf("Panic recovered: %v\n", err)
					
					// Return 500 Internal Server Error
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusInternalServerError)
					w.Write([]byte(`{"error":"Internal Server Error"}`))
				}
			}()
			
			next.ServeHTTP(w, r)
		})
	}
}

// WithTimeout adds request timeout middleware
func WithTimeout(timeout time.Duration) Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, cancel := context.WithTimeout(r.Context(), timeout)
			defer cancel()
			
			// Create a channel to signal completion
			done := make(chan struct{})
			
			// Run the handler in a goroutine
			go func() {
				next.ServeHTTP(w, r.WithContext(ctx))
				close(done)
			}()
			
			// Wait for either completion or timeout
			select {
			case <-done:
				// Handler completed successfully
			case <-ctx.Done():
				// Timeout occurred
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusRequestTimeout)
				w.Write([]byte(`{"error":"Request Timeout"}`))
			}
		})
	}
}

// WithRateLimit adds rate limiting middleware
func WithRateLimit(rps int, burst int) Middleware {
	limiter := rate.NewLimiter(rate.Limit(rps), burst)
	
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if !limiter.Allow() {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusTooManyRequests)
				w.Write([]byte(`{"error":"Too Many Requests"}`))
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// WithRequestID adds a unique request ID to the context
func WithRequestID() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Generate request ID (simplified for now)
			requestID := fmt.Sprintf("%d", time.Now().UnixNano())
			
			// Add to context
			ctx := context.WithValue(r.Context(), "requestID", requestID)
			
			// Add to response header
			w.Header().Set("X-Request-ID", requestID)
			
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// WithContentType ensures Content-Type is set for responses
func WithContentType(contentType string) Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", contentType)
			next.ServeHTTP(w, r)
		})
	}
}

// applyCORS applies CORS configuration to a handler
func applyCORS(handler http.Handler, config *CORSConfig) http.Handler {
	c := cors.New(cors.Options{
		AllowedOrigins:   config.AllowedOrigins,
		AllowedMethods:   config.AllowedMethods,
		AllowedHeaders:   config.AllowedHeaders,
		ExposedHeaders:   config.ExposedHeaders,
		AllowCredentials: config.AllowCredentials,
		MaxAge:           config.MaxAge,
	})
	
	return c.Handler(handler)
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
	written    bool
}

func (rw *responseWriter) WriteHeader(code int) {
	if !rw.written {
		rw.statusCode = code
		rw.ResponseWriter.WriteHeader(code)
		rw.written = true
	}
}

func (rw *responseWriter) Write(b []byte) (int, error) {
	if !rw.written {
		rw.WriteHeader(http.StatusOK)
	}
	return rw.ResponseWriter.Write(b)
}