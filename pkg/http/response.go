package http

import (
	"encoding/json"
	"net/http"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// Response provides helper methods for HTTP responses
type Response struct {
	http.ResponseWriter
	written bool
}

// NewResponse wraps an http.ResponseWriter with helper methods
func NewResponse(w http.ResponseWriter) *Response {
	return &Response{ResponseWriter: w}
}

// JSON sends a JSON response with the given status code
func (r *Response) JSON(status int, data interface{}) error {
	r.Header().Set("Content-Type", "application/json; charset=utf-8")
	r.WriteHeader(status)
	r.written = true
	
	if data == nil {
		return nil
	}
	
	encoder := json.NewEncoder(r)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}

// Success sends a success response
func (r *Response) Success(data interface{}) error {
	return r.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Data:    data,
	})
}

// Created sends a created response
func (r *Response) Created(data interface{}) error {
	return r.JSON(http.StatusCreated, SuccessResponse{
		Success: true,
		Data:    data,
	})
}

// NoContent sends a no content response
func (r *Response) NoContent() error {
	r.WriteHeader(http.StatusNoContent)
	r.written = true
	return nil
}

// Error sends an error response
func (r *Response) Error(status int, message string) error {
	return r.JSON(status, ErrorResponse{
		Success: false,
		Error: ErrorDetail{
			Message: message,
			Code:    getErrorCode(status),
		},
	})
}

// ErrorWithDetails sends an error response with additional details
func (r *Response) ErrorWithDetails(status int, message string, details interface{}) error {
	return r.JSON(status, ErrorResponse{
		Success: false,
		Error: ErrorDetail{
			Message: message,
			Code:    getErrorCode(status),
			Details: details,
		},
	})
}

// BadRequest sends a bad request error response
func (r *Response) BadRequest(message string) error {
	return r.Error(http.StatusBadRequest, message)
}

// Unauthorized sends an unauthorized error response
func (r *Response) Unauthorized(message string) error {
	if message == "" {
		message = "Unauthorized"
	}
	return r.Error(http.StatusUnauthorized, message)
}

// Forbidden sends a forbidden error response
func (r *Response) Forbidden(message string) error {
	if message == "" {
		message = "Forbidden"
	}
	return r.Error(http.StatusForbidden, message)
}

// NotFound sends a not found error response
func (r *Response) NotFound(message string) error {
	if message == "" {
		message = "Not Found"
	}
	return r.Error(http.StatusNotFound, message)
}

// InternalServerError sends an internal server error response
func (r *Response) InternalServerError(message string) error {
	if message == "" {
		message = "Internal Server Error"
	}
	return r.Error(http.StatusInternalServerError, message)
}

// ValidationError sends a validation error response
func (r *Response) ValidationError(errors map[string]string) error {
	return r.ErrorWithDetails(
		http.StatusUnprocessableEntity,
		"Validation failed",
		errors,
	)
}

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Success bool        `json:"success"`
	Error   ErrorDetail `json:"error"`
}

// ErrorDetail contains error details
type ErrorDetail struct {
	Message string      `json:"message"`
	Code    string      `json:"code"`
	Details interface{} `json:"details,omitempty"`
}

// PaginatedResponse represents a paginated API response (deprecated)
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data"`
	Pagination Metadata    `json:"pagination"`
}

// Metadata contains pagination metadata (deprecated)
type Metadata struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	TotalPages int `json:"total_pages"`
	TotalItems int `json:"total_items"`
}

// CursorPaginatedResponse represents a cursor-based paginated API response
type CursorPaginatedResponse struct {
	Success bool                        `json:"success"`
	Data    interface{}                 `json:"data"`
	Meta    pagination.CursorResponse   `json:"meta"`
}

// Paginated sends a paginated response (deprecated)
func (r *Response) Paginated(data interface{}, page, pageSize, totalItems int) error {
	totalPages := (totalItems + pageSize - 1) / pageSize
	
	return r.JSON(http.StatusOK, PaginatedResponse{
		Success: true,
		Data:    data,
		Pagination: Metadata{
			Page:       page,
			PageSize:   pageSize,
			TotalPages: totalPages,
			TotalItems: totalItems,
		},
	})
}

// CursorPaginated sends a cursor-based paginated response
func (r *Response) CursorPaginated(data interface{}, meta pagination.CursorResponse) error {
	return r.JSON(http.StatusOK, CursorPaginatedResponse{
		Success: true,
		Data:    data,
		Meta:    meta,
	})
}

// getErrorCode returns an error code based on HTTP status
func getErrorCode(status int) string {
	switch status {
	case http.StatusBadRequest:
		return "BAD_REQUEST"
	case http.StatusUnauthorized:
		return "UNAUTHORIZED"
	case http.StatusForbidden:
		return "FORBIDDEN"
	case http.StatusNotFound:
		return "NOT_FOUND"
	case http.StatusMethodNotAllowed:
		return "METHOD_NOT_ALLOWED"
	case http.StatusConflict:
		return "CONFLICT"
	case http.StatusUnprocessableEntity:
		return "VALIDATION_ERROR"
	case http.StatusTooManyRequests:
		return "TOO_MANY_REQUESTS"
	case http.StatusInternalServerError:
		return "INTERNAL_ERROR"
	case http.StatusServiceUnavailable:
		return "SERVICE_UNAVAILABLE"
	default:
		return "ERROR"
	}
}