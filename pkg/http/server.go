package http

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gorilla/mux"
)

// Server represents an HTTP server with enhanced functionality
type Server struct {
	config     *Config
	router     *mux.Router
	httpServer *http.Server
	middleware []Middleware
}

// Config holds server configuration
type Config struct {
	Host            string
	Port            int
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	IdleTimeout     time.Duration
	ShutdownTimeout time.Duration
	MaxHeaderBytes  int
	EnableCORS      bool
	CORSOptions     *CORSConfig
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins   []string
	AllowedMethods   []string
	AllowedHeaders   []string
	ExposedHeaders   []string
	AllowCredentials bool
	MaxAge           int
}

// NewServer creates a new HTTP server instance
func NewServer(config *Config) *Server {
	if config == nil {
		config = DefaultConfig()
	}

	router := mux.NewRouter()

	return &Server{
		config:     config,
		router:     router,
		middleware: make([]Middleware, 0),
	}
}

// DefaultConfig returns a default server configuration
func DefaultConfig() *Config {
	return &Config{
		Host:            "0.0.0.0",
		Port:            9077,
		ReadTimeout:     15 * time.Second,
		WriteTimeout:    15 * time.Second,
		IdleTimeout:     60 * time.Second,
		ShutdownTimeout: 30 * time.Second,
		MaxHeaderBytes:  1 << 20, // 1 MB
		EnableCORS:      true,
		CORSOptions: &CORSConfig{
			AllowedOrigins:   []string{"*"},
			AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization"},
			ExposedHeaders:   []string{"Content-Length"},
			AllowCredentials: true,
			MaxAge:           86400,
		},
	}
}

// Use adds middleware to the server
func (s *Server) Use(middleware ...Middleware) {
	s.middleware = append(s.middleware, middleware...)
}

// Router returns the underlying router
func (s *Server) Router() *mux.Router {
	return s.router
}

// Start starts the HTTP server with graceful shutdown
func (s *Server) Start(ctx context.Context) error {
	// Build middleware chain
	handler := s.buildHandler()

	// Create HTTP server
	s.httpServer = &http.Server{
		Addr:           fmt.Sprintf("%s:%d", s.config.Host, s.config.Port),
		Handler:        handler,
		ReadTimeout:    s.config.ReadTimeout,
		WriteTimeout:   s.config.WriteTimeout,
		IdleTimeout:    s.config.IdleTimeout,
		MaxHeaderBytes: s.config.MaxHeaderBytes,
	}

	// Channel to listen for errors
	serverErrors := make(chan error, 1)

	// Start server in a goroutine
	go func() {
		fmt.Printf("HTTP server starting on %s\n", s.httpServer.Addr)
		serverErrors <- s.httpServer.ListenAndServe()
	}()

	// Channel to listen for interrupt signals
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, os.Interrupt, syscall.SIGTERM)

	// Block until we receive a signal or server error
	select {
	case err := <-serverErrors:
		if err != http.ErrServerClosed {
			return fmt.Errorf("server error: %w", err)
		}
	case sig := <-shutdown:
		fmt.Printf("Shutdown signal received: %v\n", sig)

		// Create shutdown context with timeout
		ctx, cancel := context.WithTimeout(ctx, s.config.ShutdownTimeout)
		defer cancel()

		// Attempt graceful shutdown
		if err := s.httpServer.Shutdown(ctx); err != nil {
			return fmt.Errorf("graceful shutdown failed: %w", err)
		}
	case <-ctx.Done():
		return ctx.Err()
	}

	return nil
}

// Stop stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	if s.httpServer == nil {
		return nil
	}

	return s.httpServer.Shutdown(ctx)
}

// buildHandler builds the final HTTP handler with middleware chain
func (s *Server) buildHandler() http.Handler {
	// Start with the router
	handler := http.Handler(s.router)

	// Apply middleware in reverse order (last added is executed first)
	for i := len(s.middleware) - 1; i >= 0; i-- {
		handler = s.middleware[i](handler)
	}

	// Apply CORS if enabled
	if s.config.EnableCORS && s.config.CORSOptions != nil {
		handler = applyCORS(handler, s.config.CORSOptions)
	}

	return handler
}

// Handle registers a new route with the given pattern and handler
func (s *Server) Handle(pattern string, handler http.Handler) *mux.Route {
	return s.router.Handle(pattern, handler)
}

// HandleFunc registers a new route with the given pattern and handler function
func (s *Server) HandleFunc(pattern string, handler func(http.ResponseWriter, *http.Request)) *mux.Route {
	return s.router.HandleFunc(pattern, handler)
}

// ServeHTTP implements http.Handler interface
func (s *Server) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	s.router.ServeHTTP(w, r)
}
