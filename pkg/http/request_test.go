package http

import (
	"bytes"
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gorilla/mux"
)

func TestDecodeJSON(t *testing.T) {
	type TestStruct struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}

	tests := []struct {
		name    string
		body    string
		wantErr bool
	}{
		{
			name:    "valid JSON",
			body:    `{"name":"test","value":123}`,
			wantErr: false,
		},
		{
			name:    "empty body",
			body:    "",
			wantErr: true,
		},
		{
			name:    "invalid JSON",
			body:    `{"name":"test"`,
			wantErr: true,
		},
		{
			name:    "extra data",
			body:    `{"name":"test","value":123}{"extra":"data"}`,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("POST", "/", strings.NewReader(tt.body))
			r := NewRequest(req)

			var data TestStruct
			err := r.DecodeJ<PERSON>(&data)

			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeJSON() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPathParam(t *testing.T) {
	router := mux.NewRouter()
	
	var gotID string
	router.HandleFunc("/users/{id}", func(w http.ResponseWriter, req *http.Request) {
		r := NewRequest(req)
		gotID = r.PathParam("id")
	})

	req := httptest.NewRequest("GET", "/users/123", nil)
	rr := httptest.NewRecorder()
	
	router.ServeHTTP(rr, req)
	
	if gotID != "123" {
		t.Errorf("PathParam() = %v, want %v", gotID, "123")
	}
}

func TestQueryParams(t *testing.T) {
	req := httptest.NewRequest("GET", "/?name=test&age=25&active=true", nil)
	r := NewRequest(req)

	// Test QueryParam
	if got := r.QueryParam("name"); got != "test" {
		t.Errorf("QueryParam(name) = %v, want %v", got, "test")
	}

	// Test QueryParamInt
	if got := r.QueryParamInt("age", 0); got != 25 {
		t.Errorf("QueryParamInt(age) = %v, want %v", got, 25)
	}

	// Test QueryParamInt with default
	if got := r.QueryParamInt("missing", 10); got != 10 {
		t.Errorf("QueryParamInt(missing) = %v, want %v", got, 10)
	}

	// Test QueryParamBool
	if got := r.QueryParamBool("active", false); !got {
		t.Errorf("QueryParamBool(active) = %v, want %v", got, true)
	}

	// Test QueryParams
	params := r.QueryParams()
	if len(params) != 3 {
		t.Errorf("QueryParams() returned %d params, want 3", len(params))
	}
}

func TestBearerToken(t *testing.T) {
	tests := []struct {
		name   string
		header string
		want   string
	}{
		{
			name:   "valid bearer token",
			header: "Bearer abc123",
			want:   "abc123",
		},
		{
			name:   "lowercase bearer",
			header: "bearer abc123",
			want:   "abc123",
		},
		{
			name:   "no bearer prefix",
			header: "abc123",
			want:   "",
		},
		{
			name:   "empty header",
			header: "",
			want:   "",
		},
		{
			name:   "multiple spaces",
			header: "Bearer  abc123",
			want:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)
			if tt.header != "" {
				req.Header.Set("Authorization", tt.header)
			}
			r := NewRequest(req)

			if got := r.BearerToken(); got != tt.want {
				t.Errorf("BearerToken() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRequestID(t *testing.T) {
	req := httptest.NewRequest("GET", "/", nil)
	
	// Without request ID in context
	r := NewRequest(req)
	if got := r.RequestID(); got != "" {
		t.Errorf("RequestID() = %v, want empty string", got)
	}

	// With request ID in context
	ctx := context.WithValue(req.Context(), "requestID", "test-id")
	req = req.WithContext(ctx)
	r = NewRequest(req)
	if got := r.RequestID(); got != "test-id" {
		t.Errorf("RequestID() = %v, want %v", got, "test-id")
	}
}

func TestRemoteIP(t *testing.T) {
	tests := []struct {
		name       string
		remoteAddr string
		headers    map[string]string
		want       string
	}{
		{
			name:       "X-Forwarded-For",
			remoteAddr: "***********:1234",
			headers: map[string]string{
				"X-Forwarded-For": "********, ********",
			},
			want: "********",
		},
		{
			name:       "X-Real-IP",
			remoteAddr: "***********:1234",
			headers: map[string]string{
				"X-Real-IP": "********",
			},
			want: "********",
		},
		{
			name:       "RemoteAddr with port",
			remoteAddr: "***********:1234",
			headers:    map[string]string{},
			want:       "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)
			req.RemoteAddr = tt.remoteAddr
			for k, v := range tt.headers {
				req.Header.Set(k, v)
			}
			r := NewRequest(req)

			if got := r.RemoteIP(); got != tt.want {
				t.Errorf("RemoteIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetPagination(t *testing.T) {
	tests := []struct {
		name            string
		query           string
		defaultPageSize int
		maxPageSize     int
		wantPage        int
		wantPageSize    int
		wantOffset      int
	}{
		{
			name:            "default values",
			query:           "",
			defaultPageSize: 20,
			maxPageSize:     100,
			wantPage:        1,
			wantPageSize:    20,
			wantOffset:      0,
		},
		{
			name:            "custom values",
			query:           "?page=3&page_size=50",
			defaultPageSize: 20,
			maxPageSize:     100,
			wantPage:        3,
			wantPageSize:    50,
			wantOffset:      100,
		},
		{
			name:            "exceeds max page size",
			query:           "?page=1&page_size=200",
			defaultPageSize: 20,
			maxPageSize:     100,
			wantPage:        1,
			wantPageSize:    100,
			wantOffset:      0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/"+tt.query, nil)
			r := NewRequest(req)

			pagination := r.GetPagination(tt.defaultPageSize, tt.maxPageSize)

			if pagination.Page != tt.wantPage {
				t.Errorf("Page = %v, want %v", pagination.Page, tt.wantPage)
			}
			if pagination.PageSize != tt.wantPageSize {
				t.Errorf("PageSize = %v, want %v", pagination.PageSize, tt.wantPageSize)
			}
			if pagination.Offset != tt.wantOffset {
				t.Errorf("Offset = %v, want %v", pagination.Offset, tt.wantOffset)
			}
		})
	}
}

func TestGetSortOrder(t *testing.T) {
	tests := []struct {
		name             string
		query            string
		defaultField     string
		defaultDirection string
		wantField        string
		wantDirection    string
	}{
		{
			name:             "default values",
			query:            "",
			defaultField:     "created_at",
			defaultDirection: "desc",
			wantField:        "created_at",
			wantDirection:    "desc",
		},
		{
			name:             "custom values",
			query:            "?sort_by=name&sort_order=asc",
			defaultField:     "created_at",
			defaultDirection: "desc",
			wantField:        "name",
			wantDirection:    "asc",
		},
		{
			name:             "invalid direction",
			query:            "?sort_by=name&sort_order=invalid",
			defaultField:     "created_at",
			defaultDirection: "desc",
			wantField:        "name",
			wantDirection:    "desc",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/"+tt.query, nil)
			r := NewRequest(req)

			sortOrder := r.GetSortOrder(tt.defaultField, tt.defaultDirection)

			if sortOrder.Field != tt.wantField {
				t.Errorf("Field = %v, want %v", sortOrder.Field, tt.wantField)
			}
			if sortOrder.Direction != tt.wantDirection {
				t.Errorf("Direction = %v, want %v", sortOrder.Direction, tt.wantDirection)
			}
		})
	}
}