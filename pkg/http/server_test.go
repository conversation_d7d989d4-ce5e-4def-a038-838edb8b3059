package http

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestNewServer(t *testing.T) {
	// Test with default config
	server := NewServer(nil)
	if server == nil {
		t.<PERSON>al("NewServer returned nil")
	}
	if server.config == nil {
		t.<PERSON>al("Server config is nil")
	}
	if server.router == nil {
		t.Fatal("Server router is nil")
	}

	// Test with custom config
	config := &Config{
		Host: "127.0.0.1",
		Port: 8081,
	}
	server = NewServer(config)
	if server.config.Host != "127.0.0.1" {
		t.<PERSON><PERSON><PERSON>("Expected host %s, got %s", "127.0.0.1", server.config.Host)
	}
	if server.config.Port != 8081 {
		t.<PERSON>rrorf("Expected port %d, got %d", 8081, server.config.Port)
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.Host != "0.0.0.0" {
		t.<PERSON><PERSON><PERSON>("Expected host %s, got %s", "0.0.0.0", config.Host)
	}
	if config.Port != 9077 {
		t.Errorf("Expected port %d, got %d", 9077, config.Port)
	}
	if config.ReadTimeout != 15*time.Second {
		t.Errorf("Expected read timeout %v, got %v", 15*time.Second, config.ReadTimeout)
	}
	if !config.EnableCORS {
		t.Error("Expected CORS to be enabled")
	}
	if config.CORSOptions == nil {
		t.Error("Expected CORS options to be set")
	}
}

func TestServerMiddleware(t *testing.T) {
	server := NewServer(nil)

	// Test middleware registration
	middleware1Called := false
	middleware2Called := false

	middleware1 := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			middleware1Called = true
			next.ServeHTTP(w, r)
		})
	}

	middleware2 := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			middleware2Called = true
			next.ServeHTTP(w, r)
		})
	}

	server.Use(middleware1, middleware2)

	// Register a test handler
	server.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Build handler and test
	handler := server.buildHandler()

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if !middleware1Called {
		t.Error("Middleware 1 was not called")
	}
	if !middleware2Called {
		t.Error("Middleware 2 was not called")
	}
}

func TestServerRouting(t *testing.T) {
	server := NewServer(nil)

	// Test HandleFunc
	server.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test"))
	})

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	server.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, rr.Code)
	}
	if rr.Body.String() != "test" {
		t.Errorf("Expected body 'test', got '%s'", rr.Body.String())
	}

	// Test 404
	req = httptest.NewRequest("GET", "/notfound", nil)
	rr = httptest.NewRecorder()

	server.ServeHTTP(rr, req)

	if rr.Code != http.StatusNotFound {
		t.Errorf("Expected status %d, got %d", http.StatusNotFound, rr.Code)
	}
}

func TestServerStop(t *testing.T) {
	server := NewServer(nil)

	// Test stop without start
	ctx := context.Background()
	err := server.Stop(ctx)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}
