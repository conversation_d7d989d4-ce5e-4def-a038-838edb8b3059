# HTTP Package

Package HTTP cung cấp server wrapper, middleware system, và các helper utilities cho việc xây dựng HTTP APIs trong Blog API v3.

## Features

- HTTP server với graceful shutdown
- Middleware system linh hoạt
- Request helpers cho parsing và validation
- Response helpers cho consistent API responses
- Built-in middleware: logging, recovery, rate limiting, CORS
- Integration với gorilla/mux router

## Installation

```bash
go get github.com/blog-api-v3/blog-api-v3/pkg/http
```

## Usage

### Basic Server Setup

```go
package main

import (
    "context"
    "log"
    
    httpPkg "github.com/blog-api-v3/blog-api-v3/pkg/http"
)

func main() {
    // Create server với default config
    server := httpPkg.NewServer(nil)
    
    // Hoặc với custom config
    config := &httpPkg.Config{
        Host:            "localhost",
        Port:            9077,
        ReadTimeout:     30 * time.Second,
        WriteTimeout:    30 * time.Second,
        ShutdownTimeout: 10 * time.Second,
    }
    server = httpPkg.NewServer(config)
    
    // Add middleware
    server.Use(
        httpPkg.WithRequestID(),
        httpPkg.WithLogging(),
        httpPkg.WithRecovery(),
        httpPkg.WithTimeout(30 * time.Second),
    )
    
    // Register routes
    server.HandleFunc("/health", healthHandler)
    
    // Start server
    ctx := context.Background()
    if err := server.Start(ctx); err != nil {
        log.Fatal(err)
    }
}
```

### Using Middleware

```go
// Built-in middleware
server.Use(
    httpPkg.WithLogging(),                    // Request logging
    httpPkg.WithRecovery(),                   // Panic recovery
    httpPkg.WithTimeout(30 * time.Second),    // Request timeout
    httpPkg.WithRateLimit(100, 200),          // Rate limiting (100 req/s, burst 200)
    httpPkg.WithRequestID(),                  // Add request ID
    httpPkg.WithContentType("application/json"), // Set default content type
)

// Custom middleware
func AuthMiddleware() httpPkg.Middleware {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            token := httpPkg.NewRequest(r).BearerToken()
            if token == "" {
                httpPkg.NewResponse(w).Unauthorized("Token required")
                return
            }
            
            // Validate token...
            
            next.ServeHTTP(w, r)
        })
    }
}

// Chain middleware
apiMiddleware := httpPkg.Chain(
    httpPkg.WithContentType("application/json"),
    AuthMiddleware(),
)
server.Router().PathPrefix("/api").Handler(apiMiddleware(apiHandler))
```

### Request Helpers

```go
func userHandler(w http.ResponseWriter, r *http.Request) {
    req := httpPkg.NewRequest(r)
    res := httpPkg.NewResponse(w)
    
    // Path parameters (với gorilla/mux)
    userID := req.PathParam("id")
    
    // Query parameters
    search := req.QueryParam("search")
    limit := req.QueryParamInt("limit", 10)
    active := req.QueryParamBool("active", true)
    
    // Pagination
    pagination := req.GetPagination(20, 100) // default 20, max 100
    // pagination.Page, pagination.PageSize, pagination.Offset
    
    // Sort order
    sort := req.GetSortOrder("created_at", "desc")
    // sort.Field, sort.Direction
    
    // Headers
    contentType := req.HeaderValue("Content-Type")
    bearerToken := req.BearerToken()
    
    // Request ID
    requestID := req.RequestID()
    
    // Remote IP
    clientIP := req.RemoteIP()
    
    // Parse JSON body
    var input CreateUserInput
    if err := req.DecodeJSON(&input); err != nil {
        res.BadRequest(err.Error())
        return
    }
    
    // Process request...
}
```

### Response Helpers

```go
func userHandler(w http.ResponseWriter, r *http.Request) {
    res := httpPkg.NewResponse(w)
    
    // Success responses
    res.Success(user)                    // 200 OK
    res.Created(user)                    // 201 Created
    res.NoContent()                      // 204 No Content
    
    // Error responses
    res.BadRequest("Invalid input")      // 400
    res.Unauthorized("Token expired")    // 401
    res.Forbidden("Access denied")       // 403
    res.NotFound("User not found")       // 404
    res.InternalServerError("Database error") // 500
    
    // Validation errors
    errors := map[string]string{
        "email": "Invalid email format",
        "name":  "Name is required",
    }
    res.ValidationError(errors)          // 422
    
    // Custom error với details
    res.ErrorWithDetails(409, "Email already exists", map[string]string{
        "email": "<EMAIL>",
    })
    
    // Paginated response
    users := []User{...}
    res.Paginated(users, page, pageSize, totalCount)
}
```

### CORS Configuration

```go
config := &httpPkg.Config{
    EnableCORS: true,
    CORSOptions: &httpPkg.CORSConfig{
        AllowedOrigins:   []string{"http://localhost:3000", "https://app.example.com"},
        AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization"},
        ExposedHeaders:   []string{"X-Total-Count"},
        AllowCredentials: true,
        MaxAge:           86400, // 24 hours
    },
}
```

### Complete Example

```go
package main

import (
    "context"
    "log"
    
    httpPkg "github.com/blog-api-v3/blog-api-v3/pkg/http"
)

type User struct {
    ID    string `json:"id"`
    Name  string `json:"name"`
    Email string `json:"email"`
}

func main() {
    server := httpPkg.NewServer(nil)
    
    // Global middleware
    server.Use(
        httpPkg.WithRequestID(),
        httpPkg.WithLogging(),
        httpPkg.WithRecovery(),
    )
    
    // API routes
    api := server.Router().PathPrefix("/api/cms/v1").Subrouter()
    
    // Apply API-specific middleware
    api.Use(
        httpPkg.WithContentType("application/json"),
        httpPkg.WithRateLimit(100, 200),
    )
    
    // User routes
    api.HandleFunc("/users", listUsers).Methods("GET")
    api.HandleFunc("/users", createUser).Methods("POST")
    api.HandleFunc("/users/{id}", getUser).Methods("GET")
    api.HandleFunc("/users/{id}", updateUser).Methods("PUT")
    api.HandleFunc("/users/{id}", deleteUser).Methods("DELETE")
    
    // Health check
    server.HandleFunc("/health", healthCheck)
    
    // Start server
    ctx := context.Background()
    if err := server.Start(ctx); err != nil {
        log.Fatal(err)
    }
}

func listUsers(w http.ResponseWriter, r *http.Request) {
    req := httpPkg.NewRequest(r)
    res := httpPkg.NewResponse(w)
    
    // Get pagination
    pagination := req.GetPagination(20, 100)
    
    // Get sort order
    sort := req.GetSortOrder("created_at", "desc")
    
    // Query database...
    users := []User{
        {ID: "1", Name: "John", Email: "<EMAIL>"},
        {ID: "2", Name: "Jane", Email: "<EMAIL>"},
    }
    totalCount := 2
    
    // Send paginated response
    res.Paginated(users, pagination.Page, pagination.PageSize, totalCount)
}

func createUser(w http.ResponseWriter, r *http.Request) {
    req := httpPkg.NewRequest(r)
    res := httpPkg.NewResponse(w)
    
    // Parse request body
    var input struct {
        Name  string `json:"name"`
        Email string `json:"email"`
    }
    
    if err := req.DecodeJSON(&input); err != nil {
        res.BadRequest(err.Error())
        return
    }
    
    // Validate
    if input.Name == "" || input.Email == "" {
        res.ValidationError(map[string]string{
            "name":  "Name is required",
            "email": "Email is required",
        })
        return
    }
    
    // Create user...
    user := User{
        ID:    "3",
        Name:  input.Name,
        Email: input.Email,
    }
    
    res.Created(user)
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
    res := httpPkg.NewResponse(w)
    res.Success(map[string]string{
        "status": "healthy",
        "version": "1.0.0",
    })
}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    "id": "123",
    "name": "John Doe"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "details": {
      "email": "Invalid email format"
    }
  }
}
```

### Paginated Response (Deprecated - Use Cursor-based pagination)
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 2,
    "page_size": 20,
    "total_pages": 5,
    "total_items": 95
  }
}
```

### Cursor-based Paginated Response
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": "eyJpZCI6MTAwLCJ0aW1lIjoiMjAyNC0wMS0xNFQxMDozMDowMFoifQ==",
    "has_more": true,
    "has_previous": true,
    "limit": 20
  }
}
```

## Best Practices

1. **Always use response helpers**: Đảm bảo response format nhất quán
2. **Add request ID middleware**: Giúp trace requests
3. **Use recovery middleware**: Prevent panics từ crashing server
4. **Set appropriate timeouts**: Tránh long-running requests
5. **Configure CORS properly**: Chỉ allow trusted origins
6. **Rate limit public endpoints**: Protect against abuse
7. **Log all requests**: Use logging middleware cho debugging
8. **Validate all inputs**: Use request helpers và validation