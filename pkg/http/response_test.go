package http

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestJSONResponse(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	data := map[string]string{"message": "test"}
	err := r.<PERSON>(http.StatusOK, data)
	if err != nil {
		t.Fatalf("JSON() error = %v", err)
	}

	// Check status code
	if rr.Code != http.StatusOK {
		t.<PERSON>rf("Status code = %v, want %v", rr.Code, http.StatusOK)
	}

	// Check Content-Type
	ct := rr.Header().Get("Content-Type")
	if ct != "application/json; charset=utf-8" {
		t.<PERSON><PERSON>("Content-Type = %v, want %v", ct, "application/json; charset=utf-8")
	}

	// Check response body
	var response map[string]string
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if response["message"] != "test" {
		t.<PERSON>("Response message = %v, want %v", response["message"], "test")
	}
}

func TestSuccessResponse(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	data := map[string]string{"id": "123"}
	err := r.Success(data)
	if err != nil {
		t.Fatalf("Success() error = %v", err)
	}

	// Check response
	var response SuccessResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if !response.Success {
		t.Error("Success field should be true")
	}
}

func TestCreatedResponse(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	err := r.Created(nil)
	if err != nil {
		t.Fatalf("Created() error = %v", err)
	}

	if rr.Code != http.StatusCreated {
		t.Errorf("Status code = %v, want %v", rr.Code, http.StatusCreated)
	}
}

func TestNoContentResponse(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	err := r.NoContent()
	if err != nil {
		t.Fatalf("NoContent() error = %v", err)
	}

	if rr.Code != http.StatusNoContent {
		t.Errorf("Status code = %v, want %v", rr.Code, http.StatusNoContent)
	}

	if rr.Body.Len() != 0 {
		t.Error("Body should be empty")
	}
}

func TestErrorResponses(t *testing.T) {
	tests := []struct {
		name       string
		fn         func(*Response) error
		wantStatus int
		wantCode   string
	}{
		{
			name: "BadRequest",
			fn: func(r *Response) error {
				return r.BadRequest("bad request")
			},
			wantStatus: http.StatusBadRequest,
			wantCode:   "BAD_REQUEST",
		},
		{
			name: "Unauthorized",
			fn: func(r *Response) error {
				return r.Unauthorized("")
			},
			wantStatus: http.StatusUnauthorized,
			wantCode:   "UNAUTHORIZED",
		},
		{
			name: "Forbidden",
			fn: func(r *Response) error {
				return r.Forbidden("")
			},
			wantStatus: http.StatusForbidden,
			wantCode:   "FORBIDDEN",
		},
		{
			name: "NotFound",
			fn: func(r *Response) error {
				return r.NotFound("")
			},
			wantStatus: http.StatusNotFound,
			wantCode:   "NOT_FOUND",
		},
		{
			name: "InternalServerError",
			fn: func(r *Response) error {
				return r.InternalServerError("")
			},
			wantStatus: http.StatusInternalServerError,
			wantCode:   "INTERNAL_ERROR",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rr := httptest.NewRecorder()
			r := NewResponse(rr)

			err := tt.fn(r)
			if err != nil {
				t.Fatalf("%s() error = %v", tt.name, err)
			}

			if rr.Code != tt.wantStatus {
				t.Errorf("Status code = %v, want %v", rr.Code, tt.wantStatus)
			}

			var response ErrorResponse
			if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to unmarshal response: %v", err)
			}
			if response.Success {
				t.Error("Success field should be false")
			}
			if response.Error.Code != tt.wantCode {
				t.Errorf("Error code = %v, want %v", response.Error.Code, tt.wantCode)
			}
		})
	}
}

func TestValidationError(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	errors := map[string]string{
		"email": "required",
		"name":  "too short",
	}

	err := r.ValidationError(errors)
	if err != nil {
		t.Fatalf("ValidationError() error = %v", err)
	}

	if rr.Code != http.StatusUnprocessableEntity {
		t.Errorf("Status code = %v, want %v", rr.Code, http.StatusUnprocessableEntity)
	}

	var response ErrorResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if response.Error.Code != "VALIDATION_ERROR" {
		t.Errorf("Error code = %v, want %v", response.Error.Code, "VALIDATION_ERROR")
	}
	if response.Error.Details == nil {
		t.Error("Error details should not be nil")
	}
}

func TestPaginatedResponse(t *testing.T) {
	rr := httptest.NewRecorder()
	r := NewResponse(rr)

	data := []string{"item1", "item2", "item3"}
	err := r.Paginated(data, 2, 10, 25)
	if err != nil {
		t.Fatalf("Paginated() error = %v", err)
	}

	var response PaginatedResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Success field should be true")
	}
	if response.Pagination.Page != 2 {
		t.Errorf("Page = %v, want %v", response.Pagination.Page, 2)
	}
	if response.Pagination.PageSize != 10 {
		t.Errorf("PageSize = %v, want %v", response.Pagination.PageSize, 10)
	}
	if response.Pagination.TotalPages != 3 {
		t.Errorf("TotalPages = %v, want %v", response.Pagination.TotalPages, 3)
	}
	if response.Pagination.TotalItems != 25 {
		t.Errorf("TotalItems = %v, want %v", response.Pagination.TotalItems, 25)
	}
}

func TestGetErrorCode(t *testing.T) {
	tests := []struct {
		status int
		want   string
	}{
		{http.StatusBadRequest, "BAD_REQUEST"},
		{http.StatusUnauthorized, "UNAUTHORIZED"},
		{http.StatusForbidden, "FORBIDDEN"},
		{http.StatusNotFound, "NOT_FOUND"},
		{http.StatusMethodNotAllowed, "METHOD_NOT_ALLOWED"},
		{http.StatusConflict, "CONFLICT"},
		{http.StatusUnprocessableEntity, "VALIDATION_ERROR"},
		{http.StatusTooManyRequests, "TOO_MANY_REQUESTS"},
		{http.StatusInternalServerError, "INTERNAL_ERROR"},
		{http.StatusServiceUnavailable, "SERVICE_UNAVAILABLE"},
		{999, "ERROR"}, // Unknown status
	}

	for _, tt := range tests {
		t.Run(tt.want, func(t *testing.T) {
			if got := getErrorCode(tt.status); got != tt.want {
				t.Errorf("getErrorCode(%d) = %v, want %v", tt.status, got, tt.want)
			}
		})
	}
}