package validator

import (
	"context"
	"net/url"
	"regexp"
	"strings"
	"unicode"
)

// Custom validator functions for common use cases

// IsVietnamesePhone validates Vietnamese phone numbers
func IsVietnamesePhone(phone string) bool {
	// Remove spaces and dashes
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "-", "")

	// Check with country code
	if strings.HasPrefix(phone, "+84") {
		phone = "0" + phone[3:]
	} else if strings.HasPrefix(phone, "84") {
		phone = "0" + phone[2:]
	}

	// Validate format
	if len(phone) != 10 {
		return false
	}

	if !strings.HasPrefix(phone, "0") {
		return false
	}

	// Valid prefixes for Vietnamese mobile numbers
	validPrefixes := []string{
		"090", "093", "089", "070", "079", "077", "076", "078",
		"083", "084", "085", "081", "082", "086", "096", "097",
		"098", "032", "033", "034", "035", "036", "037", "038",
		"039", "056", "058", "059", "091", "094", "088", "052", "092",
	}

	prefix := phone[0:3]
	for _, vp := range validPrefixes {
		if prefix == vp {
			// Check remaining digits
			for i := 3; i < len(phone); i++ {
				if phone[i] < '0' || phone[i] > '9' {
					return false
				}
			}
			return true
		}
	}

	return false
}

// IsVietnameseIDCard validates Vietnamese ID card numbers
func IsVietnameseIDCard(id string) bool {
	// Remove spaces
	id = strings.ReplaceAll(id, " ", "")

	// Old ID: 9 digits, New ID (CCCD): 12 digits
	if len(id) != 9 && len(id) != 12 {
		return false
	}

	// Check if all characters are digits
	for _, c := range id {
		if c < '0' || c > '9' {
			return false
		}
	}

	// For 12-digit CCCD, validate province code (first 3 digits)
	if len(id) == 12 {
		provinceCode := id[0:3]
		// Valid province codes range from 001 to 096
		if provinceCode < "001" || provinceCode > "096" {
			return false
		}
	}

	return true
}

// IsSlug validates URL-friendly slugs
func IsSlug(slug string) bool {
	if slug == "" {
		return false
	}

	// Slug pattern: lowercase letters, numbers, and hyphens
	// Cannot start or end with hyphen
	// No consecutive hyphens
	pattern := `^[a-z0-9]+(?:-[a-z0-9]+)*$`
	match, _ := regexp.MatchString(pattern, slug)
	return match
}

// IsStrongPassword validates password strength
func IsStrongPassword(password string) bool {
	if len(password) < 8 {
		return false
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	// Require at least 3 out of 4 character types
	count := 0
	if hasUpper {
		count++
	}
	if hasLower {
		count++
	}
	if hasNumber {
		count++
	}
	if hasSpecial {
		count++
	}

	return count >= 3
}

// IsUsername validates username format
func IsUsername(username string) bool {
	if len(username) < 3 || len(username) > 30 {
		return false
	}

	// Username pattern: alphanumeric, underscore, dot
	// Cannot start with dot or underscore
	// Cannot end with dot or underscore
	// No consecutive dots or underscores
	pattern := `^[a-zA-Z0-9]([a-zA-Z0-9._]*[a-zA-Z0-9])?$`
	match, _ := regexp.MatchString(pattern, username)

	if !match {
		return false
	}

	// Check for consecutive dots or underscores
	if strings.Contains(username, "..") || strings.Contains(username, "__") {
		return false
	}

	return true
}

// IsHexColor validates hex color codes
func IsHexColor(color string) bool {
	// Support both #RGB and #RRGGBB formats
	pattern := `^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$`
	match, _ := regexp.MatchString(pattern, color)
	return match
}

// IsIPv4 validates IPv4 addresses
func IsIPv4(ip string) bool {
	pattern := `^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`
	match, _ := regexp.MatchString(pattern, ip)
	return match
}

// IsIPv6 validates IPv6 addresses
func IsIPv6(ip string) bool {
	pattern := `^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::)$`
	match, _ := regexp.MatchString(pattern, ip)
	return match
}

// IsMAC validates MAC addresses
func IsMAC(mac string) bool {
	// Support both : and - separators
	pattern := `^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$`
	match, _ := regexp.MatchString(pattern, mac)
	return match
}

// IsPort validates port numbers
func IsPort(port int) bool {
	return port >= 1 && port <= 65535
}

// IsDomain validates domain names
func IsDomain(domain string) bool {
	if len(domain) > 253 {
		return false
	}

	// Domain pattern
	pattern := `^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`
	match, _ := regexp.MatchString(pattern, domain)
	return match
}

// IsHTTPS validates if URL uses HTTPS
func IsHTTPS(urlStr string) bool {
	u, err := url.Parse(urlStr)
	if err != nil {
		return false
	}
	return u.Scheme == "https"
}

// RegisterCustomValidators registers all custom validators with a validator instance
func RegisterCustomValidators(v Validator) error {
	customValidators := map[string]ValidationFunc{
		"vnphone": func(ctx context.Context, fl FieldLevel) bool {
			phone, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsVietnamesePhone(phone)
		},
		"vnidcard": func(ctx context.Context, fl FieldLevel) bool {
			id, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsVietnameseIDCard(id)
		},
		"slug": func(ctx context.Context, fl FieldLevel) bool {
			slug, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsSlug(slug)
		},
		"strongpassword": func(ctx context.Context, fl FieldLevel) bool {
			password, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsStrongPassword(password)
		},
		"username": func(ctx context.Context, fl FieldLevel) bool {
			username, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsUsername(username)
		},
		"hexcolor": func(ctx context.Context, fl FieldLevel) bool {
			color, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsHexColor(color)
		},
		"ipv4": func(ctx context.Context, fl FieldLevel) bool {
			ip, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsIPv4(ip)
		},
		"ipv6": func(ctx context.Context, fl FieldLevel) bool {
			ip, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsIPv6(ip)
		},
		"mac": func(ctx context.Context, fl FieldLevel) bool {
			mac, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsMAC(mac)
		},
		"port": func(ctx context.Context, fl FieldLevel) bool {
			port, ok := fl.Field().(int)
			if !ok {
				return false
			}
			return IsPort(port)
		},
		"domain": func(ctx context.Context, fl FieldLevel) bool {
			domain, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsDomain(domain)
		},
		"https": func(ctx context.Context, fl FieldLevel) bool {
			urlStr, ok := fl.Field().(string)
			if !ok {
				return false
			}
			return IsHTTPS(urlStr)
		},
	}

	// Register each validator
	for tag, fn := range customValidators {
		if err := v.RegisterValidation(tag, fn); err != nil {
			return err
		}
	}

	// Register translations
	translations := map[string]map[string]string{
		"en": {
			"vnphone":        "{0} must be a valid Vietnamese phone number",
			"vnidcard":       "{0} must be a valid Vietnamese ID card number",
			"slug":           "{0} must be a valid URL slug",
			"strongpassword": "{0} must be a strong password (8+ chars, 3/4 of uppercase, lowercase, number, special)",
			"username":       "{0} must be a valid username (3-30 chars, alphanumeric, dot, underscore)",
			"hexcolor":       "{0} must be a valid hex color code",
			"ipv4":           "{0} must be a valid IPv4 address",
			"ipv6":           "{0} must be a valid IPv6 address",
			"mac":            "{0} must be a valid MAC address",
			"port":           "{0} must be a valid port number (1-65535)",
			"domain":         "{0} must be a valid domain name",
			"https":          "{0} must be an HTTPS URL",
		},
		"vi": {
			"vnphone":        "{0} phải là số điện thoại Việt Nam hợp lệ",
			"vnidcard":       "{0} phải là số CMND/CCCD Việt Nam hợp lệ",
			"slug":           "{0} phải là URL slug hợp lệ",
			"strongpassword": "{0} phải là mật khẩu mạnh (8+ ký tự, 3/4 loại: chữ hoa, chữ thường, số, ký tự đặc biệt)",
			"username":       "{0} phải là tên người dùng hợp lệ (3-30 ký tự, chữ cái, số, dấu chấm, gạch dưới)",
			"hexcolor":       "{0} phải là mã màu hex hợp lệ",
			"ipv4":           "{0} phải là địa chỉ IPv4 hợp lệ",
			"ipv6":           "{0} phải là địa chỉ IPv6 hợp lệ",
			"mac":            "{0} phải là địa chỉ MAC hợp lệ",
			"port":           "{0} phải là số cổng hợp lệ (1-65535)",
			"domain":         "{0} phải là tên miền hợp lệ",
			"https":          "{0} phải là URL HTTPS",
		},
	}

	for locale, trans := range translations {
		for tag, translation := range trans {
			if err := v.RegisterTranslation(tag, locale, translation); err != nil {
				return err
			}
		}
	}

	return nil
}