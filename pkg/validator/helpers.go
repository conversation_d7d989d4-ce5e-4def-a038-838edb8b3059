package validator

import (
	"context"
	"fmt"
	"reflect"
	"strings"
)

// Default validator instance
var defaultValidator Validator

// Initialize sets the default validator instance
func Initialize(opts ...Options) error {
	v, err := NewValidator(opts...)
	if err != nil {
		return err
	}
	
	// Register custom validators
	if err := RegisterCustomValidators(v); err != nil {
		return err
	}
	
	defaultValidator = v
	return nil
}

// GetDefaultValidator returns the default validator instance
func GetDefaultValidator() Validator {
	if defaultValidator == nil {
		// Initialize with default options if not already initialized
		Initialize()
	}
	return defaultValidator
}

// Validate validates a struct using the default validator
func Validate(v interface{}) error {
	return GetDefaultValidator().Validate(context.Background(), v)
}

// ValidateWithContext validates a struct with context using the default validator
func ValidateWithContext(ctx context.Context, v interface{}) error {
	return GetDefaultValidator().Validate(ctx, v)
}

// ValidateField validates a single field using the default validator
func ValidateField(field interface{}, tag string) error {
	return GetDefaultValidator().ValidateField(context.Background(), field, tag)
}

// ValidateFieldWithContext validates a single field with context using the default validator
func ValidateFieldWithContext(ctx context.Context, field interface{}, tag string) error {
	return GetDefaultValidator().ValidateField(ctx, field, tag)
}

// MustValidate validates and panics if validation fails
func MustValidate(v interface{}) {
	if err := Validate(v); err != nil {
		panic(fmt.Sprintf("validation failed: %v", err))
	}
}

// IsValid checks if a struct is valid
func IsValid(v interface{}) bool {
	return Validate(v) == nil
}

// ValidateMap validates a map against a set of rules
func ValidateMap(data map[string]interface{}, rules map[string]string) error {
	ctx := context.Background()
	validator := GetDefaultValidator()
	
	var allErrors ValidationErrors
	
	for field, rule := range rules {
		value, exists := data[field]
		
		// Check if field is required
		if strings.Contains(rule, "required") && !exists {
			allErrors = append(allErrors, &validationError{
				field:   field,
				tag:     "required",
				value:   nil,
				message: fmt.Sprintf("%s is required", field),
			})
			continue
		}
		
		// Skip validation if field doesn't exist and not required
		if !exists {
			continue
		}
		
		// Validate the field
		if err := validator.ValidateField(ctx, value, rule); err != nil {
			if ve, ok := err.(ValidationErrors); ok {
				// Update field name in errors
				for _, e := range ve {
					if vErr, ok := e.(*validationError); ok {
						vErr.field = field
					}
				}
				allErrors = append(allErrors, ve...)
			}
		}
	}
	
	if len(allErrors) > 0 {
		return allErrors
	}
	
	return nil
}

// ValidateStruct validates a struct and returns field-specific errors
func ValidateStruct(v interface{}) map[string][]string {
	err := Validate(v)
	if err == nil {
		return nil
	}
	
	if ve, ok := err.(ValidationErrors); ok {
		formatter := NewErrorFormatter("en")
		return formatter.FormatFieldErrors(ve)
	}
	
	return map[string][]string{
		"_error": {err.Error()},
	}
}

// ValidatePartial validates only specified fields of a struct
func ValidatePartial(v interface{}, fields ...string) error {
	if len(fields) == 0 {
		return Validate(v)
	}
	
	ctx := context.Background()
	validator := GetDefaultValidator()
	
	rv := reflect.ValueOf(v)
	if rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	
	if rv.Kind() != reflect.Struct {
		return fmt.Errorf("ValidatePartial only works with structs")
	}
	
	rt := rv.Type()
	var allErrors ValidationErrors
	
	for _, fieldName := range fields {
		field, found := rt.FieldByName(fieldName)
		if !found {
			continue
		}
		
		fieldValue := rv.FieldByName(fieldName)
		if !fieldValue.IsValid() {
			continue
		}
		
		// Get validation tag
		tag := field.Tag.Get("validate")
		if tag == "" || tag == "-" {
			continue
		}
		
		// Validate the field
		if err := validator.ValidateField(ctx, fieldValue.Interface(), tag); err != nil {
			if ve, ok := err.(ValidationErrors); ok {
				// Update field name in errors
				for _, e := range ve {
					if vErr, ok := e.(*validationError); ok {
						vErr.field = fieldName
					}
				}
				allErrors = append(allErrors, ve...)
			}
		}
	}
	
	if len(allErrors) > 0 {
		return allErrors
	}
	
	return nil
}

// ValidateVar is a shorthand for ValidateField
func ValidateVar(field interface{}, tag string) error {
	return ValidateField(field, tag)
}

// RegisterValidation registers a custom validation with the default validator
func RegisterValidation(tag string, fn ValidationFunc) error {
	return GetDefaultValidator().RegisterValidation(tag, fn)
}

// RegisterTranslation registers a translation with the default validator
func RegisterTranslation(tag string, locale string, translation string) error {
	return GetDefaultValidator().RegisterTranslation(tag, locale, translation)
}

// SetLocale sets the locale for the default validator
func SetLocale(locale string) error {
	return GetDefaultValidator().SetLocale(locale)
}

// ValidationBuilder provides a fluent interface for building validation rules
type ValidationBuilder struct {
	rules []string
}

// NewValidationBuilder creates a new validation builder
func NewValidationBuilder() *ValidationBuilder {
	return &ValidationBuilder{
		rules: make([]string, 0),
	}
}

// Required adds required validation
func (vb *ValidationBuilder) Required() *ValidationBuilder {
	vb.rules = append(vb.rules, "required")
	return vb
}

// Email adds email validation
func (vb *ValidationBuilder) Email() *ValidationBuilder {
	vb.rules = append(vb.rules, "email")
	return vb
}

// Min adds minimum length/value validation
func (vb *ValidationBuilder) Min(n int) *ValidationBuilder {
	vb.rules = append(vb.rules, fmt.Sprintf("min=%d", n))
	return vb
}

// Max adds maximum length/value validation
func (vb *ValidationBuilder) Max(n int) *ValidationBuilder {
	vb.rules = append(vb.rules, fmt.Sprintf("max=%d", n))
	return vb
}

// Len adds exact length validation
func (vb *ValidationBuilder) Len(n int) *ValidationBuilder {
	vb.rules = append(vb.rules, fmt.Sprintf("len=%d", n))
	return vb
}

// OneOf adds oneof validation
func (vb *ValidationBuilder) OneOf(values ...string) *ValidationBuilder {
	vb.rules = append(vb.rules, fmt.Sprintf("oneof=%s", strings.Join(values, " ")))
	return vb
}

// Custom adds a custom validation tag
func (vb *ValidationBuilder) Custom(tag string) *ValidationBuilder {
	vb.rules = append(vb.rules, tag)
	return vb
}

// Build returns the validation tag string
func (vb *ValidationBuilder) Build() string {
	return strings.Join(vb.rules, ",")
}

// SanitizeStruct sanitizes a struct by trimming strings and applying other cleanups
func SanitizeStruct(v interface{}) error {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr || rv.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("SanitizeStruct only works with struct pointers")
	}
	
	rv = rv.Elem()
	rt := rv.Type()
	
	for i := 0; i < rv.NumField(); i++ {
		field := rv.Field(i)
		fieldType := rt.Field(i)
		
		// Skip unexported fields
		if !field.CanSet() {
			continue
		}
		
		// Check for sanitize tag
		sanitizeTag := fieldType.Tag.Get("sanitize")
		if sanitizeTag == "-" {
			continue
		}
		
		// Apply sanitization based on field type
		switch field.Kind() {
		case reflect.String:
			if field.CanSet() {
				value := field.String()
				// Trim whitespace by default
				value = strings.TrimSpace(value)
				
				// Apply additional sanitization based on tag
				if strings.Contains(sanitizeTag, "lower") {
					value = strings.ToLower(value)
				}
				if strings.Contains(sanitizeTag, "upper") {
					value = strings.ToUpper(value)
				}
				if strings.Contains(sanitizeTag, "title") {
					value = strings.Title(value)
				}
				
				field.SetString(value)
			}
		case reflect.Ptr:
			if !field.IsNil() && field.Elem().Kind() == reflect.Struct {
				SanitizeStruct(field.Interface())
			}
		case reflect.Struct:
			SanitizeStruct(field.Addr().Interface())
		}
	}
	
	return nil
}