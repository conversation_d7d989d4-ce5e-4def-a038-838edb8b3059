# Validator Package

Package validator cung cấp chức năng validation linh hoạt cho Blog API v3, hỗ trợ struct tags, custom validators, và error formatting.

## Features

- **Struct Validation**: Validate structs với tags
- **Field Validation**: Validate từng field riêng lẻ
- **Custom Validators**: Đăng ký custom validation rules
- **Vietnamese Support**: Validators cho số điện thoại, CMND/CCCD Việt Nam
- **Error Formatting**: Format errors cho nhiều output types
- **Internationalization**: Hỗ trợ đa ngôn ngữ (en, vi)
- **Request Validation**: Tích hợp với HTTP package
- **Validation Helpers**: Helper functions cho common use cases

## Installation

```bash
go get github.com/blog-api-v3/blog-api-v3/pkg/validator
```

## Usage

### Basic Validation

```go
import "github.com/blog-api-v3/blog-api-v3/pkg/validator"

// Define struct với validation tags
type User struct {
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8,strongpassword"`
    Age      int    `json:"age" validate:"required,min=18,max=120"`
    Phone    string `json:"phone" validate:"omitempty,vnphone"`
}

// Validate struct
user := User{
    Email:    "<EMAIL>",
    Password: "SecureP@ss123",
    Age:      25,
    Phone:    "0912345678",
}

if err := validator.Validate(&user); err != nil {
    // Handle validation errors
    if ve, ok := err.(validator.ValidationErrors); ok {
        for _, e := range ve {
            fmt.Printf("Field: %s, Error: %s\n", e.Field(), e.Message())
        }
    }
}
```

### Custom Validator

```go
// Initialize validator với options
v, err := validator.NewValidator(validator.Options{
    TagName:       "validate",
    DefaultLocale: "vi",
})

// Register custom validator
err = v.RegisterValidation("customtag", func(ctx context.Context, fl validator.FieldLevel) bool {
    // Custom validation logic
    value := fl.Field().(string)
    return len(value) > 10
})

// Register translation
err = v.RegisterTranslation("customtag", "en", "{0} must be longer than 10 characters")
err = v.RegisterTranslation("customtag", "vi", "{0} phải dài hơn 10 ký tự")
```

### Vietnamese Validators

```go
// Phone number validation
type Contact struct {
    Phone  string `validate:"vnphone"`  // 0912345678
    IDCard string `validate:"vnidcard"` // 123456789 or 001234567890
}

// Standalone validation
isValid := validator.IsVietnamesePhone("0912345678")    // true
isValid = validator.IsVietnamesePhone("+84912345678")   // true
isValid = validator.IsVietnameseIDCard("123456789")     // true (old format)
isValid = validator.IsVietnameseIDCard("001234567890")  // true (new CCCD)
```

### Request Validation

```go
// Create request validator
rv := validator.NewRequestValidator(v)

// Validate JSON request
type CreateUserRequest struct {
    Name  string `json:"name" validate:"required,min=2"`
    Email string `json:"email" validate:"required,email"`
}

func handleCreateUser(w http.ResponseWriter, r *http.Request) {
    var req CreateUserRequest
    if err := rv.ValidateJSON(r, &req); err != nil {
        // Error is automatically formatted
        w.WriteHeader(http.StatusBadRequest)
        json.NewEncoder(w).Encode(err)
        return
    }
    
    // Process valid request
}

// Validate query parameters
type SearchRequest struct {
    Query string `query:"q" validate:"required,min=3"`
    Page  int    `query:"page" validate:"min=1"`
}

func handleSearch(w http.ResponseWriter, r *http.Request) {
    var req SearchRequest
    if err := rv.ValidateQuery(r, &req); err != nil {
        // Handle error
        return
    }
}

// Use middleware
mux.Handle("/api/users", rv.ValidateJSONMiddleware(reflect.TypeOf(CreateUserRequest{})))
```

### Validation Helpers

```go
// Validate map data
data := map[string]interface{}{
    "email": "<EMAIL>",
    "age":   25,
}

rules := map[string]string{
    "email": "required,email",
    "age":   "required,min=18,max=100",
}

err := validator.ValidateMap(data, rules)

// Partial validation
user := User{Email: "invalid", Password: "pass"}
err = validator.ValidatePartial(&user, "Email") // Only validate email

// Validation builder
tag := validator.NewValidationBuilder().
    Required().
    Email().
    Build() // "required,email"

// Sanitize struct
type Input struct {
    Name  string `sanitize:"trim,title"`
    Email string `sanitize:"trim,lower"`
}

input := &Input{
    Name:  "  john doe  ",
    Email: "  <EMAIL>  ",
}

validator.SanitizeStruct(input)
// input.Name = "John Doe"
// input.Email = "<EMAIL>"
```

### Error Formatting

```go
// Format validation errors
formatter := validator.NewErrorFormatter("vi")

// As JSON
jsonBytes, _ := formatter.FormatAsJSON(validationErrors)

// As text
text := formatter.FormatAsText(validationErrors)
// Output: Có 2 lỗi xác thực
// - Email: Email phải là địa chỉ email hợp lệ
// - Password: Password phải dài ít nhất 8 ký tự

// For API response
response := validator.NewValidationResponse(validationErrors, "vi")
// {
//   "status": "error",
//   "code": "VALIDATION_ERROR",
//   "message": "Có 2 lỗi xác thực",
//   "errors": {
//     "email": ["Email phải là địa chỉ email hợp lệ"],
//     "password": ["Password phải dài ít nhất 8 ký tự"]
//   }
// }
```

## Available Validators

### Built-in Validators (go-playground/validator)

- `required` - Field không được empty
- `email` - Valid email address
- `url` - Valid URL
- `min=n` - Minimum length/value
- `max=n` - Maximum length/value
- `len=n` - Exact length
- `gte=n` - Greater than or equal
- `lte=n` - Less than or equal
- `oneof=a b c` - One of allowed values
- `contains=substr` - Contains substring
- `numeric` - Numeric string
- `alpha` - Alphabetic characters only
- `alphanumeric` - Alphanumeric characters

### Custom Validators

- `vnphone` - Vietnamese phone number
- `vnidcard` - Vietnamese ID card (CMND/CCCD)
- `strongpassword` - Strong password (8+ chars, 3/4 types)
- `username` - Valid username format
- `slug` - URL-friendly slug
- `hexcolor` - Hex color code (#RGB or #RRGGBB)
- `ipv4` - IPv4 address
- `ipv6` - IPv6 address
- `mac` - MAC address
- `port` - Port number (1-65535)
- `domain` - Domain name
- `https` - HTTPS URL only

## Best Practices

1. **Initialize Once**: Initialize validator at startup và reuse
   ```go
   func init() {
       validator.Initialize(validator.Options{
           DefaultLocale: "vi",
       })
   }
   ```

2. **Use Struct Tags**: Define validation rules trong struct tags
   ```go
   type Model struct {
       Field string `validate:"required,min=3" sanitize:"trim"`
   }
   ```

3. **Handle Errors Properly**: Check error types và format appropriately
   ```go
   if err := validator.Validate(data); err != nil {
       if ve, ok := err.(validator.ValidationErrors); ok {
           // Handle validation errors
           response := validator.NewValidationResponse(ve, locale)
       }
   }
   ```

4. **Sanitize Input**: Clean input trước khi validate
   ```go
   validator.SanitizeStruct(&input)
   err := validator.Validate(&input)
   ```

5. **Context in Validators**: Use context cho custom validators
   ```go
   func(ctx context.Context, fl validator.FieldLevel) bool {
       // Can access request context, user info, etc.
       return true
   }
   ```

## Examples

### Registration Form

```go
type RegistrationForm struct {
    Username string `json:"username" validate:"required,username" sanitize:"trim"`
    Email    string `json:"email" validate:"required,email" sanitize:"trim,lower"`
    Password string `json:"password" validate:"required,strongpassword"`
    Confirm  string `json:"confirm" validate:"required,eqfield=Password"`
    Phone    string `json:"phone" validate:"vnphone"`
    Terms    bool   `json:"terms" validate:"required"`
}

func handleRegistration(w http.ResponseWriter, r *http.Request) {
    var form RegistrationForm
    
    // Validate JSON request
    if err := requestValidator.ValidateJSON(r, &form); err != nil {
        w.WriteHeader(http.StatusBadRequest)
        json.NewEncoder(w).Encode(err)
        return
    }
    
    // Process registration...
}
```

### API Endpoint Validation

```go
// Middleware approach
router.Handle("/api/users", 
    validator.Middleware(validateCreateUser)).
    Methods("POST")

func validateCreateUser(r *http.Request) (interface{}, error) {
    var req CreateUserRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        return nil, err
    }
    
    validator.SanitizeStruct(&req)
    
    if err := validator.Validate(&req); err != nil {
        return nil, err
    }
    
    return &req, nil
}

func createUserHandler(w http.ResponseWriter, r *http.Request) {
    // Get validated data
    req, _ := validator.GetValidatedDataT[CreateUserRequest](r)
    
    // Process request...
}
```

### Custom Business Rules

```go
// Register business rule validators
validator.RegisterValidation("unique_email", func(ctx context.Context, fl validator.FieldLevel) bool {
    email := fl.Field().(string)
    
    // Check database
    exists, _ := userRepo.EmailExists(ctx, email)
    return !exists
})

validator.RegisterTranslation("unique_email", "en", "Email {0} is already registered")
validator.RegisterTranslation("unique_email", "vi", "Email {0} đã được đăng ký")

// Use in struct
type User struct {
    Email string `validate:"required,email,unique_email"`
}
```

## Error Response Format

Standard validation error response:

```json
{
  "status": "error",
  "code": "VALIDATION_ERROR",
  "message": "There are 2 validation errors",
  "errors": {
    "email": ["Email must be a valid email address"],
    "password": ["Password must be at least 8 characters"]
  },
  "details": [
    {
      "field": "email",
      "code": "email",
      "message": "Email must be a valid email address",
      "value": "invalid"
    },
    {
      "field": "password",
      "code": "min",
      "message": "Password must be at least 8 characters",
      "value": "short",
      "param": "8"
    }
  ]
}
```

## Performance Tips

1. **Reuse Validators**: Don't create new validators for each request
2. **Cache Translations**: Translations are cached automatically
3. **Validate Early**: Validate at the edge (middleware/handlers)
4. **Batch Validation**: Validate multiple fields together
5. **Lazy Loading**: Only load translations when needed