package validator

import (
	"encoding/json"
	"fmt"
	"strings"
)

// ErrorFormatter formats validation errors for different output formats
type ErrorFormatter struct {
	locale string
}

// NewErrorFormatter creates a new error formatter
func NewErrorFormatter(locale string) *ErrorFormatter {
	return &ErrorFormatter{
		locale: locale,
	}
}

// FormatError formats a single validation error
func (ef *ErrorFormatter) FormatError(err ValidationError) map[string]interface{} {
	return map[string]interface{}{
		"field":   err.Field(),
		"tag":     err.Tag(),
		"value":   err.Value(),
		"message": err.Message(),
	}
}

// FormatErrors formats multiple validation errors
func (ef *ErrorFormatter) FormatErrors(errs ValidationErrors) interface{} {
	result := make(map[string]interface{})
	
	// Group errors by field
	fieldErrors := make(map[string][]string)
	for _, err := range errs {
		fieldErrors[err.Field()] = append(fieldErrors[err.Field()], err.Message())
	}
	
	result["errors"] = fieldErrors
	result["message"] = ef.getSummaryMessage(len(errs))
	
	return result
}

// FormatAsJSON formats validation errors as JSON
func (ef *ErrorFormatter) FormatAsJSON(errs ValidationErrors) ([]byte, error) {
	formatted := ef.FormatErrors(errs)
	return json.MarshalIndent(formatted, "", "  ")
}

// FormatAsText formats validation errors as plain text
func (ef *ErrorFormatter) FormatAsText(errs ValidationErrors) string {
	if len(errs) == 0 {
		return ""
	}
	
	var sb strings.Builder
	sb.WriteString(ef.getSummaryMessage(len(errs)))
	sb.WriteString("\n")
	
	for _, err := range errs {
		sb.WriteString(fmt.Sprintf("- %s: %s\n", err.Field(), err.Message()))
	}
	
	return sb.String()
}

// FormatAsHTML formats validation errors as HTML
func (ef *ErrorFormatter) FormatAsHTML(errs ValidationErrors) string {
	if len(errs) == 0 {
		return ""
	}
	
	var sb strings.Builder
	sb.WriteString(`<div class="validation-errors">`)
	sb.WriteString(fmt.Sprintf(`<p class="error-summary">%s</p>`, ef.getSummaryMessage(len(errs))))
	sb.WriteString(`<ul class="error-list">`)
	
	for _, err := range errs {
		sb.WriteString(fmt.Sprintf(`<li><strong>%s:</strong> %s</li>`, err.Field(), err.Message()))
	}
	
	sb.WriteString(`</ul></div>`)
	return sb.String()
}

// FormatForAPI formats validation errors for API responses
func (ef *ErrorFormatter) FormatForAPI(errs ValidationErrors) map[string]interface{} {
	errors := make([]map[string]interface{}, 0, len(errs))
	
	for _, err := range errs {
		errors = append(errors, map[string]interface{}{
			"field":   err.Field(),
			"code":    err.Tag(),
			"message": err.Message(),
			"value":   err.Value(),
		})
	}
	
	return map[string]interface{}{
		"status":  "error",
		"code":    "VALIDATION_ERROR",
		"message": ef.getSummaryMessage(len(errs)),
		"errors":  errors,
	}
}

// FormatFieldErrors formats errors grouped by field
func (ef *ErrorFormatter) FormatFieldErrors(errs ValidationErrors) map[string][]string {
	fieldErrors := make(map[string][]string)
	
	for _, err := range errs {
		fieldErrors[err.Field()] = append(fieldErrors[err.Field()], err.Message())
	}
	
	return fieldErrors
}

// FormatFlatErrors formats errors as a flat list of messages
func (ef *ErrorFormatter) FormatFlatErrors(errs ValidationErrors) []string {
	messages := make([]string, 0, len(errs))
	
	for _, err := range errs {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field(), err.Message()))
	}
	
	return messages
}

// getSummaryMessage returns a summary message based on error count and locale
func (ef *ErrorFormatter) getSummaryMessage(count int) string {
	if ef.locale == "vi" {
		if count == 1 {
			return "Có 1 lỗi xác thực"
		}
		return fmt.Sprintf("Có %d lỗi xác thực", count)
	}
	
	// Default to English
	if count == 1 {
		return "There is 1 validation error"
	}
	return fmt.Sprintf("There are %d validation errors", count)
}

// ValidationResponse represents a standard validation error response
type ValidationResponse struct {
	Status  string                 `json:"status"`
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Errors  map[string][]string    `json:"errors,omitempty"`
	Details []ValidationErrorDetail `json:"details,omitempty"`
}

// ValidationErrorDetail represents detailed validation error information
type ValidationErrorDetail struct {
	Field   string      `json:"field"`
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
	Param   string      `json:"param,omitempty"`
}

// NewValidationResponse creates a new validation response from errors
func NewValidationResponse(errs ValidationErrors, locale string) *ValidationResponse {
	formatter := NewErrorFormatter(locale)
	
	// Create field errors map
	fieldErrors := formatter.FormatFieldErrors(errs)
	
	// Create detailed errors
	details := make([]ValidationErrorDetail, 0, len(errs))
	for _, err := range errs {
		details = append(details, ValidationErrorDetail{
			Field:   err.Field(),
			Code:    err.Tag(),
			Message: err.Message(),
			Value:   err.Value(),
			Param:   err.Param(),
		})
	}
	
	return &ValidationResponse{
		Status:  "error",
		Code:    "VALIDATION_ERROR",
		Message: formatter.getSummaryMessage(len(errs)),
		Errors:  fieldErrors,
		Details: details,
	}
}

// Error implements the error interface for ValidationResponse
func (vr *ValidationResponse) Error() string {
	return vr.Message
}

// ToJSON converts ValidationResponse to JSON
func (vr *ValidationResponse) ToJSON() ([]byte, error) {
	return json.Marshal(vr)
}

// ToJSONIndent converts ValidationResponse to indented JSON
func (vr *ValidationResponse) ToJSONIndent() ([]byte, error) {
	return json.MarshalIndent(vr, "", "  ")
}