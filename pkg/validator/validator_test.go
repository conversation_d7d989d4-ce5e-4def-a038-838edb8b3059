package validator

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

// Test structs
type User struct {
	ID       string `json:"id" validate:"required,ulid"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,strongpassword"`
	Age      int    `json:"age" validate:"min=18,max=120"`
	Phone    string `json:"phone" validate:"vnphone"`
	IDCard   string `json:"id_card" validate:"vnidcard"`
	Username string `json:"username" validate:"required,username"`
	Website  string `json:"website" validate:"omitempty,url,https"`
}

type Address struct {
	Street  string `json:"street" validate:"required"`
	City    string `json:"city" validate:"required"`
	Country string `json:"country" validate:"required,oneof=VN US UK"`
	ZipCode string `json:"zip_code" validate:"required,len=5"`
}

func TestValidator(t *testing.T) {
	// Initialize validator
	v, err := NewValidator(Options{
		TagName:       "validate",
		DefaultLocale: "en",
	})
	if err != nil {
		t.Fatalf("Failed to create validator: %v", err)
	}

	// Register custom validators
	if err := RegisterCustomValidators(v); err != nil {
		t.Fatalf("Failed to register custom validators: %v", err)
	}

	t.Run("ValidUser", func(t *testing.T) {
		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "<EMAIL>",
			Password: "SecureP@ss123",
			Age:      25,
			Phone:    "0912345678",
			IDCard:   "123456789",
			Username: "john_doe",
			Website:  "https://example.com",
		}

		if err := v.Validate(context.Background(), &user); err != nil {
			t.Errorf("Expected valid user, got error: %v", err)
		}
	})

	t.Run("InvalidEmail", func(t *testing.T) {
		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "invalid-email",
			Password: "SecureP@ss123",
			Age:      25,
			Username: "john_doe",
		}

		err := v.Validate(context.Background(), &user)
		if err == nil {
			t.Error("Expected validation error for invalid email")
		}

		if ve, ok := err.(ValidationErrors); ok {
			found := false
			for _, e := range ve {
				// Field name could be either "Email" or "email" (JSON tag)
				if (e.Field() == "Email" || e.Field() == "email") && e.Tag() == "email" {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected email validation error. Got errors: %v", ve)
			}
		}
	})

	t.Run("WeakPassword", func(t *testing.T) {
		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "<EMAIL>",
			Password: "weak",
			Age:      25,
			Username: "john_doe",
		}

		err := v.Validate(context.Background(), &user)
		if err == nil {
			t.Error("Expected validation error for weak password")
		}

		if ve, ok := err.(ValidationErrors); ok {
			found := false
			for _, e := range ve {
				if (e.Field() == "Password" || e.Field() == "password") && e.Tag() == "strongpassword" {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected password validation error. Got errors: %v", ve)
			}
		}
	})

	t.Run("InvalidVietnamesePhone", func(t *testing.T) {
		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "<EMAIL>",
			Password: "SecureP@ss123",
			Age:      25,
			Phone:    "0123456789", // Invalid prefix
			Username: "john_doe",
		}

		err := v.Validate(context.Background(), &user)
		if err == nil {
			t.Error("Expected validation error for invalid Vietnamese phone")
		}

		if ve, ok := err.(ValidationErrors); ok {
			found := false
			for _, e := range ve {
				if (e.Field() == "Phone" || e.Field() == "phone") && e.Tag() == "vnphone" {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected Vietnamese phone validation error. Got errors: %v", ve)
			}
		}
	})

	t.Run("ValidateField", func(t *testing.T) {
		email := "<EMAIL>"
		if err := v.ValidateField(context.Background(), email, "required,email"); err != nil {
			t.Errorf("Expected valid email, got error: %v", err)
		}

		invalidEmail := "invalid"
		if err := v.ValidateField(context.Background(), invalidEmail, "email"); err == nil {
			t.Error("Expected validation error for invalid email field")
		}
	})

	t.Run("SetLocale", func(t *testing.T) {
		// Set Vietnamese locale
		if err := v.SetLocale("vi"); err != nil {
			t.Fatalf("Failed to set locale: %v", err)
		}

		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "invalid",
			Password: "SecureP@ss123",
			Age:      25,
			Username: "john_doe",
		}

		err := v.Validate(context.Background(), &user)
		if err == nil {
			t.Error("Expected validation error")
		}

		// Check if error message is in Vietnamese
		if ve, ok := err.(ValidationErrors); ok && len(ve) > 0 {
			msg := ve[0].Message()
			// Vietnamese translation might not be available for all tags
			// Just verify we got an error message
			if msg == "" {
				t.Error("Expected non-empty error message")
			}
		}

		// Switch back to English
		v.SetLocale("en")
	})
}

func TestCustomValidators(t *testing.T) {
	tests := []struct {
		name      string
		validator func(string) bool
		input     string
		expected  bool
	}{
		// Vietnamese phone tests
		{"ValidVNPhone", IsVietnamesePhone, "0912345678", true},
		{"ValidVNPhoneWithCountryCode", IsVietnamesePhone, "+84912345678", true},
		{"InvalidVNPhonePrefix", IsVietnamesePhone, "0123456789", false},
		{"InvalidVNPhoneLength", IsVietnamesePhone, "091234567", false},

		// Vietnamese ID card tests
		{"ValidOldIDCard", IsVietnameseIDCard, "123456789", true},
		{"ValidNewIDCard", IsVietnameseIDCard, "001234567890", true},
		{"InvalidIDCardLength", IsVietnameseIDCard, "12345", false},
		{"InvalidIDCardChars", IsVietnameseIDCard, "12345678a", false},

		// Slug tests
		{"ValidSlug", IsSlug, "hello-world", true},
		{"ValidSlugNumbers", IsSlug, "article-123", true},
		{"InvalidSlugStartDash", IsSlug, "-hello", false},
		{"InvalidSlugEndDash", IsSlug, "hello-", false},
		{"InvalidSlugUppercase", IsSlug, "Hello-World", false},

		// Username tests
		{"ValidUsername", IsUsername, "john_doe", true},
		{"ValidUsernameWithDot", IsUsername, "john.doe", true},
		{"InvalidUsernameTooShort", IsUsername, "ab", false},
		{"InvalidUsernameStartUnderscore", IsUsername, "_john", false},
		{"InvalidUsernameConsecutiveDots", IsUsername, "john..doe", false},

		// Hex color tests
		{"ValidHexColor6", IsHexColor, "#FF5733", true},
		{"ValidHexColor3", IsHexColor, "#F53", true},
		{"InvalidHexColorNoHash", IsHexColor, "FF5733", false},
		{"InvalidHexColorLength", IsHexColor, "#FF57", false},

		// IP tests
		{"ValidIPv4", IsIPv4, "***********", true},
		{"InvalidIPv4", IsIPv4, "256.1.1.1", false},
		{"ValidDomain", IsDomain, "example.com", true},
		{"ValidSubdomain", IsDomain, "sub.example.com", true},
		{"InvalidDomain", IsDomain, "example", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.validator(tt.input)
			if result != tt.expected {
				t.Errorf("Expected %v for input %q, got %v", tt.expected, tt.input, result)
			}
		})
	}
}

func TestStrongPassword(t *testing.T) {
	tests := []struct {
		password string
		expected bool
	}{
		{"SecureP@ss123", true},     // Has upper, lower, number, special
		{"SecurePass123", true},      // Has upper, lower, number (3/4)
		{"securepass123", false},     // Only lower and number (2/4)
		{"SECUREPASS123", false},     // Only upper and number (2/4)
		{"Secure@Pass", true},        // Has upper, lower, special (3/4)
		{"short", false},             // Too short
		{"longenoughbutWeak", false}, // Only 2 character types
	}

	for _, tt := range tests {
		t.Run(tt.password, func(t *testing.T) {
			result := IsStrongPassword(tt.password)
			if result != tt.expected {
				t.Errorf("Expected %v for password %q, got %v", tt.expected, tt.password, result)
			}
		})
	}
}

func TestValidationHelpers(t *testing.T) {
	// Initialize default validator
	if err := Initialize(); err != nil {
		t.Fatalf("Failed to initialize validator: %v", err)
	}

	t.Run("ValidateMap", func(t *testing.T) {
		data := map[string]interface{}{
			"email": "<EMAIL>",
			"age":   25,
			"name":  "John Doe",
		}

		rules := map[string]string{
			"email": "required,email",
			"age":   "required,min=18,max=100",
			"name":  "required,min=2,max=50",
		}

		if err := ValidateMap(data, rules); err != nil {
			t.Errorf("Expected valid data, got error: %v", err)
		}

		// Test with invalid data
		data["email"] = "invalid"
		data["age"] = 150

		err := ValidateMap(data, rules)
		if err == nil {
			t.Error("Expected validation error")
		}
	})

	t.Run("ValidatePartial", func(t *testing.T) {
		user := User{
			ID:       "01ARZ3NDEKTSV4RRFFQ69G5FAV",
			Email:    "invalid", // Invalid but won't be validated
			Password: "SecureP@ss123",
			Age:      25,
			Username: "john_doe",
		}

		// Only validate specific fields
		if err := ValidatePartial(&user, "Password", "Age", "Username"); err != nil {
			t.Errorf("Expected valid partial validation, got error: %v", err)
		}

		// Now validate the email field
		if err := ValidatePartial(&user, "Email"); err == nil {
			t.Error("Expected validation error for email field")
		}
	})

	t.Run("ValidationBuilder", func(t *testing.T) {
		tag := NewValidationBuilder().
			Required().
			Email().
			Build()

		if tag != "required,email" {
			t.Errorf("Expected 'required,email', got %q", tag)
		}

		tag2 := NewValidationBuilder().
			Required().
			Min(3).
			Max(50).
			Custom("lowercase").
			Build()

		if tag2 != "required,min=3,max=50,lowercase" {
			t.Errorf("Expected 'required,min=3,max=50,lowercase', got %q", tag2)
		}
	})

	t.Run("SanitizeStruct", func(t *testing.T) {
		type SanitizeTest struct {
			Name     string `sanitize:"trim,title"`
			Email    string `sanitize:"trim,lower"`
			Username string `sanitize:"trim"`
			Skip     string `sanitize:"-"`
		}

		data := &SanitizeTest{
			Name:     "  john doe  ",
			Email:    "  <EMAIL>  ",
			Username: "  user_name  ",
			Skip:     "  no change  ",
		}

		if err := SanitizeStruct(data); err != nil {
			t.Fatalf("Failed to sanitize struct: %v", err)
		}

		if data.Name != "John Doe" {
			t.Errorf("Expected 'John Doe', got %q", data.Name)
		}

		if data.Email != "<EMAIL>" {
			t.Errorf("Expected '<EMAIL>', got %q", data.Email)
		}

		if data.Username != "user_name" {
			t.Errorf("Expected 'user_name', got %q", data.Username)
		}

		if data.Skip != "  no change  " {
			t.Errorf("Expected '  no change  ', got %q", data.Skip)
		}
	})
}

func TestRequestValidator(t *testing.T) {
	// Initialize validator
	v, _ := NewValidator()
	rv := NewRequestValidator(v)

	t.Run("ValidateJSON", func(t *testing.T) {
		type CreateUserRequest struct {
			Name  string `json:"name" validate:"required,min=2"`
			Email string `json:"email" validate:"required,email"`
		}

		// Valid request
		body := `{"name": "John Doe", "email": "<EMAIL>"}`
		req := httptest.NewRequest(http.MethodPost, "/users", strings.NewReader(body))
		req.Header.Set("Content-Type", "application/json")

		var data CreateUserRequest
		if err := rv.ValidateJSON(req, &data); err != nil {
			t.Errorf("Expected valid JSON, got error: %v", err)
		}

		// Invalid JSON
		body2 := `{"name": "J", "email": "invalid"}`
		req2 := httptest.NewRequest(http.MethodPost, "/users", strings.NewReader(body2))
		req2.Header.Set("Content-Type", "application/json")

		var data2 CreateUserRequest
		err := rv.ValidateJSON(req2, &data2)
		if err == nil {
			t.Error("Expected validation error")
		}

		if vr, ok := err.(*ValidationResponse); ok {
			if vr.Code != "VALIDATION_ERROR" {
				t.Errorf("Expected VALIDATION_ERROR code, got %s", vr.Code)
			}
		}
	})

	t.Run("ValidateQuery", func(t *testing.T) {
		type SearchRequest struct {
			Query string `query:"q" validate:"required,min=3"`
			Page  int    `query:"page" validate:"min=1"`
			Limit int    `query:"limit" validate:"min=1,max=100"`
		}

		req := httptest.NewRequest(http.MethodGet, "/search?q=golang&page=1&limit=20", nil)

		var data SearchRequest
		if err := rv.ValidateQuery(req, &data); err != nil {
			t.Errorf("Expected valid query, got error: %v", err)
		}

		if data.Query != "golang" || data.Page != 1 || data.Limit != 20 {
			t.Error("Query parameters not parsed correctly")
		}

		// Invalid query
		req2 := httptest.NewRequest(http.MethodGet, "/search?q=go&page=0", nil)

		var data2 SearchRequest
		err := rv.ValidateQuery(req2, &data2)
		if err == nil {
			t.Error("Expected validation error")
		}
	})
}

func TestErrorFormatter(t *testing.T) {
	// Create some validation errors
	errors := ValidationErrors{
		&validationError{
			field:   "Email",
			tag:     "email",
			value:   "invalid",
			message: "Email must be a valid email address",
		},
		&validationError{
			field:   "Password",
			tag:     "min",
			value:   "short",
			param:   "8",
			message: "Password must be at least 8 characters",
		},
	}

	formatter := NewErrorFormatter("en")

	t.Run("FormatAsJSON", func(t *testing.T) {
		json, err := formatter.FormatAsJSON(errors)
		if err != nil {
			t.Fatalf("Failed to format as JSON: %v", err)
		}

		if !strings.Contains(string(json), "Email") {
			t.Error("JSON should contain Email field")
		}
	})

	t.Run("FormatAsText", func(t *testing.T) {
		text := formatter.FormatAsText(errors)
		if !strings.Contains(text, "2 validation errors") {
			t.Error("Text should contain error count")
		}
	})

	t.Run("FormatFieldErrors", func(t *testing.T) {
		fieldErrors := formatter.FormatFieldErrors(errors)
		if len(fieldErrors) != 2 {
			t.Errorf("Expected 2 field errors, got %d", len(fieldErrors))
		}

		if len(fieldErrors["Email"]) != 1 {
			t.Error("Expected 1 error for Email field")
		}
	})

	t.Run("ValidationResponse", func(t *testing.T) {
		response := NewValidationResponse(errors, "en")
		if response.Code != "VALIDATION_ERROR" {
			t.Errorf("Expected VALIDATION_ERROR code, got %s", response.Code)
		}

		if len(response.Details) != 2 {
			t.Errorf("Expected 2 error details, got %d", len(response.Details))
		}

		json, err := response.ToJSON()
		if err != nil {
			t.Fatalf("Failed to convert to JSON: %v", err)
		}

		if !strings.Contains(string(json), "VALIDATION_ERROR") {
			t.Error("JSON should contain error code")
		}
	})
}