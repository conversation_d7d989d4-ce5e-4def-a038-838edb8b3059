package validator

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"
)

// RequestValidator provides HTTP request validation functionality
type RequestValidator struct {
	validator      Validator
	maxBodySize    int64
	errorFormatter *ErrorFormatter
}

// NewRequestValidator creates a new request validator
func NewRequestValidator(validator Validator) *RequestValidator {
	return &RequestValidator{
		validator:      validator,
		maxBodySize:    10 * 1024 * 1024, // 10MB default
		errorFormatter: NewErrorFormatter("en"),
	}
}

// SetMaxBodySize sets the maximum allowed request body size
func (rv *RequestValidator) SetMaxBodySize(size int64) {
	rv.maxBodySize = size
}

// SetLocale sets the locale for error messages
func (rv *RequestValidator) SetLocale(locale string) {
	rv.validator.SetLocale(locale)
	rv.errorFormatter = NewErrorFormatter(locale)
}

// ValidateJSON validates JSON request body
func (rv *RequestValidator) ValidateJSON(r *http.Request, dst interface{}) error {
	// Limit request body size
	r.Body = http.MaxBytesReader(nil, r.Body, rv.maxBodySize)
	
	// Decode JSON
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields()
	
	if err := decoder.Decode(dst); err != nil {
		if err == io.EOF {
			return &ValidationResponse{
				Status:  "error",
				Code:    "INVALID_JSON",
				Message: "Request body is empty",
			}
		}
		
		switch e := err.(type) {
		case *json.UnmarshalTypeError:
			return &ValidationResponse{
				Status:  "error",
				Code:    "INVALID_JSON",
				Message: fmt.Sprintf("Invalid type for field '%s': expected %s, got %s", e.Field, e.Type.String(), e.Value),
			}
		case *json.SyntaxError:
			return &ValidationResponse{
				Status:  "error",
				Code:    "INVALID_JSON",
				Message: fmt.Sprintf("Invalid JSON syntax at position %d", e.Offset),
			}
		default:
			if strings.Contains(err.Error(), "unknown field") {
				field := strings.TrimPrefix(err.Error(), "json: unknown field ")
				return &ValidationResponse{
					Status:  "error",
					Code:    "UNKNOWN_FIELD",
					Message: fmt.Sprintf("Unknown field: %s", field),
				}
			}
			return &ValidationResponse{
				Status:  "error",
				Code:    "INVALID_JSON",
				Message: "Invalid JSON format",
			}
		}
	}
	
	// Check for extra data
	if decoder.More() {
		return &ValidationResponse{
			Status:  "error",
			Code:    "INVALID_JSON",
			Message: "Request body contains multiple JSON values",
		}
	}
	
	// Sanitize struct if applicable
	SanitizeStruct(dst)
	
	// Validate struct
	if err := rv.validator.Validate(r.Context(), dst); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			return NewValidationResponse(ve, rv.errorFormatter.locale)
		}
		return err
	}
	
	return nil
}

// ValidateQuery validates query parameters
func (rv *RequestValidator) ValidateQuery(r *http.Request, dst interface{}) error {
	// Parse query parameters into struct
	if err := rv.parseQueryParams(r, dst); err != nil {
		return &ValidationResponse{
			Status:  "error",
			Code:    "INVALID_QUERY",
			Message: fmt.Sprintf("Failed to parse query parameters: %v", err),
		}
	}
	
	// Sanitize struct
	SanitizeStruct(dst)
	
	// Validate struct
	if err := rv.validator.Validate(r.Context(), dst); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			return NewValidationResponse(ve, rv.errorFormatter.locale)
		}
		return err
	}
	
	return nil
}

// ValidateForm validates form data
func (rv *RequestValidator) ValidateForm(r *http.Request, dst interface{}) error {
	// Parse form
	if err := r.ParseForm(); err != nil {
		return &ValidationResponse{
			Status:  "error",
			Code:    "INVALID_FORM",
			Message: "Failed to parse form data",
		}
	}
	
	// Convert form values to struct
	if err := rv.parseFormData(r, dst); err != nil {
		return &ValidationResponse{
			Status:  "error",
			Code:    "INVALID_FORM",
			Message: fmt.Sprintf("Failed to parse form data: %v", err),
		}
	}
	
	// Sanitize struct
	SanitizeStruct(dst)
	
	// Validate struct
	if err := rv.validator.Validate(r.Context(), dst); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			return NewValidationResponse(ve, rv.errorFormatter.locale)
		}
		return err
	}
	
	return nil
}

// Middleware returns a middleware function for automatic request validation
func (rv *RequestValidator) Middleware(validationFunc func(*http.Request) (interface{}, error)) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Call validation function
			if validationFunc != nil {
				data, err := validationFunc(r)
				if err != nil {
					rv.handleValidationError(w, err)
					return
				}
				
				// Store validated data in context
				ctx := context.WithValue(r.Context(), "validatedData", data)
				r = r.WithContext(ctx)
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// ValidateJSONMiddleware creates a middleware for JSON validation
func (rv *RequestValidator) ValidateJSONMiddleware(structType reflect.Type) func(http.Handler) http.Handler {
	return rv.Middleware(func(r *http.Request) (interface{}, error) {
		// Create new instance of the struct
		dst := reflect.New(structType).Interface()
		
		// Validate JSON
		if err := rv.ValidateJSON(r, dst); err != nil {
			return nil, err
		}
		
		return dst, nil
	})
}

// handleValidationError writes validation error response
func (rv *RequestValidator) handleValidationError(w http.ResponseWriter, err error) {
	w.Header().Set("Content-Type", "application/json")
	
	if vr, ok := err.(*ValidationResponse); ok {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(vr)
		return
	}
	
	// Generic error
	w.WriteHeader(http.StatusBadRequest)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "error",
		"code":    "VALIDATION_ERROR",
		"message": err.Error(),
	})
}

// parseQueryParams parses query parameters into a struct
func (rv *RequestValidator) parseQueryParams(r *http.Request, dst interface{}) error {
	values := r.URL.Query()
	return rv.parseValues(values, dst)
}

// parseFormData parses form data into a struct
func (rv *RequestValidator) parseFormData(r *http.Request, dst interface{}) error {
	return rv.parseValues(r.Form, dst)
}

// parseValues parses URL values into a struct
func (rv *RequestValidator) parseValues(values map[string][]string, dst interface{}) error {
	dstValue := reflect.ValueOf(dst)
	if dstValue.Kind() != reflect.Ptr || dstValue.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("destination must be a pointer to struct")
	}
	
	dstValue = dstValue.Elem()
	dstType := dstValue.Type()
	
	for i := 0; i < dstValue.NumField(); i++ {
		field := dstValue.Field(i)
		fieldType := dstType.Field(i)
		
		// Skip unexported fields
		if !field.CanSet() {
			continue
		}
		
		// Get field name from tag or use field name
		fieldName := fieldType.Tag.Get("form")
		if fieldName == "" {
			fieldName = fieldType.Tag.Get("query")
		}
		if fieldName == "" {
			fieldName = fieldType.Tag.Get("json")
			if idx := strings.Index(fieldName, ","); idx != -1 {
				fieldName = fieldName[:idx]
			}
		}
		if fieldName == "" || fieldName == "-" {
			fieldName = fieldType.Name
		}
		
		// Get values
		vals, ok := values[fieldName]
		if !ok || len(vals) == 0 {
			continue
		}
		
		// Set field value based on type
		if err := setFieldValue(field, vals); err != nil {
			return fmt.Errorf("failed to set field %s: %w", fieldName, err)
		}
	}
	
	return nil
}

// setFieldValue sets a field value from string values
func setFieldValue(field reflect.Value, values []string) error {
	switch field.Kind() {
	case reflect.String:
		field.SetString(values[0])
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		var v int64
		if _, err := fmt.Sscanf(values[0], "%d", &v); err != nil {
			return err
		}
		field.SetInt(v)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		var v uint64
		if _, err := fmt.Sscanf(values[0], "%d", &v); err != nil {
			return err
		}
		field.SetUint(v)
	case reflect.Float32, reflect.Float64:
		var v float64
		if _, err := fmt.Sscanf(values[0], "%f", &v); err != nil {
			return err
		}
		field.SetFloat(v)
	case reflect.Bool:
		v := values[0] == "true" || values[0] == "1" || values[0] == "yes" || values[0] == "on"
		field.SetBool(v)
	case reflect.Slice:
		// Handle slice of strings
		if field.Type().Elem().Kind() == reflect.String {
			field.Set(reflect.ValueOf(values))
		}
	default:
		return fmt.Errorf("unsupported field type: %s", field.Kind())
	}
	
	return nil
}

// GetValidatedData retrieves validated data from request context
func GetValidatedData(r *http.Request) interface{} {
	return r.Context().Value("validatedData")
}

// GetValidatedDataT retrieves typed validated data from request context
func GetValidatedDataT[T any](r *http.Request) (*T, bool) {
	data := GetValidatedData(r)
	if data == nil {
		return nil, false
	}
	
	typed, ok := data.(*T)
	return typed, ok
}