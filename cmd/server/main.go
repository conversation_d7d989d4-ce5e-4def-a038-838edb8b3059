package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/joho/godotenv"
	"github.com/blog-api-v3/blog-api-v3/internal/api"
	"github.com/blog-api-v3/blog-api-v3/internal/config"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
)

func main() {
	// Load .env file if it exists
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Warning: Failed to load .env file: %v", err)
		} else {
			log.Println("Loaded environment variables from .env file")
		}
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger with beautiful formatting
	loggerConfig := utils.LoggerConfig{
		Level:  cfg.Log.Level,
		Format: "pretty", // Use beautiful colored text format
		Output: nil,      // Use stdout
	}
	logger := utils.NewLoggerWithConfig(loggerConfig)
	logger.Info("🚀 Starting Blog API Server with beautiful logs...")

	// Initialize database (mock for testing)
	var db *sql.DB // Use nil for mock mode

	// Initialize validator
	if err := validator.Initialize(); err != nil {
		logger.WithError(err).Fatal("Failed to initialize validator")
	}
	v := validator.GetDefaultValidator()

	// Initialize API server
	server, err := api.NewServer(cfg, db, v, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize API server")
	}

	// Start server in a goroutine
	go func() {
		addr := fmt.Sprintf(":%d", cfg.Server.Port)
		fullURL := fmt.Sprintf("http://localhost%s", addr)
		logger.WithField("url", fullURL).Info("🌐 Starting HTTP server - Click to open")
		
		if err := server.Start(addr); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	logger.Info("Server exited")
}