package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"github.com/joho/godotenv"

	"github.com/blog-api-v3/blog-api-v3/internal/database/seeders"
)

func main() {
	var (
		command      = flag.String("command", "run", "Seed command: run, rollback, list")
		seederNames  = flag.String("seeders", "", "Comma-separated list of specific seeders to run")
		dbURL        = flag.String("database", os.Getenv("DATABASE_URL"), "Database connection URL")
		verbose      = flag.Bool("verbose", false, "Enable verbose logging")
		envFile      = flag.String("env", ".env", "Path to .env file")
	)
	flag.Parse()

	// Load .env file if it exists
	if _, err := os.Stat(*envFile); err == nil {
		if err := godotenv.Load(*envFile); err != nil {
			log.Printf("Warning: Failed to load .env file: %v", err)
		} else {
			log.Printf("Loaded environment variables from: %s", *envFile)
		}
	}

	if *dbURL == "" {
		// Try to build from environment variables
		host := os.Getenv("DB_HOST")
		port := os.Getenv("DB_PORT")
		user := os.Getenv("DB_USER")
		pass := os.Getenv("DB_PASSWORD")
		name := os.Getenv("DB_NAME")
		charset := os.Getenv("DB_CHARSET")

		if host != "" && user != "" && name != "" {
			if port == "" {
				port = "3306"
			}
			if charset == "" {
				charset = "utf8mb4"
			}
			*dbURL = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local", 
				user, pass, host, port, name, charset)
		} else {
			log.Fatal("Database connection information is required. Set DATABASE_URL or DB_* environment variables")
		}
	}

	// Configure GORM logger
	gormLogger := logger.Default
	if *verbose {
		gormLogger = gormLogger.LogMode(logger.Info)
	} else {
		gormLogger = gormLogger.LogMode(logger.Silent)
	}

	// Connect to database
	db, err := gorm.Open(mysql.Open(*dbURL), &gorm.Config{
		Logger: gormLogger,
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get the underlying sql.DB instance
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}

	// Create seeder manager
	manager := seeders.NewManager(sqlDB)

	// Execute command
	switch *command {
	case "run":
		if *seederNames == "" {
			log.Println("Running all seeders...")
			if err := manager.RunAll(); err != nil {
				log.Fatalf("Seeding failed: %v", err)
			}
		} else {
			seederList := strings.Split(*seederNames, ",")
			log.Printf("Running specific seeders: %v", seederList)
			if err := manager.RunSpecific(seederList); err != nil {
				log.Fatalf("Seeding failed: %v", err)
			}
		}

	case "rollback":
		log.Println("Rolling back seeders...")
		if err := manager.RollbackAll(); err != nil {
			log.Fatalf("Rollback failed: %v", err)
		}

	case "list":
		fmt.Println("Available seeders:")
		for i, name := range manager.List() {
			fmt.Printf("%d. %s\n", i+1, name)
		}

	default:
		log.Fatalf("Unknown command: %s", *command)
	}
}