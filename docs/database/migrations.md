# Database Migrations

## Overview

The Blog API v3 uses [golang-migrate/migrate](https://github.com/golang-migrate/migrate), a database migration tool written in Go. This tool provides:

- Database schema version control
- Sequential migration execution  
- Rollback capabilities
- Support for multiple database drivers (MySQL, PostgreSQL, SQLite, etc.)
- CLI and programmatic interfaces
- Atomic migrations with proper transaction handling

The migration system ensures consistent database state across different environments while maintaining the ability to evolve the schema over time.

## Installation

### Install golang-migrate CLI

```bash
# macOS
brew install golang-migrate

# Linux (download binary)
curl -L https://github.com/golang-migrate/migrate/releases/download/v4.17.0/migrate.linux-amd64.tar.gz | tar xvz
sudo mv migrate /usr/local/bin/

# Using Go
go install -tags 'mysql' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
```

### Install as Go dependency

```bash
go get -u github.com/golang-migrate/migrate/v4
go get -u github.com/golang-migrate/migrate/v4/database/mysql
go get -u github.com/golang-migrate/migrate/v4/source/file
```

## Migration System Architecture

### Directory Structure

```
pkg/
└── database/
    ├── migrations/
    │   ├── 000001_create_user_accounts_table.up.sql
    │   ├── 000001_create_user_accounts_table.down.sql
    │   ├── 000002_create_blog_posts_table.up.sql
    │   ├── 000002_create_blog_posts_table.down.sql
    │   └── ...
    └── migrate.go
```

### Migration File Naming Convention

- Format: `{version}_{description}.{direction}.sql`
- Version: 6-digit sequential number (e.g., `000001`, `000002`)
- Description: Snake case description of the migration
- Direction: Either `up` or `down`
- Example: `000001_create_user_accounts_table.up.sql`

### Programmatic Usage

```go
// pkg/database/migrate.go
package database

import (
    "database/sql"
    "fmt"
    
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/mysql"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

type MigrationManager struct {
    migrate *migrate.Migrate
}

func NewMigrationManager(db *sql.DB, migrationsPath string) (*MigrationManager, error) {
    driver, err := mysql.WithInstance(db, &mysql.Config{})
    if err != nil {
        return nil, fmt.Errorf("creating mysql driver: %w", err)
    }
    
    m, err := migrate.NewWithDatabaseInstance(
        fmt.Sprintf("file://%s", migrationsPath),
        "mysql",
        driver,
    )
    if err != nil {
        return nil, fmt.Errorf("creating migration instance: %w", err)
    }
    
    return &MigrationManager{migrate: m}, nil
}

func (mm *MigrationManager) Up() error {
    if err := mm.migrate.Up(); err != nil && err != migrate.ErrNoChange {
        return fmt.Errorf("running migrations up: %w", err)
    }
    return nil
}

func (mm *MigrationManager) Down(steps int) error {
    if err := mm.migrate.Steps(-steps); err != nil {
        return fmt.Errorf("rolling back %d migrations: %w", steps, err)
    }
    return nil
}

func (mm *MigrationManager) Version() (uint, bool, error) {
    return mm.migrate.Version()
}

func (mm *MigrationManager) Force(version int) error {
    return mm.migrate.Force(version)
}

func (mm *MigrationManager) Close() error {
    sourceErr, dbErr := mm.migrate.Close()
    if sourceErr != nil {
        return fmt.Errorf("closing source: %w", sourceErr)
    }
    if dbErr != nil {
        return fmt.Errorf("closing database: %w", dbErr)
    }
    return nil
}
```

## Migration Examples

### 1. Basic Table Creation

```sql
-- 000001_create_user_accounts_table.up.sql
CREATE TABLE IF NOT EXISTS user_accounts (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar VARCHAR(255),
    bio TEXT,
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_accounts_email (email),
    INDEX idx_user_accounts_username (username),
    INDEX idx_user_accounts_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

```sql
-- 000001_create_user_accounts_table.down.sql
DROP TABLE IF EXISTS user_accounts;
```

### 2. Adding Columns

```sql
-- 000002_add_last_login_to_user_accounts.up.sql
ALTER TABLE user_accounts 
    ADD COLUMN last_login_at TIMESTAMP NULL,
    ADD COLUMN login_count INT UNSIGNED DEFAULT 0;
```

```sql
-- 000002_add_last_login_to_user_accounts.down.sql
ALTER TABLE user_accounts 
    DROP COLUMN last_login_at,
    DROP COLUMN login_count;
```

### 3. Creating Blog Tables with Relationships

```sql
-- 000003_create_blog_posts_table.up.sql
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(255),
    status ENUM('draft', 'published', 'archived', 'deleted') DEFAULT 'draft',
    view_count INT UNSIGNED DEFAULT 0,
    like_count INT UNSIGNED DEFAULT 0,
    author_id INT UNSIGNED NOT NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_posts_slug (slug),
    INDEX idx_posts_status (status),
    INDEX idx_posts_author (author_id),
    INDEX idx_posts_published (published_at),
    
    CONSTRAINT fk_posts_author FOREIGN KEY (author_id) 
        REFERENCES user_accounts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

```sql
-- 000003_create_blog_posts_table.down.sql
DROP TABLE IF EXISTS blog_posts;
```

### 4. Data Migration

```sql
-- 000004_add_role_to_user_accounts.up.sql
-- Add role column with default value
ALTER TABLE user_accounts 
    ADD COLUMN role ENUM('admin', 'moderator', 'user') DEFAULT 'user' AFTER status;

-- Update existing users to have 'user' role
UPDATE user_accounts SET role = 'user' WHERE role IS NULL;

-- Make role column NOT NULL
ALTER TABLE user_accounts MODIFY role ENUM('admin', 'moderator', 'user') NOT NULL DEFAULT 'user';
```

```sql
-- 000004_add_role_to_user_accounts.down.sql
ALTER TABLE user_accounts DROP COLUMN role;
```

### 5. Creating Multiple Related Tables

```sql
-- 000005_create_blog_categories_and_tags.up.sql
-- Create categories table
CREATE TABLE IF NOT EXISTS blog_categories (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id INT UNSIGNED NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_categories_slug (slug),
    INDEX idx_categories_parent (parent_id),
    
    CONSTRAINT fk_categories_parent FOREIGN KEY (parent_id) 
        REFERENCES blog_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create tags table
CREATE TABLE IF NOT EXISTS blog_tags (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_tags_slug (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create post_categories junction table
CREATE TABLE IF NOT EXISTS blog_post_categories (
    post_id INT UNSIGNED NOT NULL,
    category_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (post_id, category_id),
    
    CONSTRAINT fk_post_categories_post FOREIGN KEY (post_id) 
        REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_categories_category FOREIGN KEY (category_id) 
        REFERENCES blog_categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create post_tags junction table
CREATE TABLE IF NOT EXISTS blog_post_tags (
    post_id INT UNSIGNED NOT NULL,
    tag_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (post_id, tag_id),
    
    CONSTRAINT fk_post_tags_post FOREIGN KEY (post_id) 
        REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_tags_tag FOREIGN KEY (tag_id) 
        REFERENCES blog_tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

```sql
-- 000005_create_blog_categories_and_tags.down.sql
DROP TABLE IF EXISTS blog_post_tags;
DROP TABLE IF EXISTS blog_post_categories;
DROP TABLE IF EXISTS blog_tags;
DROP TABLE IF EXISTS blog_categories;
```

## CLI Usage

### Using migrate CLI

```bash
# Create a new migration
migrate create -ext sql -dir pkg/database/migrations -seq create_comments_table

# Run all pending migrations
migrate -path pkg/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" up

# Rollback last migration
migrate -path pkg/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" down 1

# Force a specific version (use with caution)
migrate -path pkg/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" force 3

# Check current version
migrate -path pkg/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" version

# Drop everything (use with extreme caution)
migrate -path pkg/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" drop -f
```

### Environment Variables

```bash
# Set database URL as environment variable
export DATABASE_URL="mysql://user:password@tcp(localhost:3306)/dbname"

# Use with migrate CLI
migrate -path pkg/database/migrations -database "$DATABASE_URL" up
```

### Programmatic CLI Integration

```go
// cmd/migrate/main.go
package main

import (
    "database/sql"
    "flag"
    "fmt"
    "log"
    "os"
    
    _ "github.com/go-sql-driver/mysql"
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/mysql"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

func main() {
    var (
        command    = flag.String("command", "up", "Migration command: up, down, version, force")
        steps      = flag.Int("steps", 0, "Number of migrations to run (for down command)")
        version    = flag.Int("version", 0, "Force to specific version")
        dbURL      = flag.String("database", os.Getenv("DATABASE_URL"), "Database connection URL")
        migrations = flag.String("path", "./pkg/database/migrations", "Path to migrations directory")
    )
    flag.Parse()
    
    if *dbURL == "" {
        log.Fatal("Database URL is required")
    }
    
    db, err := sql.Open("mysql", *dbURL)
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }
    defer db.Close()
    
    driver, err := mysql.WithInstance(db, &mysql.Config{})
    if err != nil {
        log.Fatalf("Failed to create migration driver: %v", err)
    }
    
    m, err := migrate.NewWithDatabaseInstance(
        fmt.Sprintf("file://%s", *migrations),
        "mysql",
        driver,
    )
    if err != nil {
        log.Fatalf("Failed to create migrate instance: %v", err)
    }
    
    switch *command {
    case "up":
        if err := m.Up(); err != nil && err != migrate.ErrNoChange {
            log.Fatalf("Migration up failed: %v", err)
        }
        fmt.Println("Migrations completed successfully")
        
    case "down":
        if *steps == 0 {
            *steps = 1
        }
        if err := m.Steps(-*steps); err != nil {
            log.Fatalf("Migration down failed: %v", err)
        }
        fmt.Printf("Rolled back %d migrations\n", *steps)
        
    case "version":
        version, dirty, err := m.Version()
        if err != nil {
            log.Fatalf("Failed to get version: %v", err)
        }
        if dirty {
            fmt.Printf("Version: %d (dirty)\n", version)
        } else {
            fmt.Printf("Version: %d\n", version)
        }
        
    case "force":
        if *version == 0 {
            log.Fatal("Version number required for force command")
        }
        if err := m.Force(*version); err != nil {
            log.Fatalf("Force migration failed: %v", err)
        }
        fmt.Printf("Forced to version %d\n", *version)
        
    default:
        log.Fatalf("Unknown command: %s", *command)
    }
}
```

## Best Practices

### 1. Migration Naming
- Use sequential numbering: `000001_`, `000002_`, etc.
- Use descriptive names: `create_user_accounts_table`, `add_index_to_blog_posts`
- Use snake_case for consistency
- Keep names concise but clear

### 2. One Change Per Migration
- Each migration should focus on a single change
- Makes debugging easier
- Simplifies rollbacks
- Example: Don't mix table creation with data migration

### 3. Always Write Down Migrations
- Every `.up.sql` should have a corresponding `.down.sql`
- Test both up and down migrations
- Ensure down migrations don't cause data loss where avoidable

### 4. Use Transactions
- Wrap migrations in transactions when possible
- Not all DDL statements can be rolled back in MySQL
- Be aware of implicit commits in MySQL

### 5. Data Safety
- Always backup before running migrations in production
- Test migrations with production-like data volumes
- Consider using `pt-online-schema-change` for large tables

### 6. Avoid Breaking Changes
- Don't rename columns (add new, migrate data, drop old)
- Don't change column types that might lose data
- Add NOT NULL constraints in multiple steps:
  1. Add nullable column
  2. Backfill data
  3. Add NOT NULL constraint

### 7. Migration Testing
```bash
# Test up migration
migrate -path pkg/database/migrations -database "$TEST_DB_URL" up

# Test down migration
migrate -path pkg/database/migrations -database "$TEST_DB_URL" down 1

# Test full cycle
migrate -path pkg/database/migrations -database "$TEST_DB_URL" up
migrate -path pkg/database/migrations -database "$TEST_DB_URL" down
migrate -path pkg/database/migrations -database "$TEST_DB_URL" up
```

## Makefile Integration

```makefile
# Migration commands
migrate-create: ## Create new migration file
	@read -p "Migration name: " name; \
	migrate create -ext sql -dir pkg/database/migrations -seq $$name

migrate-up: ## Run all pending migrations
	migrate -path pkg/database/migrations -database "$(DATABASE_URL)" up

migrate-down: ## Rollback last migration
	migrate -path pkg/database/migrations -database "$(DATABASE_URL)" down 1

migrate-force: ## Force to specific version
	@read -p "Version: " version; \
	migrate -path pkg/database/migrations -database "$(DATABASE_URL)" force $$version

migrate-version: ## Show current migration version
	migrate -path pkg/database/migrations -database "$(DATABASE_URL)" version

migrate-drop: ## Drop everything (DANGEROUS!)
	migrate -path pkg/database/migrations -database "$(DATABASE_URL)" drop -f

.PHONY: migrate-create migrate-up migrate-down migrate-force migrate-version migrate-drop
```

## Migration Workflow

### Development Workflow

1. **Create Migration**
   ```bash
   make migrate-create
   # Enter: add_user_preferences_table
   ```

2. **Edit Migration Files**
   - Edit `pkg/database/migrations/000006_add_user_preferences_table.up.sql`
   - Edit `pkg/database/migrations/000006_add_user_preferences_table.down.sql`

3. **Run Migration**
   ```bash
   make migrate-up
   ```

4. **Test Rollback**
   ```bash
   make migrate-down
   make migrate-up
   ```

### Production Deployment

1. **Backup Database**
   ```bash
   mysqldump -u user -p database > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Check Current Version**
   ```bash
   make migrate-version
   ```

3. **Run Migrations**
   ```bash
   make migrate-up
   ```

4. **Verify**
   ```bash
   make migrate-version
   ```

## Related Documentation

- [Database Seeding](./seeding.md)
- [Database Models](./models.md)
- [Development Workflow](../development/workflow.md)