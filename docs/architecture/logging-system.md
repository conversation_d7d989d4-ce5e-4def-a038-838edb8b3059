# Logging System - <PERSON><PERSON> thống ghi log

## Tổng quan

Logging System cung cấp hệ thống ghi log toàn diện cho Blog API v3, hỗ trợ multiple log levels, structured logging, centralized log management, và khả năng chuyển đổi giữa debug/production modes. Hệ thống được thiết kế để có hiệu năng cao, dễ dàng debug và phân tích.

## Mục tiêu

- **Structured Logging**: Log format chuẩn với metadata phong phú
- **Performance**: Low-overhead logging, async processing
- **Centralized Management**: Tập trung logs từ nhiều services
- **Debug Support**: Chi tiết logging cho development/debugging
- **Production Optimization**: Optimized cho production environment
- **Security**: Bảo mật sensitive data trong logs
- **Compliance**: Tuân thủ GDPR và data retention policies

## Kiến trúc hệ thống

### Logging Architecture Overview

```mermaid
flowchart TD
    A[Logging System] --> B[Log Producers]
    A --> C[Log Pipeline]
    A --> D[Log Storage]
    A --> E[Log Analysis]
    A --> F[Log Management]
    
    B --> B1[Application Logs]
    B --> B2[Access Logs]
    B --> B3[Error Logs]
    B --> B4[Audit Logs]
    B --> B5[Security Logs]
    
    C --> C1[Log Collectors]
    C --> C2[Log Processors]
    C --> C3[Log Forwarders]
    C --> C4[Log Filters]
    
    D --> D1[Hot Storage]
    D --> D2[Warm Storage]
    D --> D3[Cold Storage]
    D --> D4[Archive Storage]
    
    E --> E1[Real-time Analysis]
    E --> E2[Log Search]
    E --> E3[Log Aggregation]
    E --> E4[Anomaly Detection]
    
    F --> F1[Retention Policies]
    F --> F2[Access Control]
    F --> F3[Log Rotation]
    F --> F4[Compliance]
```

### Components

#### Log Producers
- **Application Logs**: Business logic, operations, events
- **Access Logs**: HTTP requests, API calls, user actions
- **Error Logs**: Exceptions, failures, stack traces
- **Audit Logs**: Security events, data changes, compliance
- **Security Logs**: Authentication, authorization, threats

#### Log Pipeline
- **Collectors**: Gather logs từ multiple sources
- **Processors**: Transform, enrich, filter logs
- **Forwarders**: Route logs đến destinations
- **Filters**: Remove sensitive data, apply rules

#### Log Storage
- **Hot Storage**: Recent logs (1-7 days) - fast access
- **Warm Storage**: Medium-term (7-30 days) - balanced
- **Cold Storage**: Long-term (30-365 days) - compressed
- **Archive**: Compliance archive (>1 year) - deep storage

## Log Levels & Modes

### 1. Log Levels Definition

```go
type LogLevel int

const (
    TRACE LogLevel = iota // Most verbose - development only
    DEBUG                  // Detailed debug information
    INFO                   // General information
    WARN                   // Warning conditions
    ERROR                  // Error conditions
    FATAL                  // Fatal errors causing shutdown
    PANIC                  // Panic conditions
)

type LogMode string

const (
    ModeDevelopment LogMode = "development"
    ModeDebug       LogMode = "debug"
    ModeStaging     LogMode = "staging"
    ModeProduction  LogMode = "production"
)

// Log level configuration by mode
var LogLevelByMode = map[LogMode]LogLevel{
    ModeDevelopment: TRACE,
    ModeDebug:       DEBUG,
    ModeStaging:     INFO,
    ModeProduction:  WARN,
}
```

### 2. Environment-based Configuration

```yaml
# Development Mode
logging:
  mode: development
  level: trace
  output:
    - console
    - file
  console:
    format: text
    colorize: true
    include_caller: true
  file:
    path: ./logs/development.log
    max_size: 100MB
    max_age: 7
    compress: false
  
# Debug Mode  
logging:
  mode: debug
  level: debug
  output:
    - console
    - file
    - remote
  console:
    format: text
    colorize: true
    include_caller: true
    include_stack_trace: true
  file:
    path: ./logs/debug.log
    max_size: 500MB
    max_age: 30
    compress: true
  remote:
    enabled: true
    endpoint: http://log-aggregator:9077
    
# Production Mode
logging:
  mode: production
  level: warn
  output:
    - file
    - remote
  console:
    enabled: false
  file:
    path: /var/log/blog-api/production.log
    max_size: 1GB
    max_age: 90
    max_backups: 10
    compress: true
  remote:
    enabled: true
    endpoint: https://logs.production.com
    batch_size: 1000
    flush_interval: 5s
```

## Structured Logging Implementation

### 1. Logger Interface

```go
type Logger interface {
    // Basic logging methods
    Trace(msg string, fields ...Field)
    Debug(msg string, fields ...Field)
    Info(msg string, fields ...Field)
    Warn(msg string, fields ...Field)
    Error(msg string, fields ...Field)
    Fatal(msg string, fields ...Field)
    Panic(msg string, fields ...Field)
    
    // Contextual logging
    WithContext(ctx context.Context) Logger
    WithFields(fields ...Field) Logger
    WithError(err error) Logger
    
    // Performance logging
    WithTimer() TimerLogger
    
    // Structured fields
    With(key string, value interface{}) Logger
}

type Field struct {
    Key   string
    Value interface{}
}

type TimerLogger interface {
    Logger
    Stop() // Logs duration when stopped
}
```

### 2. Logger Implementation with Zap

```go
package logging

import (
    "context"
    "time"
    
    "go.uber.org/zap"
    "go.uber.org/zap/zapcore"
)

type ZapLogger struct {
    logger *zap.Logger
    sugar  *zap.SugaredLogger
    config *LogConfig
}

func NewLogger(config *LogConfig) (*ZapLogger, error) {
    zapConfig := buildZapConfig(config)
    
    logger, err := zapConfig.Build(
        zap.AddCaller(),
        zap.AddCallerSkip(1),
        zap.AddStacktrace(zapcore.ErrorLevel),
    )
    if err != nil {
        return nil, err
    }
    
    // Add hooks based on mode
    if config.Mode == ModeProduction {
        logger = logger.WithOptions(
            zap.Hooks(sanitizeHook, complianceHook),
        )
    }
    
    return &ZapLogger{
        logger: logger,
        sugar:  logger.Sugar(),
        config: config,
    }, nil
}

func (l *ZapLogger) Info(msg string, fields ...Field) {
    zapFields := convertFields(fields)
    l.logger.Info(msg, zapFields...)
}

func (l *ZapLogger) WithContext(ctx context.Context) Logger {
    // Extract context values
    fields := []zap.Field{}
    
    if requestID := ctx.Value("request_id"); requestID != nil {
        fields = append(fields, zap.String("request_id", requestID.(string)))
    }
    
    if userID := ctx.Value("user_id"); userID != nil {
        fields = append(fields, zap.Uint("user_id", userID.(uint)))
    }
    
    if tenantID := ctx.Value("tenant_id"); tenantID != nil {
        fields = append(fields, zap.Uint("tenant_id", tenantID.(uint)))
    }
    
    return &ZapLogger{
        logger: l.logger.With(fields...),
        sugar:  l.sugar,
        config: l.config,
    }
}

func (l *ZapLogger) WithTimer() TimerLogger {
    start := time.Now()
    return &timerLogger{
        ZapLogger: l,
        start:     start,
    }
}

type timerLogger struct {
    *ZapLogger
    start time.Time
}

func (t *timerLogger) Stop() {
    duration := time.Since(t.start)
    t.logger.Info("operation completed",
        zap.Duration("duration", duration),
        zap.Int64("duration_ms", duration.Milliseconds()),
    )
}
```

### 3. Log Format Examples

#### Development Mode (Human-readable)
```
2024-07-15T10:30:45.123+07:00 [INFO] server started successfully
    service=blog-api
    version=3.0.0
    port=9077
    environment=development
    
2024-07-15T10:30:46.456+07:00 [DEBUG] database connection established
    driver=mysql
    host=localhost
    database=blog_api_dev
    pool_size=10
    caller=database/connection.go:45
```

#### Production Mode (JSON)
```json
{
  "timestamp": "2024-07-15T03:30:45.123Z",
  "level": "info",
  "service": "blog-api",
  "version": "3.0.0",
  "environment": "production",
  "hostname": "api-server-01",
  "message": "server started successfully",
  "port": 9077,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "trace_id": "4bf92f3577b34da6a3ce929d0e0e4736"
}
```

## Logging Categories

### 1. Application Logs

```go
// Business logic logging
func (s *PostService) CreatePost(ctx context.Context, post *Post) error {
    logger := s.logger.WithContext(ctx)
    timer := logger.WithTimer()
    defer timer.Stop()
    
    logger.Info("creating new post",
        zap.String("title", post.Title),
        zap.Uint("author_id", post.AuthorID),
        zap.String("status", post.Status),
    )
    
    // Validate
    if err := s.validator.Validate(post); err != nil {
        logger.Warn("post validation failed",
            zap.Error(err),
            zap.Any("validation_errors", err.Details()),
        )
        return err
    }
    
    // Create
    if err := s.repo.Create(ctx, post); err != nil {
        logger.Error("failed to create post",
            zap.Error(err),
            zap.String("error_type", fmt.Sprintf("%T", err)),
        )
        return err
    }
    
    logger.Info("post created successfully",
        zap.Uint("post_id", post.ID),
        zap.String("slug", post.Slug),
    )
    
    return nil
}
```

### 2. HTTP Access Logs

```go
func AccessLogMiddleware(logger Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        path := c.Request.URL.Path
        raw := c.Request.URL.RawQuery
        
        // Process request
        c.Next()
        
        // Log after processing
        latency := time.Since(start)
        clientIP := c.ClientIP()
        method := c.Request.Method
        statusCode := c.Writer.Status()
        errorMessage := c.Errors.ByType(gin.ErrorTypePrivate).String()
        
        fields := []zap.Field{
            zap.String("method", method),
            zap.String("path", path),
            zap.String("query", raw),
            zap.Int("status", statusCode),
            zap.String("ip", clientIP),
            zap.Duration("latency", latency),
            zap.Int64("latency_ms", latency.Milliseconds()),
            zap.String("user_agent", c.Request.UserAgent()),
            zap.Int("body_size", c.Writer.Size()),
        }
        
        // Add user context if authenticated
        if userID, exists := c.Get("user_id"); exists {
            fields = append(fields, zap.Uint("user_id", userID.(uint)))
        }
        
        // Log based on status code
        switch {
        case statusCode >= 500:
            logger.Error("server error", append(fields, zap.String("error", errorMessage))...)
        case statusCode >= 400:
            logger.Warn("client error", fields...)
        case statusCode >= 300:
            logger.Info("redirection", fields...)
        default:
            logger.Info("request completed", fields...)
        }
    }
}
```

### 3. Error Logs

```go
type ErrorLogger struct {
    logger Logger
}

func (e *ErrorLogger) LogError(ctx context.Context, err error, msg string) {
    logger := e.logger.WithContext(ctx)
    
    // Extract error details
    fields := []zap.Field{
        zap.Error(err),
        zap.String("error_type", fmt.Sprintf("%T", err)),
    }
    
    // Add stack trace for internal errors
    if stackErr, ok := err.(interface{ StackTrace() string }); ok {
        fields = append(fields, zap.String("stack_trace", stackErr.StackTrace()))
    }
    
    // Add context from error
    if ctxErr, ok := err.(interface{ Context() map[string]interface{} }); ok {
        fields = append(fields, zap.Any("error_context", ctxErr.Context()))
    }
    
    // Log based on error type
    switch err.(type) {
    case *ValidationError:
        logger.Warn(msg, fields...)
    case *NotFoundError:
        logger.Info(msg, fields...)
    case *AuthError:
        logger.Warn(msg, fields...)
    default:
        logger.Error(msg, fields...)
    }
}

// Panic recovery with logging
func RecoverMiddleware(logger Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        defer func() {
            if err := recover(); err != nil {
                logger.Panic("panic recovered",
                    zap.Any("error", err),
                    zap.String("path", c.Request.URL.Path),
                    zap.String("method", c.Request.Method),
                    zap.Stack("stack"),
                )
                
                c.AbortWithStatusJSON(500, gin.H{
                    "error": "Internal server error",
                })
            }
        }()
        
        c.Next()
    }
}
```

### 4. Audit Logs

```go
type AuditLogger struct {
    logger   Logger
    storage  AuditStorage
}

type AuditEvent struct {
    ID          string    `json:"id"`
    Timestamp   time.Time `json:"timestamp"`
    UserID      uint      `json:"user_id"`
    TenantID    uint      `json:"tenant_id"`
    Action      string    `json:"action"`
    Resource    string    `json:"resource"`
    ResourceID  string    `json:"resource_id"`
    OldValue    JSON      `json:"old_value,omitempty"`
    NewValue    JSON      `json:"new_value,omitempty"`
    IPAddress   string    `json:"ip_address"`
    UserAgent   string    `json:"user_agent"`
    Result      string    `json:"result"`
    Metadata    JSON      `json:"metadata,omitempty"`
}

func (a *AuditLogger) LogDataChange(ctx context.Context, event *AuditEvent) {
    // Log to structured logger
    a.logger.Info("audit event",
        zap.String("audit_id", event.ID),
        zap.String("action", event.Action),
        zap.String("resource", event.Resource),
        zap.String("resource_id", event.ResourceID),
        zap.Uint("user_id", event.UserID),
        zap.String("result", event.Result),
    )
    
    // Store in audit storage (database, immutable storage)
    if err := a.storage.Store(ctx, event); err != nil {
        a.logger.Error("failed to store audit event",
            zap.Error(err),
            zap.String("audit_id", event.ID),
        )
    }
}

// Example usage
func (s *UserService) UpdateProfile(ctx context.Context, userID uint, updates *ProfileUpdate) error {
    oldProfile, _ := s.GetProfile(ctx, userID)
    
    // Perform update
    err := s.repo.UpdateProfile(ctx, userID, updates)
    
    // Audit log
    s.auditLogger.LogDataChange(ctx, &AuditEvent{
        ID:         uuid.New().String(),
        Timestamp:  time.Now(),
        UserID:     getCurrentUserID(ctx),
        Action:     "profile.update",
        Resource:   "user_profile",
        ResourceID: fmt.Sprintf("%d", userID),
        OldValue:   toJSON(oldProfile),
        NewValue:   toJSON(updates),
        IPAddress:  getClientIP(ctx),
        Result:     ternary(err == nil, "success", "failure"),
    })
    
    return err
}
```

### 5. Security Logs

```go
type SecurityLogger struct {
    logger    Logger
    alerting  AlertingService
}

func (s *SecurityLogger) LogSecurityEvent(event *SecurityEvent) {
    fields := []zap.Field{
        zap.String("event_type", event.Type),
        zap.String("severity", event.Severity),
        zap.String("source_ip", event.SourceIP),
        zap.Uint("user_id", event.UserID),
        zap.Any("details", event.Details),
    }
    
    switch event.Severity {
    case "critical":
        s.logger.Error("critical security event", fields...)
        s.alerting.SendAlert(event)
    case "high":
        s.logger.Warn("high severity security event", fields...)
    default:
        s.logger.Info("security event", fields...)
    }
}

// Authentication logging
func (s *SecurityLogger) LogAuthEvent(ctx context.Context, event string, success bool, details map[string]interface{}) {
    logger := s.logger.WithContext(ctx)
    
    fields := []zap.Field{
        zap.String("auth_event", event),
        zap.Bool("success", success),
        zap.String("ip_address", getClientIP(ctx)),
        zap.Any("details", details),
    }
    
    if success {
        logger.Info("authentication event", fields...)
    } else {
        logger.Warn("authentication failure", fields...)
        
        // Check for brute force
        if s.detectBruteForce(ctx, event) {
            logger.Error("possible brute force attack detected", fields...)
            s.alerting.SendSecurityAlert("brute_force", details)
        }
    }
}
```

## Log Processing & Analysis

### 1. Log Pipeline Architecture

```mermaid
flowchart LR
    A[Log Sources] --> B[Log Shipper]
    B --> C[Message Queue]
    C --> D[Log Processor]
    D --> E[Log Router]
    E --> F[Storage]
    E --> G[Real-time Analysis]
    E --> H[Alerting]
    
    F --> F1[Elasticsearch]
    F --> F2[S3/Object Storage]
    F --> F3[Time Series DB]
    
    G --> G1[Kibana]
    G --> G2[Grafana]
    G --> G3[Custom Dashboard]
    
    H --> H1[PagerDuty]
    H --> H2[Slack]
    H --> H3[Email]
```

### 2. Log Shipper Configuration

```yaml
# Filebeat configuration
filebeat:
  inputs:
    - type: log
      enabled: true
      paths:
        - /var/log/blog-api/*.log
      multiline:
        pattern: '^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
      processors:
        - add_host_metadata:
            when.not.contains.tags: forwarded
        - add_docker_metadata: ~
        - add_kubernetes_metadata: ~
        
  output:
    kafka:
      hosts: ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
      topic: "application-logs"
      partition.round_robin:
        reachable_only: true
      compression: gzip
      max_message_bytes: 1000000
```

### 3. Log Processing with Logstash

```ruby
# Logstash pipeline
input {
  kafka {
    bootstrap_servers => "kafka1:9092,kafka2:9092,kafka3:9092"
    topics => ["application-logs"]
    consumer_threads => 4
    codec => json
  }
}

filter {
  # Parse JSON logs
  json {
    source => "message"
  }
  
  # Extract fields
  mutate {
    add_field => {
      "[@metadata][target_index]" => "logs-%{[service]}-%{+YYYY.MM.dd}"
    }
  }
  
  # GeoIP enrichment
  if [ip_address] {
    geoip {
      source => "ip_address"
      target => "geoip"
    }
  }
  
  # Anonymize sensitive data in production
  if [environment] == "production" {
    mutate {
      gsub => [
        "email", "[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}", "***@***.***",
        "credit_card", "\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}", "****-****-****-****"
      ]
    }
  }
  
  # Add processing timestamp
  ruby {
    code => "event.set('processed_at', Time.now.utc.iso8601)"
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["es1:9200", "es2:9200", "es3:9200"]
    index => "%{[@metadata][target_index]}"
    template_name => "application-logs"
    template => "/etc/logstash/templates/logs.json"
  }
  
  # Send errors to dedicated index
  if [level] == "error" or [level] == "fatal" {
    elasticsearch {
      hosts => ["es1:9200", "es2:9200", "es3:9200"]
      index => "errors-%{+YYYY.MM.dd}"
    }
  }
  
  # Real-time alerts
  if [level] == "fatal" or [alert] == "true" {
    http {
      url => "http://alerting-service/webhook"
      http_method => "post"
      format => "json"
    }
  }
}
```

## Debug Mode Features

### 1. Debug Mode Configuration

```go
type DebugConfig struct {
    Enabled              bool
    VerboseSQL           bool
    RequestBodyLogging   bool
    ResponseBodyLogging  bool
    SlowQueryThreshold   time.Duration
    MemoryProfiling      bool
    CPUProfiling         bool
    TraceSampling        float64
}

func SetupDebugMode(app *Application, config *DebugConfig) {
    if !config.Enabled {
        return
    }
    
    // Enable verbose SQL logging
    if config.VerboseSQL {
        app.DB.LogMode(logger.Info)
        app.DB.Debug()
    }
    
    // Enable request/response logging
    if config.RequestBodyLogging {
        app.Use(RequestBodyLogger())
    }
    
    // Enable profiling endpoints
    if config.MemoryProfiling || config.CPUProfiling {
        SetupProfilingEndpoints(app, config)
    }
    
    // Enable debug endpoints
    SetupDebugEndpoints(app)
}
```

### 2. Debug Endpoints

```go
func SetupDebugEndpoints(app *Application) {
    debug := app.Group("/debug")
    debug.Use(DebugAuthMiddleware())
    
    // Goroutine dump
    debug.GET("/goroutines", func(c *gin.Context) {
        buf := make([]byte, 1<<20)
        stackLen := runtime.Stack(buf, true)
        c.Data(200, "text/plain", buf[:stackLen])
    })
    
    // Memory stats
    debug.GET("/memstats", func(c *gin.Context) {
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        c.JSON(200, m)
    })
    
    // Configuration dump
    debug.GET("/config", func(c *gin.Context) {
        c.JSON(200, app.Config.Sanitized())
    })
    
    // Database stats
    debug.GET("/db/stats", func(c *gin.Context) {
        stats := app.DB.Stats()
        c.JSON(200, stats)
    })
    
    // Cache stats
    debug.GET("/cache/stats", func(c *gin.Context) {
        c.JSON(200, app.Cache.Stats())
    })
    
    // Active connections
    debug.GET("/connections", func(c *gin.Context) {
        c.JSON(200, gin.H{
            "http_connections": app.GetHTTPConnections(),
            "websocket_connections": app.GetWebSocketConnections(),
            "database_connections": app.DB.Stats(),
        })
    })
}
```

### 3. Request Tracing

```go
type RequestTracer struct {
    logger Logger
}

func (t *RequestTracer) TraceRequest(c *gin.Context) {
    traceID := uuid.New().String()
    c.Set("trace_id", traceID)
    
    // Create trace logger
    traceLogger := t.logger.With(
        zap.String("trace_id", traceID),
        zap.String("method", c.Request.Method),
        zap.String("path", c.Request.URL.Path),
    )
    
    // Log request details
    traceLogger.Debug("request started",
        zap.Any("headers", c.Request.Header),
        zap.String("ip", c.ClientIP()),
        zap.String("user_agent", c.Request.UserAgent()),
    )
    
    // Capture request body if enabled
    if body, err := c.GetRawData(); err == nil && len(body) > 0 {
        c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
        traceLogger.Debug("request body",
            zap.ByteString("body", body),
            zap.Int("size", len(body)),
        )
    }
    
    // Continue processing
    c.Next()
    
    // Log response
    traceLogger.Debug("request completed",
        zap.Int("status", c.Writer.Status()),
        zap.Int("size", c.Writer.Size()),
        zap.Duration("duration", time.Since(start)),
    )
}
```

## Production Mode Optimization

### 1. Production Configuration

```go
type ProductionLogConfig struct {
    // Sampling
    SamplingRate      float64 // 0.1 = 10% of logs
    ErrorSamplingRate float64 // 1.0 = 100% of errors
    
    // Filtering
    MinLevel      LogLevel
    ExcludePaths  []string // Health checks, metrics
    
    // Performance
    AsyncLogging  bool
    BatchSize     int
    FlushInterval time.Duration
    
    // Security
    SanitizeFields []string
    MaskPatterns   []string
}

func NewProductionLogger(config *ProductionLogConfig) Logger {
    // Create sampled logger
    samplerConfig := &zap.SamplingConfig{
        Initial:    100,
        Thereafter: int(1 / config.SamplingRate),
    }
    
    // Build production config
    zapConfig := zap.NewProductionConfig()
    zapConfig.Level = zap.NewAtomicLevelAt(convertLogLevel(config.MinLevel))
    zapConfig.Sampling = samplerConfig
    
    // Async wrapper for performance
    if config.AsyncLogging {
        return NewAsyncLogger(logger, config.BatchSize, config.FlushInterval)
    }
    
    return logger
}
```

### 2. Async Logging

```go
type AsyncLogger struct {
    logger    Logger
    buffer    chan *LogEntry
    batchSize int
    interval  time.Duration
    done      chan struct{}
}

func NewAsyncLogger(logger Logger, batchSize int, interval time.Duration) *AsyncLogger {
    a := &AsyncLogger{
        logger:    logger,
        buffer:    make(chan *LogEntry, batchSize*10),
        batchSize: batchSize,
        interval:  interval,
        done:      make(chan struct{}),
    }
    
    go a.processor()
    return a
}

func (a *AsyncLogger) processor() {
    batch := make([]*LogEntry, 0, a.batchSize)
    ticker := time.NewTicker(a.interval)
    defer ticker.Stop()
    
    for {
        select {
        case entry := <-a.buffer:
            batch = append(batch, entry)
            if len(batch) >= a.batchSize {
                a.flush(batch)
                batch = batch[:0]
            }
            
        case <-ticker.C:
            if len(batch) > 0 {
                a.flush(batch)
                batch = batch[:0]
            }
            
        case <-a.done:
            // Flush remaining logs
            if len(batch) > 0 {
                a.flush(batch)
            }
            return
        }
    }
}

func (a *AsyncLogger) Info(msg string, fields ...Field) {
    select {
    case a.buffer <- &LogEntry{
        Level:   INFO,
        Message: msg,
        Fields:  fields,
        Time:    time.Now(),
    }:
    default:
        // Buffer full, drop log or handle overflow
        a.handleOverflow()
    }
}
```

### 3. Log Sanitization

```go
type Sanitizer struct {
    patterns map[string]*regexp.Regexp
    fields   []string
}

func NewSanitizer(config *SanitizerConfig) *Sanitizer {
    s := &Sanitizer{
        patterns: make(map[string]*regexp.Regexp),
        fields:   config.SensitiveFields,
    }
    
    // Compile patterns
    s.patterns["email"] = regexp.MustCompile(`[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}`)
    s.patterns["credit_card"] = regexp.MustCompile(`\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}`)
    s.patterns["ssn"] = regexp.MustCompile(`\d{3}-\d{2}-\d{4}`)
    s.patterns["api_key"] = regexp.MustCompile(`[A-Za-z0-9]{32,}`)
    
    return s
}

func (s *Sanitizer) Sanitize(fields []Field) []Field {
    sanitized := make([]Field, len(fields))
    copy(sanitized, fields)
    
    for i, field := range sanitized {
        // Check if field should be sanitized
        if s.shouldSanitize(field.Key) {
            sanitized[i].Value = s.mask(field.Value)
            continue
        }
        
        // Check patterns
        if str, ok := field.Value.(string); ok {
            for name, pattern := range s.patterns {
                if pattern.MatchString(str) {
                    sanitized[i].Value = pattern.ReplaceAllString(str, s.getMask(name))
                }
            }
        }
    }
    
    return sanitized
}
```

## Log Storage & Retention

### 1. Storage Strategy

```yaml
storage:
  hot_storage:
    duration: 7d
    storage_class: ssd
    replication: 2
    index_optimization: true
    
  warm_storage:
    duration: 30d
    storage_class: standard
    compression: true
    replication: 1
    
  cold_storage:
    duration: 365d
    storage_class: archive
    compression: maximum
    access_tier: infrequent
    
  retention_policies:
    - type: application_logs
      hot: 7d
      warm: 30d
      cold: 90d
      delete: 365d
      
    - type: audit_logs
      hot: 30d
      warm: 90d
      cold: 365d
      archive: 7y  # Compliance requirement
      
    - type: security_logs
      hot: 30d
      warm: 90d
      cold: 365d
      delete: never
```

### 2. Log Rotation

```go
type LogRotator struct {
    maxSize    int64
    maxAge     time.Duration
    maxBackups int
    compress   bool
}

func (r *LogRotator) Rotate(logPath string) error {
    info, err := os.Stat(logPath)
    if err != nil {
        return err
    }
    
    // Check size
    if info.Size() > r.maxSize {
        return r.rotateFile(logPath)
    }
    
    // Check age
    if time.Since(info.ModTime()) > r.maxAge {
        return r.rotateFile(logPath)
    }
    
    return nil
}

func (r *LogRotator) rotateFile(logPath string) error {
    // Generate backup name
    backupPath := fmt.Sprintf("%s.%s", logPath, time.Now().Format("20060102-150405"))
    
    // Rename current file
    if err := os.Rename(logPath, backupPath); err != nil {
        return err
    }
    
    // Compress if enabled
    if r.compress {
        if err := r.compressFile(backupPath); err != nil {
            return err
        }
    }
    
    // Clean old backups
    return r.cleanOldBackups(logPath)
}
```

## Monitoring & Alerting

### 1. Log Metrics

```go
type LogMetrics struct {
    TotalLogs     prometheus.CounterVec
    LogSize       prometheus.HistogramVec
    ErrorRate     prometheus.GaugeVec
    LogLatency    prometheus.HistogramVec
}

func NewLogMetrics() *LogMetrics {
    return &LogMetrics{
        TotalLogs: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "logs_total",
                Help: "Total number of logs",
            },
            []string{"level", "service", "component"},
        ),
        
        ErrorRate: prometheus.NewGaugeVec(
            prometheus.GaugeOpts{
                Name: "log_error_rate",
                Help: "Error log rate per second",
            },
            []string{"service", "error_type"},
        ),
    }
}
```

### 2. Alert Rules

```yaml
alerts:
  - name: high_error_rate
    condition: rate(logs_total{level="error"}[5m]) > 10
    severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors/sec"
      
  - name: disk_space_low
    condition: log_storage_available_bytes < 10737418240  # 10GB
    severity: critical
    annotations:
      summary: "Log storage space low"
      description: "Only {{ $value | humanize }} remaining"
      
  - name: log_processing_lag
    condition: log_processing_lag_seconds > 300
    severity: warning
    annotations:
      summary: "Log processing lagging"
      description: "Processing lag is {{ $value }} seconds"
```

## Best Practices

### 1. Logging Guidelines

```yaml
guidelines:
  do:
    - Use structured logging với fields
    - Include request_id trong mọi log
    - Log ở appropriate level
    - Include context (user_id, tenant_id)
    - Use timer cho performance critical operations
    - Sanitize sensitive data
    
  dont:
    - Log passwords hoặc secrets
    - Log toàn bộ request/response bodies
    - Use string concatenation cho log messages
    - Log trong tight loops
    - Ignore error logging
    - Use fmt.Printf trong production
```

### 2. Performance Considerations

```go
// Good - structured logging
logger.Info("user login successful",
    zap.Uint("user_id", userID),
    zap.String("ip", clientIP),
    zap.Duration("auth_time", authDuration),
)

// Bad - string formatting
log.Printf("User %d logged in from %s in %v", userID, clientIP, authDuration)

// Good - conditional debug logging
if logger.Core().Enabled(zapcore.DebugLevel) {
    logger.Debug("detailed debug info",
        zap.Any("complex_object", expensiveToCompute()),
    )
}

// Bad - always compute
logger.Debug(fmt.Sprintf("Debug: %v", expensiveToCompute()))
```

## Security Considerations

### 1. Sensitive Data Protection

```go
var SensitiveFields = []string{
    "password",
    "api_key",
    "secret",
    "token",
    "credit_card",
    "ssn",
    "private_key",
}

func (l *SecureLogger) Log(level LogLevel, msg string, fields ...Field) {
    // Filter sensitive fields
    filtered := make([]Field, 0, len(fields))
    for _, field := range fields {
        if !isSensitive(field.Key) {
            filtered = append(filtered, field)
        } else {
            filtered = append(filtered, Field{
                Key:   field.Key,
                Value: "[REDACTED]",
            })
        }
    }
    
    l.logger.Log(level, msg, filtered...)
}
```

### 2. Compliance & Audit

```yaml
compliance:
  gdpr:
    personal_data_retention: 90d
    anonymization_required: true
    right_to_erasure: true
    
  pci_dss:
    credit_card_masking: true
    secure_transmission: true
    access_control: true
    
  hipaa:
    phi_encryption: true
    audit_trail: true
    minimum_retention: 6y
```

## Tài liệu liên quan

- [Monitoring & Metrics](./monitoring.md)
- [Security Best Practices](../best-practices/security.md)
- [Performance Optimization](../best-practices/performance.md)
- [Error Handling](../api/error-handling.md)
- [Development Environment](../development/environment-setup.md)
- [Production Deployment](../deployment/production.md)