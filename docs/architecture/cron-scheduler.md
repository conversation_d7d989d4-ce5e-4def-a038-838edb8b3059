# Hệ thống Cron và Scheduler - Tài liệu Tiếng Việt

## Tổng quan

Hệ thống Cron và Scheduler cung cấp khả năng thực thi các tác vụ định kỳ và lên lịch trong Blog API v3, bao gồm backup dữ liệu, g<PERSON>i email định kỳ, cleanup storage, và các tác vụ bảo trì hệ thống.

## Mục tiêu

- **Automated Tasks**: Tự động hóa các tác vụ định kỳ
- **Reliable Scheduling**: Lên lịch đáng tin cậy với failover
- **Flexible Timing**: Hỗ trợ multiple scheduling patterns
- **Distributed Execution**: Thực thi phân tán trên nhiều node
- **Monitoring**: <PERSON> dõi và báo cáo trạng thái jobs
- **Error Handling**: <PERSON>ử lý lỗi và retry logic

## Kiến trú<PERSON> hệ thống

### Scheduler Architecture

```mermaid
flowchart TD
    A[Scheduler Engine] --> B[Job Registry]
    A --> C[Cron Parser]
    A --> D[Execution Engine]
    
    B --> E[Job Definitions]
    B --> F[Job History]
    B --> G[Job Status]
    
    C --> H[Cron Expressions]
    C --> I[Timezone Handling]
    
    D --> J[Worker Pool]
    D --> K[Distributed Lock]
    D --> L[Job Queue]
    
    J --> M[Job Executor 1]
    J --> N[Job Executor 2]
    J --> O[Job Executor 3]
    
    P[Monitoring] --> A
    Q[Alerting] --> A
```

### Components

#### Scheduler Engine
- **Job Scheduling**: Lên lịch và trigger jobs
- **Cron Expression Parser**: Parse cron expressions
- **Timezone Support**: Hỗ trợ multiple timezones
- **Distributed Coordination**: Coordination giữa multiple instances

#### Job Registry
- **Job Definition Storage**: Lưu trữ định nghĩa jobs
- **Job History**: Lịch sử thực thi
- **Job Status Tracking**: Theo dõi trạng thái jobs

#### Execution Engine
- **Worker Management**: Quản lý worker pool
- **Load Balancing**: Phân tải jobs
- **Failure Handling**: Xử lý lỗi và retry

## Job Definition Structure

### Job Model

```go
type ScheduledJob struct {
    // Identity
    ID          string    `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    TenantID    string    `json:"tenant_id"`
    
    // Scheduling
    CronExpr    string    `json:"cron_expr"`
    Timezone    string    `json:"timezone"`
    StartTime   *time.Time `json:"start_time"`
    EndTime     *time.Time `json:"end_time"`
    
    // Execution
    Handler     string    `json:"handler"`
    Payload     interface{} `json:"payload"`
    Timeout     time.Duration `json:"timeout"`
    MaxRetries  int       `json:"max_retries"`
    
    // Status
    Status      string    `json:"status"` // enabled, disabled, paused
    LastRun     *time.Time `json:"last_run"`
    NextRun     *time.Time `json:"next_run"`
    
    // Metadata
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    CreatedBy   string    `json:"created_by"`
}
```

### Job Execution Record

```go
type JobExecution struct {
    ID          string        `json:"id"`
    JobID       string        `json:"job_id"`
    Status      string        `json:"status"` // running, completed, failed
    StartedAt   time.Time     `json:"started_at"`
    CompletedAt *time.Time    `json:"completed_at"`
    Duration    time.Duration `json:"duration"`
    Output      string        `json:"output"`
    Error       string        `json:"error"`
    RetryCount  int          `json:"retry_count"`
    NodeID      string        `json:"node_id"`
}
```

## Cron Expression Support

### Standard Cron Format

```yaml
# Format: second minute hour day month weekday
# Fields:  0-59   0-59   0-23 1-31 1-12  0-7 (0 or 7 is Sunday)

examples:
  every_minute: "0 * * * * *"
  every_hour: "0 0 * * * *"
  daily_at_midnight: "0 0 0 * * *"
  daily_at_2am: "0 0 2 * * *"
  weekly_sunday_3am: "0 0 3 * * 0"
  monthly_first_day: "0 0 0 1 * *"
  yearly_jan_first: "0 0 0 1 1 *"
  
  # Business hours (Mon-Fri, 9AM-5PM)
  business_hours: "0 0 9-17 * * 1-5"
  
  # Every 15 minutes during business hours
  frequent_business: "0 */15 9-17 * * 1-5"
  
  # Last day of month at midnight
  month_end: "0 0 0 L * *"
```

### Extended Cron Features

```yaml
# Custom extensions
extended_features:
  # Human readable
  human_readable:
    - "@yearly"     # "0 0 0 1 1 *"
    - "@annually"   # "0 0 0 1 1 *"
    - "@monthly"    # "0 0 0 1 * *"
    - "@weekly"     # "0 0 0 * * 0"
    - "@daily"      # "0 0 0 * * *"
    - "@midnight"   # "0 0 0 * * *"
    - "@hourly"     # "0 0 * * * *"
    
  # Interval based
  intervals:
    - "@every 30s"
    - "@every 5m"
    - "@every 1h"
    - "@every 24h"
    
  # Special expressions
  special:
    - "TZ=Asia/Ho_Chi_Minh 0 0 9 * * *"  # Timezone specific
    - "RANDOM(0-59) 0 * * * *"           # Random minute
```

## Scheduler Implementation

### Cron Engine

```mermaid
sequenceDiagram
    participant Scheduler as Scheduler Engine
    participant Parser as Cron Parser
    participant DB as Database
    participant Lock as Distributed Lock
    participant Executor as Job Executor
    participant Monitor as Monitoring
    
    loop Every Minute
        Scheduler->>DB: Get due jobs
        DB->>Scheduler: Return job list
        
        loop For each job
            Scheduler->>Parser: Parse cron expression
            Parser->>Scheduler: Next execution time
            
            Scheduler->>Lock: Acquire job lock
            Lock->>Scheduler: Lock acquired
            
            Scheduler->>Executor: Execute job
            Executor->>Monitor: Log execution start
            
            Executor->>Executor: Run job handler
            
            alt Success
                Executor->>DB: Update success status
                Executor->>Monitor: Log success
            else Failure
                Executor->>DB: Update failure status
                Executor->>Monitor: Log failure
                
                alt Retry needed
                    Executor->>Scheduler: Schedule retry
                end
            end
            
            Scheduler->>Lock: Release job lock
        end
    end
```

### Distributed Coordination

```mermaid
flowchart TD
    A[Scheduler Node 1] --> B[Distributed Lock Manager]
    C[Scheduler Node 2] --> B
    D[Scheduler Node 3] --> B
    
    B --> E[Redis Lock]
    B --> F[Database Lock]
    B --> G[Etcd Lock]
    
    H[Job Due] --> I{Lock Available?}
    I -->|Yes| J[Acquire Lock]
    I -->|No| K[Skip Execution]
    
    J --> L[Execute Job]
    L --> M[Release Lock]
    
    K --> N[Log Skip Event]
```

## Predefined Jobs

### 1. Database Maintenance Jobs

```yaml
# Database cleanup job
database_cleanup:
  name: "Database Cleanup"
  cron: "0 0 2 * * *"  # Daily at 2 AM
  handler: "database.cleanup"
  payload:
    tables:
      - name: "job_executions"
        retention_days: 30
      - name: "audit_logs"
        retention_days: 90
      - name: "temp_files"
        retention_days: 7
        
# Database backup job
database_backup:
  name: "Database Backup"
  cron: "0 0 1 * * *"  # Daily at 1 AM
  handler: "database.backup"
  payload:
    backup_type: "incremental"
    storage: "s3"
    encryption: true
    retention_days: 30
```

### 2. Media Cleanup Jobs

```yaml
# Orphaned media cleanup
media_cleanup:
  name: "Media Cleanup"
  cron: "0 0 3 * * 0"  # Weekly on Sunday at 3 AM
  handler: "media.cleanup_orphaned"
  payload:
    age_threshold_days: 7
    batch_size: 1000
    
# Thumbnail regeneration
thumbnail_regen:
  name: "Thumbnail Regeneration"
  cron: "0 0 4 1 * *"  # Monthly on 1st at 4 AM
  handler: "media.regenerate_thumbnails"
  payload:
    force_regenerate: false
    size_variants: ["small", "medium", "large"]
```

### 3. Notification Jobs

```yaml
# Digest email sending
email_digest:
  name: "Weekly Email Digest"
  cron: "0 0 9 * * 1"  # Monday at 9 AM
  handler: "notification.send_digest"
  payload:
    digest_type: "weekly"
    template: "weekly_digest"
    
# Push notification cleanup
push_cleanup:
  name: "Push Notification Cleanup"
  cron: "0 30 1 * * *"  # Daily at 1:30 AM
  handler: "notification.cleanup_delivered"
  payload:
    retention_days: 30
```

### 4. Analytics Jobs

```yaml
# Daily analytics aggregation
analytics_daily:
  name: "Daily Analytics"
  cron: "0 15 0 * * *"  # Daily at 12:15 AM
  handler: "analytics.aggregate_daily"
  payload:
    metrics: ["page_views", "user_sessions", "post_engagement"]
    
# Monthly reports
monthly_reports:
  name: "Monthly Reports"
  cron: "0 0 6 1 * *"  # 1st of month at 6 AM
  handler: "analytics.generate_monthly_report"
  payload:
    report_types: ["traffic", "content", "user_growth"]
    send_email: true
```

### 5. Security Jobs

```yaml
# Security audit
security_audit:
  name: "Security Audit"
  cron: "0 0 5 * * *"  # Daily at 5 AM
  handler: "security.audit"
  payload:
    checks: ["failed_logins", "suspicious_activities", "permission_changes"]
    
# SSL certificate check
ssl_check:
  name: "SSL Certificate Check"
  cron: "0 0 8 * * 1"  # Weekly on Monday at 8 AM
  handler: "security.check_ssl_certificates"
  payload:
    alert_days_before_expiry: 30
```

## Job Handlers

### Handler Interface

```go
type JobHandler interface {
    Name() string
    Execute(ctx context.Context, payload interface{}) (*JobResult, error)
    Validate(payload interface{}) error
}

type JobResult struct {
    Success bool
    Output  string
    Data    map[string]interface{}
    Metrics map[string]float64
}
```

### Example Handler Implementation

```go
// Database cleanup handler
type DatabaseCleanupHandler struct {
    db     *gorm.DB
    logger *zap.Logger
}

func (h *DatabaseCleanupHandler) Name() string {
    return "database.cleanup"
}

func (h *DatabaseCleanupHandler) Execute(ctx context.Context, payload interface{}) (*JobResult, error) {
    config := payload.(DatabaseCleanupConfig)
    
    result := &JobResult{
        Success: true,
        Data:    make(map[string]interface{}),
        Metrics: make(map[string]float64),
    }
    
    totalCleaned := 0
    
    for _, table := range config.Tables {
        cleanedRows, err := h.cleanupTable(table)
        if err != nil {
            result.Success = false
            return result, err
        }
        
        totalCleaned += cleanedRows
        result.Data[table.Name] = cleanedRows
    }
    
    result.Output = fmt.Sprintf("Cleaned %d rows from %d tables", 
        totalCleaned, len(config.Tables))
    result.Metrics["rows_cleaned"] = float64(totalCleaned)
    
    return result, nil
}

func (h *DatabaseCleanupHandler) cleanupTable(config TableCleanupConfig) (int, error) {
    cutoffDate := time.Now().AddDate(0, 0, -config.RetentionDays)
    
    result := h.db.Where("created_at < ?", cutoffDate).
        Delete(&config.Name)
    
    return int(result.RowsAffected), result.Error
}
```

## Job Monitoring

### Execution Tracking

```mermaid
flowchart TD
    A[Job Execution Start] --> B[Create Execution Record]
    B --> C[Update Status: RUNNING]
    C --> D[Execute Job Handler]
    
    D --> E{Execution Result?}
    E -->|Success| F[Update Status: COMPLETED]
    E -->|Error| G[Update Status: FAILED]
    
    F --> H[Record Success Metrics]
    G --> I[Record Error Details]
    
    H --> J[Check Next Schedule]
    I --> K{Retry Needed?}
    
    K -->|Yes| L[Schedule Retry]
    K -->|No| M[Send Alert]
    
    J --> N[Update Next Run Time]
    L --> N
    M --> N
```

### Health Monitoring

```go
type JobHealthStatus struct {
    JobID           string        `json:"job_id"`
    JobName         string        `json:"job_name"`
    Status          string        `json:"status"`
    LastExecution   *time.Time    `json:"last_execution"`
    NextExecution   *time.Time    `json:"next_execution"`
    SuccessRate     float64       `json:"success_rate"`
    AverageRuntime  time.Duration `json:"average_runtime"`
    ConsecutiveFails int          `json:"consecutive_fails"`
    IsHealthy       bool          `json:"is_healthy"`
}

func (s *Scheduler) GetJobHealth(jobID string) *JobHealthStatus {
    // Get recent executions
    executions := s.getRecentExecutions(jobID, 10)
    
    // Calculate metrics
    successCount := 0
    totalRuntime := time.Duration(0)
    consecutiveFails := 0
    
    for i, exec := range executions {
        if exec.Status == "completed" {
            successCount++
            totalRuntime += exec.Duration
            consecutiveFails = 0
        } else if exec.Status == "failed" {
            if i == 0 { // Most recent first
                consecutiveFails++
            }
        }
    }
    
    successRate := float64(successCount) / float64(len(executions))
    avgRuntime := totalRuntime / time.Duration(successCount)
    
    return &JobHealthStatus{
        JobID:           jobID,
        SuccessRate:     successRate,
        AverageRuntime:  avgRuntime,
        ConsecutiveFails: consecutiveFails,
        IsHealthy:       successRate >= 0.9 && consecutiveFails < 3,
    }
}
```

### Alerting System

```yaml
alert_rules:
  job_failure:
    condition: "consecutive_failures >= 3"
    severity: "critical"
    message: "Job {{.job_name}} has failed {{.consecutive_failures}} times consecutively"
    
  job_overdue:
    condition: "minutes_since_expected > 60"
    severity: "warning" 
    message: "Job {{.job_name}} is overdue by {{.minutes_overdue}} minutes"
    
  low_success_rate:
    condition: "success_rate < 0.8"
    severity: "warning"
    message: "Job {{.job_name}} success rate is {{.success_rate}}%"
    
  long_runtime:
    condition: "runtime > expected_runtime * 2"
    severity: "warning"
    message: "Job {{.job_name}} took {{.runtime}} to complete"
```

## Advanced Features

### Conditional Execution

```go
type ConditionalJob struct {
    ScheduledJob
    Conditions []JobCondition `json:"conditions"`
}

type JobCondition struct {
    Type      string      `json:"type"`      // "file_exists", "db_count", "api_check"
    Config    interface{} `json:"config"`
    Operator  string      `json:"operator"`  // "eq", "gt", "lt", "contains"
    Value     interface{} `json:"value"`
}

// Example: Only run if file count > 1000
condition_example:
  type: "file_count"
  config:
    path: "/tmp/uploads"
    pattern: "*.pending"
  operator: "gt"
  value: 1000
```

### Job Dependencies

```mermaid
flowchart TD
    A[Job A: Database Backup] --> B[Job B: Backup Verification]
    B --> C[Job C: Upload to S3]
    C --> D[Job D: Cleanup Local Backup]
    
    E[Job E: Analytics Collection] --> F[Job F: Report Generation]
    F --> G[Job G: Email Report]
    
    H[Job H: Media Processing] --> I[Job I: Thumbnail Generation]
    H --> J[Job J: Metadata Extraction]
    
    I --> K[Job K: CDN Sync]
    J --> K
```

### Dynamic Scheduling

```go
type DynamicScheduler struct {
    scheduler *Scheduler
    rules     []SchedulingRule
}

type SchedulingRule struct {
    Condition string `json:"condition"`
    Action    string `json:"action"`
    CronExpr  string `json:"cron_expr"`
}

// Example: Increase backup frequency during high activity
dynamic_rules:
  - condition: "daily_posts > 100"
    action: "update_schedule"
    job: "database_backup" 
    cron_expr: "0 0 */6 * * *"  # Every 6 hours instead of daily
    
  - condition: "server_load < 0.3"
    action: "enable_job"
    job: "analytics_heavy_processing"
    
  - condition: "storage_usage > 0.8"
    action: "trigger_immediately"
    job: "storage_cleanup"
```

## Performance Optimization

### Concurrent Execution

```mermaid
flowchart TD
    A[Scheduler] --> B[Job Queue]
    B --> C[Worker Pool]
    
    C --> D[Worker 1: High Priority]
    C --> E[Worker 2: Medium Priority]
    C --> F[Worker 3: Low Priority]
    
    D --> G[CPU Intensive Jobs]
    E --> H[I/O Intensive Jobs]
    F --> I[Background Jobs]
    
    J[Load Balancer] --> C
    K[Resource Monitor] --> J
```

### Resource Management

```go
type ResourceLimits struct {
    MaxConcurrentJobs int           `json:"max_concurrent_jobs"`
    MaxMemoryUsage    int64         `json:"max_memory_usage"`
    MaxCPUUsage       float64       `json:"max_cpu_usage"`
    JobTimeouts       map[string]time.Duration `json:"job_timeouts"`
}

type ResourceMonitor struct {
    limits   ResourceLimits
    current  ResourceUsage
    alerts   chan ResourceAlert
}

func (rm *ResourceMonitor) CanExecuteJob(jobType string) bool {
    // Check current resource usage
    if rm.current.RunningJobs >= rm.limits.MaxConcurrentJobs {
        return false
    }
    
    if rm.current.MemoryUsage >= rm.limits.MaxMemoryUsage {
        return false
    }
    
    if rm.current.CPUUsage >= rm.limits.MaxCPUUsage {
        return false
    }
    
    return true
}
```

## Deployment Configurations

### Single Instance Setup

```yaml
scheduler:
  enabled: true
  tick_interval: "1m"
  max_concurrent_jobs: 10
  job_timeout: "30m"
  
  storage:
    type: "database"
    connection: "postgresql://..."
    
  jobs:
    - name: "database_cleanup"
      cron: "0 0 2 * * *"
      handler: "database.cleanup"
      enabled: true
```

### Multi-Instance Setup

```yaml
scheduler:
  enabled: true
  instance_id: "${HOSTNAME}"
  coordination:
    type: "redis"
    redis_url: "redis://cluster:6379"
    lock_ttl: "5m"
    
  leader_election:
    enabled: true
    lease_duration: "30s"
    renew_deadline: "20s"
    retry_period: "5s"
    
  jobs:
    # Jobs will only run on the leader instance
    - name: "critical_backup"
      cron: "0 0 1 * * *"
      handler: "database.backup"
      leader_only: true
      
    # Jobs can run on any instance
    - name: "log_processing"
      cron: "*/5 * * * * *"
      handler: "logs.process"
      distributed: true
```

### Kubernetes Deployment

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: scheduler-leader
spec:
  schedule: "* * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: scheduler
            image: blog-api:latest
            command: ["./scheduler"]
            args: ["--mode=leader"]
            env:
            - name: REDIS_URL
              value: "redis://redis-cluster:6379"
          restartPolicy: OnFailure
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-workers
spec:
  replicas: 3
  selector:
    matchLabels:
      app: scheduler-worker
  template:
    spec:
      containers:
      - name: worker
        image: blog-api:latest
        command: ["./scheduler"]
        args: ["--mode=worker"]
```

## API Endpoints

### Job Management
- `GET /api/cms/v1/scheduler/jobs` - Danh sách jobs
- `POST /api/cms/v1/scheduler/jobs` - Tạo job mới
- `PUT /api/cms/v1/scheduler/jobs/{id}` - Cập nhật job
- `DELETE /api/cms/v1/scheduler/jobs/{id}` - Xóa job
- `POST /api/cms/v1/scheduler/jobs/{id}/trigger` - Trigger job ngay lập tức

### Job Control
- `POST /api/cms/v1/scheduler/jobs/{id}/enable` - Bật job
- `POST /api/cms/v1/scheduler/jobs/{id}/disable` - Tắt job
- `POST /api/cms/v1/scheduler/jobs/{id}/pause` - Tạm dừng job
- `POST /api/cms/v1/scheduler/jobs/{id}/resume` - Tiếp tục job

### Monitoring
- `GET /api/cms/v1/scheduler/status` - Trạng thái scheduler
- `GET /api/cms/v1/scheduler/jobs/{id}/executions` - Lịch sử thực thi
- `GET /api/cms/v1/scheduler/jobs/{id}/health` - Health status
- `GET /api/cms/v1/scheduler/metrics` - System metrics

## Security Considerations

### Job Execution Security

```go
type JobSecurityContext struct {
    UserID      string   `json:"user_id"`
    Permissions []string `json:"permissions"`
    Sandbox     bool     `json:"sandbox"`
    ResourceLimits ResourceLimits `json:"resource_limits"`
}

func (s *Scheduler) ExecuteJobSecurely(job *ScheduledJob, ctx JobSecurityContext) error {
    // Validate permissions
    if !s.hasPermission(ctx, job.Handler) {
        return errors.New("insufficient permissions")
    }
    
    // Create sandbox environment
    if ctx.Sandbox {
        return s.executeInSandbox(job, ctx)
    }
    
    return s.executeJob(job, ctx)
}
```

### Audit Logging

```go
type JobAuditLog struct {
    ID          string    `json:"id"`
    JobID       string    `json:"job_id"`
    Action      string    `json:"action"`  // created, updated, executed, deleted
    UserID      string    `json:"user_id"`
    IPAddress   string    `json:"ip_address"`
    Changes     string    `json:"changes"`
    Timestamp   time.Time `json:"timestamp"`
}
```

## Best Practices

### Job Design
- **Idempotent Operations**: Jobs có thể chạy lại an toàn
- **Error Handling**: Xử lý lỗi graceful
- **Resource Cleanup**: Cleanup resources sau khi chạy
- **Progress Reporting**: Report tiến độ cho long-running jobs

### Scheduling
- **Avoid Peak Hours**: Tránh giờ cao điểm
- **Stagger Jobs**: Phân tán thời gian chạy
- **Consider Dependencies**: Xem xét dependencies giữa jobs
- **Monitor Performance**: Theo dõi performance impact

### Maintenance
- **Regular Review**: Xem xét jobs định kỳ
- **Log Rotation**: Rotate execution logs
- **Archive Old Data**: Archive old execution records
- **Performance Tuning**: Tune performance dựa trên metrics

## Tài liệu liên quan

- [Architecture Overview](./overview.md)
- [Queue System](./queue-system.md)
- [Inter-module Communication](./inter-module-communication.md)
- [Performance Best Practices](../best-practices/performance.md)
- [Monitoring Guidelines](../best-practices/monitoring.md)