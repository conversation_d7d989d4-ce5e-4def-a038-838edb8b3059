# Cursor-Based Pagination Guide

## Overview

This API uses cursor-based pagination for all list endpoints. Cursor pagination provides better performance and consistency compared to traditional offset-based pagination, especially for large datasets.

## How It Works

Instead of using page numbers, cursor pagination uses an opaque cursor string that points to a specific position in the dataset. The cursor encodes information about the last item in the current page, allowing the API to efficiently fetch the next set of results.

## Request Parameters

### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `cursor` | string | Cursor pointing to the page position | null (first page) |
| `limit` | integer | Number of items per page (1-100) | 20 |

### Example Request

```http
GET /api/cms/v1/users?cursor=eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==&limit=20
```

## Response Format

All paginated responses follow this structure:

```json
{
  "success": true,
  "data": [
    // Array of items
  ],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": "eyJpZCI6MTAwLCJ0aW1lIjoiMjAyNC0wMS0xNFQxMDozMDowMFoifQ==",
    "has_more": true,
    "has_previous": true,
    "limit": 20
  }
}
```

### Meta Fields

| Field | Type | Description |
|-------|------|-------------|
| `next_cursor` | string\|null | Cursor for the next page. `null` if no more pages |
| `previous_cursor` | string\|null | Cursor for the previous page. `null` if on first page |
| `has_more` | boolean | Whether there are more pages after current page |
| `has_previous` | boolean | Whether there are pages before current page |
| `limit` | integer | Number of items per page used in this response |

## Usage Examples

### First Page

Request the first page without any cursor:

```http
GET /api/cms/v1/posts?limit=20
```

Response:
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "next_cursor": "eyJpZCI6MjAsInRpbWUiOiIyMDI0LTAxLTE1VDEwOjMwOjAwWiJ9",
    "previous_cursor": null,
    "has_more": true,
    "has_previous": false,
    "limit": 20
  }
}
```

### Next Page

Use the `next_cursor` from the previous response:

```http
GET /api/cms/v1/posts?cursor=eyJpZCI6MjAsInRpbWUiOiIyMDI0LTAxLTE1VDEwOjMwOjAwWiJ9&limit=20
```

### Previous Page

Use the `previous_cursor` to navigate backwards:

```http
GET /api/cms/v1/posts?cursor=eyJpZCI6MTAwLCJ0aW1lIjoiMjAyNC0wMS0xNFQxMDozMDowMFoifQ==&limit=20
```

### Last Page

When you reach the last page:

```json
{
  "success": true,
  "data": [...],
  "meta": {
    "next_cursor": null,
    "previous_cursor": "eyJpZCI6OTAsInRpbWUiOiIyMDI0LTAxLTE1VDA5OjAwOjAwWiJ9",
    "has_more": false,
    "has_previous": true,
    "limit": 20
  }
}
```

## Cursor Structure

Cursors are base64-encoded JSON objects containing:

```json
{
  "id": 123,
  "time": "2024-01-15T10:30:00Z",
  "score": 0.95  // Optional, for relevance-based sorting
}
```

**Note**: Cursors are opaque strings and should not be constructed or modified by clients. Always use the cursors provided by the API.

## Best Practices

### 1. Don't Parse Cursors

Treat cursors as opaque strings. The internal structure may change without notice.

```javascript
// ❌ Bad
const decodedCursor = JSON.parse(atob(cursor));
const nextId = decodedCursor.id + 1;

// ✅ Good
const nextPageUrl = `/api/posts?cursor=${response.meta.next_cursor}`;
```

### 2. Handle Edge Cases

Always check for null cursors:

```javascript
if (response.meta.has_more && response.meta.next_cursor) {
  // Fetch next page
  fetchPage(response.meta.next_cursor);
}
```

### 3. Respect Rate Limits

Cursor pagination is efficient but still consumes resources:

```javascript
// Add delay between requests
async function fetchAllPages() {
  let cursor = null;
  do {
    const response = await fetchPage(cursor);
    processData(response.data);
    cursor = response.meta.next_cursor;
    
    // Add delay to respect rate limits
    if (cursor) {
      await delay(100);
    }
  } while (cursor);
}
```

### 4. Use Appropriate Limits

Choose limit values based on your use case:

- **Mobile apps**: 10-20 items
- **Web apps**: 20-50 items  
- **Data export**: 100 items (maximum)

### 5. Implement Loading States

Show appropriate UI feedback:

```javascript
const [loading, setLoading] = useState(false);
const [hasMore, setHasMore] = useState(true);

async function loadMore() {
  if (loading || !hasMore) return;
  
  setLoading(true);
  try {
    const response = await fetchPage(cursor);
    setHasMore(response.meta.has_more);
    setCursor(response.meta.next_cursor);
    // Update data...
  } finally {
    setLoading(false);
  }
}
```

## Migration from Offset Pagination

If you're migrating from offset-based pagination:

### Old Format (Deprecated)
```http
GET /api/cms/v1/users?page=2&page_size=20
```

### New Format
```http
GET /api/cms/v1/users?cursor=eyJpZCI6MjAsInRpbWUiOiIyMDI0LTAxLTE1VDEwOjMwOjAwWiJ9&limit=20
```

### Key Differences

| Aspect | Offset Pagination | Cursor Pagination |
|--------|------------------|-------------------|
| Parameters | `page`, `page_size` | `cursor`, `limit` |
| Performance | Slower for large offsets | Consistent performance |
| Consistency | Can skip/duplicate items | Always consistent |
| Total Count | Usually provided | Not provided |
| Random Access | Possible (jump to page X) | Not possible |

## Error Handling

### Invalid Cursor

```json
{
  "success": false,
  "error": {
    "message": "Invalid cursor format",
    "code": "INVALID_CURSOR"
  }
}
```

### Expired Cursor

Cursors may expire after a certain time:

```json
{
  "success": false,
  "error": {
    "message": "Cursor has expired. Please restart from the beginning.",
    "code": "CURSOR_EXPIRED"
  }
}
```

### Invalid Limit

```json
{
  "success": false,
  "error": {
    "message": "Limit must be between 1 and 100",
    "code": "INVALID_LIMIT"
  }
}
```

## FAQ

### Q: How do I get the total count of items?

A: Cursor pagination doesn't provide total counts for performance reasons. If you need to show progress, consider:
- Showing "Load More" instead of page numbers
- Using infinite scroll
- Implementing a separate count endpoint if absolutely necessary

### Q: Can I jump to a specific page?

A: No, cursor pagination doesn't support random access. You must navigate sequentially through the pages.

### Q: How long are cursors valid?

A: Cursors are typically valid for 24 hours. After that, you'll need to start from the beginning.

### Q: Can I use the same cursor multiple times?

A: Yes, cursors are idempotent. Using the same cursor will always return the same page (unless the data has been deleted).

### Q: What happens if items are added/deleted while paginating?

A: Cursor pagination maintains consistency:
- New items added after your current position will appear in subsequent pages
- Deleted items won't cause skips or duplicates
- The cursor points to a specific position, not a page number

## See Also

- [API Response Standards](./response-standard.md)
- [Error Handling](./error-handling.md)
- [Rate Limiting](./rate-limiting.md)