# CMS API - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

CMS API được thiết kế cho các tenant admin và content managers để quản lý nội dung, cấu hình và người dùng trong hệ thống blog. API này yêu cầu authentication và có đầy đủ quyền CRUD.

## Base URL

```
Production: https://api.yourblog.com/cms/v1
Staging: https://staging-api.yourblog.com/cms/v1
Development: http://localhost:9077/cms/v1
```

## Authentication

### Bearer Token
```http
Authorization: Bearer <jwt_token>
```

### Headers yêu cầu
```http
Content-Type: application/json
Accept: application/json
X-Tenant-ID: <tenant_id>
X-Website-ID: <website_id>
X-Request-ID: <unique_request_id>
```

**<PERSON>ô tả Headers:**
- `X-Tenant-ID`: Context tenant hiệ<PERSON> tạ<PERSON> (bắ<PERSON> buộc cho tất cả operations)
- `X-Website-ID`: Context website hiện tại (bắt buộc cho content operations)
- `X-Request-ID`: Unique request identifier cho tracing

**Context Switching:**
Global users có thể chuyển đổi giữa các tenant/website contexts khác nhau bằng cách thay đổi headers này. API sẽ validate rằng authenticated user có quyền truy cập vào tenant và website được chỉ định.

## API Structure Overview

```mermaid
flowchart TD
    A[CMS API] --> B[Authentication]
    A --> C[Content Management]
    A --> D[User Management]
    A --> E[Media Management]
    A --> F[Website Configuration]
    A --> G[Analytics & Reports]
    A --> H[System Settings]
    
    C --> C1[Posts]
    C --> C2[Categories] 
    C --> C3[Tags]
    C --> C4[Comments]
    C --> C5[Pages]
    
    D --> D1[Users]
    D --> D2[Roles & Permissions]
    D --> D3[Invitations]
    
    E --> E1[Files]
    E --> E2[Folders]
    E --> E3[Media Library]
    
    F --> F1[Themes]
    F --> F2[Menus]
    F --> F3[Widgets]
    F --> F4[SEO Settings]
```

## Authentication Endpoints

### Login
```http
POST /cms/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "permissions": ["posts:create", "posts:update", "users:manage"]
    },
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "def50200abcd...",
      "expires_in": 3600,
      "token_type": "Bearer"
    }
  }
}
```

### Refresh Token
```http
POST /cms/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "def50200abcd..."
}
```

### Logout
```http
POST /cms/v1/auth/logout
Authorization: Bearer <token>
```

## Content Management

### Posts Management

#### List Posts
```http
GET /cms/v1/posts?cursor=eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ&limit=20&status=all&author=123&category=5
Authorization: Bearer <token>
X-Website-ID: <website_id>
```

**Query Parameters:**
- `cursor`: Cursor for pagination (base64 encoded position)
- `limit`: Items per page (default: 20, max: 100)
- `status`: Post status (all, draft, published, archived)
- `author`: Filter by author ID
- `category`: Filter by category ID
- `search`: Search in title and content
- `sort`: Sort field (created_at, updated_at, title)
- `order`: Sort order (asc, desc)

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "posts": [
      {
        "id": 123,
        "title": "Blog Post Title",
        "slug": "blog-post-title",
        "excerpt": "Post excerpt...",
        "content": "Full post content...",
        "status": "published",
        "featured_image": {
          "id": 456,
          "url": "https://cdn.example.com/image.jpg",
          "alt": "Featured image"
        },
        "author": {
          "id": 1,
          "name": "John Doe",
          "avatar": "https://cdn.example.com/avatar.jpg"
        },
        "categories": [
          {
            "id": 5,
            "name": "Technology",
            "slug": "technology"
          }
        ],
        "tags": [
          {
            "id": 10,
            "name": "Go",
            "slug": "go"
          }
        ],
        "seo": {
          "meta_title": "SEO optimized title",
          "meta_description": "SEO description",
          "meta_keywords": ["go", "programming"]
        },
        "stats": {
          "views": 1250,
          "likes": 45,
          "comments": 12
        },
        "published_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-15T09:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  },
  "meta": {
    "limit": 20,
    "has_more": true,
    "next_cursor": "eyJpZCI6MTM0LCJ0aW1lIjoiMjAyNC0wMS0xNFQwODozMDowMFoifQ",
    "prev_cursor": null,
    "total": 156,
    "website_id": 1
  },
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  }
}
```

#### Create Post
```http
POST /cms/v1/posts
Authorization: Bearer <token>
X-Website-ID: <website_id>
Content-Type: application/json

{
  "title": "New Blog Post",
  "content": "Post content here...",
  "excerpt": "Short excerpt",
  "status": "draft",
  "featured_image_id": 456,
  "category_ids": [5, 8],
  "tag_ids": [10, 15, 20],
  "seo": {
    "meta_title": "Custom SEO title",
    "meta_description": "SEO description",
    "meta_keywords": ["keyword1", "keyword2"]
  },
  "scheduled_at": "2024-01-20T10:00:00Z"
}
```

#### Update Post
```http
PUT /cms/v1/posts/{id}
Authorization: Bearer <token>
Content-Type: application/json
```

#### Delete Post
```http
DELETE /cms/v1/posts/{id}
Authorization: Bearer <token>
```

#### Publish Post
```http
POST /cms/v1/posts/{id}/publish
Authorization: Bearer <token>

{
  "scheduled_at": "2024-01-20T10:00:00Z" // Optional
}
```

#### Duplicate Post
```http
POST /cms/v1/posts/{id}/duplicate
Authorization: Bearer <token>

{
  "title": "Copy of Original Title",
  "status": "draft"
}
```

### Categories Management

#### List Categories
```http
GET /cms/v1/categories?include_posts_count=true
Authorization: Bearer <token>
X-Website-ID: <website_id>
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/cms/v1/categories",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "categories": [
      {
        "id": 5,
        "name": "Technology",
        "slug": "technology",
        "description": "Tech related posts",
        "color": "#3B82F6",
        "image": {
          "id": 789,
          "url": "https://cdn.example.com/category.jpg"
        },
        "posts_count": 45,
        "parent_id": null,
        "children": [
          {
            "id": 8,
            "name": "Programming",
            "slug": "programming",
            "posts_count": 23
          }
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-10T12:00:00Z"
      }
    ]
  },
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  }
}
```

#### Create Category
```http
POST /cms/v1/categories
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New Category",
  "description": "Category description",
  "color": "#FF5722",
  "parent_id": 5,
  "image_id": 789
}
```

#### Update Category
```http
PUT /cms/v1/categories/{id}
Authorization: Bearer <token>
```

#### Delete Category
```http
DELETE /cms/v1/categories/{id}?move_posts_to=8
Authorization: Bearer <token>
```

### Comments Management

#### List Comments
```http
GET /cms/v1/comments?cursor=eyJpZCI6NDU2fQ&status=pending&post_id=123&limit=20
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": 456,
        "content": "Great article!",
        "status": "pending",
        "author": {
          "id": 789,
          "name": "Jane Doe",
          "email": "<EMAIL>",
          "avatar": "https://cdn.example.com/avatar.jpg"
        },
        "post": {
          "id": 123,
          "title": "Blog Post Title",
          "slug": "blog-post-title"
        },
        "parent_id": null,
        "replies_count": 2,
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "limit": 20,
      "has_next": true,
      "has_prev": false,
      "next_cursor": "eyJpZCI6NDU3LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
      "prev_cursor": null
    }
  }
}
```

#### Moderate Comment
```http
POST /cms/v1/comments/{id}/moderate
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "approve", // approve, reject, spam
  "reason": "Comment approved by moderator"
}
```

#### Bulk Moderate Comments
```http
POST /cms/v1/comments/bulk-moderate
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_ids": [456, 789, 012],
  "action": "approve"
}
```

## User Management

### Users

#### List Users
```http
GET /cms/v1/users?cursor=eyJpZCI6MTIzfQ&role=author&status=active&search=john&limit=20
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "author",
        "status": "active",
        "avatar": "https://cdn.example.com/avatar.jpg",
        "bio": "Content writer and blogger",
        "social_links": {
          "twitter": "johndoe",
          "linkedin": "johndoe"
        },
        "stats": {
          "posts_count": 25,
          "comments_count": 156,
          "followers_count": 89
        },
        "last_login_at": "2024-01-15T09:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "limit": 20,
      "has_next": true,
      "has_prev": false,
      "next_cursor": "eyJpZCI6MTM0LCJ0aW1lIjoiMjAyNC0wMS0wMVQwMDowMDowMFoifQ",
      "prev_cursor": null
    }
  }
}
```

#### Create User
```http
POST /cms/v1/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "author",
  "bio": "User biography",
  "send_invitation": true
}
```

#### Update User
```http
PUT /cms/v1/users/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Name",
  "role": "editor",
  "status": "active"
}
```

#### Delete User
```http
DELETE /cms/v1/users/{id}?reassign_posts_to=456
Authorization: Bearer <token>
```

### Roles & Permissions

#### List Roles
```http
GET /cms/v1/roles
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": 1,
        "name": "admin",
        "display_name": "Administrator",
        "description": "Full system access",
        "permissions": [
          "posts:create",
          "posts:update",
          "posts:delete",
          "users:manage",
          "settings:update"
        ],
        "users_count": 2,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### Assign Role to User
```http
POST /cms/v1/users/{user_id}/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "role_id": 2,
  "expires_at": "2024-12-31T23:59:59Z" // Optional
}
```

## Media Management

### Media Library

#### List Media Files
```http
GET /cms/v1/media?cursor=eyJpZCI6NDU2fQ&type=image&folder_id=123&search=logo&limit=20
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": 456,
        "name": "company-logo.png",
        "original_name": "logo.png",
        "mime_type": "image/png",
        "size": 245760,
        "width": 800,
        "height": 600,
        "url": "https://cdn.example.com/logo.png",
        "thumbnails": {
          "small": "https://cdn.example.com/logo-150x150.png",
          "medium": "https://cdn.example.com/logo-300x300.png",
          "large": "https://cdn.example.com/logo-600x600.png"
        },
        "folder": {
          "id": 123,
          "name": "Logos",
          "path": "/uploads/logos"
        },
        "metadata": {
          "alt_text": "Company Logo",
          "caption": "Our brand logo",
          "exif": {
            "camera": "Canon EOS R5",
            "lens": "RF 24-70mm F2.8",
            "iso": 100
          }
        },
        "usage_count": 5,
        "uploaded_by": {
          "id": 1,
          "name": "Admin User"
        },
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "limit": 20,
      "has_next": true,
      "has_prev": false,
      "next_cursor": "eyJpZCI6NDU3LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
      "prev_cursor": null
    }
  }
}
```

#### Upload Media
```http
POST /cms/v1/media/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary_file>
folder_id: 123
alt_text: "Image description"
caption: "Image caption"
```

#### Update Media Metadata
```http
PUT /cms/v1/media/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "alt_text": "Updated alt text",
  "caption": "Updated caption",
  "folder_id": 124
}
```

#### Delete Media
```http
DELETE /cms/v1/media/{id}
Authorization: Bearer <token>
```

### Folders

#### List Folders
```http
GET /cms/v1/media/folders?parent_id=0
Authorization: Bearer <token>
```

#### Create Folder
```http
POST /cms/v1/media/folders
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New Folder",
  "parent_id": 123
}
```

## Website Configuration

### Themes

#### List Themes
```http
GET /cms/v1/themes
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "themes": [
      {
        "id": "modern-blog",
        "name": "Modern Blog",
        "version": "1.2.0",
        "author": "Theme Developer",
        "description": "A modern, responsive blog theme",
        "screenshot": "https://cdn.example.com/theme-preview.jpg",
        "status": "active",
        "settings": {
          "primary_color": "#3B82F6",
          "font_family": "Inter",
          "layout": "sidebar-right"
        },
        "supported_features": [
          "dark_mode",
          "custom_colors",
          "multiple_layouts"
        ],
        "installed_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### Activate Theme
```http
POST /cms/v1/themes/{theme_id}/activate
Authorization: Bearer <token>
```

#### Update Theme Settings
```http
PUT /cms/v1/themes/{theme_id}/settings
Authorization: Bearer <token>
Content-Type: application/json

{
  "primary_color": "#FF5722",
  "font_family": "Roboto",
  "layout": "sidebar-left"
}
```

### Menus

#### List Menus
```http
GET /cms/v1/menus
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "menus": [
      {
        "id": 1,
        "name": "Main Navigation",
        "location": "header",
        "items": [
          {
            "id": 1,
            "title": "Home",
            "url": "/",
            "type": "page",
            "target": "_self",
            "order": 1,
            "parent_id": null,
            "children": []
          },
          {
            "id": 2,
            "title": "Blog",
            "url": "/blog",
            "type": "page",
            "order": 2,
            "children": [
              {
                "id": 3,
                "title": "Technology",
                "url": "/category/technology",
                "type": "category",
                "order": 1
              }
            ]
          }
        ],
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### Create Menu
```http
POST /cms/v1/menus
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Footer Menu",
  "location": "footer",
  "items": [
    {
      "title": "About",
      "url": "/about",
      "type": "page",
      "order": 1
    }
  ]
}
```

### SEO Settings

#### Get SEO Settings
```http
GET /cms/v1/seo/settings
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "site_title": "My Blog",
    "site_description": "A blog about technology and programming",
    "meta_keywords": ["blog", "technology", "programming"],
    "og_image": "https://cdn.example.com/og-image.jpg",
    "twitter_handle": "@myblog",
    "google_analytics_id": "G-XXXXXXXXXX",
    "google_search_console_id": "google123456789",
    "robots_txt": "User-agent: *\nDisallow: /admin/\nSitemap: https://myblog.com/sitemap.xml",
    "schema_markup": {
      "organization": {
        "name": "My Blog",
        "url": "https://myblog.com",
        "logo": "https://cdn.example.com/logo.png"
      }
    }
  }
}
```

#### Update SEO Settings
```http
PUT /cms/v1/seo/settings
Authorization: Bearer <token>
Content-Type: application/json

{
  "site_title": "Updated Blog Title",
  "site_description": "Updated description",
  "google_analytics_id": "G-YYYYYYYYYY"
}
```

## Analytics & Reports

### Dashboard Analytics
```http
GET /cms/v1/analytics/dashboard?period=30d
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_posts": 156,
      "total_users": 89,
      "total_comments": 456,
      "total_views": 12543
    },
    "growth": {
      "posts_growth": 12.5,
      "users_growth": 8.3,
      "comments_growth": 15.2,
      "views_growth": 23.1
    },
    "top_posts": [
      {
        "id": 123,
        "title": "Popular Post Title",
        "views": 1250,
        "likes": 45,
        "comments": 12
      }
    ],
    "recent_activity": [
      {
        "type": "post_published",
        "user": "John Doe",
        "post": "New Blog Post",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Content Analytics
```http
GET /cms/v1/analytics/content?period=7d&type=posts
Authorization: Bearer <token>
```

### User Analytics
```http
GET /cms/v1/analytics/users?period=30d
Authorization: Bearer <token>
```

## System Settings

### General Settings
```http
GET /cms/v1/settings/general
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "site_name": "My Blog",
    "site_url": "https://myblog.com",
    "admin_email": "<EMAIL>",
    "timezone": "Asia/Ho_Chi_Minh",
    "language": "vi",
    "date_format": "DD/MM/YYYY",
    "time_format": "HH:mm",
    "allow_registration": true,
    "moderate_comments": true,
    "posts_per_page": 10
  }
}
```

#### Update General Settings
```http
PUT /cms/v1/settings/general
Authorization: Bearer <token>
Content-Type: application/json

{
  "site_name": "Updated Blog Name",
  "timezone": "UTC",
  "allow_registration": false
}
```

### Email Settings
```http
GET /cms/v1/settings/email
Authorization: Bearer <token>
```

```http
PUT /cms/v1/settings/email
Authorization: Bearer <token>
Content-Type: application/json

{
  "smtp_host": "smtp.gmail.com",
  "smtp_port": 587,
  "smtp_username": "<EMAIL>",
  "smtp_password": "password123",
  "from_email": "<EMAIL>",
  "from_name": "My Blog"
}
```

## Error Handling

### Error Response Format
```json
{
  "status": {
    "code": 422,
    "message": "Validation failed",
    "success": false,
    "error_code": "VALIDATION_ERROR",
    "path": "/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": [
      {
        "field": "title",
        "message": "Title is required",
        "code": "REQUIRED"
      },
      {
        "field": "content",
        "message": "Content must be at least 100 characters",
        "code": "MIN_LENGTH"
      }
    ]
  },
  "data": null,
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  }
}
```

### HTTP Status Codes
- `200` OK - Request successful
- `201` Created - Resource created successfully
- `400` Bad Request - Invalid request data
- `401` Unauthorized - Authentication required
- `403` Forbidden - Insufficient permissions
- `404` Not Found - Resource not found
- `422` Unprocessable Entity - Validation error
- `429` Too Many Requests - Rate limit exceeded
- `500` Internal Server Error - Server error

## Rate Limiting

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Rate Limits by Endpoint
- **Authentication**: 5 requests per minute
- **Content Creation**: 100 requests per hour
- **Media Upload**: 50 uploads per hour
- **General API**: 1000 requests per hour

## Webhooks

### Register Webhook
```http
POST /cms/v1/webhooks
Authorization: Bearer <token>
Content-Type: application/json

{
  "url": "https://your-app.com/webhook",
  "events": ["post.published", "comment.created"],
  "secret": "webhook_secret_key"
}
```

### Webhook Events
- `post.created` - Includes website_id context
- `post.updated` - Includes website_id context 
- `post.published` - Includes website_id context
- `post.deleted` - Includes website_id context
- `comment.created` - Includes website_id context
- `comment.moderated` - Includes website_id context
- `user.registered` - Includes website_id context
- `media.uploaded` - Includes website_id context

### Webhook Payload Format
```json
{
  "event": "post.published",
  "timestamp": "2024-01-15T10:30:00Z",
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  },
  "data": {
    "post_id": 123,
    "title": "New Blog Post",
    "author_id": 1,
    "published_at": "2024-01-15T10:30:00Z"
  }
}
```

## Multi-Tenancy Features

### Global User Model
- **Global Users**: Users exist globally across all tenants with unique email
- **Tenant Memberships**: Users can be members of multiple tenants
- **Website Roles**: Users have specific roles on each website within a tenant
- **Context Switching**: Users can switch between tenant/website contexts seamlessly

### Access Control
- **Tenant-level Access**: Users must be members of a tenant to access its resources
- **Website-level Permissions**: Roles and permissions are scoped to specific websites
- **Cross-tenant Identity**: Single identity across multiple tenants
- **Invitation System**: Users can be invited to join tenants and assigned website roles

### Resource Isolation
- **Content Isolation**: All content (posts, media, etc.) is isolated by website_id
- **Permission Isolation**: Roles and permissions are website-specific
- **Cache Isolation**: Cache keys include both tenant_id and website_id
- **Database Isolation**: Queries are filtered by appropriate tenant/website context

### Context Management
- **Header-based Context**: Use X-Tenant-ID and X-Website-ID headers
- **Session Context**: Current context stored in user session
- **Context Validation**: API validates user access to requested context
- **Context Switching API**: Dedicated endpoints for switching contexts

### Performance Optimization
- **Multi-level Caching**: Global user cache + tenant-specific cache + website-specific cache
- **Optimized Queries**: Efficient joins across user_global_users → user_tenant_memberships → user_website_roles
- **Index Strategy**: Composite indexes on (global_user_id, tenant_id, website_id)
- **CDN Routing**: Website-specific CDN routing and caching

## Tài liệu liên quan

- [Frontend API](./frontend-api.md)
- [Authentication Guide](./authentication.md)
- [Error Handling](./error-handling.md)
- [Rate Limiting](./rate-limiting.md)
- [Response Standard](./response-standard.md)