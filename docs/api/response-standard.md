# Chuẩn Response API - Tài liệu Tiếng Việt

## Tổng quan

Tài liệu này định nghĩa chuẩn response format mà tất cả các modules và API endpoints phải tuân theo trong Blog API v3. Việc standardization này đảm bảo consistency và dễ dàng integration cho frontend clients.

## Naming Conventions

### JSON Field Names
- **Format**: `snake_case` cho tất cả JSON field names
- **Consistency**: Nhất quán với database column naming conventions
- **Examples**: `user_id`, `created_at`, `featured_image`, `next_cursor`, `has_more`

### API Endpoints
- **Format**: `kebab-case` cho URL paths
- **Examples**: `/api/cms/v1/blog-posts`, `/cms/v1/user-roles`, `/api/cms/v1/search-suggestions`

### Error Codes
- **Format**: `UPPER_SNAKE_CASE` cho error codes
- **Examples**: `VALIDATION_ERROR`, `NOT_FOUND`, `RATE_LIMIT_EXCEEDED`, `TENANT_LIMIT_EXCEEDED`

### HTTP Headers
- **Format**: `Kebab-Case` cho custom headers
- **Examples**: `X-Tenant-ID`, `X-Website-ID`, `X-Request-ID`

## Cấu trúc Response Chuẩn

### Core Response Structure

```go
package response

import (
    "time"
)

// Status chứa thông tin về trạng thái của request
type Status struct {
    Code      int         `json:"code"`
    Message   string      `json:"message"`
    Success   bool        `json:"success"`
    ErrorCode string      `json:"error_code,omitempty"`
    Path      string      `json:"path"`
    Timestamp string      `json:"timestamp"`
    Details   interface{} `json:"details,omitempty"`
}

// Meta chứa metadata về pagination và thông tin bổ sung
type Meta struct {
    NextCursor string `json:"next_cursor,omitempty"`
    HasMore    bool   `json:"has_more,omitempty"`
    Total      *int64 `json:"total,omitempty"`        // Optional total count
    Limit      int    `json:"limit,omitempty"`        // Page size
    Runtime    string `json:"runtime,omitempty"`      // Request processing time
    WebsiteID  *int64 `json:"website_id,omitempty"`   // Website context for multi-tenancy
}

// Response là cấu trúc chính cho tất cả API responses
type Response struct {
    Status  Status      `json:"status"`
    Data    interface{} `json:"data"`
    Meta    *Meta       `json:"meta,omitempty"`
    Website *WebsiteContext `json:"website,omitempty"`  // Website context for multi-tenancy
}

// WebsiteContext chứa thông tin website cho multi-tenancy
type WebsiteContext struct {
    ID     int64  `json:"id"`
    Domain string `json:"domain"`
    Name   string `json:"name"`
    Theme  string `json:"theme,omitempty"`
}

// Detail để mô tả chi tiết lỗi validation hoặc thông tin bổ sung
type Detail struct {
    Field   string `json:"field,omitempty"`
    Message string `json:"message"`
    Code    string `json:"code,omitempty"`
}
```

### Helper Functions

```go
// Core helper functions (implementation details omitted for brevity)
func SuccessResponse(data interface{}, path string) *Response
func SuccessWithMetaResponse(data interface{}, meta *Meta, path string) *Response
func SuccessWithWebsiteResponse(data interface{}, websiteCtx *WebsiteContext, path string) *Response
func SuccessWithMetaAndWebsiteResponse(data interface{}, meta *Meta, websiteCtx *WebsiteContext, path string) *Response

// Error response helpers
func ErrorResponse(code int, message, errorCode, path string, details interface{}) *Response
func ValidationErrorResponse(path string, details []Detail) *Response
func NotFoundResponse(resource, path string) *Response
func UnauthorizedResponse(path string) *Response
func ForbiddenResponse(path string) *Response
func InternalErrorResponse(path string, details interface{}) *Response
```

**📝 Note:** Full implementation available in `pkg/response/response.go`

// Enhanced error response helpers
func NotFoundWithDetailsResponse(path, resourceType, resourceID string, availableIDs []string) *Response
func ForbiddenWithPermissionsResponse(path, requiredPermission string, userPermissions []string) *Response
func RateLimitExceededResponse(path string, limit, remaining int, resetTime time.Time) *Response
func TenantLimitExceededResponse(path string, currentUsage, limit int, plan, upgradeURL string) *Response
func ExternalServiceErrorResponse(path, serviceName, serviceStatus string, retryAfter int) *Response
```

## Status Codes và Error Codes

### HTTP Status Codes

| Status Code | Ý nghĩa | Sử dụng |
|-------------|---------|---------|
| `200` | OK | Request thành công |
| `201` | Created | Resource được tạo thành công |
| `400` | Bad Request | Request không hợp lệ |
| `401` | Unauthorized | Cần authentication |
| `403` | Forbidden | Không có quyền truy cập |
| `404` | Not Found | Resource không tồn tại |
| `422` | Unprocessable Entity | Lỗi validation |
| `429` | Too Many Requests | Rate limit exceeded |
| `500` | Internal Server Error | Lỗi server |

### Error Codes Chuẩn

| Error Code | HTTP Status | Mô tả | Details Structure |
|------------|-------------|--------|-------------------|
| `VALIDATION_ERROR` | 422 | Dữ liệu đầu vào không hợp lệ | `[{field, message, code}]` |
| `NOT_FOUND` | 404 | Resource không tìm thấy | `{type, id, available_ids?}` |
| `UNAUTHORIZED` | 401 | Chưa đăng nhập | `{required_auth, login_url}` |
| `FORBIDDEN` | 403 | Không có quyền | `{required_permission, user_permissions}` |
| `DUPLICATE_ENTRY` | 409 | Dữ liệu đã tồn tại | `{field, existing_value, suggested_alternatives?}` |
| `RATE_LIMIT_EXCEEDED` | 429 | Vượt quá giới hạn request | `{limit, remaining, reset_time, retry_after}` |
| `INTERNAL_ERROR` | 500 | Lỗi hệ thống | `{error_id, support_contact}` |
| `DATABASE_ERROR` | 500 | Lỗi cơ sở dữ liệu | `{error_id, affected_tables?}` |
| `EXTERNAL_SERVICE_ERROR` | 502 | Lỗi dịch vụ bên ngoài | `{service_name, service_status, retry_after}` |
| `TENANT_LIMIT_EXCEEDED` | 403 | Vượt quá giới hạn tenant | `{current_usage, limit, upgrade_url}` |
| `WEBSITE_INACTIVE` | 403 | Website không hoạt động | `{website_id, status, reactivation_url}` |
| `CONTENT_TOO_LARGE` | 413 | Nội dung quá lớn | `{current_size, max_size, compression_tips}` |

## Response Examples

### Success Response - Single Item

```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/api/cms/v1/posts/building-modern-apis",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "id": 123,
    "title": "Building Modern APIs with Go",
    "slug": "building-modern-apis",
    "content": "Full post content...",
    "author": {
      "id": 1,
      "name": "John Doe"
    },
    "published_at": "2024-01-15T10:30:00Z"
  }
}
```

### Success Response - List with Pagination

```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/api/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": [
    {
      "id": 123,
      "title": "Post Title 1",
      "slug": "post-title-1"
    },
    {
      "id": 124,
      "title": "Post Title 2", 
      "slug": "post-title-2"
    }
  ],
  "meta": {
    "next_cursor": "eyJpZCI6MTI0LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
    "previous_cursor": null,
    "has_more": true,
    "has_previous": false,
    "limit": 12,
    "runtime": "45ms",
    "website_id": 1
  },
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  }
}
```

### Error Response - Validation Error

```json
{
  "status": {
    "code": 422,
    "message": "Validation failed",
    "success": false,
    "error_code": "VALIDATION_ERROR",
    "path": "/api/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": [
      {
        "field": "title",
        "message": "Title is required",
        "code": "REQUIRED"
      },
      {
        "field": "content",
        "message": "Content must be at least 100 characters",
        "code": "MIN_LENGTH"
      }
    ]
  },
  "data": null
}
```

### Error Response - Not Found

```json
{
  "status": {
    "code": 404,
    "message": "Post not found",
    "success": false,
    "error_code": "NOT_FOUND",
    "path": "/api/cms/v1/posts/invalid-slug",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": null
}
```

### Error Response - Authentication Required

```json
{
  "status": {
    "code": 401,
    "message": "Authentication required",
    "success": false,
    "error_code": "UNAUTHORIZED",
    "path": "/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": null
}
```

### Error Response - Rate Limit

```json
{
  "status": {
    "code": 429,
    "message": "Rate limit exceeded",
    "success": false,
    "error_code": "RATE_LIMIT_EXCEEDED",
    "path": "/api/cms/v1/search",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "limit": 100,
      "remaining": 0,
      "reset_time": "2024-01-15T11:00:00Z",
      "retry_after": 1800
    }
  },
  "data": null
}
```

### Error Response - Enhanced NOT_FOUND
```json
{
  "status": {
    "code": 404,
    "message": "Post not found",
    "success": false,
    "error_code": "NOT_FOUND",
    "path": "/api/cms/v1/posts/invalid-slug",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "type": "post",
      "id": "invalid-slug",
      "available_ids": ["recent-post-1", "recent-post-2", "recent-post-3"]
    }
  },
  "data": null
}
```

### Error Response - FORBIDDEN with Permissions
```json
{
  "status": {
    "code": 403,
    "message": "Insufficient permissions to delete post",
    "success": false,
    "error_code": "FORBIDDEN",
    "path": "/api/cms/v1/posts/123",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "required_permission": "post.delete",
      "user_permissions": ["post.read", "post.create", "post.update"],
      "contact_admin": "<EMAIL>"
    }
  },
  "data": null
}
```

### Error Response - TENANT_LIMIT_EXCEEDED
```json
{
  "status": {
    "code": 403,
    "message": "Tenant user limit exceeded",
    "success": false,
    "error_code": "TENANT_LIMIT_EXCEEDED",
    "path": "/api/cms/v1/users",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "current_usage": 100,
      "limit": 100,
      "plan": "basic",
      "upgrade_url": "https://yourblog.com/upgrade",
      "contact_sales": "<EMAIL>"
    }
  },
  "data": null
}
```

### Error Response - EXTERNAL_SERVICE_ERROR
```json
{
  "status": {
    "code": 502,
    "message": "Email service temporarily unavailable",
    "success": false,
    "error_code": "EXTERNAL_SERVICE_ERROR",
    "path": "/api/cms/v1/notifications/send",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "service_name": "SendGrid",
      "service_status": "degraded",
      "retry_after": 300,
      "status_page": "https://status.sendgrid.com",
      "alternative_action": "Email will be queued and sent when service recovers"
    }
  },
  "data": null
}
```

## Pagination Standard

### Cursor-based Pagination

Tất cả list endpoints phải sử dụng cursor-based pagination. Xem [Cursor Pagination Guide](./cursor-pagination.md) để biết chi tiết đầy đủ.

```json
{
  "meta": {
    "next_cursor": "eyJpZCI6MTI0LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
    "previous_cursor": "eyJpZCI6MTAwLCJ0aW1lIjoiMjAyNC0wMS0xNFQxMDozMDowMFoifQ",
    "has_more": true,
    "has_previous": true,
    "limit": 12,
    "runtime": "45ms"
  }
}
```

### Cursor Format

Cursor được encode bằng base64 và chứa thông tin cần thiết để lấy page tiếp theo:

```go
type CursorData struct {
    ID   int64     `json:"id"`
    Time time.Time `json:"time,omitempty"`
    Score float64  `json:"score,omitempty"`
}

func EncodeCursor(data CursorData) string
func DecodeCursor(cursor string) (*CursorData, error)
```

## Middleware Implementation

### Response Wrapper Middleware

```go
// Middleware functions for response handling
func ResponseWrapper() gin.HandlerFunc  // Adds runtime tracking and headers
func ErrorHandler() gin.HandlerFunc     // Handles errors and converts to standard format
func WebsiteContextMiddleware() gin.HandlerFunc  // Validates website context
```

**📝 Note:** Full middleware implementation available in `internal/middleware/response.go`
```

## Usage Examples

### Controller Implementation

```go
// Example controller usage
func (pc *PostController) GetPosts(c *gin.Context) {
    // Get data from service
    posts, meta, err := pc.postService.GetPosts(...)
    if err != nil {
        resp := response.InternalErrorResponse(c.Request.URL.Path, err.Error())
        c.JSON(resp.Status.Code, resp)
        return
    }

    // Create website context
    websiteCtx := &response.WebsiteContext{...}

    // Return success response
    resp := response.SuccessWithMetaAndWebsiteResponse(posts, meta, websiteCtx, c.Request.URL.Path)
    c.JSON(http.StatusOK, resp)
}

func (pc *PostController) CreatePost(c *gin.Context) {
    // Bind and validate request
    if validationErrors := pc.validateRequest(&req); len(validationErrors) > 0 {
        resp := response.ValidationErrorResponse(c.Request.URL.Path, validationErrors)
        c.JSON(resp.Status.Code, resp)
        return
    }

    // Create post and return response
    post, err := pc.postService.CreatePost(&req)
    // ... handle error and return response
}
```

**📝 Note:** Full controller examples available in `internal/controllers/`

## Testing Standards

### Unit Test Examples

```go
func TestGetPosts_Success(t *testing.T) {
    // Execute request
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)

    // Assert response structure
    var resp response.Response
    json.Unmarshal(w.Body.Bytes(), &resp)
    assert.True(t, resp.Status.Success)
    assert.NotNil(t, resp.Data)
    assert.NotNil(t, resp.Website)
}

func TestCreatePost_ValidationError(t *testing.T) {
    // Test validation error response
    assert.Equal(t, 422, w.Code)
    assert.Equal(t, "VALIDATION_ERROR", resp.Status.ErrorCode)
    assert.NotNil(t, resp.Status.Details)
}
```

**📝 Note:** Full test examples available in `internal/controllers/*_test.go`

## Documentation Standards

### API Docs Format

Tất cả API documentation phải include:

1. **Request Format**: Method, URL, headers, body
2. **Response Format**: Sử dụng chuẩn response structure
3. **Error Cases**: List tất cả possible error responses
4. **Examples**: Cả success và error examples

### Swagger/OpenAPI Integration

```yaml
components:
  schemas:
    Status:
      type: object
      required:
        - code
        - message
        - success
        - path
        - timestamp
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Request processed successfully"
        success:
          type: boolean
          example: true
        error_code:
          type: string
          example: "NOT_FOUND"
        path:
          type: string
          example: "/api/cms/v1/posts"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        details:
          type: object
          
    Meta:
      type: object
      properties:
        next_cursor:
          type: string
          example: "eyJpZCI6MTI0fQ"
        has_more:
          type: boolean
          example: true
        total:
          type: integer
          example: 156
        limit:
          type: integer
          example: 12
        runtime:
          type: string
          example: "45ms"
          
    Response:
      type: object
      required:
        - status
        - data
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
        meta:
          $ref: '#/components/schemas/Meta'
```

## Validation Rules

### Tất cả modules phải:

1. ✅ Sử dụng chuẩn response structure
2. ✅ Implement proper error handling
3. ✅ Sử dụng cursor-based pagination
4. ✅ Include proper status codes và error codes
5. ✅ Provide detailed validation errors
6. ✅ Add request/response logging
7. ✅ Include performance metrics (runtime)
8. ✅ Follow naming conventions
9. ✅ Include website context for multi-tenancy
10. ✅ Filter all data by website_id
11. ✅ Validate user access to website resources

### Code Review Checklist

- [ ] Response structure tuân theo chuẩn
- [ ] Error codes được định nghĩa properly
- [ ] Pagination implementation đúng
- [ ] Validation errors chi tiết
- [ ] Status codes appropriate
- [ ] Runtime metrics included
- [ ] Tests cover success và error cases
- [ ] Documentation updated
- [ ] Website context included in responses
- [ ] Website ID validation implemented
- [ ] Multi-tenancy isolation enforced
- [ ] Cache keys use website_id prefix

## Migration Guide

### Updating Existing APIs

1. **Install response package**:
   ```go
   import "your-project/pkg/response"
   ```

2. **Update controller methods**:
   ```go
   // Old way
   c.JSON(200, gin.H{"data": posts, "total": total})
   
   // New way with website context
   websiteID := c.GetInt64("website_id")
   websiteCtx := &response.WebsiteContext{
       ID:     websiteID,
       Domain: getWebsiteDomain(websiteID),
       Name:   getWebsiteName(websiteID),
   }
   resp := response.SuccessWithMetaAndWebsiteResponse(posts, meta, websiteCtx, c.Request.URL.Path)
   c.JSON(http.StatusOK, resp)
   ```

3. **Update error handling**:
   ```go
   // Old way
   c.JSON(404, gin.H{"error": "Post not found"})
   
   // New way
   resp := response.NotFoundResponse("Post", c.Request.URL.Path)
   c.JSON(resp.Status.Code, resp)
   ```

4. **Update tests**:
   ```go
   var resp response.Response
   json.Unmarshal(w.Body.Bytes(), &resp)
   assert.True(t, resp.Status.Success)
   assert.NotNil(t, resp.Website)  // Test website context
   ```

### Multi-Tenancy Middleware Integration

```go
func WebsiteContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            resp := response.ErrorResponse(400, "Website ID is required", "MISSING_WEBSITE_ID", c.Request.URL.Path, nil)
            c.JSON(resp.Status.Code, resp)
            c.Abort()
            return
        }
        
        id, err := strconv.ParseInt(websiteID, 10, 64)
        if err != nil {
            resp := response.ErrorResponse(400, "Invalid website ID", "INVALID_WEBSITE_ID", c.Request.URL.Path, nil)
            c.JSON(resp.Status.Code, resp)
            c.Abort()
            return
        }
        
        c.Set("website_id", id)
        c.Next()
    }
}
```

## Best Practices

1. **Consistency**: Luôn sử dụng helper functions thay vì tạo response manually
2. **Error Details**: Cung cấp meaningful error messages và codes
3. **Performance**: Include runtime metrics để monitor performance
4. **Security**: Không expose sensitive information trong error details
5. **Logging**: Log tất cả errors với proper context
6. **Testing**: Test cả success và error scenarios
7. **Documentation**: Keep API docs updated với response examples

## Tài liệu liên quan

- [API Overview](./overview.md)
- [Error Handling](./error-handling.md)
- [Authentication](./authentication.md)
- [Rate Limiting](./rate-limiting.md)
- [Testing Guidelines](../best-practices/testing.md)