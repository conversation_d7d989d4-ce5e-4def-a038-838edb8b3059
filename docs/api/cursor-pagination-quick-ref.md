# Cursor Pagination Quick Reference

## Request

```http
GET /api/resource?cursor={cursor}&limit={limit}
```

| Parameter | Required | Default | Max | Description |
|-----------|----------|---------|-----|-------------|
| `cursor` | No | null | - | Base64 encoded cursor string |
| `limit` | No | 20 | 100 | Items per page |

## Response

```json
{
  "data": [...],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": "eyJpZCI6MTAwLCJ0aW1lIjoiMjAyNC0wMS0xNFQxMDozMDowMFoifQ==",
    "has_more": true,
    "has_previous": true,
    "limit": 20
  }
}
```

## Client Implementation

### JavaScript/TypeScript

```typescript
async function* fetchAllPages(endpoint: string) {
  let cursor: string | null = null;
  
  do {
    const params = new URLSearchParams();
    if (cursor) params.append('cursor', cursor);
    params.append('limit', '50');
    
    const response = await fetch(`${endpoint}?${params}`);
    const data = await response.json();
    
    yield data.data;
    cursor = data.meta.next_cursor;
    
  } while (cursor);
}

// Usage
for await (const batch of fetchAllPages('/api/users')) {
  console.log(batch);
}
```

### Go

```go
func FetchAllPages(endpoint string) ([]Resource, error) {
    var allResources []Resource
    cursor := ""
    
    for {
        url := fmt.Sprintf("%s?limit=50", endpoint)
        if cursor != "" {
            url += "&cursor=" + cursor
        }
        
        resp, err := http.Get(url)
        if err != nil {
            return nil, err
        }
        
        var response Response
        json.NewDecoder(resp.Body).Decode(&response)
        resp.Body.Close()
        
        allResources = append(allResources, response.Data...)
        
        if response.Meta.NextCursor == "" {
            break
        }
        cursor = response.Meta.NextCursor
    }
    
    return allResources, nil
}
```

### React Hook

```typescript
import { useState, useCallback } from 'react';

interface PaginationMeta {
  next_cursor: string | null;
  previous_cursor: string | null;
  has_more: boolean;
  has_previous: boolean;
  limit: number;
}

export function useCursorPagination<T>(endpoint: string) {
  const [data, setData] = useState<T[]>([]);
  const [meta, setMeta] = useState<PaginationMeta | null>(null);
  const [loading, setLoading] = useState(false);
  
  const fetchPage = useCallback(async (cursor?: string) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (cursor) params.append('cursor', cursor);
      params.append('limit', '20');
      
      const response = await fetch(`${endpoint}?${params}`);
      const result = await response.json();
      
      setData(prev => cursor ? [...prev, ...result.data] : result.data);
      setMeta(result.meta);
    } finally {
      setLoading(false);
    }
  }, [endpoint]);
  
  const loadMore = useCallback(() => {
    if (meta?.has_more && meta.next_cursor) {
      fetchPage(meta.next_cursor);
    }
  }, [meta, fetchPage]);
  
  const reset = useCallback(() => {
    setData([]);
    setMeta(null);
    fetchPage();
  }, [fetchPage]);
  
  return { data, meta, loading, loadMore, reset, hasMore: meta?.has_more ?? false };
}
```

## Common Patterns

### Infinite Scroll

```typescript
const { data, loading, hasMore, loadMore } = useCursorPagination('/api/posts');

return (
  <InfiniteScroll
    dataLength={data.length}
    next={loadMore}
    hasMore={hasMore}
    loader={<Spinner />}
  >
    {data.map(item => <Item key={item.id} {...item} />)}
  </InfiniteScroll>
);
```

### Load More Button

```typescript
const { data, loading, hasMore, loadMore } = useCursorPagination('/api/posts');

return (
  <>
    <List items={data} />
    {hasMore && (
      <Button onClick={loadMore} disabled={loading}>
        {loading ? 'Loading...' : 'Load More'}
      </Button>
    )}
  </>
);
```

### Bidirectional Navigation

```typescript
const [cursor, setCursor] = useState<string | null>(null);
const { data, meta } = useQuery(['posts', cursor], () => 
  fetchPosts(cursor)
);

return (
  <>
    <Button 
      disabled={!meta?.has_previous}
      onClick={() => setCursor(meta?.previous_cursor || null)}
    >
      Previous
    </Button>
    
    <PostList posts={data} />
    
    <Button 
      disabled={!meta?.has_more}
      onClick={() => setCursor(meta?.next_cursor || null)}
    >
      Next
    </Button>
  </>
);
```

## Error Handling

```typescript
try {
  const response = await fetch(`/api/resource?cursor=${invalidCursor}`);
  if (!response.ok) {
    const error = await response.json();
    
    switch (error.error.code) {
      case 'INVALID_CURSOR':
        // Reset to first page
        return fetchPage();
        
      case 'CURSOR_EXPIRED':
        // Notify user and reset
        toast.error('Session expired. Starting from beginning.');
        return fetchPage();
        
      default:
        throw new Error(error.error.message);
    }
  }
} catch (err) {
  console.error('Pagination error:', err);
}
```

## Migration Checklist

- [ ] Replace `page` with `cursor` parameter
- [ ] Replace `page_size` with `limit` parameter  
- [ ] Update response parsing to use `meta` instead of `pagination`
- [ ] Remove any UI showing total pages or allowing page jumps
- [ ] Implement proper cursor storage for navigation
- [ ] Add error handling for invalid/expired cursors
- [ ] Test with large datasets to verify performance improvements

## See Also

- [Full Cursor Pagination Guide](./cursor-pagination.md)
- [API Response Standards](./response-standard.md)