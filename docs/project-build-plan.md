# Kế Hoạch Xây Dựng Dự Án

## Tổng Quan

Tài liệu này trình bày kế hoạch xây dựng dự án Blog API v3 với kiến trúc module, tập trung vào việc ngăn chặn circular dependencies và thiết lập ranh giới module rõ ràng.

## Mục Tiêu Kiến Trúc

1. **Thiết Kế Module**: Tách biệt rõ ràng giữa pkg (thư viện dùng chung) và các module internal
2. **Không Có Circular Dependencies**: Áp dụng quy tắc phụ thuộc nghiêm ngặt
3. **Phát Triển API-First**: Bắt đầu với mock data để kiểm tra API contracts
4. **Clean Architecture**: Thiết kế hướng domain với ranh giới rõ ràng

## Hệ Thống Phân Cấp Phụ Thuộc Module

```
┌─────────────────────────────────────────────────────────┐
│                    API Layer (HTTP)                      │
├─────────────────────────────────────────────────────────┤
│                   Module Layer                           │
│  ┌─────────┐  ┌──────────┐  ┌─────────┐  ┌──────────┐ │
│  │ Website │  │   User   │  │  Auth   │  │ Tenant   │ │
│  └────┬────┘  └────┬─────┘  └────┬────┘  └────┬─────┘ │
│       │            │              │             │        │
│  ┌────┴────────────┴──────────────┴─────────────┴────┐ │
│  │              Onboarding Module                     │ │
│  └────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    pkg (Shared Libraries)                │
│  ┌─────────┐  ┌──────────┐  ┌─────────┐  ┌──────────┐ │
│  │Database │  │   HTTP   │  │  Utils  │  │ Validator│ │
│  └─────────┘  └──────────┘  └─────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────┘
```

## Phân Chia Các Task Xây Dựng

### Giai Đoạn 1: Nền Tảng (tầng pkg)

#### Task 1: Thiết Lập Cấu Trúc Dự Án
- Tạo go.mod với tên module phù hợp
- Thiết lập cấu trúc thư mục
- Cấu hình .gitignore
- Tạo Makefile cho các task thường dùng

#### Task 2: Package Database
- Thiết lập interface kết nối database
- Triển khai hỗ trợ MySQL driver
- Thêm connection pooling
- Tạo tích hợp migration

#### Task 3: Package HTTP  
- Tạo HTTP server wrapper
- Thiết lập interface middleware
- Thêm các helper request/response
- Triển khai xử lý lỗi

#### Task 4: Package Utils
- Tạo các tiện ích chung (string, time, v.v.)
- Thêm configuration loader
- Thiết lập interface logging
- Tạo ID generator

#### Task 5: Package Validator
- Thiết lập interface validation
- Tích hợp go-playground/validator
- Tạo các rule validation tùy chỉnh
- Thêm format lỗi

### Giai Đoạn 2: Các Module Chính

#### Task 6: Module Auth
- Định nghĩa các interface auth (chưa triển khai)
- Tạo các model auth (User, Token, v.v.)
- Thiết lập các route API auth với mock response
- Tài liệu hóa auth flow

#### Task 7: Module User
- Định nghĩa các interface user
- Tạo các model user (Account, Profile)
- Thiết lập các route API user với mock response
- Thêm các rule validation cho user

#### Task 8: Module Tenant
- Định nghĩa các interface tenant
- Tạo các model tenant
- Thiết lập các route API tenant với mock response
- Thêm context multi-tenancy

#### Task 9: Module Website
- Định nghĩa các interface website
- Tạo các model website
- Thiết lập các route API website với mock response
- Thêm cấu hình website

#### Task 10: Module Onboarding
- Định nghĩa các interface onboarding
- Tạo các model flow onboarding
- Thiết lập các route API onboarding với mock response
- Tích hợp với các module khác (chỉ interface)

### Giai Đoạn 3: Tích Hợp & Kiểm Thử

#### Task 11: Tích Hợp API
- Kết nối tất cả các module
- Thiết lập đăng ký route
- Thêm tài liệu API
- Tạo Postman collection

#### Task 12: Kiểm Tra Circular Dependency
- Chạy phân tích go mod graph
- Tạo visualization phụ thuộc
- Sửa các circular import
- Tài liệu hóa ranh giới module

#### Task 13: Triển Khai Mock Data
- Tạo các generator mock data
- Triển khai in-memory repositories
- Thiết lập test fixtures
- Thêm script seed data

## Thiết Kế Interface Module

### Các Interface Module Auth
```go
// internal/modules/auth/interfaces.go
type AuthService interface {
    Login(email, password string) (*Token, error)
    Logout(token string) error
    Refresh(refreshToken string) (*Token, error)
    Validate(token string) (*Claims, error)
}

type UserProvider interface {
    GetByEmail(email string) (*User, error)
    GetByID(id string) (*User, error)
}
```

### Các Interface Module User
```go
// internal/modules/user/interfaces.go
type UserService interface {
    Create(user *CreateUserRequest) (*User, error)
    Update(id string, user *UpdateUserRequest) (*User, error)
    Delete(id string) error
    GetByID(id string) (*User, error)
    List(filter *UserFilter) ([]*User, error)
}

type UserRepository interface {
    Save(user *User) error
    FindByID(id string) (*User, error)
    FindByEmail(email string) (*User, error)
    Delete(id string) error
}
```

### Ngăn Chặn Circular Dependencies

1. **Quy Tắc Phụ Thuộc**:
   - Các module pkg không được import từ internal
   - Các module internal có thể import từ pkg
   - Các module internal giao tiếp qua interface
   - Không import trực tiếp giữa các module

2. **Shared Types**:
   - Các type chung đặt trong pkg/types
   - Domain model giữ trong module riêng
   - DTOs cho giao tiếp giữa các module

3. **Hệ Thống Event**:
   - Sử dụng event bus cho giao tiếp module
   - Module publish event, không gọi trực tiếp
   - Loose coupling giữa các module

## Kế Hoạch API Endpoints

### Module Auth
- POST   /api/cms/v1/auth/login
- POST   /api/cms/v1/auth/logout  
- POST   /api/cms/v1/auth/refresh
- GET    /api/cms/v1/auth/validate

### Module User
- GET    /api/cms/v1/users
- GET    /api/cms/v1/users/:id
- POST   /api/cms/v1/users
- PUT    /api/cms/v1/users/:id
- DELETE /api/cms/v1/users/:id

### Module Tenant
- GET    /api/cms/v1/tenants
- GET    /api/cms/v1/tenants/:id
- POST   /api/cms/v1/tenants
- PUT    /api/cms/v1/tenants/:id
- DELETE /api/cms/v1/tenants/:id

### Module Website
- GET    /api/cms/v1/websites
- GET    /api/cms/v1/websites/:id
- POST   /api/cms/v1/websites
- PUT    /api/cms/v1/websites/:id
- DELETE /api/cms/v1/websites/:id

### Module Onboarding
- GET    /api/cms/v1/onboarding/status
- POST   /api/cms/v1/onboarding/start
- POST   /api/cms/v1/onboarding/complete
- GET    /api/cms/v1/onboarding/steps

## Tiêu Chí Thành Công

1. **Không Có Circular Dependencies**: `go mod graph` hiển thị phân cấp sạch
2. **Tất Cả API Trả Về Mock Data**: Có thể test toàn bộ API
3. **Ranh Giới Module Rõ Ràng**: Interface rõ ràng giữa các module
4. **API Được Tài Liệu Hóa**: Tài liệu OpenAPI/Swagger
5. **Test Pass**: Unit test cho mỗi module

## Ước Tính Thời Gian

- Giai đoạn 1 (Nền tảng): 2-3 ngày
- Giai đoạn 2 (Module chính): 4-5 ngày  
- Giai đoạn 3 (Tích hợp): 2-3 ngày
- Tổng cộng: ~10 ngày

## Các Bước Tiếp Theo

1. Tạo ticket task chi tiết bằng công cụ backlog
2. Bắt đầu với các task nền tảng Giai đoạn 1
3. Kiểm tra phụ thuộc thường xuyên sau mỗi module
4. Tài liệu hóa API trong quá trình phát triển