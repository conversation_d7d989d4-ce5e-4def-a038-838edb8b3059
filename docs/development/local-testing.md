# Local Testing Environment - Tà<PERSON> liệu Tiếng Việt

## Tổng quan

Local Testing Environment cung cấp môi trường phát triển và testing hoàn chỉnh cho Blog API v3, bao gồm database setup, email testing với MailCatcher, timezone handling, và distributed tracing với <PERSON>.

## Mục tiêu

- **Complete Local Environment**: Môi trường phát triển đầy đủ
- **Email Testing**: Test email functionality với MailCatcher
- **Timezone Support**: Testing multiple timezone scenarios
- **Distributed Tracing**: Debug và monitor với Jaeger
- **Database Testing**: Local database setup và seeding
- **Service Integration**: Test tất cả services locally

## Local Development Stack

### Core Services Architecture

```mermaid
flowchart TD
    A[Blog API v3] --> B[MySQL Database]
    A --> C[Redis Cache]
    A --> D[MailCatcher]
    A --> E[Jaeger Tracing]
    A --> F[MinIO Storage]
    
    B --> B1[Main Database]
    B --> B2[Test Database]
    
    C --> C1[Application Cache]
    C --> C2[Session Storage]
    C --> C3[Queue Storage]
    
    D --> D1[SMTP Server]
    D --> D2[Web Interface]
    
    E --> E1[Jaeger Agent]
    E --> E2[Jaeger Collector]
    E --> E3[Jaeger UI]
    
    F --> F1[File Storage]
    F --> F2[Image Processing]
```

## MailCatcher Integration

### 1. MailCatcher Setup

#### Installation
```bash
# Install MailCatcher
gem install mailcatcher

# Or using Docker
docker run -d \
  --name mailcatcher \
  -p 1080:1080 \
  -p 1025:1025 \
  schickling/mailcatcher
```

#### Configuration
```yaml
# config/mail.yml
mail:
  driver: "smtp"
  host: "localhost"
  port: 1025
  username: ""
  password: ""
  encryption: "none"
  from:
    address: "<EMAIL>"
    name: "Blog API v3"
  
  # MailCatcher specific settings
  mailcatcher:
    enabled: true
    web_interface: "http://localhost:1080"
    smtp_port: 1025
```

### 2. Email Testing Workflow

```mermaid
sequenceDiagram
    participant App as Blog API
    participant MC as MailCatcher
    participant Web as Web Interface
    participant Dev as Developer
    
    App->>MC: Send email via SMTP (port 1025)
    MC->>MC: Capture email
    MC->>Web: Display in web interface
    
    Dev->>Web: Open http://localhost:1080
    Web->>Dev: Show captured emails
    
    Dev->>Web: View email content
    Web->>Dev: Display HTML/Text versions
    
    Dev->>Web: Test email links
    Web->>Dev: Validate email functionality
```

#### Email Testing Features
- **Email Capture**: Catch all outgoing emails
- **HTML/Text Preview**: View both email formats
- **Attachment Support**: Test file attachments
- **Email History**: Review all sent emails
- **API Access**: Programmatic email testing

### 3. Email Testing Scenarios

#### User Registration Email
```go
// Test registration email
func TestUserRegistrationEmail(t *testing.T) {
    user := &models.User{
        Email: "<EMAIL>",
        Name:  "Test User",
    }
    
    // Send registration email
    err := emailService.SendRegistrationEmail(user)
    assert.NoError(t, err)
    
    // Verify email was captured by MailCatcher
    emails := mailcatcher.GetEmails()
    assert.Len(t, emails, 1)
    assert.Equal(t, "<EMAIL>", emails[0].To)
    assert.Contains(t, emails[0].Subject, "Welcome")
}
```

#### Password Reset Email
```go
func TestPasswordResetEmail(t *testing.T) {
    user := &models.User{Email: "<EMAIL>"}
    token := "reset-token-123"
    
    err := emailService.SendPasswordResetEmail(user, token)
    assert.NoError(t, err)
    
    email := mailcatcher.GetLatestEmail()
    assert.Contains(t, email.Body, token)
    assert.Contains(t, email.Body, "reset your password")
}
```

### 4. MailCatcher API Integration

#### Get All Emails
```bash
curl http://localhost:1080/messages
```

#### Get Specific Email
```bash
curl http://localhost:1080/messages/1.json
```

#### Clear All Emails
```bash
curl -X DELETE http://localhost:1080/messages
```

#### Email Testing Helper
```go
package testing

import (
    "encoding/json"
    "fmt"
    "net/http"
)

type MailCatcherClient struct {
    BaseURL string
}

type Email struct {
    ID      int    `json:"id"`
    From    string `json:"sender"`
    To      string `json:"recipients"`
    Subject string `json:"subject"`
    Body    string `json:"source"`
}

func NewMailCatcherClient() *MailCatcherClient {
    return &MailCatcherClient{
        BaseURL: "http://localhost:1080",
    }
}

func (m *MailCatcherClient) GetEmails() ([]Email, error) {
    resp, err := http.Get(m.BaseURL + "/messages")
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var emails []Email
    return emails, json.NewDecoder(resp.Body).Decode(&emails)
}

func (m *MailCatcherClient) GetLatestEmail() (*Email, error) {
    emails, err := m.GetEmails()
    if err != nil || len(emails) == 0 {
        return nil, fmt.Errorf("no emails found")
    }
    return &emails[len(emails)-1], nil
}

func (m *MailCatcherClient) ClearEmails() error {
    req, _ := http.NewRequest("DELETE", m.BaseURL+"/messages", nil)
    _, err := http.DefaultClient.Do(req)
    return err
}
```

## Timezone Support Testing

### 1. Timezone Configuration

```yaml
# config/timezone.yml
timezone:
  default: "UTC"
  user_detection: true
  
  supported_timezones:
    - "UTC"
    - "America/New_York"      # EST/EDT
    - "America/Los_Angeles"   # PST/PDT
    - "Europe/London"         # GMT/BST
    - "Europe/Paris"          # CET/CEST
    - "Asia/Tokyo"            # JST
    - "Asia/Shanghai"         # CST
    - "Asia/Ho_Chi_Minh"      # ICT
    - "Australia/Sydney"      # AEDT/AEST
    
  testing:
    mock_timezone: "America/New_York"
    test_scenarios:
      - timezone: "UTC"
        description: "Base timezone"
      - timezone: "Asia/Ho_Chi_Minh"
        description: "Vietnam timezone (+7)"
      - timezone: "America/New_York"
        description: "US Eastern timezone"
      - timezone: "Europe/London"
        description: "UK timezone with DST"
```

### 2. Timezone Testing Framework

```go
package timezone

import (
    "context"
    "time"
)

type TimezoneTestCase struct {
    Name        string
    Timezone    string
    InputTime   time.Time
    ExpectedUTC time.Time
    ExpectedLocal string
}

func RunTimezoneTests(t *testing.T) {
    testCases := []TimezoneTestCase{
        {
            Name:        "Vietnam Timezone",
            Timezone:    "Asia/Ho_Chi_Minh",
            InputTime:   time.Date(2024, 7, 15, 14, 30, 0, 0, time.UTC),
            ExpectedLocal: "2024-07-15 21:30:00",
        },
        {
            Name:        "US Eastern Timezone",
            Timezone:    "America/New_York",
            InputTime:   time.Date(2024, 7, 15, 14, 30, 0, 0, time.UTC),
            ExpectedLocal: "2024-07-15 10:30:00",
        },
    }
    
    for _, tc := range testCases {
        t.Run(tc.Name, func(t *testing.T) {
            tz, _ := time.LoadLocation(tc.Timezone)
            localTime := tc.InputTime.In(tz)
            
            assert.Equal(t, tc.ExpectedLocal, 
                localTime.Format("2006-01-02 15:04:05"))
        })
    }
}
```

### 3. Post Scheduling with Timezones

```mermaid
sequenceDiagram
    participant User as User (Vietnam)
    participant API as Blog API
    participant Scheduler as Task Scheduler
    participant DB as Database
    
    User->>API: Schedule post for 9:00 AM Vietnam time
    Note over User,API: User timezone: Asia/Ho_Chi_Minh
    
    API->>API: Convert to UTC (2:00 AM UTC)
    API->>DB: Store scheduled_at = 2024-07-16 02:00:00 UTC
    API->>Scheduler: Schedule task for UTC time
    
    Note over Scheduler: Wait until scheduled time
    
    Scheduler->>API: Trigger at 2024-07-16 02:00:00 UTC
    API->>API: Publish post
    API->>User: Notify published (9:00 AM Vietnam time)
```

### 4. Timezone Testing Utilities

```go
// Test helper for timezone scenarios
func TestSchedulingAcrossTimezones(t *testing.T) {
    scenarios := []struct {
        userTimezone    string
        scheduledTime   string
        expectedUTC     string
    }{
        {
            userTimezone:  "Asia/Ho_Chi_Minh",
            scheduledTime: "2024-07-16 09:00:00",
            expectedUTC:   "2024-07-16 02:00:00",
        },
        {
            userTimezone:  "America/New_York",
            scheduledTime: "2024-07-16 09:00:00",
            expectedUTC:   "2024-07-16 13:00:00",
        },
    }
    
    for _, scenario := range scenarios {
        t.Run(scenario.userTimezone, func(t *testing.T) {
            tz, _ := time.LoadLocation(scenario.userTimezone)
            
            // Parse user input time
            userTime, _ := time.ParseInLocation(
                "2006-01-02 15:04:05", 
                scenario.scheduledTime, 
                tz)
            
            // Convert to UTC
            utcTime := userTime.UTC()
            
            assert.Equal(t, scenario.expectedUTC, 
                utcTime.Format("2006-01-02 15:04:05"))
        })
    }
}
```

## Jaeger Distributed Tracing

### 1. Jaeger Setup

#### Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # jaeger.thrift
      - "6831:6831/udp"  # jaeger.thrift over UDP
      - "6832:6832/udp"  # jaeger.thrift over UDP
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    networks:
      - blog-api-network

  blog-api:
    build: .
    ports:
      - "9077:9077"
    environment:
      - APP_PORT=9077
      - JAEGER_AGENT_HOST=jaeger
      - JAEGER_AGENT_PORT=6831
      - JAEGER_SERVICE_NAME=blog-api-v3
    depends_on:
      - jaeger
      - mysql
      - redis
    networks:
      - blog-api-network

networks:
  blog-api-network:
    driver: bridge
```

### 2. Tracing Configuration

```yaml
# config/tracing.yml
tracing:
  enabled: true
  service_name: "blog-api-v3"
  
  jaeger:
    agent_host: "localhost"
    agent_port: 6831
    collector_endpoint: "http://localhost:14268/api/traces"
    
  sampling:
    type: "const"
    param: 1.0  # Sample 100% in development
    
  reporter:
    log_spans: true
    buffer_flush_interval: "1s"
    
  tags:
    environment: "development"
    version: "3.0.0"
    region: "local"
```

### 3. Tracing Implementation

#### Initialize Tracing
```go
package tracing

import (
    "github.com/uber/jaeger-client-go"
    "github.com/uber/jaeger-client-go/config"
    "github.com/opentracing/opentracing-go"
)

func InitTracer(serviceName string) (opentracing.Tracer, error) {
    cfg := config.Configuration{
        ServiceName: serviceName,
        Sampler: &config.SamplerConfig{
            Type:  jaeger.SamplerTypeConst,
            Param: 1,
        },
        Reporter: &config.ReporterConfig{
            LogSpans:            true,
            BufferFlushInterval: 1 * time.Second,
            LocalAgentHostPort:  "localhost:6831",
        },
    }
    
    tracer, _, err := cfg.NewTracer()
    if err != nil {
        return nil, err
    }
    
    opentracing.SetGlobalTracer(tracer)
    return tracer, nil
}
```

#### HTTP Middleware for Tracing
```go
func TracingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        spanName := fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
        span := opentracing.StartSpan(spanName)
        defer span.Finish()
        
        // Add request details to span
        span.SetTag("http.method", c.Request.Method)
        span.SetTag("http.url", c.Request.URL.String())
        span.SetTag("http.user_agent", c.Request.UserAgent())
        
        // Store span in context
        ctx := opentracing.ContextWithSpan(c.Request.Context(), span)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
        
        // Add response details
        span.SetTag("http.status_code", c.Writer.Status())
        if c.Writer.Status() >= 400 {
            span.SetTag("error", true)
        }
    }
}
```

### 4. Service Tracing

#### Database Operations
```go
func (r *PostRepository) GetByID(ctx context.Context, id uint) (*Post, error) {
    span, ctx := opentracing.StartSpanFromContext(ctx, "PostRepository.GetByID")
    defer span.Finish()
    
    span.SetTag("db.operation", "select")
    span.SetTag("db.table", "posts")
    span.SetTag("post.id", id)
    
    var post Post
    err := r.db.WithContext(ctx).First(&post, id).Error
    if err != nil {
        span.SetTag("error", true)
        span.LogFields(
            log.String("error.kind", "db_error"),
            log.String("error.message", err.Error()),
        )
        return nil, err
    }
    
    span.SetTag("post.slug", post.Slug)
    span.SetTag("post.status", post.Status)
    
    return &post, nil
}
```

#### External Service Calls
```go
func (s *EmailService) SendEmail(ctx context.Context, email *Email) error {
    span, ctx := opentracing.StartSpanFromContext(ctx, "EmailService.SendEmail")
    defer span.Finish()
    
    span.SetTag("email.to", email.To)
    span.SetTag("email.subject", email.Subject)
    span.SetTag("email.template", email.Template)
    
    // Send email logic
    err := s.smtp.Send(ctx, email)
    if err != nil {
        span.SetTag("error", true)
        span.LogFields(
            log.String("error.kind", "smtp_error"),
            log.String("error.message", err.Error()),
        )
        return err
    }
    
    span.LogFields(
        log.String("event", "email_sent"),
        log.String("message_id", email.MessageID),
    )
    
    return nil
}
```

### 5. Trace Analysis

#### Common Trace Scenarios
```mermaid
flowchart LR
    A[HTTP Request] --> B[Authentication]
    B --> C[Authorization]
    C --> D[Business Logic]
    D --> E[Database Query]
    D --> F[Cache Check]
    D --> G[External API]
    E --> H[Response]
    F --> H
    G --> H
    
    style A fill:#e3f2fd
    style H fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#fce4ec
```

#### Performance Analysis
- **Request Duration**: End-to-end request timing
- **Database Performance**: Query execution times
- **Cache Hit Rates**: Cache effectiveness analysis
- **External Dependencies**: Third-party service performance
- **Error Tracking**: Error rate và patterns

## Complete Local Testing Setup

### 1. Docker Compose for Full Stack

```yaml
# docker-compose.local.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: blog_api_v3
      MYSQL_USER: blog_user
      MYSQL_PASSWORD: blog_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/migrations:/docker-entrypoint-initdb.d

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  mailcatcher:
    image: schickling/mailcatcher
    ports:
      - "1080:1080"  # Web UI
      - "1025:1025"  # SMTP

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "6831:6831/udp"  # Jaeger agent
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data

volumes:
  mysql_data:
  redis_data:
  minio_data:
```

### 2. Testing Scripts

#### Start Local Environment
```bash
#!/bin/bash
# scripts/start-local.sh

echo "Starting Blog API v3 Local Environment..."

# Start services
docker-compose -f docker-compose.local.yml up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Run database migrations
echo "Running database migrations..."
go run cmd/migrate/main.go

# Seed test data
echo "Seeding test data..."
go run cmd/seed/main.go

# Start the API
echo "Starting Blog API..."
go run cmd/api/main.go &

echo "Environment ready!"
echo "- API: http://localhost:9077 (APP_PORT=9077)"
echo "- MailCatcher: http://localhost:1080"
echo "- Jaeger UI: http://localhost:16686"
echo "- MinIO Console: http://localhost:9001"
```

#### Run Tests
```bash
#!/bin/bash
# scripts/test-local.sh

echo "Running local tests..."

# Clear MailCatcher
curl -X DELETE http://localhost:1080/messages

# Run tests
go test ./... -v -race -cover

# Check email tests
echo "Checking captured emails..."
curl http://localhost:1080/messages | jq .

echo "Tests completed!"
```

### 3. Integration Testing

#### End-to-End Test Example
```go
func TestCompleteUserFlow(t *testing.T) {
    // Clear previous emails
    mailcatcher.ClearEmails()
    
    // Start tracing
    span := opentracing.StartSpan("TestCompleteUserFlow")
    defer span.Finish()
    ctx := opentracing.ContextWithSpan(context.Background(), span)
    
    // 1. Register user
    user := &RegisterRequest{
        Name:     "Test User",
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    resp := testClient.POST("/auth/register").WithJSON(user).Expect().Status(201)
    
    // 2. Verify registration email
    emails, _ := mailcatcher.GetEmails()
    assert.Len(t, emails, 1)
    assert.Contains(t, emails[0].Subject, "Welcome")
    
    // 3. Login user
    loginResp := testClient.POST("/auth/login").WithJSON(map[string]string{
        "email":    user.Email,
        "password": user.Password,
    }).Expect().Status(200)
    
    token := loginResp.JSON().Object().Value("data").Object().Value("access_token").String().Raw()
    
    // 4. Create blog post
    post := &CreatePostRequest{
        Title:   "Test Post",
        Content: "This is a test post content",
        Status:  "published",
    }
    
    testClient.POST("/cms/v1/posts").
        WithHeader("Authorization", "Bearer "+token).
        WithJSON(post).
        Expect().Status(201)
    
    // 5. Verify post appears in public API
    testClient.GET("/api/cms/v1/posts").
        Expect().Status(200).
        JSON().Object().Value("data").Array().Length().Equal(1)
    
    // 6. Check traces in Jaeger
    // Traces should be visible at http://localhost:16686
}
```

## Testing Best Practices

### 1. Test Data Management
- **Isolated Test Data**: Each test uses clean data
- **Database Transactions**: Rollback after each test
- **Seeded Data**: Consistent test data setup
- **Data Factories**: Generate test data programmatically

### 2. Email Testing
- **Clear State**: Clear MailCatcher before tests
- **Content Validation**: Verify email content và links
- **Template Testing**: Test all email templates
- **Delivery Testing**: Ensure emails are sent

### 3. Timezone Testing
- **Multiple Scenarios**: Test various timezone combinations
- **DST Handling**: Test daylight saving transitions
- **User Preferences**: Test user-specific timezones
- **API Responses**: Verify timezone formatting

### 4. Performance Testing
- **Trace Analysis**: Use Jaeger for performance insights
- **Load Testing**: Test under various loads
- **Memory Profiling**: Monitor memory usage
- **Database Performance**: Analyze query performance

## Troubleshooting

### Common Issues

#### MailCatcher Connection Issues
```bash
# Check if MailCatcher is running
curl http://localhost:1080

# Restart MailCatcher
docker-compose restart mailcatcher

# Check SMTP connection
telnet localhost 1025
```

#### Jaeger Tracing Issues
```bash
# Check Jaeger UI
curl http://localhost:16686

# Verify agent connection
netstat -an | grep 6831

# Check service logs
docker-compose logs jaeger
```

#### Timezone Issues
```go
// Debug timezone conversion
func debugTimezone(t time.Time, tz string) {
    location, _ := time.LoadLocation(tz)
    local := t.In(location)
    
    fmt.Printf("UTC: %s\n", t.Format(time.RFC3339))
    fmt.Printf("%s: %s\n", tz, local.Format(time.RFC3339))
}
```

## Tài liệu liên quan

- [Database Migrations](../database/migrations.md)
- [Database Seeding](../database/seeding.md)
- [Auth Module](../modules/auth.md)
- [Blog Module](../modules/blog.md)
- [Email Integration](../integrations/email.md)
- [Performance Monitoring](../best-practices/performance.md)