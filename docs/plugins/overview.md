# Plugin System Overview - <PERSON>ệ thống Plugin

## Tổng quan

Plugin System cung cấp **concrete implementations** cho các interfaces được định nghĩa bởi Core Modules. Plugins cho phép integrate với third-party services mà không cần modify core code, đảm bảo system có thể mở rộng và maintain dễ dàng.

> **🏗️ Architecture**: Xem [Module vs Plugin Boundaries](../architecture/module-vs-plugin-boundaries.md) để hiểu rõ sự khác biệt giữa Core Modules và Plugins.

## Mục tiêu

- **Interface Implementation**: Implement core module interfaces cho third-party services
- **Pluggable Architecture**: Swap implementations mà không affect core logic
- **Third-party Integration**: Seamless integration với external services
- **Configuration Driven**: Plugin selection thông qua configuration
- **Hot Reload**: Load/unload plugins without service restart
- **Multi-tenancy**: Plugin permissions và configuration per tenant

## Kiến trúc Plugin System

### 1. Plugin Architecture Overview

```mermaid
flowchart TD
    subgraph "Plugin System Core"
        A[Plugin Manager] --> B[Plugin Loader]
        A --> C[Plugin Registry]
        A --> D[Dependency Resolver]
        A --> E[Security Manager]
        A --> F[Event Dispatcher]
    end
    
    subgraph "Plugin Categories"
        G[Email Plugins] --> G1[SendGrid Plugin]
        G --> G2[Mailgun Plugin]
        G --> G3[SES Plugin]
        G --> G4[SMTP Plugin]

        H[Payment Plugins] --> H1[Stripe Plugin]
        H --> H2[PayPal Plugin]
        H --> H3[VNPay Plugin]
        H --> H4[Bank Transfer Plugin]

        I[Storage Plugins] --> I1[S3 Plugin]
        I --> I2[GCS Plugin]
        I --> I3[Local Storage Plugin]

        J[Analytics Plugins] --> J1[Google Analytics Plugin]
        J --> J2[Mixpanel Plugin]
        J --> J3[Custom Analytics Plugin]
    end
    
    subgraph "Plugin Lifecycle"
        K[Install] --> L[Load]
        L --> M[Initialize]
        M --> N[Register with Module]
        N --> O[Execute]
        O --> P[Unload]
    end
    
    subgraph "Security & Isolation"
        P[Permission System] --> P1[API Access Control]
        P --> P2[Resource Limits]
        P --> P3[Network Restrictions]
        
        Q[Sandboxing] --> Q1[Memory Limits]
        Q --> Q2[CPU Limits]
        Q --> Q3[File System Access]
    end
```

### 2. Plugin Structure

```
plugins/
├── email/                          # Email service implementations
│   ├── sendgrid/
│   ├── mailgun/
│   ├── ses/
│   └── smtp/
├── payment/                        # Payment gateway implementations
│   ├── stripe/
│   ├── paypal/
│   ├── vnpay/
│   └── bank-transfer/
├── storage/                        # Storage service implementations
│   ├── s3/
│   ├── gcs/
│   ├── azure-blob/
│   └── local/
├── analytics/                      # Analytics service implementations
│   ├── google-analytics/
│   ├── mixpanel/
│   ├── segment/
│   └── custom/
├── auth/                          # Authentication provider implementations
│   ├── oauth2/
│   ├── saml/
│   ├── ldap/
│   └── social/
└── notification/                   # Notification channel implementations
    ├── slack/
    ├── discord/
    ├── teams/
    └── webhook/
├── themes/                         # Theme and UI plugins
│   ├── admin-themes/
│   ├── blog-themes/
│   └── landing-page-themes/
└── custom/                         # Custom tenant-specific plugins
    ├── tenant-1/
    └── tenant-2/
```

## Plugin Interface Implementation

### Core Principle: Interface-Based Architecture

Plugins **implement interfaces** được định nghĩa bởi Core Modules. Điều này đảm bảo:
- **Consistency**: Tất cả plugins cùng loại có cùng interface
- **Interchangeability**: Có thể swap plugins mà không affect core logic
- **Testability**: Dễ dàng mock interfaces cho testing

### Example: Email Plugin Implementation

```go
// Core Module defines interface
type EmailService interface {
    SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error)
    SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error)
    HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)
    GetProviderInfo() *ProviderInfo
}

// Plugin implements interface
type SendGridPlugin struct {
    client *sendgrid.Client
    config *SendGridConfig
}

func (p *SendGridPlugin) SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error) {
    // SendGrid-specific implementation
    return p.sendViaSendGrid(email)
}

func (p *SendGridPlugin) SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error) {
    // SendGrid template implementation
    return p.sendTemplateViaSendGrid(template)
}

// Plugin registration
func init() {
    email.RegisterPlugin("sendgrid", &SendGridPlugin{})
}
```

### Example: Payment Plugin Implementation

```go
// Core Module defines interface
type PaymentService interface {
    ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error)
    CreateSubscription(ctx context.Context, subscription *SubscriptionRequest) (*SubscriptionResult, error)
    HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)
    GetProviderInfo() *ProviderInfo
}

// Plugin implements interface
type StripePlugin struct {
    client *stripe.Client
    config *StripeConfig
}

func (p *StripePlugin) ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error) {
    // Stripe-specific implementation
    return p.processViaStripe(payment)
}

// Plugin registration
func init() {
    payment.RegisterPlugin("stripe", &StripePlugin{})
}
```

### Plugin Registry Pattern

```go
// Module maintains plugin registry
type PluginRegistry interface {
    RegisterPlugin(name string, plugin Plugin) error
    GetPlugin(name string) (Plugin, error)
    GetActivePlugin() (Plugin, error)
    ListPlugins() []PluginInfo
    SetActivePlugin(name string) error
}

// Usage in module
func (s *EmailService) SendEmail(email *EmailMessage) error {
    plugin, err := s.registry.GetActivePlugin()
    if err != nil {
        return err
    }

    return plugin.SendEmail(context.Background(), email)
}
```

## Plugin Definition

### 1. Plugin Manifest (plugin.yaml)

```yaml
# Plugin Manifest
name: "google-analytics"
version: "1.0.0"
description: "Google Analytics integration for Blog API v3"
author: "Blog API Team"
license: "MIT"
homepage: "https://github.com/blog-api/plugins/google-analytics"

# Plugin Type
type: "feature"
category: "analytics"
tags: ["analytics", "google", "tracking"]

# Compatibility
engine_version: ">=3.0.0"
api_version: "v1"

# Dependencies
dependencies:
  required:
    - "core/http-client": ">=1.0.0"
  optional:
    - "features/cache": ">=1.0.0"

# Permissions
permissions:
  api:
    - "analytics.read"
    - "analytics.write"
  resources:
    - "posts"
    - "users"
  external:
    - "https://www.google-analytics.com/*"
    - "https://analytics.google.com/*"

# Configuration Schema
config_schema:
  type: "object"
  properties:
    tracking_id:
      type: "string"
      required: true
      description: "Google Analytics Tracking ID"
    measurement_id:
      type: "string"
      required: false
      description: "Google Analytics 4 Measurement ID"
    enhanced_ecommerce:
      type: "boolean"
      default: false
      description: "Enable Enhanced Ecommerce tracking"
  required: ["tracking_id"]

# Hooks
hooks:
  install: "scripts/install.go"
  uninstall: "scripts/uninstall.go"
  activate: "scripts/activate.go"
  deactivate: "scripts/deactivate.go"

# Entry Points
entry_points:
  api:
    - path: "/analytics"
      handler: "handlers/analytics.go"
  webhooks:
    - path: "/webhook/google-analytics"
      handler: "handlers/webhook.go"
  cron:
    - schedule: "0 2 * * *"
      handler: "handlers/daily_report.go"

# Resources
resources:
  memory_limit: "128MB"
  cpu_limit: "100m"
  disk_limit: "50MB"
  network: true
  file_system:
    read_only: ["/app/templates", "/app/assets"]
    read_write: ["/tmp", "/app/cache"]
```

### 2. Plugin Implementation

```go
package main

import (
    "context"
    "fmt"
    
    "github.com/blog-api/plugin-sdk/v1"
)

// Plugin struct implements the Plugin interface
type GoogleAnalyticsPlugin struct {
    sdk.BasePlugin
    config *Config
    client *GAClient
}

type Config struct {
    TrackingID         string `json:"tracking_id"`
    MeasurementID      string `json:"measurement_id"`
    EnhancedEcommerce  bool   `json:"enhanced_ecommerce"`
}

// Initialize plugin
func (p *GoogleAnalyticsPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
    // Parse configuration
    if err := p.ParseConfig(config, &p.config); err != nil {
        return fmt.Errorf("invalid configuration: %w", err)
    }
    
    // Initialize Google Analytics client
    p.client = NewGAClient(p.config.TrackingID, p.config.MeasurementID)
    
    // Register event hooks
    p.RegisterHook("post.published", p.trackPostPublished)
    p.RegisterHook("user.registered", p.trackUserRegistration)
    p.RegisterHook("page.viewed", p.trackPageView)
    
    // Register API endpoints
    p.RegisterAPIHandler("GET", "/analytics/reports", p.handleGetReports)
    p.RegisterAPIHandler("POST", "/analytics/events", p.handleTrackEvent)
    
    // Register scheduled tasks
    p.RegisterCronJob("0 2 * * *", p.generateDailyReport)
    
    return nil
}

// Hook handlers
func (p *GoogleAnalyticsPlugin) trackPostPublished(ctx context.Context, event sdk.Event) error {
    postData := event.Data.(map[string]interface{})
    
    return p.client.TrackEvent(ctx, "post_published", map[string]interface{}{
        "post_id":    postData["id"],
        "title":      postData["title"],
        "author_id":  postData["author_id"],
        "category":   postData["category"],
        "tenant_id":  event.TenantID,
        "website_id": event.WebsiteID,
    })
}

func (p *GoogleAnalyticsPlugin) trackUserRegistration(ctx context.Context, event sdk.Event) error {
    userData := event.Data.(map[string]interface{})
    
    return p.client.TrackEvent(ctx, "user_registration", map[string]interface{}{
        "user_id":    userData["id"],
        "source":     userData["source"],
        "tenant_id":  event.TenantID,
        "website_id": event.WebsiteID,
    })
}

// API handlers
func (p *GoogleAnalyticsPlugin) handleGetReports(ctx context.Context, req sdk.APIRequest) sdk.APIResponse {
    // Check permissions
    if !req.HasPermission("analytics.read") {
        return sdk.ErrorResponse(403, "insufficient permissions")
    }
    
    // Get reports from Google Analytics
    reports, err := p.client.GetReports(ctx, req.TenantID, req.WebsiteID)
    if err != nil {
        return sdk.ErrorResponse(500, err.Error())
    }
    
    return sdk.SuccessResponse(reports)
}

// Scheduled tasks
func (p *GoogleAnalyticsPlugin) generateDailyReport(ctx context.Context) error {
    // Generate daily analytics report for all tenants
    tenants, err := p.GetAllTenants(ctx)
    if err != nil {
        return err
    }
    
    for _, tenant := range tenants {
        if err := p.generateTenantReport(ctx, tenant.ID); err != nil {
            p.LogError("failed to generate report for tenant %d: %v", tenant.ID, err)
        }
    }
    
    return nil
}

// Plugin lifecycle methods
func (p *GoogleAnalyticsPlugin) Activate(ctx context.Context) error {
    p.LogInfo("Google Analytics plugin activated")
    return nil
}

func (p *GoogleAnalyticsPlugin) Deactivate(ctx context.Context) error {
    p.LogInfo("Google Analytics plugin deactivated")
    return p.client.Close()
}

func (p *GoogleAnalyticsPlugin) GetInfo() sdk.PluginInfo {
    return sdk.PluginInfo{
        Name:        "google-analytics",
        Version:     "1.0.0",
        Description: "Google Analytics integration",
        Author:      "Blog API Team",
    }
}

// Plugin factory function
func NewPlugin() sdk.Plugin {
    return &GoogleAnalyticsPlugin{}
}

// Required export for plugin loading
var Plugin = NewPlugin()
```

## Plugin SDK

### 1. Base Plugin Interface

```go
package sdk

import (
    "context"
    "time"
)

// Plugin interface that all plugins must implement
type Plugin interface {
    // Lifecycle methods
    Initialize(ctx context.Context, config map[string]interface{}) error
    Activate(ctx context.Context) error
    Deactivate(ctx context.Context) error
    
    // Plugin information
    GetInfo() PluginInfo
    
    // Health check
    HealthCheck(ctx context.Context) error
}

// BasePlugin provides common functionality
type BasePlugin struct {
    info     PluginInfo
    logger   Logger
    config   map[string]interface{}
    hooks    map[string][]HookHandler
    api      map[string]APIHandler
    cron     map[string]CronHandler
    database Database
    cache    Cache
    queue    Queue
}

// Plugin information structure
type PluginInfo struct {
    Name        string            `json:"name"`
    Version     string            `json:"version"`
    Description string            `json:"description"`
    Author      string            `json:"author"`
    License     string            `json:"license"`
    Homepage    string            `json:"homepage"`
    Tags        []string          `json:"tags"`
    Metadata    map[string]string `json:"metadata"`
}

// Event structure for hooks
type Event struct {
    Type      string                 `json:"type"`
    TenantID  uint32                 `json:"tenant_id"`
    WebsiteID uint32                 `json:"website_id"`
    UserID    uint32                 `json:"user_id"`
    Data      map[string]interface{} `json:"data"`
    Timestamp time.Time              `json:"timestamp"`
}

// API request structure
type APIRequest struct {
    TenantID    uint32                 `json:"tenant_id"`
    WebsiteID   uint32                 `json:"website_id"`
    UserID      uint32                 `json:"user_id"`
    Method      string                 `json:"method"`
    Path        string                 `json:"path"`
    Headers     map[string]string      `json:"headers"`
    Query       map[string]string      `json:"query"`
    Body        map[string]interface{} `json:"body"`
    Permissions []string               `json:"permissions"`
}

// API response structure
type APIResponse struct {
    StatusCode int                    `json:"status_code"`
    Headers    map[string]string      `json:"headers"`
    Body       map[string]interface{} `json:"body"`
}

// Hook handler function type
type HookHandler func(ctx context.Context, event Event) error

// API handler function type
type APIHandler func(ctx context.Context, req APIRequest) APIResponse

// Cron handler function type
type CronHandler func(ctx context.Context) error
```

### 2. Plugin SDK Services

```go
// Database service interface
type Database interface {
    Query(ctx context.Context, query string, args ...interface{}) ([]map[string]interface{}, error)
    Execute(ctx context.Context, query string, args ...interface{}) error
    Transaction(ctx context.Context, fn func(tx Transaction) error) error
}

// Cache service interface
type Cache interface {
    Get(ctx context.Context, key string) (interface{}, error)
    Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
    Delete(ctx context.Context, key string) error
    Clear(ctx context.Context, pattern string) error
}

// Queue service interface
type Queue interface {
    Publish(ctx context.Context, topic string, message interface{}) error
    Subscribe(ctx context.Context, topic string, handler func(message interface{}) error) error
    PublishDelayed(ctx context.Context, topic string, message interface{}, delay time.Duration) error
}

// HTTP client interface
type HTTPClient interface {
    Get(ctx context.Context, url string, headers map[string]string) (*HTTPResponse, error)
    Post(ctx context.Context, url string, body interface{}, headers map[string]string) (*HTTPResponse, error)
    Put(ctx context.Context, url string, body interface{}, headers map[string]string) (*HTTPResponse, error)
    Delete(ctx context.Context, url string, headers map[string]string) (*HTTPResponse, error)
}

// Logger interface
type Logger interface {
    Debug(msg string, args ...interface{})
    Info(msg string, args ...interface{})
    Warn(msg string, args ...interface{})
    Error(msg string, args ...interface{})
    Fatal(msg string, args ...interface{})
}

// Utility functions
func SuccessResponse(data interface{}) APIResponse {
    return APIResponse{
        StatusCode: 200,
        Body: map[string]interface{}{
            "success": true,
            "data":    data,
        },
    }
}

func ErrorResponse(statusCode int, message string) APIResponse {
    return APIResponse{
        StatusCode: statusCode,
        Body: map[string]interface{}{
            "success": false,
            "error":   message,
        },
    }
}
```

## Plugin Manager

### 1. Plugin Management Service

```go
package plugins

import (
    "context"
    "fmt"
    "sync"
    
    "github.com/blog-api/plugin-sdk/v1"
)

type PluginManager struct {
    plugins     map[string]LoadedPlugin
    registry    PluginRegistry
    loader      PluginLoader
    security    SecurityManager
    config      PluginConfig
    mu          sync.RWMutex
}

type LoadedPlugin struct {
    Info     sdk.PluginInfo `json:"info"`
    Instance sdk.Plugin     `json:"-"`
    Status   PluginStatus   `json:"status"`
    Config   map[string]interface{} `json:"config"`
    LoadedAt time.Time      `json:"loaded_at"`
    Error    string         `json:"error,omitempty"`
}

type PluginStatus string

const (
    StatusLoaded     PluginStatus = "loaded"
    StatusActive     PluginStatus = "active"
    StatusInactive   PluginStatus = "inactive"
    StatusError      PluginStatus = "error"
    StatusUnloaded   PluginStatus = "unloaded"
)

func NewPluginManager(config PluginConfig) *PluginManager {
    return &PluginManager{
        plugins:  make(map[string]LoadedPlugin),
        registry: NewPluginRegistry(),
        loader:   NewPluginLoader(),
        security: NewSecurityManager(config.Security),
        config:   config,
    }
}

// Load plugin from file system
func (pm *PluginManager) LoadPlugin(ctx context.Context, pluginPath string) error {
    pm.mu.Lock()
    defer pm.mu.Unlock()
    
    // Load plugin manifest
    manifest, err := pm.loader.LoadManifest(pluginPath)
    if err != nil {
        return fmt.Errorf("failed to load manifest: %w", err)
    }
    
    // Validate plugin
    if err := pm.validatePlugin(manifest); err != nil {
        return fmt.Errorf("plugin validation failed: %w", err)
    }
    
    // Check dependencies
    if err := pm.checkDependencies(manifest.Dependencies); err != nil {
        return fmt.Errorf("dependency check failed: %w", err)
    }
    
    // Load plugin binary
    plugin, err := pm.loader.LoadPlugin(pluginPath)
    if err != nil {
        return fmt.Errorf("failed to load plugin: %w", err)
    }
    
    // Initialize plugin
    if err := plugin.Initialize(ctx, manifest.Config); err != nil {
        return fmt.Errorf("plugin initialization failed: %w", err)
    }
    
    // Register plugin
    pm.plugins[manifest.Name] = LoadedPlugin{
        Info:     plugin.GetInfo(),
        Instance: plugin,
        Status:   StatusLoaded,
        Config:   manifest.Config,
        LoadedAt: time.Now(),
    }
    
    pm.registry.Register(manifest.Name, plugin.GetInfo())
    
    return nil
}

// Activate plugin
func (pm *PluginManager) ActivatePlugin(ctx context.Context, pluginName string) error {
    pm.mu.Lock()
    defer pm.mu.Unlock()
    
    loadedPlugin, exists := pm.plugins[pluginName]
    if !exists {
        return fmt.Errorf("plugin %s not found", pluginName)
    }
    
    if loadedPlugin.Status == StatusActive {
        return fmt.Errorf("plugin %s already active", pluginName)
    }
    
    // Check security permissions
    if err := pm.security.CheckPermissions(ctx, pluginName); err != nil {
        return fmt.Errorf("security check failed: %w", err)
    }
    
    // Activate plugin
    if err := loadedPlugin.Instance.Activate(ctx); err != nil {
        loadedPlugin.Status = StatusError
        loadedPlugin.Error = err.Error()
        pm.plugins[pluginName] = loadedPlugin
        return fmt.Errorf("plugin activation failed: %w", err)
    }
    
    loadedPlugin.Status = StatusActive
    loadedPlugin.Error = ""
    pm.plugins[pluginName] = loadedPlugin
    
    return nil
}

// Deactivate plugin
func (pm *PluginManager) DeactivatePlugin(ctx context.Context, pluginName string) error {
    pm.mu.Lock()
    defer pm.mu.Unlock()
    
    loadedPlugin, exists := pm.plugins[pluginName]
    if !exists {
        return fmt.Errorf("plugin %s not found", pluginName)
    }
    
    if loadedPlugin.Status != StatusActive {
        return fmt.Errorf("plugin %s not active", pluginName)
    }
    
    // Deactivate plugin
    if err := loadedPlugin.Instance.Deactivate(ctx); err != nil {
        return fmt.Errorf("plugin deactivation failed: %w", err)
    }
    
    loadedPlugin.Status = StatusInactive
    pm.plugins[pluginName] = loadedPlugin
    
    return nil
}

// Unload plugin
func (pm *PluginManager) UnloadPlugin(ctx context.Context, pluginName string) error {
    pm.mu.Lock()
    defer pm.mu.Unlock()
    
    loadedPlugin, exists := pm.plugins[pluginName]
    if !exists {
        return fmt.Errorf("plugin %s not found", pluginName)
    }
    
    // Deactivate if active
    if loadedPlugin.Status == StatusActive {
        if err := loadedPlugin.Instance.Deactivate(ctx); err != nil {
            return fmt.Errorf("failed to deactivate plugin: %w", err)
        }
    }
    
    // Unload plugin
    delete(pm.plugins, pluginName)
    pm.registry.Unregister(pluginName)
    
    return nil
}

// Get all plugins
func (pm *PluginManager) GetPlugins() map[string]LoadedPlugin {
    pm.mu.RLock()
    defer pm.mu.RUnlock()
    
    result := make(map[string]LoadedPlugin)
    for name, plugin := range pm.plugins {
        result[name] = plugin
    }
    
    return result
}

// Get plugin by name
func (pm *PluginManager) GetPlugin(pluginName string) (LoadedPlugin, bool) {
    pm.mu.RLock()
    defer pm.mu.RUnlock()
    
    plugin, exists := pm.plugins[pluginName]
    return plugin, exists
}

// Health check all plugins
func (pm *PluginManager) HealthCheck(ctx context.Context) map[string]error {
    pm.mu.RLock()
    defer pm.mu.RUnlock()
    
    results := make(map[string]error)
    
    for name, loadedPlugin := range pm.plugins {
        if loadedPlugin.Status == StatusActive {
            results[name] = loadedPlugin.Instance.HealthCheck(ctx)
        }
    }
    
    return results
}
```

### 2. Plugin Security Manager

```go
package plugins

import (
    "context"
    "fmt"
    "time"
)

type SecurityManager struct {
    config SecurityConfig
    limits map[string]*ResourceLimits
}

type SecurityConfig struct {
    EnableSandbox     bool              `json:"enable_sandbox"`
    DefaultLimits     ResourceLimits    `json:"default_limits"`
    AllowedDomains    []string          `json:"allowed_domains"`
    BlockedDomains    []string          `json:"blocked_domains"`
    TrustedPlugins    []string          `json:"trusted_plugins"`
}

type ResourceLimits struct {
    MemoryLimit    int64         `json:"memory_limit"`    // bytes
    CPULimit       float64       `json:"cpu_limit"`       // CPU cores
    DiskLimit      int64         `json:"disk_limit"`      // bytes
    NetworkEnabled bool          `json:"network_enabled"`
    FileSystemAccess []string    `json:"file_system_access"`
    Timeout        time.Duration `json:"timeout"`
}

func NewSecurityManager(config SecurityConfig) *SecurityManager {
    return &SecurityManager{
        config: config,
        limits: make(map[string]*ResourceLimits),
    }
}

func (sm *SecurityManager) CheckPermissions(ctx context.Context, pluginName string) error {
    // Check if plugin is in trusted list
    if sm.isTrustedPlugin(pluginName) {
        return nil
    }
    
    // Apply resource limits
    limits := sm.getPluginLimits(pluginName)
    if err := sm.enforceResourceLimits(pluginName, limits); err != nil {
        return fmt.Errorf("resource limit enforcement failed: %w", err)
    }
    
    // Check network permissions
    if limits.NetworkEnabled {
        if err := sm.validateNetworkAccess(pluginName); err != nil {
            return fmt.Errorf("network access validation failed: %w", err)
        }
    }
    
    return nil
}

func (sm *SecurityManager) enforceResourceLimits(pluginName string, limits *ResourceLimits) error {
    // Set memory limits using cgroups or similar
    if err := sm.setMemoryLimit(pluginName, limits.MemoryLimit); err != nil {
        return err
    }
    
    // Set CPU limits
    if err := sm.setCPULimit(pluginName, limits.CPULimit); err != nil {
        return err
    }
    
    // Set disk limits
    if err := sm.setDiskLimit(pluginName, limits.DiskLimit); err != nil {
        return err
    }
    
    return nil
}

func (sm *SecurityManager) validateNetworkAccess(pluginName string) error {
    // Check against allowed/blocked domains
    // Implement network policy enforcement
    return nil
}
```

## Plugin Development

### 1. Creating a New Plugin

```bash
# Plugin development CLI
blog-api plugin create --name my-plugin --type feature --template basic

# Generate plugin structure
blog-api plugin scaffold my-plugin
```

Generated structure:
```
my-plugin/
├── plugin.yaml              # Plugin manifest
├── main.go                   # Plugin entry point
├── handlers/                 # API handlers
│   └── api.go
├── hooks/                    # Event hooks
│   └── events.go
├── cron/                     # Scheduled tasks
│   └── tasks.go
├── config/                   # Configuration
│   └── schema.json
├── templates/                # Templates
├── assets/                   # Static assets
├── tests/                    # Unit tests
│   └── plugin_test.go
├── scripts/                  # Lifecycle scripts
│   ├── install.go
│   ├── uninstall.go
│   ├── activate.go
│   └── deactivate.go
├── docs/                     # Documentation
│   ├── README.md
│   └── API.md
└── Makefile                  # Build scripts
```

### 2. Plugin Testing

```go
package tests

import (
    "context"
    "testing"
    
    "github.com/blog-api/plugin-sdk/v1/testing"
)

func TestMyPlugin(t *testing.T) {
    // Create test environment
    env := testing.NewPluginTestEnv(t)
    defer env.Cleanup()
    
    // Load plugin
    plugin := &MyPlugin{}
    if err := plugin.Initialize(context.Background(), map[string]interface{}{
        "api_key": "test-key",
    }); err != nil {
        t.Fatalf("failed to initialize plugin: %v", err)
    }
    
    // Test plugin functionality
    t.Run("API Handler", func(t *testing.T) {
        req := testing.NewAPIRequest("GET", "/my-plugin/status")
        resp := plugin.HandleAPI(context.Background(), req)
        
        if resp.StatusCode != 200 {
            t.Errorf("expected status 200, got %d", resp.StatusCode)
        }
    })
    
    t.Run("Event Hook", func(t *testing.T) {
        event := testing.NewEvent("post.published", map[string]interface{}{
            "post_id": 123,
            "title":   "Test Post",
        })
        
        if err := plugin.HandleEvent(context.Background(), event); err != nil {
            t.Errorf("event handling failed: %v", err)
        }
    })
}
```

## Plugin Store & Distribution

### 1. Plugin Store API

```go
// Plugin store endpoints
// GET /api/cms/v1/plugins/store
func (h *PluginStoreHandler) ListPlugins(c *gin.Context) {
    category := c.Query("category")
    search := c.Query("search")
    page := c.Query("page")
    
    plugins, err := h.storeService.SearchPlugins(c.Request.Context(), SearchRequest{
        Category: category,
        Search:   search,
        Page:     parseInt(page, 1),
        Limit:    20,
    })
    
    if err != nil {
        c.JSON(500, gin.H{"error": "failed to search plugins"})
        return
    }
    
    c.JSON(200, gin.H{
        "plugins": plugins,
        "pagination": gin.H{
            "page":  page,
            "total": len(plugins),
        },
    })
}

// POST /api/cms/v1/plugins/install
func (h *PluginHandler) InstallPlugin(c *gin.Context) {
    var req struct {
        Name    string `json:"name" binding:"required"`
        Version string `json:"version"`
        Source  string `json:"source"` // store, url, file
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // Install plugin
    if err := h.pluginManager.InstallPlugin(c.Request.Context(), InstallRequest{
        Name:     req.Name,
        Version:  req.Version,
        Source:   req.Source,
        TenantID: c.GetUint32("tenant_id"),
    }); err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{
        "message": "plugin installed successfully",
    })
}
```

### 2. Plugin Repository Structure

```yaml
# Plugin repository metadata
repository:
  name: "Official Plugin Repository"
  url: "https://plugins.blog-api.com"
  version: "1.0"

categories:
  - name: "Authentication"
    slug: "auth"
    description: "Authentication and authorization plugins"
  - name: "Analytics"
    slug: "analytics"
    description: "Analytics and tracking plugins"
  - name: "Social Media"
    slug: "social"
    description: "Social media integration plugins"

featured_plugins:
  - "google-analytics"
  - "stripe-payment"
  - "slack-notifications"

plugins:
  google-analytics:
    name: "Google Analytics"
    description: "Comprehensive Google Analytics integration"
    versions:
      - version: "1.0.0"
        download_url: "https://plugins.blog-api.com/google-analytics/1.0.0.zip"
        checksum: "sha256:abc123..."
        compatibility: ">=3.0.0"
        release_date: "2024-01-15"
    category: "analytics"
    tags: ["analytics", "google", "tracking"]
    rating: 4.8
    downloads: 1250
    author: "Blog API Team"
    license: "MIT"
    price: "free"
```

## Best Practices

### 1. Plugin Development
- **Single Responsibility**: Each plugin should have a focused purpose
- **Error Handling**: Graceful error handling and recovery
- **Resource Management**: Proper cleanup of resources
- **Security**: Validate all inputs and sanitize outputs
- **Documentation**: Comprehensive documentation and examples

### 2. Performance
- **Lazy Loading**: Load plugins only when needed
- **Resource Limits**: Enforce memory and CPU limits
- **Caching**: Cache plugin data and configurations
- **Async Operations**: Use async operations for heavy tasks

### 3. Security
- **Sandboxing**: Isolate plugin execution
- **Permission System**: Granular permission controls
- **Code Review**: Review all plugins before installation
- **Regular Updates**: Keep plugins updated for security

### 4. Maintenance
- **Version Management**: Proper plugin versioning
- **Dependency Tracking**: Monitor plugin dependencies
- **Health Monitoring**: Regular health checks
- **Backup & Recovery**: Plugin configuration backup

## Available Plugins

### Email Plugins
- **[SendGrid Plugin](./email/sendgrid.md)** - SendGrid email service implementation
- **[Mailgun Plugin](./email/mailgun.md)** - Mailgun email service implementation
- **[SES Plugin](./email/ses.md)** - Amazon SES email service implementation
- **[SMTP Plugin](./email/smtp.md)** - Generic SMTP email implementation

### Payment Plugins
- **[Stripe Plugin](./payment/stripe.md)** - Stripe payment gateway implementation
- **[PayPal Plugin](./payment/paypal.md)** - PayPal payment gateway implementation
- **[VNPay Plugin](./payment/vnpay.md)** - VNPay payment gateway implementation
- **[Bank Transfer Plugin](./payment/bank-transfer.md)** - Bank transfer implementation

### Storage Plugins
- **[S3 Plugin](./storage/s3.md)** - Amazon S3 storage implementation
- **[GCS Plugin](./storage/gcs.md)** - Google Cloud Storage implementation
- **[Azure Blob Plugin](./storage/azure-blob.md)** - Azure Blob Storage implementation

### Analytics Plugins
- **[Google Analytics Plugin](./analytics/google-analytics.md)** - Google Analytics integration
- **[Mixpanel Plugin](./analytics/mixpanel.md)** - Mixpanel analytics integration
- **[Segment Plugin](./analytics/segment.md)** - Segment analytics integration

## Related Documentation

### Architecture & Design
- **[Module vs Plugin Boundaries](../architecture/module-vs-plugin-boundaries.md)** - Architectural guidelines
- **[Inter-module Communication](../architecture/inter-module-communication.md)** - Event-driven patterns
- **[Project Structure](../architecture/project-structure.md)** - Code organization

### Core Modules
- **[Email Module](../modules/email.md)** - Core email interfaces
- **[Payment Module](../modules/payment.md)** - Core payment interfaces
- **[Storage Module](../modules/storage.md)** - Core storage interfaces
- **[Analytics Module](../modules/analytics.md)** - Core analytics interfaces

### Development
- **[Creating Plugins](./creating-plugins.md)** - Plugin development guide
- **[Plugin Testing](./testing-plugins.md)** - Testing plugin implementations
- **[Plugin Security](./plugin-security.md)** - Security best practices
- **[Plugin Deployment](./plugin-deployment.md)** - Deployment strategies