# Module Website - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Module Website cung cấp hệ thống quản lý website frontend cho mỗi tenant, cho phép tùy chỉnh giao diện, theme, và cấu trúc trang web mà không cần kiến thức lập trình. Đây là module core trong việc tạo ra các website CMS với khả năng multi-tenant và performance cao.

## Tính năng chính

- **Quản lý Theme**: Cài đặt, tùy chỉnh theme website
- **Page Builder**: Tạo trang web với drag & drop
- **Menu Management**: Quản lý menu navigation  
- **Widget System**: Thêm các widget vào sidebar, footer
- **SEO Optimization**: Tối ưu SEO cho từng trang
- **Media Library**: Quản lý hình ảnh, video, files
- **Multi-Tenancy**: Complete tenant isolation
- **Performance**: Caching, CDN, optimization

## Cấu trúc Documentation

Documentation của Website Module được chia thành các phần chi tiết sau:

### 📋 [Models và Database Schema](./website/models-schema.md)
- Cấu trúc database và models
- Data relationships và validation
- Performance optimization
- Migration strategies

### 🎨 [Theme Management](./website/theme-management.md)
- Theme system architecture
- Theme installation và configuration
- Asset management
- Theme security và validation

### 🔧 [Page Builder System](./website/page-builder.md)
- Drag & drop interface
- Block system và types
- Template rendering
- Real-time collaboration

### 📁 [Media Management](./website/media-management.md)
- File upload và processing
- Image optimization
- CDN integration
- Storage strategies

### 🔍 [SEO Optimization](./website/seo-optimization.md)
- On-page và technical SEO
- Analytics integration
- Performance monitoring
- Content optimization

### ⚡ [Frontend Rendering](./website/frontend-rendering.md)
- Rendering strategies (SSG, SSR, CSR)
- Performance optimization
- Caching mechanisms
- Progressive enhancement

### 🔗 [API Endpoints](./website/api-endpoints.md)
- RESTful API documentation
- Request/response formats
- Authentication và authorization
- Error handling

### 🏢 [Multi-Tenancy Implementation](./website/multi-tenancy.md)
- Tenant isolation strategies
- Website context management
- Security implementation
- Resource access control

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/website/
├── models/                   # Data models
│   ├── website.go           # Website model
│   ├── theme.go             # Theme model
│   ├── page.go              # Page model
│   ├── menu.go              # Menu model
│   ├── widget.go            # Widget model
│   └── media.go             # Media model
├── services/                # Business logic
│   ├── website_service.go   # Website management
│   ├── theme_service.go     # Theme operations
│   ├── page_service.go      # Page operations
│   ├── media_service.go     # Media operations
│   └── seo_service.go       # SEO operations
├── handlers/                # HTTP handlers
│   ├── website_handler.go   # Website endpoints
│   ├── theme_handler.go     # Theme endpoints
│   ├── page_handler.go      # Page endpoints
│   └── media_handler.go     # Media endpoints
├── repositories/            # Data access layer
│   └── website_repository.go
├── templates/               # Template engine
└── renderers/               # Frontend rendering
```

## Quick Start

### 1. Tạo Website mới
```go
website := &Website{
    TenantID:    tenantID,
    Name:        "My Website",
    Subdomain:   "mysite",
    Description: "My awesome website",
}
createdWebsite, err := websiteService.CreateWebsite(website)
```

### 2. Upload và Activate Theme
```go
// Upload theme
theme, err := themeService.UploadTheme(websiteID, themeFile)

// Activate theme
err = themeService.ActivateTheme(websiteID, theme.ID)
```

### 3. Tạo Page với Page Builder
```go
page := &Page{
    WebsiteID: websiteID,
    Title:     "Homepage",
    Slug:      "home",
    Content: PageContent{
        Blocks: []Block{
            {
                Type: "heading",
                Attributes: map[string]interface{}{
                    "level": 1,
                    "text":  "Welcome to Our Website",
                },
            },
        },
    },
    Status: "published",
}
createdPage, err := pageService.CreatePage(page)
```

## Core Concepts

### Multi-Tenancy
Mỗi website thuộc về một tenant cụ thể và hoàn toàn isolated:
- Data separation qua `website_id` và `tenant_id`
- Cache keys include website context
- API endpoints filtered by website ownership

### Block-Based Content
Page Builder sử dụng block-based architecture:
- Flexible content structure
- Reusable components
- Rich editing experience
- Mobile responsive

### Performance First
Website Module được thiết kế cho performance:
- Multi-level caching
- CDN integration
- Static site generation
- Asset optimization

## Integration với Modules khác

- **Auth Module**: User authentication và authorization
- **Tenant Module**: Multi-tenant management
- **Blog Module**: Content management
- **Media Module**: File và image handling
- **SEO Module**: Search engine optimization

## Best Practices

### Development
- Always validate website ownership
- Use proper caching strategies
- Implement progressive enhancement
- Follow SEO best practices

### Security
- Validate all user inputs
- Implement proper access controls
- Use secure file upload handling
- Monitor for security issues

### Performance
- Optimize images và assets
- Use appropriate caching
- Implement lazy loading
- Monitor performance metrics

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Auth Module](./auth.md)
- [Tenant Module](./tenant.md)
- [Blog Module](./blog.md)