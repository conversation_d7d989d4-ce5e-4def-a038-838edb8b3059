# Module Media - Tài liệu Tiếng Việt

## Tổng quan

Module Media cung cấp hệ thống quản lý file đa phương tiện toàn diện, bao gồm upload, l<PERSON><PERSON> tr<PERSON>, <PERSON><PERSON> lý, tối ưu hóa và phân phối file hình ảnh, video, audio và documents cho hệ thống blog.

## Mục tiêu

- **Quản lý file tập trung**: <PERSON><PERSON> thống quản lý tất cả media files
- **Tối ưu hóa performance**: Nén, resize, format conversion tự động
- **Bảo mật**: <PERSON><PERSON><PERSON> soát quyền truy cập và virus scanning
- **Scalability**: Hỗ trợ multiple storage providers
- **CDN Integration**: Phân phối nhanh toàn cầu

## Tính năng chính

- **File Upload**: Multi-file upload với drag & drop
- **Media Library**: Th<PERSON> viện quản lý file organized
- **Image Processing**: Resize, crop, watermark, format conversion
- **Video Processing**: Thumbnail generation, format conversion, streaming
- **Storage Management**: Multiple storage backends
- **CDN Integration**: CloudFlare, AWS CloudFront, etc.
- **Access Control**: Permission-based file access
- **Metadata Extraction**: EXIF, file info, dimensions
- **Search & Filter**: Tìm kiếm file theo nhiều tiêu chí

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/media/
├── models/                   # Các model media
│   ├── file.go              # Model file chính
│   ├── folder.go            # Model thư mục
│   ├── thumbnail.go         # Model thumbnail
│   └── metadata.go          # Model metadata
├── services/                # Logic nghiệp vụ
│   ├── upload_service.go    # Dịch vụ upload
│   ├── processing_service.go # Dịch vụ xử lý file
│   ├── storage_service.go   # Dịch vụ lưu trữ
│   ├── cdn_service.go       # Dịch vụ CDN
│   └── security_service.go  # Dịch vụ bảo mật
├── handlers/                # HTTP handlers
├── repositories/            # Truy cập dữ liệu
├── processors/              # File processors
│   ├── image_processor.go   # Xử lý hình ảnh
│   ├── video_processor.go   # Xử lý video
│   └── document_processor.go # Xử lý document
├── storage/                 # Storage adapters
│   ├── local_storage.go     # Local filesystem
│   ├── s3_storage.go        # AWS S3
│   ├── minio_storage.go     # MinIO S3-compatible
│   └── gcs_storage.go       # Google Cloud Storage
└── validators/              # File validation
```

## Mô hình dữ liệu

### MediaFile
- **ID**: Định danh file
- **TenantID**: ID tenant sở hữu
- **UserID**: ID người upload
- **FolderID**: ID thư mục chứa
- **Filename**: Tên file gốc
- **Path**: Đường dẫn lưu trữ
- **URL**: URL truy cập public
- **MimeType**: Loại MIME
- **Size**: Kích thước file (bytes)
- **Width/Height**: Kích thước hình ảnh
- **Duration**: Thời lượng (video/audio)
- **Metadata**: Thông tin EXIF, tags
- **Status**: Trạng thái (uploading, processing, ready, error)

### MediaFolder
- **ID**: Định danh thư mục
- **TenantID**: ID tenant
- **ParentID**: ID thư mục cha
- **Name**: Tên thư mục
- **Path**: Đường dẫn đầy đủ
- **Type**: Loại thư mục (user, system, public)
- **Permissions**: Quyền truy cập

### MediaThumbnail
- **ID**: Định danh thumbnail
- **FileID**: ID file gốc
- **Size**: Kích thước (small, medium, large)
- **Width/Height**: Kích thước pixel
- **Path**: Đường dẫn thumbnail
- **URL**: URL truy cập

### MediaMetadata
- **FileID**: ID file
- **Type**: Loại metadata (exif, custom, auto)
- **Key**: Tên trường
- **Value**: Giá trị
- **Source**: Nguồn (user, system, extracted)

## Luồng hoạt động chính

### 1. Upload File

```mermaid
sequenceDiagram
    participant User as Người dùng
    participant UI as Frontend
    participant API as Media API
    participant Validator as File Validator
    participant Storage as Storage Service
    participant Processor as File Processor
    participant CDN as CDN Service
    
    User->>UI: Chọn file upload
    UI->>API: POST /media/upload
    API->>Validator: Validate file
    Validator->>API: Validation result
    API->>Storage: Store original file
    Storage->>API: File stored
    API->>Processor: Queue processing
    API->>UI: Upload success
    
    Processor->>Processor: Generate thumbnails
    Processor->>Processor: Extract metadata
    Processor->>Processor: Optimize file
    Processor->>Storage: Store processed files
    Processor->>CDN: Sync to CDN
    Processor->>API: Processing complete
```

### 2. Xử lý hình ảnh

```mermaid
flowchart TD
    A[Upload hình ảnh] --> B[Validate format]
    B --> C{Format hợp lệ?}
    C -->|Không| D[Reject upload]
    C -->|Có| E[Lưu file gốc]
    E --> F[Extract EXIF data]
    F --> G[Generate thumbnails]
    G --> H[Optimize for web]
    H --> I[Create WebP version]
    I --> J[Apply watermark]
    J --> K[Update database]
    K --> L[Sync to CDN]
    L --> M[Notify completion]
```

### 3. Video Processing Pipeline

```mermaid
flowchart LR
    A[Video Upload] --> B[Extract metadata]
    B --> C[Generate thumbnail]
    C --> D[Create preview GIF]
    D --> E[Transcode formats]
    E --> F[Generate subtitles]
    F --> G[Create streaming playlist]
    G --> H[Upload to CDN]
    H --> I[Update status]
```

### 4. Tìm kiếm và Filter

```mermaid
flowchart TD
    A[Search Request] --> B{Search Type?}
    B -->|Text| C[Search filename/tags]
    B -->|Filter| D[Apply filters]
    B -->|Visual| E[Image similarity]
    
    C --> F[Elasticsearch query]
    D --> G[Database filter]
    E --> H[AI visual search]
    
    F --> I[Merge results]
    G --> I
    H --> I
    
    I --> J[Sort by relevance]
    J --> K[Paginate results]
    K --> L[Return to user]
```

## Các loại File được hỗ trợ

### Hình ảnh
- **Raster**: JPEG, PNG, GIF, WebP, TIFF, BMP
- **Vector**: SVG
- **Raw**: CR2, NEF, ARW (Camera raw)
- **Features**: Resize, crop, watermark, format conversion

### Video
- **Formats**: MP4, AVI, MOV, WebM, MKV, FLV
- **Codecs**: H.264, H.265, VP9, AV1
- **Features**: Thumbnail generation, transcoding, streaming

### Audio
- **Formats**: MP3, WAV, AAC, OGG, FLAC
- **Features**: Metadata extraction, waveform generation

### Documents
- **Office**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Text**: TXT, RTF, MD
- **Features**: Thumbnail preview, text extraction

### Archives
- **Formats**: ZIP, RAR, 7Z, TAR
- **Features**: Content listing, virus scanning

## Storage Adapters

### Local Storage

Local storage adapter lưu trữ file trực tiếp trên filesystem của server.

```mermaid
flowchart LR
    A[File Upload] --> B[Local Filesystem]
    B --> C[Directory Structure]
    C --> D[/media/tenant_id/year/month/]
    D --> E[File stored]
    E --> F[Generate URL]
    F --> G[Update Database]
```

#### Cấu hình Local Storage
```yaml
storage:
  type: "local"
  local:
    base_path: "./storage/media"
    url_prefix: "https://yourdomain.com/media"
    directory_structure: "tenant/year/month"
    permissions: 0755
    max_file_size: "100MB"
    allowed_extensions: ["jpg", "png", "gif", "pdf", "mp4"]
```

#### Cấu trúc thư mục Local
```
storage/media/
├── tenant_1/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── images/
│   │   │   ├── videos/
│   │   │   └── documents/
│   │   └── 02/
│   └── thumbnails/
│       ├── small/
│       ├── medium/
│       └── large/
└── tenant_2/
    └── 2024/
        └── 01/
```

#### Ưu điểm Local Storage
- **Cost-effective**: Không có chi phí cloud storage
- **Low latency**: Truy cập nhanh cho server cùng vị trí
- **Simple setup**: Dễ cấu hình và triển khai
- **Full control**: Kiểm soát hoàn toàn dữ liệu

#### Nhược điểm Local Storage
- **Scalability**: Hạn chế khả năng mở rộng
- **Backup complexity**: Phức tạp trong backup
- **Single point failure**: Rủi ro mất dữ liệu
- **Geographic distribution**: Không phân phối địa lý

### MinIO Storage

MinIO là object storage server S3-compatible, phù hợp cho self-hosted deployments.

```mermaid
flowchart LR
    A[File Upload] --> B[MinIO Server]
    B --> C[S3-Compatible API]
    C --> D[Bucket: media-bucket]
    D --> E[Object Key: tenant/type/uuid.ext]
    E --> F[MinIO URL]
    F --> G[Optional CDN]
```

#### Cấu hình MinIO
```yaml
storage:
  type: "minio"
  minio:
    endpoint: "http://minio:9000"
    access_key: "minioadmin"
    secret_key: "minioadmin"
    bucket: "media-bucket"
    region: "us-east-1"
    ssl: false
    path_style: true
    presigned_url_expires: "24h"
```

#### MinIO Deployment với Docker
```yaml
# docker-compose.yml
version: '3.8'
services:
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    
  minio-mc:
    image: minio/mc:latest
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/media-bucket;
      /usr/bin/mc policy set public myminio/media-bucket;
      exit 0;
      "

volumes:
  minio_data:
```

#### MinIO Bucket Structure
```
media-bucket/
├── tenant_1/
│   ├── images/
│   │   ├── 2024-01-15_uuid1.jpg
│   │   └── 2024-01-15_uuid2.png
│   ├── videos/
│   │   └── 2024-01-15_uuid3.mp4
│   └── thumbnails/
│       ├── small/
│       ├── medium/
│       └── large/
└── tenant_2/
    └── images/
```

#### Ưu điểm MinIO
- **S3 Compatible**: API tương thích với AWS S3
- **Self-hosted**: Kiểm soát hoàn toàn infrastructure
- **High Performance**: Hiệu suất cao cho workload intensive
- **Distributed**: Hỗ trợ distributed deployment
- **Cost Effective**: Tiết kiệm chi phí so với cloud storage

#### Nhược điểm MinIO
- **Infrastructure Management**: Cần quản lý infrastructure
- **Backup Responsibility**: Tự chịu trách nhiệm backup
- **Scaling Complexity**: Phức tạp khi scale
- **Support**: Ít support so với cloud providers

### AWS S3 Storage

```mermaid
flowchart LR
    A[File Upload] --> B[AWS S3 Bucket]
    B --> C[S3 Key Structure]
    C --> D[media/tenant/type/uuid.ext]
    D --> E[S3 URL returned]
    E --> F[CloudFront CDN]
```

#### Cấu hình AWS S3
```yaml
storage:
  type: "s3"
  s3:
    region: "us-west-2"
    bucket: "my-media-bucket"
    access_key: "AKIAIOSFODNN7EXAMPLE"
    secret_key: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    cloudfront_domain: "d123456abcdef8.cloudfront.net"
    presigned_url_expires: "1h"
```

### Multi-Storage Strategy

Sử dụng nhiều storage adapter dựa trên tiêu chí khác nhau.

```mermaid
flowchart TD
    A[Upload Request] --> B{Storage Strategy?}
    
    B -->|File Size| C{Size < 10MB?}
    C -->|Yes| D[Local Storage]
    C -->|No| E[MinIO/S3]
    
    B -->|File Type| F{Media Type?}
    F -->|Images| G[Local + CDN]
    F -->|Videos| H[MinIO/S3]
    F -->|Documents| I[Local Storage]
    
    B -->|Tenant Plan| J{Plan Type?}
    J -->|Free/Basic| K[Local Storage]
    J -->|Premium| L[MinIO Storage]
    J -->|Enterprise| M[AWS S3]
    
    D --> N[Store & Serve]
    E --> N
    G --> N
    H --> N
    I --> N
    K --> N
    L --> N
    M --> N
```

### Storage Configuration

#### Environment-based Configuration
```yaml
# Development
storage:
  type: "local"
  local:
    base_path: "./storage/media"
    url_prefix: "http://localhost:9077/media"

# Staging  
storage:
  type: "minio"
  minio:
    endpoint: "http://staging-minio:9000"
    bucket: "staging-media"

# Production
storage:
  type: "s3"
  s3:
    region: "us-west-2"
    bucket: "prod-media-bucket"
    cloudfront_domain: "cdn.example.com"
```

#### Dynamic Storage Selection
```mermaid
flowchart TD
    A[File Upload] --> B[Storage Selector]
    B --> C{Environment?}
    
    C -->|Development| D[Local Storage]
    C -->|Staging| E[MinIO Storage]
    C -->|Production| F[AWS S3]
    
    D --> G[File Handler]
    E --> G
    F --> G
    
    G --> H[Process File]
    H --> I[Generate Thumbnails]
    I --> J[Update Database]
    J --> K[Return URLs]
```

### Storage Migration

#### Migration between Storage Types
```mermaid
sequenceDiagram
    participant Admin as Admin
    participant API as Migration API
    participant Source as Source Storage
    participant Target as Target Storage
    participant DB as Database
    
    Admin->>API: Start migration
    API->>DB: Get file list
    DB->>API: Return file records
    
    loop For each file
        API->>Source: Download file
        Source->>API: File data
        API->>Target: Upload file
        Target->>API: New URL
        API->>DB: Update file record
    end
    
    API->>Admin: Migration complete
```

### Hybrid Storage

Kết hợp nhiều storage để tối ưu cost và performance.

```mermaid
flowchart TD
    A[Upload Request] --> B{File Analysis}
    
    B --> C[File Size Check]
    B --> D[Access Frequency]
    B --> E[Tenant Tier]
    
    C -->|< 5MB| F[Local Storage]
    C -->|5-50MB| G[MinIO Storage]
    C -->|> 50MB| H[S3 Storage]
    
    D -->|High Access| I[Local + CDN]
    D -->|Medium Access| J[MinIO]
    D -->|Low Access| K[S3 Glacier]
    
    E -->|Free Tier| L[Local Only]
    E -->|Paid Tier| M[Multi-Storage]
    
    F --> N[Serve Direct]
    G --> O[Serve via MinIO]
    H --> P[Serve via S3/CDN]
    I --> N
    J --> O
    K --> Q[Archive Storage]
    L --> N
    M --> R[Intelligent Tiering]
```

## Image Processing Pipeline

### Thumbnail Generation
```yaml
thumbnail_sizes:
  small:
    width: 150
    height: 150
    crop: true
  medium:
    width: 300
    height: 300
    crop: false
  large:
    width: 800
    height: 600
    crop: false
  xl:
    width: 1200
    height: 900
    crop: false
```

### Format Optimization
```mermaid
flowchart TD
    A[Original Image] --> B{Format?}
    B -->|JPEG| C[Optimize JPEG]
    B -->|PNG| D[PNG Compression]
    B -->|GIF| E[GIF Optimization]
    
    C --> F[Generate WebP]
    D --> F
    E --> F
    
    F --> G[Generate AVIF]
    G --> H[Choose best format]
    H --> I[Save optimized]
```

## Video Processing

### Transcoding Workflow
```mermaid
sequenceDiagram
    participant Upload as Video Upload
    participant Queue as Processing Queue
    participant FFmpeg as FFmpeg Worker
    participant Storage as Storage
    participant CDN as CDN
    
    Upload->>Queue: Add video job
    Queue->>FFmpeg: Start transcoding
    FFmpeg->>FFmpeg: Extract thumbnail
    FFmpeg->>FFmpeg: Generate 480p
    FFmpeg->>FFmpeg: Generate 720p
    FFmpeg->>FFmpeg: Generate 1080p
    FFmpeg->>Storage: Save all versions
    Storage->>CDN: Upload to CDN
    CDN->>Queue: Transcoding complete
```

### Adaptive Streaming
```mermaid
flowchart LR
    A[Original Video] --> B[FFmpeg]
    B --> C[Multiple Bitrates]
    C --> D[480p - 1Mbps]
    C --> E[720p - 3Mbps]
    C --> F[1080p - 6Mbps]
    
    D --> G[HLS Playlist]
    E --> G
    F --> G
    
    G --> H[Adaptive Streaming]
```

## CDN Integration

### Multi-CDN Strategy
```mermaid
flowchart TD
    A[Media Request] --> B[CDN Router]
    B --> C{User Location?}
    C -->|Asia| D[CloudFlare Asia]
    C -->|Europe| E[AWS CloudFront EU]
    C -->|Americas| F[KeyCDN Americas]
    
    D --> G[Optimized Delivery]
    E --> G
    F --> G
```

### Cache Strategy
- **Static Assets**: Cache 1 year
- **Images**: Cache 30 days
- **Videos**: Cache 7 days
- **Thumbnails**: Cache 90 days
- **Processing Status**: No cache

## Security Features

### Upload Security
```mermaid
flowchart TD
    A[File Upload] --> B[File Type Check]
    B --> C[Size Validation]
    C --> D[Virus Scan]
    D --> E[Content Analysis]
    E --> F{All Checks Pass?}
    F -->|Không| G[Reject Upload]
    F -->|Có| H[Process File]
    
    G --> I[Log Security Event]
    H --> J[Store Safely]
```

### Access Control
- **Public Files**: Accessible by anyone
- **Private Files**: Owner and admins only
- **Shared Files**: Specific users/groups
- **Temporary Links**: Time-limited access

## API Endpoints

### File Management
- `GET /api/cms/v1/media/files` - Danh sách files
- `POST /api/cms/v1/media/upload` - Upload file
- `GET /api/cms/v1/media/files/{id}` - Chi tiết file
- `PUT /api/cms/v1/media/files/{id}` - Cập nhật metadata
- `DELETE /api/cms/v1/media/files/{id}` - Xóa file

### Folder Management
- `GET /api/cms/v1/media/folders` - Danh sách thư mục
- `POST /api/cms/v1/media/folders` - Tạo thư mục
- `PUT /api/cms/v1/media/folders/{id}` - Sửa thư mục
- `DELETE /api/cms/v1/media/folders/{id}` - Xóa thư mục

### Search & Filter
- `GET /api/cms/v1/media/search?q={query}` - Tìm kiếm
- `GET /api/cms/v1/media/filter?type=image&size=large` - Filter
- `GET /api/cms/v1/media/recent` - File gần đây
- `GET /api/cms/v1/media/popular` - File phổ biến

### Processing
- `POST /api/cms/v1/media/files/{id}/thumbnail` - Tạo thumbnail
- `POST /api/cms/v1/media/files/{id}/resize` - Resize hình ảnh
- `POST /api/cms/v1/media/files/{id}/watermark` - Thêm watermark
- `GET /api/cms/v1/media/files/{id}/status` - Trạng thái xử lý

## Performance Optimization

### Caching Strategy
```mermaid
flowchart LR
    A[Media Request] --> B[Browser Cache]
    B --> C{Cache Hit?}
    C -->|Có| D[Return Cached]
    C -->|Không| E[CDN Cache]
    E --> F{CDN Hit?}
    F -->|Có| G[Return from CDN]
    F -->|Không| H[Origin Server]
    H --> I[Process & Cache]
```

### Lazy Loading
- **Progressive JPEG**: Load incrementally
- **Placeholder Images**: Show while loading
- **Intersection Observer**: Load when visible
- **Priority Loading**: Above-fold content first

## Monitoring và Analytics

### File Usage Metrics
```mermaid
graph LR
    A[Media Analytics] --> B[Upload Stats]
    A --> C[View Counts]
    A --> D[Download Stats]
    A --> E[Storage Usage]
    A --> F[CDN Performance]
    
    B --> B1[Files per day]
    B --> B2[File types]
    B --> B3[User activity]
    
    C --> C1[Popular files]
    C --> C2[View trends]
    
    D --> D1[Download volume]
    D --> D2[Bandwidth usage]
    
    E --> E1[Storage per tenant]
    E --> E2[Growth rate]
    
    F --> F1[Cache hit rate]
    F --> F2[Response times]
```

### Performance Monitoring
- **Upload Success Rate**: Tỷ lệ upload thành công
- **Processing Time**: Thời gian xử lý file
- **CDN Hit Rate**: Tỷ lệ cache hit
- **Error Rates**: Tỷ lệ lỗi theo loại
- **Storage Growth**: Tăng trưởng dung lượng

## Backup và Recovery

### Backup Strategy
```mermaid
flowchart TD
    A[Media Files] --> B[Daily Backup]
    A --> C[Weekly Full Backup]
    A --> D[Real-time Replication]
    
    B --> E[Incremental S3]
    C --> F[Full Archive]
    D --> G[Multi-region Sync]
    
    E --> H[30-day Retention]
    F --> I[1-year Retention]
    G --> J[Real-time Recovery]
```

### Disaster Recovery
- **RTO**: Recovery Time Objective < 4 hours
- **RPO**: Recovery Point Objective < 1 hour
- **Multi-region Backup**: Geographic redundancy
- **Automated Testing**: Monthly recovery tests

## Compliance và Legal

### Data Protection
- **GDPR Compliance**: Right to deletion
- **CCPA Compliance**: Data transparency
- **File Encryption**: At rest and in transit
- **Audit Logs**: All file operations

### Content Moderation
- **AI Content Detection**: Inappropriate content
- **Copyright Detection**: Duplicate content
- **DMCA Compliance**: Takedown procedures
- **User Reporting**: Community moderation

## Tích hợp với các module khác

### Blog Module
- **Featured Images**: Hình đại diện bài viết
- **Content Images**: Hình trong nội dung
- **Gallery Posts**: Bài viết gallery
- **Media Embedding**: Embed video/audio

### Website Module
- **Theme Assets**: Logo, background, icons
- **Page Media**: Hình ảnh trong pages
- **Widget Media**: Media trong widgets
- **Custom Assets**: CSS, JS files

### Tenant Module
- **Isolated Storage**: Media riêng cho mỗi tenant
- **Storage Quotas**: Giới hạn dung lượng
- **Branding Assets**: Logo, watermark riêng
- **Custom CDN**: CDN endpoint riêng

## Best Practices

### Upload UX
- **Drag & Drop**: Giao diện kéo thả
- **Progress Indicators**: Hiển thị tiến độ
- **Batch Upload**: Upload nhiều file
- **Resume Upload**: Tiếp tục khi bị gián đoạn

### Performance
- **Lazy Loading**: Load khi cần
- **Progressive Enhancement**: Cải thiện dần
- **Responsive Images**: Hình phù hợp thiết bị
- **Format Selection**: Chọn format tối ưu

### Security
- **Input Validation**: Validate mọi input
- **File Scanning**: Quét virus/malware
- **Access Control**: Kiểm soát quyền truy cập
- **Secure Storage**: Lưu trữ an toàn

### SEO
- **Alt Text**: Mô tả hình ảnh
- **File Names**: Tên file SEO-friendly
- **Image Sitemaps**: Sitemap cho hình ảnh
- **Structured Data**: Schema markup

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Website Module](./website.md)
- [Blog Module](./blog.md)
- [Performance Best Practices](../best-practices/performance.md)
- [Security Guidelines](../best-practices/security.md)