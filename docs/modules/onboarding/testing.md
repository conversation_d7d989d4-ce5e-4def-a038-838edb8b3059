# Testing Guidelines

## Unit Testing

### Service Layer Tests

```go
package onboarding

import (
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

// Mock repository for testing
type MockOnboardingRepository struct {
    mock.Mock
}

func (m *MockOnboardingRepository) GetJourneysByWebsiteID(websiteID uint) ([]*OnboardingJourney, error) {
    args := m.Called(websiteID)
    return args.Get(0).([]*OnboardingJourney), args.Error(1)
}

func (m *MockOnboardingRepository) CreateProgress(progress *UserProgress) error {
    args := m.Called(progress)
    return args.Error(0)
}

// Test journey creation
func TestOnboardingService_CreateJourney(t *testing.T) {
    // Arrange
    mockRepo := new(MockOnboardingRepository)
    mockCache := new(MockCacheService)
    mockEventBus := new(MockEventBus)
    
    service := NewOnboardingService(mockRepo, mockCache, mockEventBus)
    
    websiteID := uint(1)
    journeyData := CreateJourneyRequest{
        Name:        "Test Journey",
        Description: "Test Description",
        Type:        "user",
        Settings:    map[string]interface{}{"gamification": map[string]interface{}{"enabled": true}},
    }
    
    expectedJourney := &OnboardingJourney{
        ID:          1,
        WebsiteID:   websiteID,
        Name:        journeyData.Name,
        Description: journeyData.Description,
        Type:        journeyData.Type,
        Settings:    journeyData.Settings,
        Status:      "active",
    }
    
    mockRepo.On("CreateJourney", mock.AnythingOfType("*onboarding.OnboardingJourney")).Return(nil)
    mockEventBus.On("Publish", "onboarding.journey.created", mock.Anything).Return(nil)
    
    // Act
    result, err := service.CreateJourneyForWebsite(websiteID, journeyData)
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, expectedJourney.Name, result.Name)
    assert.Equal(t, expectedJourney.WebsiteID, result.WebsiteID)
    assert.Equal(t, expectedJourney.Type, result.Type)
    
    mockRepo.AssertExpectations(t)
    mockEventBus.AssertExpectations(t)
}

// Test user progress creation with validation
func TestOnboardingService_StartJourney(t *testing.T) {
    tests := []struct {
        name        string
        userID      uint
        websiteID   uint
        journeyID   uint
        setupMocks  func(*MockOnboardingRepository)
        expectError bool
        errorMsg    string
    }{
        {
            name:      "successful journey start",
            userID:    1,
            websiteID: 1,
            journeyID: 1,
            setupMocks: func(mockRepo *MockOnboardingRepository) {
                journey := &OnboardingJourney{
                    ID:        1,
                    WebsiteID: 1,
                    Type:      "user",
                    Status:    "active",
                }
                mockRepo.On("GetJourneyByIDAndWebsite", uint(1), uint(1)).Return(journey, nil)
                mockRepo.On("GetProgressByJourney", uint(1), uint(1), uint(1)).Return(nil, gorm.ErrRecordNotFound)
                mockRepo.On("CreateProgress", mock.AnythingOfType("*onboarding.UserProgress")).Return(nil)
            },
            expectError: false,
        },
        {
            name:      "journey not found",
            userID:    1,
            websiteID: 1,
            journeyID: 999,
            setupMocks: func(mockRepo *MockOnboardingRepository) {
                mockRepo.On("GetJourneyByIDAndWebsite", uint(999), uint(1)).Return(nil, gorm.ErrRecordNotFound)
            },
            expectError: true,
            errorMsg:    "record not found",
        },
        {
            name:      "user already has progress",
            userID:    1,
            websiteID: 1,
            journeyID: 1,
            setupMocks: func(mockRepo *MockOnboardingRepository) {
                journey := &OnboardingJourney{ID: 1, WebsiteID: 1}
                existingProgress := &UserProgress{ID: 1, UserID: 1, JourneyID: 1}
                mockRepo.On("GetJourneyByIDAndWebsite", uint(1), uint(1)).Return(journey, nil)
                mockRepo.On("GetProgressByJourney", uint(1), uint(1), uint(1)).Return(existingProgress, nil)
            },
            expectError: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Arrange
            mockRepo := new(MockOnboardingRepository)
            mockCache := new(MockCacheService)
            mockEventBus := new(MockEventBus)
            
            service := NewOnboardingService(mockRepo, mockCache, mockEventBus)
            
            tt.setupMocks(mockRepo)
            
            // Act
            result, err := service.StartJourneyForUser(tt.userID, tt.websiteID, tt.journeyID)
            
            // Assert
            if tt.expectError {
                assert.Error(t, err)
                if tt.errorMsg != "" {
                    assert.Contains(t, err.Error(), tt.errorMsg)
                }
                assert.Nil(t, result)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
                assert.Equal(t, tt.userID, result.UserID)
                assert.Equal(t, tt.websiteID, result.WebsiteID)
                assert.Equal(t, tt.journeyID, result.JourneyID)
            }
            
            mockRepo.AssertExpectations(t)
        })
    }
}

// Test step completion with validation
func TestOnboardingService_CompleteStep(t *testing.T) {
    // Arrange
    mockRepo := new(MockOnboardingRepository)
    mockCache := new(MockCacheService)
    mockEventBus := new(MockEventBus)
    
    service := NewOnboardingService(mockRepo, mockCache, mockEventBus)
    
    userID := uint(1)
    websiteID := uint(1)
    stepID := uint(1)
    
    step := &OnboardingStep{
        ID:        stepID,
        WebsiteID: websiteID,
        JourneyID: 1,
        Name:      "Test Step",
        Type:      "form",
        Required:  true,
    }
    
    progress := &UserProgress{
        ID:             1,
        UserID:         userID,
        WebsiteID:      websiteID,
        JourneyID:      1,
        Status:         "in_progress",
        CurrentStepID:  stepID,
        CompletedSteps: []uint{},
    }
    
    stepResult := StepResult{
        StepID:     stepID,
        Status:     "completed",
        TimeSpent:  300,
        UserInput:  map[string]interface{}{"name": "John Doe"},
        UserRating: 5,
    }
    
    mockRepo.On("GetStepByIDAndWebsite", stepID, websiteID).Return(step, nil)
    mockRepo.On("GetProgressByJourney", userID, uint(1), websiteID).Return(progress, nil)
    mockRepo.On("CreateStepCompletion", mock.AnythingOfType("*onboarding.StepCompletion")).Return(nil)
    mockRepo.On("UpdateProgress", mock.AnythingOfType("*onboarding.UserProgress")).Return(nil)
    mockEventBus.On("Publish", "onboarding.step.completed", mock.Anything).Return(nil)
    
    // Act
    result, err := service.CompleteStep(userID, websiteID, stepResult)
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.True(t, result.StepCompleted)
    assert.Greater(t, result.JourneyProgress, float64(0))
    
    mockRepo.AssertExpectations(t)
    mockEventBus.AssertExpectations(t)
}
```

### Repository Layer Tests

```go
// Test repository with actual database
func TestOnboardingRepository_Integration(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    defer teardownTestDB(t, db)
    
    repo := NewOnboardingRepository(db)
    
    t.Run("CreateAndGetJourney", func(t *testing.T) {
        // Create test data
        websiteID := uint(1)
        journey := &OnboardingJourney{
            WebsiteID:   websiteID,
            Name:        "Test Journey",
            Description: "Test Description",
            Type:        "user",
            Status:      "active",
        }
        
        // Create journey
        err := repo.CreateJourney(journey)
        assert.NoError(t, err)
        assert.NotZero(t, journey.ID)
        
        // Get journey
        retrieved, err := repo.GetJourneyByIDAndWebsite(journey.ID, websiteID)
        assert.NoError(t, err)
        assert.Equal(t, journey.Name, retrieved.Name)
        assert.Equal(t, journey.WebsiteID, retrieved.WebsiteID)
    })
    
    t.Run("TenantIsolation", func(t *testing.T) {
        // Create journeys for different websites
        journey1 := &OnboardingJourney{
            WebsiteID: 1,
            Name:      "Website 1 Journey",
            Type:      "user",
            Status:    "active",
        }
        journey2 := &OnboardingJourney{
            WebsiteID: 2,
            Name:      "Website 2 Journey",
            Type:      "user",
            Status:    "active",
        }
        
        err := repo.CreateJourney(journey1)
        assert.NoError(t, err)
        err = repo.CreateJourney(journey2)
        assert.NoError(t, err)
        
        // Get journeys for website 1
        journeys1, err := repo.GetJourneysByWebsiteID(1)
        assert.NoError(t, err)
        assert.Len(t, journeys1, 1)
        assert.Equal(t, "Website 1 Journey", journeys1[0].Name)
        
        // Get journeys for website 2
        journeys2, err := repo.GetJourneysByWebsiteID(2)
        assert.NoError(t, err)
        assert.Len(t, journeys2, 1)
        assert.Equal(t, "Website 2 Journey", journeys2[0].Name)
        
        // Try to access journey1 with website2 context - should fail
        _, err = repo.GetJourneyByIDAndWebsite(journey1.ID, 2)
        assert.Error(t, err)
    })
}

func setupTestDB(t *testing.T) *gorm.DB {
    // Use SQLite for testing
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    assert.NoError(t, err)
    
    // Run migrations
    err = db.AutoMigrate(
        &OnboardingJourney{},
        &OnboardingStep{},
        &UserProgress{},
        &StepCompletion{},
    )
    assert.NoError(t, err)
    
    return db
}

func teardownTestDB(t *testing.T, db *gorm.DB) {
    sqlDB, err := db.DB()
    assert.NoError(t, err)
    err = sqlDB.Close()
    assert.NoError(t, err)
}
```

## Integration Testing

### API Integration Tests

```go
package onboarding_test

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
)

type OnboardingAPITestSuite struct {
    suite.Suite
    router   *gin.Engine
    db       *gorm.DB
    handler  *OnboardingHandler
    testUser *User
}

func (suite *OnboardingAPITestSuite) SetupSuite() {
    // Setup test database and dependencies
    suite.db = setupTestDB(suite.T())
    
    // Create test user
    suite.testUser = &User{
        ID:        1,
        Email:     "<EMAIL>",
        WebsiteID: 1,
    }
    
    // Setup router
    gin.SetMode(gin.TestMode)
    suite.router = gin.New()
    
    // Setup handler
    repo := NewOnboardingRepository(suite.db)
    service := NewOnboardingService(repo, nil, nil)
    suite.handler = NewOnboardingHandler(service)
    
    // Setup routes
    api := suite.router.Group("/api/v1/onboarding")
    api.Use(suite.mockAuthMiddleware())
    api.Use(WebsiteContextMiddleware())
    {
        api.GET("/journeys", suite.handler.GetJourneys)
        api.POST("/journeys", suite.handler.CreateJourney)
        api.POST("/progress/start", suite.handler.StartJourney)
        api.POST("/progress/step", suite.handler.CompleteStep)
    }
}

func (suite *OnboardingAPITestSuite) TearDownSuite() {
    teardownTestDB(suite.T(), suite.db)
}

func (suite *OnboardingAPITestSuite) mockAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Set("user_id", suite.testUser.ID)
        c.Next()
    }
}

func (suite *OnboardingAPITestSuite) TestCreateJourney() {
    journeyData := map[string]interface{}{
        "name":        "Test Journey",
        "description": "Test Description",
        "type":        "user",
        "settings": map[string]interface{}{
            "gamification": map[string]interface{}{
                "enabled": true,
            },
        },
    }
    
    jsonData, _ := json.Marshal(journeyData)
    
    req, _ := http.NewRequest("POST", "/api/v1/onboarding/journeys", bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Website-ID", "1")
    
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)
    
    assert.Equal(suite.T(), http.StatusCreated, w.Code)
    
    var response map[string]interface{}
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    
    assert.True(suite.T(), response["success"].(bool))
    data := response["data"].(map[string]interface{})
    assert.Equal(suite.T(), "Test Journey", data["name"])
    assert.Equal(suite.T(), "user", data["type"])
}

func (suite *OnboardingAPITestSuite) TestGetJourneys() {
    // Create test journey
    journey := &OnboardingJourney{
        WebsiteID:   1,
        Name:        "Test Journey",
        Type:        "user",
        Status:      "active",
    }
    err := suite.db.Create(journey).Error
    assert.NoError(suite.T(), err)
    
    req, _ := http.NewRequest("GET", "/api/v1/onboarding/journeys", nil)
    req.Header.Set("X-Website-ID", "1")
    
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)
    
    assert.Equal(suite.T(), http.StatusOK, w.Code)
    
    var response map[string]interface{}
    err = json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    
    assert.True(suite.T(), response["success"].(bool))
    data := response["data"].([]interface{})
    assert.Len(suite.T(), data, 1)
}

func (suite *OnboardingAPITestSuite) TestTenantIsolation() {
    // Create journeys for different websites
    journey1 := &OnboardingJourney{
        WebsiteID: 1,
        Name:      "Website 1 Journey",
        Type:      "user",
        Status:    "active",
    }
    journey2 := &OnboardingJourney{
        WebsiteID: 2,
        Name:      "Website 2 Journey",
        Type:      "user",
        Status:    "active",
    }
    
    err := suite.db.Create(journey1).Error
    assert.NoError(suite.T(), err)
    err = suite.db.Create(journey2).Error
    assert.NoError(suite.T(), err)
    
    // Request journeys for website 1
    req, _ := http.NewRequest("GET", "/api/v1/onboarding/journeys", nil)
    req.Header.Set("X-Website-ID", "1")
    
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)
    
    assert.Equal(suite.T(), http.StatusOK, w.Code)
    
    var response map[string]interface{}
    err = json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    
    data := response["data"].([]interface{})
    assert.Len(suite.T(), data, 1) // Should only see journey for website 1
    
    journey := data[0].(map[string]interface{})
    assert.Equal(suite.T(), "Website 1 Journey", journey["name"])
}

func TestOnboardingAPITestSuite(t *testing.T) {
    suite.Run(t, new(OnboardingAPITestSuite))
}
```

## End-to-End Testing

### Complete User Journey Tests

```go
func TestCompleteOnboardingJourney(t *testing.T) {
    // Setup
    suite := setupE2ETest(t)
    defer suite.teardown()
    
    // Step 1: User registers and onboarding is triggered
    user := suite.registerUser("<EMAIL>", "password123")
    
    // Step 2: Check that onboarding progress was created
    progress := suite.getOnboardingProgress(user.ID)
    assert.NotNil(t, progress)
    assert.Equal(t, "in_progress", progress.Status)
    
    // Step 3: Complete each step of the journey
    journey := suite.getJourneyByID(progress.JourneyID)
    steps := suite.getStepsByJourney(journey.ID)
    
    for _, step := range steps {
        stepResult := suite.completeStep(user.ID, step.ID, generateStepData(step.Type))
        assert.True(t, stepResult.StepCompleted)
        
        // Verify progress is updated
        updatedProgress := suite.getOnboardingProgress(user.ID)
        assert.Contains(t, updatedProgress.CompletedSteps, step.ID)
    }
    
    // Step 4: Verify journey completion
    finalProgress := suite.getOnboardingProgress(user.ID)
    assert.Equal(t, "completed", finalProgress.Status)
    assert.NotNil(t, finalProgress.CompletedAt)
    
    // Step 5: Verify completion events were triggered
    events := suite.getEvents(user.ID, "journey_completed")
    assert.Len(t, events, 1)
}

func TestOnboardingWithSkippedSteps(t *testing.T) {
    suite := setupE2ETest(t)
    defer suite.teardown()
    
    user := suite.registerUser("<EMAIL>", "password123")
    progress := suite.getOnboardingProgress(user.ID)
    journey := suite.getJourneyByID(progress.JourneyID)
    steps := suite.getStepsByJourney(journey.ID)
    
    // Complete first step
    suite.completeStep(user.ID, steps[0].ID, generateStepData(steps[0].Type))
    
    // Skip second step (if skippable)
    if steps[1].Skippable {
        skipResult := suite.skipStep(user.ID, steps[1].ID, "not_relevant")
        assert.True(t, skipResult.StepSkipped)
        
        // Verify step is marked as skipped
        updatedProgress := suite.getOnboardingProgress(user.ID)
        assert.Contains(t, updatedProgress.SkippedSteps, steps[1].ID)
    }
    
    // Complete remaining steps
    for i := 2; i < len(steps); i++ {
        suite.completeStep(user.ID, steps[i].ID, generateStepData(steps[i].Type))
    }
    
    // Verify journey completion
    finalProgress := suite.getOnboardingProgress(user.ID)
    assert.Equal(t, "completed", finalProgress.Status)
}

func TestOnboardingAbandonmentAndResumption(t *testing.T) {
    suite := setupE2ETest(t)
    defer suite.teardown()
    
    user := suite.registerUser("<EMAIL>", "password123")
    progress := suite.getOnboardingProgress(user.ID)
    journey := suite.getJourneyByID(progress.JourneyID)
    steps := suite.getStepsByJourney(journey.ID)
    
    // Complete first step
    suite.completeStep(user.ID, steps[0].ID, generateStepData(steps[0].Type))
    
    // Pause/abandon journey
    suite.pauseJourney(user.ID, journey.ID)
    
    // Verify status
    pausedProgress := suite.getOnboardingProgress(user.ID)
    assert.Equal(t, "paused", pausedProgress.Status)
    
    // Resume journey
    suite.resumeJourney(user.ID, journey.ID)
    
    // Verify status
    resumedProgress := suite.getOnboardingProgress(user.ID)
    assert.Equal(t, "in_progress", resumedProgress.Status)
    
    // Complete remaining steps
    for i := 1; i < len(steps); i++ {
        suite.completeStep(user.ID, steps[i].ID, generateStepData(steps[i].Type))
    }
    
    // Verify completion
    finalProgress := suite.getOnboardingProgress(user.ID)
    assert.Equal(t, "completed", finalProgress.Status)
}
```

## Performance Testing

### Load Testing with Multiple Tenants

```go
func TestOnboardingPerformance(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping performance test in short mode")
    }
    
    suite := setupPerformanceTest(t)
    defer suite.teardown()
    
    const (
        numWebsites     = 10
        usersPerWebsite = 100
        numGoroutines   = 50
    )
    
    // Setup test data
    websites := suite.createTestWebsites(numWebsites)
    
    for _, website := range websites {
        journey := suite.createTestJourney(website.ID)
        suite.createTestSteps(journey.ID, website.ID, 5)
    }
    
    // Performance test: concurrent user onboarding
    start := time.Now()
    
    var wg sync.WaitGroup
    userChan := make(chan TestUser, numWebsites*usersPerWebsite)
    
    // Generate test users
    go func() {
        defer close(userChan)
        for _, website := range websites {
            for i := 0; i < usersPerWebsite; i++ {
                userChan <- TestUser{
                    Email:     fmt.Sprintf("user%d@website%d.com", i, website.ID),
                    WebsiteID: website.ID,
                }
            }
        }
    }()
    
    // Process users concurrently
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for user := range userChan {
                suite.runOnboardingJourney(user)
            }
        }()
    }
    
    wg.Wait()
    duration := time.Since(start)
    
    totalUsers := numWebsites * usersPerWebsite
    usersPerSecond := float64(totalUsers) / duration.Seconds()
    
    t.Logf("Performance Results:")
    t.Logf("- Total users: %d", totalUsers)
    t.Logf("- Duration: %v", duration)
    t.Logf("- Users per second: %.2f", usersPerSecond)
    
    // Performance assertions
    assert.Less(t, duration, 60*time.Second, "Onboarding should complete within 60 seconds")
    assert.Greater(t, usersPerSecond, 10.0, "Should process at least 10 users per second")
    
    // Verify data integrity
    suite.verifyDataIntegrity(websites)
}

func (suite *PerformanceTestSuite) runOnboardingJourney(user TestUser) {
    // Register user
    registeredUser := suite.registerUser(user.Email, "password123", user.WebsiteID)
    
    // Get onboarding progress
    progress := suite.getOnboardingProgress(registeredUser.ID, user.WebsiteID)
    if progress == nil {
        suite.t.Errorf("No onboarding progress found for user %d", registeredUser.ID)
        return
    }
    
    // Complete all steps
    steps := suite.getStepsByJourney(progress.JourneyID, user.WebsiteID)
    for _, step := range steps {
        suite.completeStep(registeredUser.ID, user.WebsiteID, step.ID, generateStepData(step.Type))
    }
}
```

## Test Data Management

### Test Fixtures

```go
type TestDataManager struct {
    db *gorm.DB
}

func NewTestDataManager(db *gorm.DB) *TestDataManager {
    return &TestDataManager{db: db}
}

func (tdm *TestDataManager) CreateTestJourney(websiteID uint) *OnboardingJourney {
    journey := &OnboardingJourney{
        WebsiteID:   websiteID,
        Name:        fmt.Sprintf("Test Journey for Website %d", websiteID),
        Description: "Test journey for automated testing",
        Type:        "user",
        Status:      "active",
        Priority:    1,
        Settings: map[string]interface{}{
            "gamification": map[string]interface{}{
                "enabled":        true,
                "points_per_step": 10,
            },
        },
    }
    
    err := tdm.db.Create(journey).Error
    if err != nil {
        panic(fmt.Sprintf("Failed to create test journey: %v", err))
    }
    
    return journey
}

func (tdm *TestDataManager) CreateTestSteps(journeyID, websiteID uint, count int) []*OnboardingStep {
    steps := make([]*OnboardingStep, count)
    
    stepTypes := []string{"tutorial", "form", "action", "social"}
    
    for i := 0; i < count; i++ {
        step := &OnboardingStep{
            WebsiteID:         websiteID,
            JourneyID:         journeyID,
            Name:              fmt.Sprintf("Test Step %d", i+1),
            Description:       fmt.Sprintf("Description for test step %d", i+1),
            Type:              stepTypes[i%len(stepTypes)],
            Order:             i + 1,
            Required:          i < 3, // First 3 steps are required
            Skippable:         i >= 3, // Later steps are skippable
            EstimatedDuration: 5,
            Content:           tdm.generateStepContent(stepTypes[i%len(stepTypes)]),
        }
        
        err := tdm.db.Create(step).Error
        if err != nil {
            panic(fmt.Sprintf("Failed to create test step: %v", err))
        }
        
        steps[i] = step
    }
    
    return steps
}

func (tdm *TestDataManager) generateStepContent(stepType string) map[string]interface{} {
    switch stepType {
    case "tutorial":
        return map[string]interface{}{
            "tutorial": map[string]interface{}{
                "content_type": "text",
                "content":      "This is a test tutorial step.",
                "duration":     300,
            },
        }
    case "form":
        return map[string]interface{}{
            "form": map[string]interface{}{
                "fields": []map[string]interface{}{
                    {
                        "name":     "test_field",
                        "type":     "text",
                        "label":    "Test Field",
                        "required": true,
                    },
                },
            },
        }
    case "action":
        return map[string]interface{}{
            "action": map[string]interface{}{
                "action_type": "click",
                "target":      "#test-button",
                "validation": map[string]interface{}{
                    "method": "element_clicked",
                },
            },
        }
    case "social":
        return map[string]interface{}{
            "social": map[string]interface{}{
                "social_action": "share",
                "platform":      "twitter",
                "share_content": map[string]interface{}{
                    "text": "Just completed onboarding!",
                },
            },
        }
    default:
        return map[string]interface{}{}
    }
}

func (tdm *TestDataManager) CleanupTestData() {
    // Clean up in reverse order of dependencies
    tdm.db.Where("1 = 1").Delete(&StepCompletion{})
    tdm.db.Where("1 = 1").Delete(&UserProgress{})
    tdm.db.Where("1 = 1").Delete(&OnboardingStep{})
    tdm.db.Where("1 = 1").Delete(&OnboardingJourney{})
}
```

## Test Automation with CI/CD

### GitHub Actions Configuration

```yaml
# .github/workflows/onboarding-tests.yml
name: Onboarding Module Tests

on:
  push:
    paths:
      - 'internal/modules/onboarding/**'
      - 'docs/modules/onboarding/**'
  pull_request:
    paths:
      - 'internal/modules/onboarding/**'
      - 'docs/modules/onboarding/**'

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: onboarding_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:6
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install dependencies
      run: go mod download
    
    - name: Run unit tests
      env:
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: root
        DB_NAME: onboarding_test
        REDIS_URL: redis://localhost:6379
      run: |
        go test -v -race -coverprofile=coverage.out ./internal/modules/onboarding/...
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: onboarding
        name: onboarding-coverage
  
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: onboarding_integration_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Run integration tests
      env:
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: root
        DB_NAME: onboarding_integration_test
      run: |
        go test -v -tags=integration ./tests/integration/onboarding/...
  
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Start test environment
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30 # Wait for services to be ready
    
    - name: Run E2E tests
      run: |
        go test -v -tags=e2e ./tests/e2e/onboarding/...
    
    - name: Cleanup
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down
```

## Best Practices

### Testing Principles

1. **Test Pyramid**
   - Many unit tests (fast, isolated)
   - Some integration tests (realistic, slower)
   - Few E2E tests (comprehensive, slowest)

2. **Test Independence**
   - Each test should be independent
   - Use test fixtures and cleanup
   - Avoid test order dependencies

3. **Multi-Tenant Testing**
   - Always test tenant isolation
   - Use different website IDs in tests
   - Verify cross-tenant access is blocked

4. **Performance Testing**
   - Test with realistic data volumes
   - Verify response times
   - Test concurrent user scenarios

5. **Error Handling**
   - Test both success and failure paths
   - Verify error messages
   - Test edge cases and boundary conditions