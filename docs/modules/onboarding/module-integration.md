# Module Integration

## Tích hợp với các module khác

### 1. Complete Module Integration Flow

```mermaid
flowchart TD
    subgraph "User Registration Flow"
        A[User Registration] --> B[Auth Module]
        B --> C{Registration Success?}
        C -->|Yes| D[Trigger Onboarding]
        C -->|No| E[Handle Error]
    end
    
    subgraph "Onboarding Initialization"
        D --> F[Onboarding Module]
        F --> G[Create User Progress]
        F --> H[Determine Journey Type]
        H --> I[Load Journey Steps]
    end
    
    subgraph "Module Communications"
        F --> J[User Module]
        J --> J1[Get User Profile]
        J --> J2[Update Preferences]
        
        F --> K[Notification Module]
        K --> K1[Send Welcome Email]
        K --> K2[Schedule Follow-ups]
        K --> K3[In-app Notifications]
        
        F --> L[Tenant Module]
        L --> L1[Get Tenant Settings]
        L --> L2[Apply Branding]
        L --> L3[Check Features]
        
        F --> M[Blog Module]
        M --> M1[Create First Post]
        M --> M2[Set Categories]
        
        F --> N[Media Module]
        N --> N1[Upload Avatar]
        N --> N2[Store Assets]
        
        F --> O[RBAC Module]
        O --> O1[Assign Roles]
        O --> O2[Set Permissions]
        
        F --> P[Analytics Module]
        P --> P1[Track Events]
        P --> P2[User Behavior]
        P --> P3[Conversion Funnel]
    end
    
    subgraph "Progress Tracking"
        Q[Track Progress] --> R[Update Database]
        R --> S[Send Updates]
        S --> K
        S --> P
    end
```

### 2. Auth Module Integration

```mermaid
sequenceDiagram
    participant User
    participant Auth as Auth Module
    participant Onboarding as Onboarding Module
    participant DB as Database
    participant Queue as Message Queue
    
    User->>Auth: Register Account
    Auth->>DB: Create User Record
    Auth->>Auth: Generate JWT Token
    Auth->>Queue: Publish "user.registered" Event
    
    Queue->>Onboarding: Consume Event
    Onboarding->>DB: Create Onboarding Progress
    Onboarding->>Onboarding: Determine Journey Type
    
    Note over Onboarding: Based on user role, source, etc.
    
    Onboarding->>Auth: Get User Details
    Auth->>Onboarding: Return User Info
    
    Onboarding->>User: Redirect to Onboarding
```

#### Implementation Details

```go
// Event Handler for user registration
func (s *OnboardingService) HandleUserRegistered(event UserRegisteredEvent) error {
    // Create onboarding progress record
    progress := &UserProgress{
        UserID:    event.UserID,
        WebsiteID: event.WebsiteID,
        JourneyID: s.determineJourneyType(event),
        Status:    "in_progress",
        StartedAt: time.Now(),
    }
    
    if err := s.repo.CreateProgress(progress); err != nil {
        return err
    }
    
    // Send welcome notification
    return s.notificationService.SendWelcomeEmail(event.UserID, event.WebsiteID)
}

func (s *OnboardingService) determineJourneyType(event UserRegisteredEvent) string {
    switch event.Role {
    case "admin":
        return "admin_onboarding"
    case "blogger", "author":
        return "blogger_onboarding"
    default:
        return "user_onboarding"
    }
}
```

### 3. User Module Integration

```mermaid
sequenceDiagram
    participant Onboarding
    participant User as User Module
    participant Media as Media Module
    participant Storage as File Storage
    
    Note over Onboarding: Profile Completion Step
    
    Onboarding->>User: Request Profile Data
    User->>Onboarding: Current Profile Status
    
    Onboarding->>User: Update Profile Fields
    User->>User: Validate Profile Data
    
    alt Avatar Upload
        User->>Media: Upload Avatar Request
        Media->>Storage: Store Image
        Storage->>Media: Return URL
        Media->>User: Update Avatar URL
    end
    
    User->>Onboarding: Profile Update Complete
    Onboarding->>Onboarding: Mark Step Complete
```

#### Profile Completion Logic

```go
type ProfileCompletionStep struct {
    RequiredFields []string
    OptionalFields []string
    MinCompleteness float64
}

func (s *OnboardingService) CheckProfileCompletion(userID, websiteID uint) (*StepResult, error) {
    profile, err := s.userService.GetProfile(userID, websiteID)
    if err != nil {
        return nil, err
    }
    
    completeness := s.calculateCompleteness(profile)
    
    result := &StepResult{
        StepID:     "profile_completion",
        Completed:  completeness >= 0.7, // 70% threshold
        Progress:   completeness,
        Message:    s.getCompletionMessage(completeness),
    }
    
    return result, nil
}
```

### 4. Notification Module Integration

```mermaid
flowchart LR
    subgraph "Onboarding Events"
        A[Journey Started]
        B[Step Completed]
        C[Journey Paused]
        D[Journey Completed]
    end
    
    subgraph "Notification Types"
        E[Welcome Email]
        F[Progress Update]
        G[Reminder Email]
        H[Completion Certificate]
        I[Tips & Tricks]
    end
    
    subgraph "Delivery Channels"
        J[Email]
        K[In-App]
        L[Push]
        M[SMS]
    end
    
    A --> E --> J
    A --> E --> K
    
    B --> F --> K
    B --> F --> L
    
    C --> G --> J
    C --> G --> L
    
    D --> H --> J
    D --> I --> J
```

#### Notification Templates

```go
type NotificationTemplate struct {
    ID          string
    Type        string // "email", "in_app", "push", "sms"
    Template    string
    Variables   map[string]interface{}
    Timing      NotificationTiming
}

type NotificationTiming struct {
    Trigger     string        // "immediate", "delayed", "scheduled"
    Delay       time.Duration
    ScheduledAt *time.Time
}

// Send welcome email sequence
func (s *OnboardingService) SendWelcomeSequence(userID, websiteID uint) error {
    templates := []NotificationTemplate{
        {
            ID:       "welcome_immediate",
            Type:     "email",
            Template: "welcome_email",
            Timing:   NotificationTiming{Trigger: "immediate"},
        },
        {
            ID:       "getting_started",
            Type:     "email", 
            Template: "getting_started_guide",
            Timing:   NotificationTiming{Trigger: "delayed", Delay: 24 * time.Hour},
        },
        {
            ID:       "progress_check",
            Type:     "email",
            Template: "progress_reminder",
            Timing:   NotificationTiming{Trigger: "delayed", Delay: 72 * time.Hour},
        },
    }
    
    for _, template := range templates {
        if err := s.notificationService.Schedule(userID, websiteID, template); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 5. Blog Module Integration

```mermaid
sequenceDiagram
    participant Onboarding
    participant Blog as Blog Module
    participant SEO as SEO Module
    participant Media as Media Module
    
    Note over Onboarding: Create First Post Step
    
    Onboarding->>Blog: Initialize Post Creation
    Blog->>Onboarding: Return Editor Config
    
    Onboarding->>Blog: Save Draft Post
    Blog->>SEO: Generate Meta Tags
    SEO->>Blog: Return SEO Data
    
    Onboarding->>Media: Upload Featured Image
    Media->>Blog: Attach Image to Post
    
    Onboarding->>Blog: Publish Post
    Blog->>Onboarding: Post Published Event
    Onboarding->>Onboarding: Complete Blog Step
```

#### First Post Creation Helper

```go
type FirstPostHelper struct {
    blogService BlogService
    templates   map[string]PostTemplate
}

type PostTemplate struct {
    Title       string
    Content     string
    Category    string
    Tags        []string
    SEOHints    map[string]string
}

func (h *FirstPostHelper) CreateGuidedPost(userID, websiteID uint, userInputs PostInputs) (*BlogPost, error) {
    template := h.getTemplateForUser(userInputs.Category)
    
    post := &BlogPost{
        Title:     userInputs.Title,
        Content:   h.mergeContentWithTemplate(template.Content, userInputs.Content),
        Category:  template.Category,
        Tags:      h.mergeTags(template.Tags, userInputs.Tags),
        AuthorID:  userID,
        WebsiteID: websiteID,
        Status:    "draft",
    }
    
    // Apply SEO optimization
    post.MetaTitle = h.generateMetaTitle(post.Title)
    post.MetaDescription = h.generateMetaDescription(post.Content)
    
    return h.blogService.CreatePost(post)
}
```

### 6. Tenant Module Integration

```mermaid
flowchart TD
    subgraph "Tenant Onboarding"
        A[Tenant Admin Registers] --> B[Create Tenant]
        B --> C[Initialize Tenant Settings]
        C --> D[Custom Onboarding Journey]
    end
    
    subgraph "Tenant Customization"
        D --> E[Brand Settings]
        E --> E1[Logo Upload]
        E --> E2[Color Scheme]
        E --> E3[Custom Domain]
        
        D --> F[Feature Selection]
        F --> F1[Enable Modules]
        F --> F2[Set Limits]
        F --> F3[Configure Integrations]
        
        D --> G[Team Setup]
        G --> G1[Invite Members]
        G --> G2[Assign Roles]
        G --> G3[Set Permissions]
    end
    
    subgraph "Module Activation"
        F1 --> H[Blog Module]
        F1 --> I[Payment Module]
        F1 --> J[Analytics Module]
        F1 --> K[SEO Module]
    end
```

#### Tenant-Specific Onboarding

```go
type TenantOnboardingConfig struct {
    TenantID           uint
    CustomJourneys     map[string]string
    BrandingSettings   BrandingConfig
    FeatureFlags       map[string]bool
    ModulePermissions  map[string][]string
}

func (s *OnboardingService) GetTenantConfig(tenantID uint) (*TenantOnboardingConfig, error) {
    tenant, err := s.tenantService.GetTenant(tenantID)
    if err != nil {
        return nil, err
    }
    
    config := &TenantOnboardingConfig{
        TenantID: tenantID,
        BrandingSettings: BrandingConfig{
            PrimaryColor:   tenant.PrimaryColor,
            SecondaryColor: tenant.SecondaryColor,
            LogoURL:       tenant.LogoURL,
            FontFamily:    tenant.FontFamily,
        },
        FeatureFlags: tenant.EnabledFeatures,
    }
    
    // Apply tenant-specific journey customizations
    config.CustomJourneys = s.loadCustomJourneys(tenantID)
    
    return config, nil
}
```

### 7. RBAC Module Integration

```mermaid
sequenceDiagram
    participant Onboarding
    participant RBAC as RBAC Module
    participant User as User Module
    participant Tenant as Tenant Module
    
    Note over Onboarding: Role Assignment Step
    
    Onboarding->>Tenant: Get Available Roles
    Tenant->>RBAC: List Tenant Roles
    RBAC->>Onboarding: Return Role Options
    
    Onboarding->>RBAC: Assign Role to User
    RBAC->>RBAC: Validate Role Assignment
    RBAC->>User: Update User Permissions
    
    User->>Onboarding: Permissions Updated
    Onboarding->>Onboarding: Update Journey Based on Role
```

#### Role-Based Journey Modification

```go
func (s *OnboardingService) AdjustJourneyForRole(progressID uint, newRole string) error {
    progress, err := s.repo.GetProgress(progressID)
    if err != nil {
        return err
    }
    
    // Get role-specific journey
    newJourneyID := s.getJourneyForRole(newRole)
    
    // Preserve completed steps that are common
    commonSteps := s.findCommonSteps(progress.JourneyID, newJourneyID)
    
    // Update progress with new journey
    progress.JourneyID = newJourneyID
    progress.CompletedSteps = s.filterCompletedSteps(progress.CompletedSteps, commonSteps)
    
    return s.repo.UpdateProgress(progress)
}

func (s *OnboardingService) getJourneyForRole(role string) string {
    roleJourneyMap := map[string]string{
        "admin":     "admin_onboarding",
        "blogger":   "blogger_onboarding", 
        "author":    "blogger_onboarding",
        "editor":    "editor_onboarding",
        "user":      "user_onboarding",
    }
    
    if journey, exists := roleJourneyMap[role]; exists {
        return journey
    }
    
    return "user_onboarding" // default
}
```

### 8. Analytics Module Integration

```mermaid
flowchart LR
    subgraph "Onboarding Events"
        A[Step Started]
        B[Step Completed]
        C[Step Failed]
        D[Journey Completed]
        E[Journey Abandoned]
    end
    
    subgraph "Analytics Processing"
        F[Event Collection]
        G[Data Aggregation]
        H[Metric Calculation]
        I[Report Generation]
    end
    
    subgraph "Insights"
        J[Completion Rate]
        K[Drop-off Points]
        L[Time to Complete]
        M[User Segments]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    E --> F
    
    F --> G --> H --> I
    
    I --> J
    I --> K
    I --> L
    I --> M
```

#### Event Tracking Implementation

```go
type OnboardingEvent struct {
    EventID     string
    UserID      uint
    WebsiteID   uint
    JourneyID   string
    StepID      string
    EventType   string // "started", "completed", "failed", "skipped"
    Timestamp   time.Time
    Duration    *time.Duration
    Metadata    map[string]interface{}
}

func (s *OnboardingService) TrackEvent(event OnboardingEvent) error {
    // Send to analytics module
    if err := s.analyticsService.Track(event); err != nil {
        return err
    }
    
    // Update progress tracking
    return s.updateProgressMetrics(event)
}

func (s *OnboardingService) updateProgressMetrics(event OnboardingEvent) error {
    switch event.EventType {
    case "step_completed":
        return s.markStepCompleted(event.UserID, event.WebsiteID, event.StepID)
    case "journey_completed":
        return s.markJourneyCompleted(event.UserID, event.WebsiteID, event.JourneyID)
    case "journey_abandoned":
        return s.markJourneyAbandoned(event.UserID, event.WebsiteID, event.JourneyID)
    }
    
    return nil
}
```

### 9. Payment Module Integration (for Paid Features)

```mermaid
sequenceDiagram
    participant Onboarding
    participant Payment as Payment Module
    participant Subscription as Subscription Service
    participant User
    
    Note over Onboarding: Premium Feature Selection
    
    Onboarding->>Payment: Get Available Plans
    Payment->>Onboarding: Return Plan Options
    
    User->>Onboarding: Select Premium Plan
    Onboarding->>Payment: Initialize Payment
    
    Payment->>User: Show Payment Form
    User->>Payment: Submit Payment
    
    Payment->>Subscription: Create Subscription
    Subscription->>Onboarding: Subscription Active
    
    Onboarding->>Onboarding: Unlock Premium Journey
```

### 10. SEO Module Integration

```mermaid
flowchart TD
    subgraph "SEO Onboarding Steps"
        A[Basic SEO Setup] --> B[Meta Tags]
        B --> C[Sitemap Config]
        C --> D[Analytics Setup]
        D --> E[Search Console]
    end
    
    subgraph "Module Actions"
        B --> B1[Title Template]
        B --> B2[Description Template]
        B --> B3[Social Media Tags]
        
        C --> C1[Enable Sitemap]
        C --> C2[Set Priorities]
        C --> C3[Exclude Pages]
        
        D --> D1[GA4 Setup]
        D --> D2[GTM Installation]
        
        E --> E1[Verify Domain]
        E --> E2[Submit Sitemap]
    end
```

### 11. Socket Module Integration (Real-time Updates)

```mermaid
sequenceDiagram
    participant Browser as User Browser
    participant Socket as Socket Module
    participant Onboarding
    participant Analytics
    
    Browser->>Socket: Connect WebSocket
    Socket->>Browser: Connection Established
    
    loop Progress Updates
        Browser->>Onboarding: Complete Step
        Onboarding->>Socket: Broadcast Progress
        Socket->>Browser: Update UI Real-time
        
        Onboarding->>Analytics: Track Event
        Analytics->>Socket: Update Metrics
        Socket->>Browser: Show Achievement
    end
    
    Note over Socket: Real-time notifications, progress bars, achievements
```

### 12. Media Module Integration

```mermaid
flowchart LR
    subgraph "Media Tasks in Onboarding"
        A[Avatar Upload]
        B[Logo Upload]
        C[Banner Images]
        D[Tutorial Videos]
        E[Document Upload]
    end
    
    subgraph "Media Processing"
        F[Upload Handler]
        G[Image Optimization]
        H[Video Processing]
        I[Storage Management]
    end
    
    subgraph "Integration Points"
        J[User Profile]
        K[Tenant Branding]
        L[Blog Posts]
        M[Help Content]
    end
    
    A --> F --> G --> J
    B --> F --> G --> K
    C --> F --> G --> L
    D --> F --> H --> M
    E --> F --> I --> M
```

## Module Communication Summary

| Source Module | Target Module | Event/Action | Purpose |
|--------------|---------------|--------------|---------|
| Auth | Onboarding | user.registered | Trigger onboarding journey |
| Onboarding | User | profile.update | Complete profile information |
| Onboarding | Notification | journey.started | Send welcome email |
| Onboarding | Blog | post.create | Guide first post creation |
| Onboarding | Tenant | settings.update | Configure tenant preferences |
| Onboarding | RBAC | role.assign | Set user permissions |
| Onboarding | Analytics | event.track | Monitor progress |
| Onboarding | Payment | plan.select | Handle premium features |
| Onboarding | SEO | meta.configure | Setup SEO basics |
| Onboarding | Socket | progress.update | Real-time UI updates |
| Onboarding | Media | file.upload | Handle media assets |