# API Endpoints

> **Note:** All list endpoints use cursor-based pagination. See [Cursor Pagination Guide](../../api/cursor-pagination.md) for details.

## Journey Management

### List Onboarding Journeys

```http
GET /api/cms/v1/onboarding/journeys?cursor={cursor}&limit=10
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `cursor` (optional): Cursor for pagination
- `limit` (optional): Number of items per page (default: 20, max: 100)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "website_id": 1,
      "tenant_id": 1,
      "name": "User Onboarding",
      "description": "Standard onboarding flow for new users",
      "type": "user",
      "version": "1.0",
      "is_default": true,
      "status": "active",
      "priority": 1,
      "settings": {
        "gamification": {
          "enabled": true,
          "points_per_step": 10
        },
        "notifications": {
          "email_enabled": true,
          "in_app_enabled": true
        }
      },
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": null,
    "has_more": true,
    "has_previous": false,
    "limit": 10
  }
}
```

### Get Journey Details

```http
GET /api/cms/v1/onboarding/journeys/{journey_id}
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "website_id": 1,
    "tenant_id": 1,
    "name": "User Onboarding",
    "description": "Standard onboarding flow for new users",
    "type": "user",
    "version": "1.0",
    "is_default": true,
    "status": "active",
    "priority": 1,
    "settings": {
      "gamification": {
        "enabled": true,
        "points_per_step": 10
      }
    },
    "steps": [
      {
        "id": 1,
        "name": "Complete Profile",
        "description": "Fill out your basic profile information",
        "type": "form",
        "order": 1,
        "required": true,
        "skippable": false,
        "estimated_duration": 5,
        "content": {
          "form": {
            "fields": [
              {
                "name": "full_name",
                "type": "text",
                "label": "Full Name",
                "required": true
              },
              {
                "name": "bio",
                "type": "textarea",
                "label": "Bio",
                "required": false
              }
            ]
          }
        }
      }
    ],
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### Create Journey

```http
POST /api/cms/v1/onboarding/journeys
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "New User Journey",
  "description": "Custom onboarding for new users",
  "type": "user",
  "is_default": false,
  "status": "active",
  "priority": 2,
  "settings": {
    "gamification": {
      "enabled": true,
      "points_per_step": 15
    },
    "notifications": {
      "email_enabled": true,
      "in_app_enabled": true,
      "push_enabled": false
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "website_id": 1,
    "tenant_id": 1,
    "name": "New User Journey",
    "description": "Custom onboarding for new users",
    "type": "user",
    "version": "1.0",
    "is_default": false,
    "status": "active",
    "priority": 2,
    "settings": {
      "gamification": {
        "enabled": true,
        "points_per_step": 15
      },
      "notifications": {
        "email_enabled": true,
        "in_app_enabled": true,
        "push_enabled": false
      }
    },
    "created_by": 1,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### Update Journey

```http
PUT /api/cms/v1/onboarding/journeys/{journey_id}
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Updated Journey Name",
  "description": "Updated description",
  "status": "active",
  "priority": 3,
  "settings": {
    "gamification": {
      "enabled": false
    }
  }
}
```

### Delete Journey

```http
DELETE /api/cms/v1/onboarding/journeys/{journey_id}
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "Journey deleted successfully"
}
```

## Step Management

### List Journey Steps

```http
GET /api/cms/v1/onboarding/journeys/{journey_id}/steps
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

### Create Step

```http
POST /api/cms/v1/onboarding/journeys/{journey_id}/steps
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Tutorial Step",
  "description": "Learn the basics",
  "type": "tutorial",
  "order": 1,
  "required": true,
  "skippable": false,
  "estimated_duration": 10,
  "content": {
    "tutorial": {
      "content_type": "video",
      "content": "Welcome to our platform! This video will guide you through the basics.",
      "media_url": "https://example.com/tutorial-video.mp4",
      "duration": 300
    }
  },
  "validation_rules": {
    "completion_criteria": "video_watched",
    "min_watch_percentage": 80
  }
}
```

### Update Step

```http
PUT /api/cms/v1/onboarding/steps/{step_id}
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

### Delete Step

```http
DELETE /api/cms/v1/onboarding/steps/{step_id}
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

## User Progress

### Get User Progress

```http
GET /api/cms/v1/onboarding/progress
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `journey_id` (optional): Filter by specific journey
- `user_id` (optional): Admin only - get progress for specific user

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "website_id": 1,
      "journey_id": 1,
      "current_step_id": 3,
      "completed_steps": [1, 2],
      "skipped_steps": [],
      "failed_steps": [],
      "status": "in_progress",
      "completion_score": 0.67,
      "started_at": "2023-01-01T00:00:00Z",
      "last_active_at": "2023-01-01T12:00:00Z",
      "total_time_spent": 45,
      "step_time_spent": {
        "1": 15,
        "2": 30
      },
      "retry_count": {
        "2": 1
      },
      "journey": {
        "id": 1,
        "name": "User Onboarding",
        "type": "user",
        "total_steps": 5
      }
    }
  ]
}
```

### Start Journey

```http
POST /api/cms/v1/onboarding/progress/start
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "journey_id": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "user_id": 123,
    "website_id": 1,
    "journey_id": 1,
    "current_step_id": 1,
    "status": "in_progress",
    "started_at": "2023-01-01T00:00:00Z",
    "next_step": {
      "id": 1,
      "name": "Complete Profile",
      "description": "Fill out your basic profile information",
      "type": "form",
      "estimated_duration": 5
    }
  }
}
```

### Complete Step

```http
POST /api/cms/v1/onboarding/progress/step
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "step_id": 1,
  "result": {
    "form_data": {
      "full_name": "John Doe",
      "bio": "Software developer passionate about tech"
    }
  },
  "time_spent": 300,
  "user_rating": 5,
  "user_feedback": "Easy to complete!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "step_completed": true,
    "journey_progress": 0.4,
    "points_earned": 10,
    "achievement_unlocked": {
      "type": "badge",
      "name": "Profile Master",
      "description": "Completed profile setup",
      "icon_url": "https://example.com/badges/profile-master.png"
    },
    "next_step": {
      "id": 2,
      "name": "Set Preferences",
      "description": "Customize your experience",
      "type": "form",
      "estimated_duration": 3
    }
  }
}
```

### Skip Step

```http
POST /api/cms/v1/onboarding/progress/skip
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "step_id": 2,
  "reason": "not_relevant"
}
```

### Pause Journey

```http
POST /api/cms/v1/onboarding/progress/pause
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "journey_id": 1,
  "reason": "will_continue_later"
}
```

### Resume Journey

```http
POST /api/cms/v1/onboarding/progress/resume
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "journey_id": 1
}
```

## Analytics

### Completion Rate Analytics

```http
GET /api/cms/v1/onboarding/analytics/completion
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `journey_id` (optional): Filter by journey
- `date_from` (optional): Start date (YYYY-MM-DD)
- `date_to` (optional): End date (YYYY-MM-DD)
- `group_by` (optional): day, week, month

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_completion_rate": 0.73,
    "total_users": 1000,
    "completed_users": 730,
    "avg_completion_time": "45m",
    "by_journey": [
      {
        "journey_id": 1,
        "journey_name": "User Onboarding",
        "completion_rate": 0.75,
        "total_users": 800,
        "completed_users": 600,
        "avg_completion_time": "40m"
      }
    ],
    "by_time_period": [
      {
        "date": "2023-01-01",
        "completion_rate": 0.72,
        "total_users": 50,
        "completed_users": 36
      }
    ]
  }
}
```

### Funnel Analysis

```http
GET /api/cms/v1/onboarding/analytics/funnel
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `journey_id` (required): Journey to analyze
- `date_from` (optional): Start date
- `date_to` (optional): End date

**Response:**
```json
{
  "success": true,
  "data": {
    "journey_id": 1,
    "journey_name": "User Onboarding",
    "overall_conversion_rate": 0.65,
    "steps": [
      {
        "step_id": 1,
        "step_name": "Complete Profile",
        "order": 1,
        "user_count": 1000,
        "conversion_rate": 1.0,
        "dropoff_rate": 0.0
      },
      {
        "step_id": 2,
        "step_name": "Set Preferences",
        "order": 2,
        "user_count": 850,
        "conversion_rate": 0.85,
        "dropoff_rate": 0.15
      },
      {
        "step_id": 3,
        "step_name": "Tutorial",
        "order": 3,
        "user_count": 650,
        "conversion_rate": 0.65,
        "dropoff_rate": 0.235
      }
    ],
    "dropoff_points": [
      {
        "from_step_id": 1,
        "to_step_id": 2,
        "dropoff_count": 150,
        "dropoff_rate": 0.15,
        "severity": "medium"
      },
      {
        "from_step_id": 2,
        "to_step_id": 3,
        "dropoff_count": 200,
        "dropoff_rate": 0.235,
        "severity": "high"
      }
    ]
  }
}
```

### User Segments Analysis

```http
GET /api/cms/v1/onboarding/analytics/segments
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `segment_by` (optional): role, source, device_type, location
- `date_from` (optional): Start date
- `date_to` (optional): End date

**Response:**
```json
{
  "success": true,
  "data": {
    "segment_type": "role",
    "segments": [
      {
        "segment_name": "user",
        "user_count": 800,
        "completion_rate": 0.75,
        "avg_completion_time": "40m",
        "characteristics": [
          "High engagement with tutorials",
          "Prefers step-by-step guidance"
        ]
      },
      {
        "segment_name": "blogger",
        "user_count": 150,
        "completion_rate": 0.68,
        "avg_completion_time": "55m",
        "characteristics": [
          "Spends more time on content creation steps",
          "Higher dropout at technical configuration"
        ]
      },
      {
        "segment_name": "admin",
        "user_count": 50,
        "completion_rate": 0.90,
        "avg_completion_time": "65m",
        "characteristics": [
          "Completes all technical steps",
          "Low dropout rate overall"
        ]
      }
    ]
  }
}
```

### Dashboard Metrics

```http
GET /api/cms/v1/onboarding/analytics/dashboard
X-Website-ID: {website_id}
Authorization: Bearer {token}
```

**Query Parameters:**
- `date_range` (optional): 7d, 30d, 90d (default: 30d)

**Response:**
```json
{
  "success": true,
  "data": {
    "website_id": 1,
    "date_range": {
      "start": "2023-01-01",
      "end": "2023-01-31"
    },
    "overview": {
      "total_users": 1000,
      "active_journeys": 3,
      "completion_rate": 0.73,
      "avg_completion_time": "45m"
    },
    "journey_metrics": [
      {
        "journey_id": 1,
        "journey_name": "User Onboarding",
        "user_count": 800,
        "completion_rate": 0.75,
        "avg_duration": "40m",
        "dropoff_rate": 0.25,
        "satisfaction": 4.2,
        "status": "healthy"
      }
    ],
    "user_segments": [
      {
        "segment_name": "mobile_users",
        "user_count": 600,
        "completion_rate": 0.68,
        "characteristics": ["Higher dropout on form steps"]
      }
    ],
    "completion_trend": [
      {
        "date": "2023-01-01",
        "value": 0.72,
        "change": 0.02
      }
    ],
    "critical_issues": [
      {
        "type": "high_dropout",
        "severity": "critical",
        "description": "Step 3 has 35% dropout rate",
        "affected_users": 200,
        "journey_id": 1,
        "step_id": 3,
        "detected_at": "2023-01-15T10:00:00Z"
      }
    ],
    "recommendations": [
      {
        "type": "step_optimization",
        "priority": "high",
        "title": "Simplify Tutorial Step",
        "description": "Tutorial step has high dropout. Consider splitting into smaller steps.",
        "expected_impact": "15% improvement in completion rate",
        "action_items": [
          "Break tutorial into 2-3 smaller steps",
          "Add progress indicators",
          "Provide skip option for experienced users"
        ]
      }
    ]
  }
}
```

## Templates

### List Templates

```http
GET /api/cms/v1/onboarding/templates
Authorization: Bearer {token}
```

**Query Parameters:**
- `category` (optional): Filter by category
- `industry` (optional): Filter by industry
- `public_only` (optional): true/false (default: false)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "SaaS User Onboarding",
      "description": "Complete onboarding template for SaaS users",
      "category": "user_onboarding",
      "industry": "technology",
      "is_public": true,
      "version": "1.0",
      "usage_count": 150,
      "rating": 4.5,
      "created_at": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### Get Template Details

```http
GET /api/cms/v1/onboarding/templates/{template_id}
Authorization: Bearer {token}
```

### Apply Template

```http
POST /api/cms/v1/onboarding/templates/{template_id}/apply
X-Website-ID: {website_id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "customize": {
    "journey_name": "My Custom Journey",
    "branding": {
      "primary_color": "#007bff",
      "logo_url": "https://example.com/logo.png"
    }
  }
}
```

## Error Responses

### Standard Error Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "field": "name",
      "message": "Name is required"
    }
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR` (400): Request validation failed
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource conflict (e.g., duplicate journey)
- `RATE_LIMITED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

### Website Isolation Errors

- `INVALID_WEBSITE_ID` (400): Website ID missing or invalid
- `RESOURCE_NOT_IN_WEBSITE` (404): Resource doesn't belong to the specified website
- `WEBSITE_ACCESS_DENIED` (403): User doesn't have access to this website

## Rate Limiting

API endpoints are rate-limited per user and website:

- **Standard endpoints**: 100 requests per minute
- **Analytics endpoints**: 20 requests per minute
- **Bulk operations**: 10 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1609459200
```