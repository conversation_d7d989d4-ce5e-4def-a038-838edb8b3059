# Data Models & Database Schema

## <PERSON><PERSON> hình dữ liệu

### OnboardingJourney

```go
type OnboardingJourney struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    
    // Journey Information
    Name        string    `gorm:"size:255;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    Type        string    `gorm:"size:50;not null" json:"type"` // user, admin, blogger
    Version     string    `gorm:"size:20;default:'1.0'" json:"version"`
    
    // Configuration
    IsDefault   bool      `gorm:"default:false" json:"is_default"`
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, inactive, draft
    Priority    int       `gorm:"default:0" json:"priority"`
    
    // Journey Settings
    Settings    JSON      `gorm:"type:json" json:"settings"`
    
    // Metadata
    CreatedBy   uint      `json:"created_by"`
    UpdatedBy   uint      `json:"updated_by"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    // Uses status field for soft deletes instead of DeletedAt
    
    // Relationships
    Website Website           `json:"-"`
    Tenant  Tenant           `json:"-"`
    Steps   []OnboardingStep `json:"steps,omitempty"`
    Creator User             `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
}

func (OnboardingJourney) TableName() string {
    return "blog_onboarding_journey"
}
```

### OnboardingStep

```go
type OnboardingStep struct {
    ID         uint      `gorm:"primarykey" json:"id"`
    WebsiteID  uint      `gorm:"not null;index" json:"website_id"`
    JourneyID  uint      `gorm:"not null;index" json:"journey_id"`
    
    // Step Information
    Name        string    `gorm:"size:255;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    Type        string    `gorm:"size:50;not null" json:"type"` // tutorial, action, form, social
    
    // Step Configuration
    Content     JSON      `gorm:"type:json" json:"content"`
    Order       int       `gorm:"not null" json:"order"`
    Required    bool      `gorm:"default:true" json:"required"`
    Skippable   bool      `gorm:"default:false" json:"skippable"`
    
    // Validation Rules
    ValidationRules JSON  `gorm:"type:json" json:"validation_rules"`
    
    // Timing & Progress
    EstimatedDuration int `gorm:"default:0" json:"estimated_duration"` // in minutes
    RetryLimit        int `gorm:"default:3" json:"retry_limit"`
    
    // Conditions
    ShowConditions JSON   `gorm:"type:json" json:"show_conditions"`
    SkipConditions JSON   `gorm:"type:json" json:"skip_conditions"`
    
    // Metadata
    Status    string    `gorm:"size:20;default:'active'" json:"status"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    
    // Relationships
    Journey OnboardingJourney `json:"-"`
}

func (OnboardingStep) TableName() string {
    return "blog_onboarding_step"
}
```

### UserProgress

```go
type UserProgress struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    UserID    uint      `gorm:"not null;index:idx_user_journey" json:"user_id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    JourneyID uint      `gorm:"not null;index:idx_user_journey" json:"journey_id"`
    
    // Progress Information
    CurrentStepID    uint     `gorm:"default:0" json:"current_step_id"`
    CompletedSteps   JSON     `gorm:"type:json" json:"completed_steps"` // array of step IDs
    SkippedSteps     JSON     `gorm:"type:json" json:"skipped_steps"`
    FailedSteps      JSON     `gorm:"type:json" json:"failed_steps"`
    
    // Status & Timing
    Status           string   `gorm:"size:20;default:'not_started'" json:"status"` // not_started, in_progress, completed, abandoned, paused
    CompletionScore  float64  `gorm:"default:0" json:"completion_score"` // 0.0 to 1.0
    
    // Timestamps
    StartedAt        *time.Time `json:"started_at"`
    CompletedAt      *time.Time `json:"completed_at"`
    LastActiveAt     *time.Time `json:"last_active_at"`
    AbandonedAt      *time.Time `json:"abandoned_at"`
    
    // User Data & Preferences
    UserData         JSON     `gorm:"type:json" json:"user_data"` // collected during onboarding
    Preferences      JSON     `gorm:"type:json" json:"preferences"`
    
    // Analytics
    TotalTimeSpent   int      `gorm:"default:0" json:"total_time_spent"` // in minutes
    StepTimeSpent    JSON     `gorm:"type:json" json:"step_time_spent"`
    RetryCount       JSON     `gorm:"type:json" json:"retry_count"`
    
    // Metadata
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    
    // Relationships
    User    User              `json:"user,omitempty"`
    Journey OnboardingJourney `json:"journey,omitempty"`
}

func (UserProgress) TableName() string {
    return "blog_user_onboarding_progress"
}

// Unique constraint for user + journey
func (up *UserProgress) BeforeCreate(tx *gorm.DB) error {
    var count int64
    tx.Model(&UserProgress{}).Where(
        "user_id = ? AND journey_id = ? AND website_id = ?", 
        up.UserID, up.JourneyID, up.WebsiteID,
    ).Count(&count)
    
    if count > 0 {
        return errors.New("user already has progress for this journey")
    }
    return nil
}
```

### StepCompletion

```go
type StepCompletion struct {
    ID         uint      `gorm:"primarykey" json:"id"`
    ProgressID uint      `gorm:"not null;index" json:"progress_id"`
    StepID     uint      `gorm:"not null;index" json:"step_id"`
    UserID     uint      `gorm:"not null;index" json:"user_id"`
    WebsiteID  uint      `gorm:"not null;index" json:"website_id"`
    
    // Completion Details
    Status        string    `gorm:"size:20;not null" json:"status"` // completed, skipped, failed
    AttemptCount  int       `gorm:"default:1" json:"attempt_count"`
    TimeSpent     int       `gorm:"default:0" json:"time_spent"` // in seconds
    
    // Result Data
    Result        JSON      `gorm:"type:json" json:"result"`
    UserInput     JSON      `gorm:"type:json" json:"user_input"`
    ValidationErrors JSON   `gorm:"type:json" json:"validation_errors"`
    
    // Feedback
    UserRating    *int      `json:"user_rating"` // 1-5 stars
    UserFeedback  string    `gorm:"type:text" json:"user_feedback"`
    
    // Timestamps
    StartedAt     time.Time  `json:"started_at"`
    CompletedAt   *time.Time `json:"completed_at"`
    
    // Relationships
    Progress UserProgress    `json:"-"`
    Step     OnboardingStep  `json:"step,omitempty"`
    User     User           `json:"-"`
}

func (StepCompletion) TableName() string {
    return "blog_onboarding_step_completion"
}
```

### OnboardingTemplate

```go
type OnboardingTemplate struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    
    // Template Information
    Name        string    `gorm:"size:255;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    Category    string    `gorm:"size:100" json:"category"`
    Industry    string    `gorm:"size:100" json:"industry"`
    
    // Template Content
    JourneyData JSON      `gorm:"type:json" json:"journey_data"`
    StepsData   JSON      `gorm:"type:json" json:"steps_data"`
    
    // Configuration
    IsPublic    bool      `gorm:"default:false" json:"is_public"`
    Version     string    `gorm:"size:20;default:'1.0'" json:"version"`
    
    // Usage Statistics
    UsageCount  int       `gorm:"default:0" json:"usage_count"`
    Rating      float64   `gorm:"default:0" json:"rating"`
    
    // Metadata
    CreatedBy   uint      `json:"created_by"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Creator User `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
}

func (OnboardingTemplate) TableName() string {
    return "blog_onboarding_template"
}
```

### OnboardingEvent

```go
type OnboardingEvent struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    JourneyID uint      `gorm:"not null;index" json:"journey_id"`
    StepID    *uint     `gorm:"index" json:"step_id"`
    
    // Event Information
    EventType   string    `gorm:"size:50;not null;index" json:"event_type"`
    EventAction string    `gorm:"size:100" json:"event_action"`
    
    // Event Data
    EventData   JSON      `gorm:"type:json" json:"event_data"`
    UserAgent   string    `gorm:"size:500" json:"user_agent"`
    IPAddress   string    `gorm:"size:45" json:"ip_address"`
    SessionID   string    `gorm:"size:255" json:"session_id"`
    
    // Timing
    Duration    *int      `json:"duration"` // in milliseconds
    CreatedAt   time.Time `gorm:"index" json:"created_at"`
    
    // Relationships
    User    User              `json:"-"`
    Journey OnboardingJourney `json:"-"`
    Step    *OnboardingStep   `json:"step,omitempty"`
}

func (OnboardingEvent) TableName() string {
    return "blog_onboarding_event"
}

// Event Types Constants
const (
    EventJourneyStarted    = "journey_started"
    EventJourneyCompleted  = "journey_completed"
    EventJourneyAbandoned  = "journey_abandoned"
    EventJourneyPaused     = "journey_paused"
    EventJourneyResumed    = "journey_resumed"
    
    EventStepStarted       = "step_started"
    EventStepCompleted     = "step_completed"
    EventStepSkipped       = "step_skipped"
    EventStepFailed        = "step_failed"
    EventStepRetried       = "step_retried"
    
    EventUserAction        = "user_action"
    EventFormSubmitted     = "form_submitted"
    EventTutorialViewed    = "tutorial_viewed"
    EventHelpRequested     = "help_requested"
)
```

## Database Indexes

### Performance Indexes

```sql
-- OnboardingJourney indexes
CREATE INDEX idx_onboarding_journey_website_id ON blog_onboarding_journey(website_id);
CREATE INDEX idx_onboarding_journey_tenant_id ON blog_onboarding_journey(tenant_id);
CREATE INDEX idx_onboarding_journey_type ON blog_onboarding_journey(type);
CREATE INDEX idx_onboarding_journey_status ON blog_onboarding_journey(status);

-- OnboardingStep indexes
CREATE INDEX idx_onboarding_step_journey_id ON blog_onboarding_step(journey_id);
CREATE INDEX idx_onboarding_step_website_id ON blog_onboarding_step(website_id);
CREATE INDEX idx_onboarding_step_order ON blog_onboarding_step(journey_id, order);
CREATE INDEX idx_onboarding_step_type ON blog_onboarding_step(type);

-- UserProgress indexes
CREATE UNIQUE INDEX idx_user_progress_unique ON blog_user_onboarding_progress(user_id, journey_id, website_id);
CREATE INDEX idx_user_progress_website_id ON blog_user_onboarding_progress(website_id);
CREATE INDEX idx_user_progress_status ON blog_user_onboarding_progress(status);
CREATE INDEX idx_user_progress_created_at ON blog_user_onboarding_progress(created_at);

-- StepCompletion indexes
CREATE INDEX idx_step_completion_progress_id ON blog_onboarding_step_completion(progress_id);
CREATE INDEX idx_step_completion_step_id ON blog_onboarding_step_completion(step_id);
CREATE INDEX idx_step_completion_user_id ON blog_onboarding_step_completion(user_id);
CREATE INDEX idx_step_completion_website_id ON blog_onboarding_step_completion(website_id);
CREATE INDEX idx_step_completion_status ON blog_onboarding_step_completion(status);

-- OnboardingEvent indexes
CREATE INDEX idx_onboarding_event_user_id ON blog_onboarding_event(user_id);
CREATE INDEX idx_onboarding_event_website_id ON blog_onboarding_event(website_id);
CREATE INDEX idx_onboarding_event_journey_id ON blog_onboarding_event(journey_id);
CREATE INDEX idx_onboarding_event_type ON blog_onboarding_event(event_type);
CREATE INDEX idx_onboarding_event_created_at ON blog_onboarding_event(created_at);

-- Composite indexes for common queries
CREATE INDEX idx_onboarding_event_user_type_date ON blog_onboarding_event(user_id, event_type, created_at);
CREATE INDEX idx_user_progress_website_status ON blog_user_onboarding_progress(website_id, status);
```

## JSON Schema Definitions

### Journey Settings Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "gamification": {
      "type": "object",
      "properties": {
        "enabled": {"type": "boolean"},
        "points_per_step": {"type": "integer"},
        "badges": {
          "type": "array",
          "items": {"type": "string"}
        }
      }
    },
    "personalization": {
      "type": "object",
      "properties": {
        "adaptive": {"type": "boolean"},
        "user_profiling": {"type": "boolean"},
        "content_recommendations": {"type": "boolean"}
      }
    },
    "notifications": {
      "type": "object",
      "properties": {
        "email_enabled": {"type": "boolean"},
        "in_app_enabled": {"type": "boolean"},
        "push_enabled": {"type": "boolean"},
        "reminder_schedule": {
          "type": "array",
          "items": {"type": "string"}
        }
      }
    },
    "completion_criteria": {
      "type": "object",
      "properties": {
        "required_steps_percentage": {"type": "number"},
        "optional_steps_for_completion": {"type": "integer"}
      }
    }
  }
}
```

### Step Content Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "type": {
      "type": "string",
      "enum": ["tutorial", "action", "form", "social"]
    },
    "tutorial": {
      "type": "object",
      "properties": {
        "content_type": {
          "type": "string",
          "enum": ["text", "video", "interactive", "slideshow"]
        },
        "content": {"type": "string"},
        "media_url": {"type": "string"},
        "duration": {"type": "integer"},
        "interactive_elements": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "type": {"type": "string"},
              "selector": {"type": "string"},
              "action": {"type": "string"},
              "content": {"type": "string"}
            }
          }
        }
      }
    },
    "action": {
      "type": "object",
      "properties": {
        "action_type": {
          "type": "string",
          "enum": ["click", "form_submit", "navigation", "upload", "api_call"]
        },
        "target": {"type": "string"},
        "validation": {
          "type": "object",
          "properties": {
            "method": {"type": "string"},
            "criteria": {"type": "object"}
          }
        },
        "help_content": {"type": "string"},
        "success_message": {"type": "string"},
        "error_message": {"type": "string"}
      }
    },
    "form": {
      "type": "object",
      "properties": {
        "fields": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "name": {"type": "string"},
              "type": {"type": "string"},
              "label": {"type": "string"},
              "required": {"type": "boolean"},
              "validation": {"type": "object"},
              "options": {"type": "array"}
            }
          }
        },
        "submit_action": {"type": "string"},
        "success_redirect": {"type": "string"}
      }
    },
    "social": {
      "type": "object",
      "properties": {
        "social_action": {
          "type": "string",
          "enum": ["follow", "share", "connect", "invite"]
        },
        "platform": {"type": "string"},
        "target_user": {"type": "string"},
        "share_content": {"type": "object"},
        "incentive": {
          "type": "object",
          "properties": {
            "type": {"type": "string"},
            "value": {"type": "string"}
          }
        }
      }
    }
  }
}
```

### User Data Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "profile": {
      "type": "object",
      "properties": {
        "experience_level": {"type": "string"},
        "goals": {
          "type": "array",
          "items": {"type": "string"}
        },
        "interests": {
          "type": "array", 
          "items": {"type": "string"}
        },
        "time_availability": {"type": "string"},
        "preferred_learning_style": {"type": "string"}
      }
    },
    "preferences": {
      "type": "object",
      "properties": {
        "notification_frequency": {"type": "string"},
        "content_density": {"type": "string"},
        "ui_complexity": {"type": "string"},
        "language": {"type": "string"}
      }
    },
    "progress_data": {
      "type": "object",
      "properties": {
        "completion_timestamps": {"type": "object"},
        "step_durations": {"type": "object"},
        "retry_counts": {"type": "object"},
        "skip_reasons": {"type": "object"}
      }
    }
  }
}
```

## Repository Patterns with Multi-tenancy

### Base Repository Interface

```go
type OnboardingRepository interface {
    // Journey Management
    GetJourneysByWebsiteID(websiteID uint) ([]*OnboardingJourney, error)
    GetJourneyByIDAndWebsite(journeyID, websiteID uint) (*OnboardingJourney, error)
    CreateJourney(journey *OnboardingJourney) error
    UpdateJourney(journey *OnboardingJourney) error
    DeleteJourney(journeyID, websiteID uint) error
    
    // Step Management
    GetStepsByJourneyID(journeyID, websiteID uint) ([]*OnboardingStep, error)
    GetStepByIDAndWebsite(stepID, websiteID uint) (*OnboardingStep, error)
    CreateStep(step *OnboardingStep) error
    UpdateStep(step *OnboardingStep) error
    
    // Progress Tracking
    GetUserProgressByWebsite(userID, websiteID uint) ([]*UserProgress, error)
    GetProgressByJourney(userID, journeyID, websiteID uint) (*UserProgress, error)
    CreateProgress(progress *UserProgress) error
    UpdateProgress(progress *UserProgress) error
    
    // Step Completion
    CreateStepCompletion(completion *StepCompletion) error
    GetStepCompletions(progressID uint) ([]*StepCompletion, error)
    
    // Analytics
    CreateEvent(event *OnboardingEvent) error
    GetEventsByWebsite(websiteID uint, filters EventFilters) ([]*OnboardingEvent, error)
    
    // Templates
    GetPublicTemplates() ([]*OnboardingTemplate, error)
    GetTemplatesByCategory(category string) ([]*OnboardingTemplate, error)
}
```

### Implementation with Tenant Isolation

```go
type onboardingRepository struct {
    db *gorm.DB
}

func (r *onboardingRepository) GetJourneysByWebsiteID(websiteID uint) ([]*OnboardingJourney, error) {
    var journeys []*OnboardingJourney
    err := r.db.Where("website_id = ? AND status = ?", websiteID, "active").
        Order("priority DESC, created_at ASC").
        Find(&journeys).Error
    return journeys, err
}

func (r *onboardingRepository) GetUserProgressByWebsite(userID, websiteID uint) ([]*UserProgress, error) {
    var progress []*UserProgress
    err := r.db.Where("user_id = ? AND website_id = ?", userID, websiteID).
        Preload("Journey").
        Find(&progress).Error
    return progress, err
}

func (r *onboardingRepository) CreateProgress(progress *UserProgress) error {
    // Ensure tenant isolation
    if progress.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    // Verify journey belongs to the same website
    var journey OnboardingJourney
    if err := r.db.First(&journey, progress.JourneyID).Error; err != nil {
        return err
    }
    
    if journey.WebsiteID != progress.WebsiteID {
        return errors.New("journey does not belong to the specified website")
    }
    
    return r.db.Create(progress).Error
}
```