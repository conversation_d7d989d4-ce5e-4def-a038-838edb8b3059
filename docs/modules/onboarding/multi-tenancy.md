# Multi-Tenancy Implementation

## Tenant Isolation Strategy

### Database-Level Isolation

```go
// All onboarding models include website_id for tenant isolation
type OnboardingJourney struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"` // Tenant isolation
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`   // Additional isolation
    
    Name        string    `gorm:"size:255;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    Type        string    `gorm:"size:50;not null" json:"type"`
    Status      string    `gorm:"size:20;default:'active'" json:"status"`
    
    // Tenant-specific settings
    Settings    JSON      `gorm:"type:json" json:"settings"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    // Uses status field for soft deletes instead of DeletedAt
}

type OnboardingStep struct {
    ID         uint      `gorm:"primarykey" json:"id"`
    WebsiteID  uint      `gorm:"not null;index" json:"website_id"` // Tenant isolation
    JourneyID  uint      `gorm:"not null;index" json:"journey_id"`
    
    Name        string    `gorm:"size:255;not null" json:"name"`
    Type        string    `gorm:"size:50;not null" json:"type"`
    Content     JSON      `gorm:"type:json" json:"content"`
    Order       int       `gorm:"not null" json:"order"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

type UserProgress struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    UserID    uint      `gorm:"not null;index:idx_user_journey" json:"user_id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"` // Tenant isolation
    JourneyID uint      `gorm:"not null;index:idx_user_journey" json:"journey_id"`
    
    Status           string   `gorm:"size:20;default:'not_started'" json:"status"`
    CurrentStepID    uint     `gorm:"default:0" json:"current_step_id"`
    CompletedSteps   JSON     `gorm:"type:json" json:"completed_steps"`
    
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// Composite indexes for tenant isolation and performance
func (OnboardingJourney) TableName() string {
    return "blog_onboarding_journey"
}

func (OnboardingStep) TableName() string {
    return "blog_onboarding_step"
}

func (UserProgress) TableName() string {
    return "blog_user_onboarding_progress"
}
```

### Repository Pattern with Tenant Filtering

```go
type OnboardingRepository interface {
    // Journey Management - All methods include website_id
    GetJourneysByWebsiteID(websiteID uint) ([]*OnboardingJourney, error)
    GetJourneyByIDAndWebsite(journeyID, websiteID uint) (*OnboardingJourney, error)
    CreateJourney(journey *OnboardingJourney) error
    UpdateJourney(journey *OnboardingJourney) error
    DeleteJourney(journeyID, websiteID uint) error
    
    // Step Management - Website isolation
    GetStepsByJourneyID(journeyID, websiteID uint) ([]*OnboardingStep, error)
    GetStepByIDAndWebsite(stepID, websiteID uint) (*OnboardingStep, error)
    CreateStep(step *OnboardingStep) error
    UpdateStep(step *OnboardingStep) error
    
    // Progress Tracking - User and website isolation
    GetUserProgressByWebsite(userID, websiteID uint) ([]*UserProgress, error)
    GetProgressByJourney(userID, journeyID, websiteID uint) (*UserProgress, error)
    CreateProgress(progress *UserProgress) error
    UpdateProgress(progress *UserProgress) error
}

type onboardingRepository struct {
    db *gorm.DB
}

func NewOnboardingRepository(db *gorm.DB) OnboardingRepository {
    return &onboardingRepository{db: db}
}

// Always filter by website_id to ensure tenant isolation
func (r *onboardingRepository) GetJourneysByWebsiteID(websiteID uint) ([]*OnboardingJourney, error) {
    var journeys []*OnboardingJourney
    err := r.db.Where("website_id = ? AND status = ?", websiteID, "active").
        Order("priority DESC, created_at ASC").
        Find(&journeys).Error
    return journeys, err
}

func (r *onboardingRepository) GetJourneyByIDAndWebsite(journeyID, websiteID uint) (*OnboardingJourney, error) {
    var journey OnboardingJourney
    err := r.db.Where("id = ? AND website_id = ?", journeyID, websiteID).
        First(&journey).Error
    return &journey, err
}

func (r *onboardingRepository) GetUserProgressByWebsite(userID, websiteID uint) ([]*UserProgress, error) {
    var progress []*UserProgress
    err := r.db.Where("user_id = ? AND website_id = ?", userID, websiteID).
        Preload("Journey").
        Find(&progress).Error
    return progress, err
}

// Validate tenant ownership before operations
func (r *onboardingRepository) CreateProgress(progress *UserProgress) error {
    // Ensure website_id is set
    if progress.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    // Verify journey belongs to the same website
    var journey OnboardingJourney
    if err := r.db.Select("id, website_id").Where("id = ?", progress.JourneyID).First(&journey).Error; err != nil {
        return err
    }
    
    if journey.WebsiteID != progress.WebsiteID {
        return errors.New("journey does not belong to the specified website")
    }
    
    return r.db.Create(progress).Error
}

func (r *onboardingRepository) UpdateProgress(progress *UserProgress) error {
    // Ensure we only update progress for the correct website
    result := r.db.Where("id = ? AND website_id = ?", progress.ID, progress.WebsiteID).
        Updates(progress)
    
    if result.Error != nil {
        return result.Error
    }
    
    if result.RowsAffected == 0 {
        return errors.New("progress not found or access denied")
    }
    
    return nil
}
```

### Service Layer with Website Context

```go
type OnboardingService struct {
    repo  OnboardingRepository
    cache CacheService
    eventBus EventBus
}

func NewOnboardingService(repo OnboardingRepository, cache CacheService, eventBus EventBus) *OnboardingService {
    return &OnboardingService{
        repo:     repo,
        cache:    cache,
        eventBus: eventBus,
    }
}

// All service methods require website context
func (s *OnboardingService) GetJourneyForWebsite(websiteID, journeyID uint) (*OnboardingJourney, error) {
    // Check cache first with website-specific key
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("journey:%d", journeyID))
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*OnboardingJourney), nil
    }
    
    // Fetch from database with website_id filter
    journey, err := s.repo.GetJourneyByIDAndWebsite(journeyID, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result with website-specific key
    s.cache.Set(cacheKey, journey, 30*time.Minute)
    return journey, nil
}

func (s *OnboardingService) CreateJourneyForWebsite(websiteID uint, journeyData CreateJourneyRequest) (*OnboardingJourney, error) {
    journey := &OnboardingJourney{
        WebsiteID:   websiteID,
        Name:        journeyData.Name,
        Description: journeyData.Description,
        Type:        journeyData.Type,
        Status:      "active",
        Settings:    journeyData.Settings,
    }
    
    if err := s.repo.CreateJourney(journey); err != nil {
        return nil, err
    }
    
    // Publish event with website context
    s.eventBus.Publish("onboarding.journey.created", map[string]interface{}{
        "journey_id": journey.ID,
        "website_id": websiteID,
        "type":       journey.Type,
    })
    
    return journey, nil
}

func (s *OnboardingService) StartJourneyForUser(userID, websiteID, journeyID uint) (*UserProgress, error) {
    // Verify journey exists and belongs to website
    journey, err := s.GetJourneyForWebsite(websiteID, journeyID)
    if err != nil {
        return nil, err
    }
    
    // Check if user already has progress for this journey
    existingProgress, err := s.repo.GetProgressByJourney(userID, journeyID, websiteID)
    if err == nil && existingProgress != nil {
        return existingProgress, nil // User already started this journey
    }
    
    // Create new progress record
    progress := &UserProgress{
        UserID:    userID,
        WebsiteID: websiteID,
        JourneyID: journeyID,
        Status:    "in_progress",
        StartedAt: &time.Time{},
    }
    *progress.StartedAt = time.Now()
    
    if err := s.repo.CreateProgress(progress); err != nil {
        return nil, err
    }
    
    // Clear cache
    s.clearUserProgressCache(userID, websiteID)
    
    return progress, nil
}

// Website-specific cache keys
func (s *OnboardingService) getCacheKey(websiteID uint, suffix string) string {
    return fmt.Sprintf("onboarding:website:%d:%s", websiteID, suffix)
}

func (s *OnboardingService) clearUserProgressCache(userID, websiteID uint) {
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("user_progress:%d", userID))
    s.cache.Delete(cacheKey)
}
```

### API Layer with Website Context Middleware

```go
// Middleware to extract and validate website context
func WebsiteContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            c.JSON(400, gin.H{"error": "Website ID is required"})
            c.Abort()
            return
        }
        
        id, err := strconv.ParseUint(websiteID, 10, 32)
        if err != nil {
            c.JSON(400, gin.H{"error": "Invalid website ID"})
            c.Abort()
            return
        }
        
        // TODO: Verify user has access to this website
        if !userHasAccessToWebsite(c, uint(id)) {
            c.JSON(403, gin.H{"error": "Access denied to website"})
            c.Abort()
            return
        }
        
        c.Set("website_id", uint(id))
        c.Next()
    }
}

// Handler example with website context
func (h *OnboardingHandler) GetJourneys(c *gin.Context) {
    websiteID := c.GetUint("website_id")
    
    journeys, err := h.service.GetJourneysByWebsite(websiteID)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{
        "success": true,
        "data":    journeys,
    })
}

func (h *OnboardingHandler) CreateJourney(c *gin.Context) {
    websiteID := c.GetUint("website_id")
    
    var req CreateJourneyRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    journey, err := h.service.CreateJourneyForWebsite(websiteID, req)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(201, gin.H{
        "success": true,
        "data":    journey,
    })
}

func (h *OnboardingHandler) StartJourney(c *gin.Context) {
    websiteID := c.GetUint("website_id")
    userID := getUserIDFromToken(c) // Extract from JWT
    
    var req StartJourneyRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    progress, err := h.service.StartJourneyForUser(userID, websiteID, req.JourneyID)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{
        "success": true,
        "data":    progress,
    })
}
```

### Cache Strategy with Tenant Isolation

```go
type TenantAwareCacheService struct {
    cache  CacheService
    prefix string
}

func NewTenantAwareCacheService(cache CacheService) *TenantAwareCacheService {
    return &TenantAwareCacheService{
        cache:  cache,
        prefix: "onboarding",
    }
}

func (c *TenantAwareCacheService) Set(websiteID uint, key string, value interface{}, ttl time.Duration) error {
    tenantKey := c.buildTenantKey(websiteID, key)
    return c.cache.Set(tenantKey, value, ttl)
}

func (c *TenantAwareCacheService) Get(websiteID uint, key string) (interface{}, bool) {
    tenantKey := c.buildTenantKey(websiteID, key)
    return c.cache.Get(tenantKey)
}

func (c *TenantAwareCacheService) Delete(websiteID uint, key string) error {
    tenantKey := c.buildTenantKey(websiteID, key)
    return c.cache.Delete(tenantKey)
}

func (c *TenantAwareCacheService) DeletePattern(websiteID uint, pattern string) error {
    tenantPattern := c.buildTenantKey(websiteID, pattern)
    return c.cache.DeletePattern(tenantPattern)
}

func (c *TenantAwareCacheService) buildTenantKey(websiteID uint, key string) string {
    return fmt.Sprintf("%s:website:%d:%s", c.prefix, websiteID, key)
}

// Usage in service layer
func (s *OnboardingService) CacheJourney(websiteID uint, journey *OnboardingJourney) {
    key := fmt.Sprintf("journey:%d", journey.ID)
    s.cache.Set(websiteID, key, journey, 30*time.Minute)
}

func (s *OnboardingService) GetCachedJourney(websiteID, journeyID uint) (*OnboardingJourney, bool) {
    key := fmt.Sprintf("journey:%d", journeyID)
    if cached, found := s.cache.Get(websiteID, key); found {
        return cached.(*OnboardingJourney), true
    }
    return nil, false
}
```

### Database Migrations for Multi-Tenancy

```sql
-- Onboarding Journey table with tenant isolation
CREATE TABLE blog_onboarding_journey (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    website_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    is_default BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active',
    priority INT DEFAULT 0,
    settings JSON,
    created_by BIGINT,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Uses status field for soft deletes instead of deleted_at
    
    INDEX idx_website_id (website_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    -- Removed deleted_at index as we use status field instead
    
    -- Composite indexes for common queries
    INDEX idx_website_status (website_id, status),
    INDEX idx_website_type (website_id, type),
    
    FOREIGN KEY (website_id) REFERENCES blog_websites(id) ON DELETE CASCADE
);

-- Onboarding Step table
CREATE TABLE blog_onboarding_step (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    website_id BIGINT NOT NULL,
    journey_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    content JSON,
    order_num INT NOT NULL,
    required BOOLEAN DEFAULT TRUE,
    skippable BOOLEAN DEFAULT FALSE,
    validation_rules JSON,
    estimated_duration INT DEFAULT 0,
    retry_limit INT DEFAULT 3,
    show_conditions JSON,
    skip_conditions JSON,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_id (website_id),
    INDEX idx_journey_id (journey_id),
    INDEX idx_type (type),
    INDEX idx_order (journey_id, order_num),
    
    FOREIGN KEY (website_id) REFERENCES blog_websites(id) ON DELETE CASCADE,
    FOREIGN KEY (journey_id) REFERENCES blog_onboarding_journey(id) ON DELETE CASCADE
);

-- User Progress table
CREATE TABLE blog_user_onboarding_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    website_id BIGINT NOT NULL,
    journey_id BIGINT NOT NULL,
    current_step_id BIGINT DEFAULT 0,
    completed_steps JSON,
    skipped_steps JSON,
    failed_steps JSON,
    status VARCHAR(20) DEFAULT 'not_started',
    completion_score DECIMAL(3,2) DEFAULT 0.00,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    last_active_at TIMESTAMP NULL,
    abandoned_at TIMESTAMP NULL,
    user_data JSON,
    preferences JSON,
    total_time_spent INT DEFAULT 0,
    step_time_spent JSON,
    retry_count JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_website_id (website_id),
    INDEX idx_journey_id (journey_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_user_website (user_id, website_id),
    INDEX idx_website_status (website_id, status),
    
    -- Unique constraint to prevent duplicate progress per user per journey per website
    UNIQUE KEY uk_user_journey_website (user_id, journey_id, website_id),
    
    FOREIGN KEY (user_id) REFERENCES blog_users(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES blog_websites(id) ON DELETE CASCADE,
    FOREIGN KEY (journey_id) REFERENCES blog_onboarding_journey(id) ON DELETE CASCADE
);

-- Step Completion table
CREATE TABLE blog_onboarding_step_completion (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    progress_id BIGINT NOT NULL,
    step_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    website_id BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL,
    attempt_count INT DEFAULT 1,
    time_spent INT DEFAULT 0,
    result JSON,
    user_input JSON,
    validation_errors JSON,
    user_rating INT,
    user_feedback TEXT,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP NULL,
    
    INDEX idx_progress_id (progress_id),
    INDEX idx_step_id (step_id),
    INDEX idx_user_id (user_id),
    INDEX idx_website_id (website_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    
    FOREIGN KEY (progress_id) REFERENCES blog_user_onboarding_progress(id) ON DELETE CASCADE,
    FOREIGN KEY (step_id) REFERENCES blog_onboarding_step(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES blog_users(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES blog_websites(id) ON DELETE CASCADE
);

-- Onboarding Events table for analytics
CREATE TABLE blog_onboarding_event (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    website_id BIGINT NOT NULL,
    journey_id BIGINT NOT NULL,
    step_id BIGINT,
    event_type VARCHAR(50) NOT NULL,
    event_action VARCHAR(100),
    event_data JSON,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    session_id VARCHAR(255),
    duration INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_website_id (website_id),
    INDEX idx_journey_id (journey_id),
    INDEX idx_step_id (step_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    
    -- Composite indexes for analytics queries
    INDEX idx_website_event_date (website_id, event_type, created_at),
    INDEX idx_user_event_date (user_id, event_type, created_at),
    
    FOREIGN KEY (user_id) REFERENCES blog_users(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES blog_websites(id) ON DELETE CASCADE,
    FOREIGN KEY (journey_id) REFERENCES blog_onboarding_journey(id) ON DELETE CASCADE
);
```

### Security Considerations

```go
// Security service to validate website access
type WebsiteSecurityService struct {
    userRepo    UserRepository
    websiteRepo WebsiteRepository
}

func (s *WebsiteSecurityService) ValidateWebsiteAccess(userID, websiteID uint) error {
    // Check if user has access to the website
    hasAccess, err := s.userRepo.HasWebsiteAccess(userID, websiteID)
    if err != nil {
        return err
    }
    
    if !hasAccess {
        return errors.New("user does not have access to this website")
    }
    
    // Check if website is active
    website, err := s.websiteRepo.GetByID(websiteID)
    if err != nil {
        return err
    }
    
    if website.Status != "active" {
        return errors.New("website is not active")
    }
    
    return nil
}

// Enhanced middleware with security validation
func SecureWebsiteContextMiddleware(securityService *WebsiteSecurityService) gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            c.JSON(400, gin.H{"error": "Website ID is required"})
            c.Abort()
            return
        }
        
        id, err := strconv.ParseUint(websiteID, 10, 32)
        if err != nil {
            c.JSON(400, gin.H{"error": "Invalid website ID"})
            c.Abort()
            return
        }
        
        userID := getUserIDFromToken(c)
        if err := securityService.ValidateWebsiteAccess(userID, uint(id)); err != nil {
            c.JSON(403, gin.H{"error": "Access denied"})
            c.Abort()
            return
        }
        
        c.Set("website_id", uint(id))
        c.Set("user_id", userID)
        c.Next()
    }
}
```

### Event System with Tenant Context

```go
type TenantEvent struct {
    Type      string
    WebsiteID uint
    UserID    uint
    Data      map[string]interface{}
    Timestamp time.Time
}

type TenantEventBus struct {
    handlers map[string][]TenantEventHandler
    mutex    sync.RWMutex
}

type TenantEventHandler func(event TenantEvent) error

func (bus *TenantEventBus) Subscribe(eventType string, handler TenantEventHandler) {
    bus.mutex.Lock()
    defer bus.mutex.Unlock()
    
    if bus.handlers == nil {
        bus.handlers = make(map[string][]TenantEventHandler)
    }
    
    bus.handlers[eventType] = append(bus.handlers[eventType], handler)
}

func (bus *TenantEventBus) Publish(event TenantEvent) error {
    bus.mutex.RLock()
    handlers := bus.handlers[event.Type]
    bus.mutex.RUnlock()
    
    for _, handler := range handlers {
        if err := handler(event); err != nil {
            // Log error but continue with other handlers
            log.Printf("Event handler error: %v", err)
        }
    }
    
    return nil
}

// Usage in onboarding service
func (s *OnboardingService) PublishJourneyStarted(userID, websiteID, journeyID uint) {
    event := TenantEvent{
        Type:      "onboarding.journey.started",
        WebsiteID: websiteID,
        UserID:    userID,
        Data: map[string]interface{}{
            "journey_id": journeyID,
        },
        Timestamp: time.Now(),
    }
    
    s.eventBus.Publish(event)
}
```

## Best Practices for Multi-Tenancy

### 1. Always Include Website ID
- Every database query must include `website_id` filter
- Cache keys must include website context
- API responses should be scoped to website

### 2. Validate Tenant Access
- Check user permissions for website access
- Verify resource ownership before operations
- Use middleware for consistent validation

### 3. Performance Optimization
- Create composite indexes with `website_id`
- Use tenant-aware caching strategies
- Implement proper query patterns

### 4. Security Measures
- Never trust client-provided website IDs without validation
- Implement proper authorization checks
- Log access attempts for audit purposes

### 5. Testing Strategy
- Test cross-tenant data isolation
- Verify authorization boundaries
- Test with multiple concurrent tenants