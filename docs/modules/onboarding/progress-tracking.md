# Progress Tracking & Analytics

## Progress Tracking

### Metrics theo dõi

```mermaid
graph LR
    A[Onboarding Metrics] --> B[Completion Rate]
    A --> C[Drop-off Points]
    A --> D[Time to Complete]
    A --> E[Step Success Rate]
    A --> F[User Satisfaction]
    
    B --> B1[Overall %]
    B --> B2[By Step %]
    
    C --> C1[Step Analysis]
    C --> C2[User Segments]
    
    D --> D1[Average Time]
    D --> D2[Per Step Time]
    
    E --> E1[Success Rate]
    E --> E2[Retry Rate]
```

#### Core KPIs Implementation

```go
type OnboardingMetrics struct {
    JourneyID        uint
    WebsiteID        uint
    
    // Completion Metrics
    TotalUsers       int     `json:"total_users"`
    CompletedUsers   int     `json:"completed_users"`
    CompletionRate   float64 `json:"completion_rate"`
    
    // Time Metrics
    AvgTimeToComplete time.Duration `json:"avg_time_to_complete"`
    MedianTime        time.Duration `json:"median_time"`
    
    // Step Performance
    StepMetrics      map[string]StepMetric `json:"step_metrics"`
    
    // Drop-off Analysis
    DropoffPoints    []DropoffPoint `json:"dropoff_points"`
    
    // User Satisfaction
    AvgRating        float64 `json:"avg_rating"`
    FeedbackCount    int     `json:"feedback_count"`
}

type StepMetric struct {
    StepID         uint          `json:"step_id"`
    StepName       string        `json:"step_name"`
    StartedCount   int           `json:"started_count"`
    CompletedCount int           `json:"completed_count"`
    SkippedCount   int           `json:"skipped_count"`
    FailedCount    int           `json:"failed_count"`
    SuccessRate    float64       `json:"success_rate"`
    AvgDuration    time.Duration `json:"avg_duration"`
    RetryRate      float64       `json:"retry_rate"`
}

type DropoffPoint struct {
    StepID       uint    `json:"step_id"`
    StepName     string  `json:"step_name"`
    DropoffRate  float64 `json:"dropoff_rate"`
    UserCount    int     `json:"user_count"`
    IsCritical   bool    `json:"is_critical"`
}
```

#### Metrics Calculation Service

```go
type MetricsService struct {
    repo OnboardingRepository
    cache CacheService
}

func (s *MetricsService) CalculateJourneyMetrics(journeyID, websiteID uint, dateRange DateRange) (*OnboardingMetrics, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("journey_metrics:%d:%d:%s", journeyID, websiteID, dateRange.String())
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*OnboardingMetrics), nil
    }
    
    // Calculate fresh metrics
    metrics := &OnboardingMetrics{
        JourneyID: journeyID,
        WebsiteID: websiteID,
    }
    
    // Get all progress records for the journey
    progressList, err := s.repo.GetProgressByJourneyAndDateRange(journeyID, websiteID, dateRange)
    if err != nil {
        return nil, err
    }
    
    metrics.TotalUsers = len(progressList)
    
    // Calculate completion metrics
    var completedCount int
    var totalTime time.Duration
    var completionTimes []time.Duration
    
    for _, progress := range progressList {
        if progress.Status == "completed" && progress.CompletedAt != nil {
            completedCount++
            if progress.StartedAt != nil {
                duration := progress.CompletedAt.Sub(*progress.StartedAt)
                totalTime += duration
                completionTimes = append(completionTimes, duration)
            }
        }
    }
    
    metrics.CompletedUsers = completedCount
    if metrics.TotalUsers > 0 {
        metrics.CompletionRate = float64(completedCount) / float64(metrics.TotalUsers)
    }
    
    // Calculate time metrics
    if len(completionTimes) > 0 {
        metrics.AvgTimeToComplete = totalTime / time.Duration(len(completionTimes))
        metrics.MedianTime = s.calculateMedian(completionTimes)
    }
    
    // Calculate step metrics
    metrics.StepMetrics = s.calculateStepMetrics(journeyID, websiteID, dateRange)
    
    // Calculate dropoff points
    metrics.DropoffPoints = s.calculateDropoffPoints(journeyID, websiteID, dateRange)
    
    // Calculate satisfaction metrics
    metrics.AvgRating, metrics.FeedbackCount = s.calculateSatisfactionMetrics(journeyID, websiteID, dateRange)
    
    // Cache the results
    s.cache.Set(cacheKey, metrics, 30*time.Minute)
    
    return metrics, nil
}

func (s *MetricsService) calculateStepMetrics(journeyID, websiteID uint, dateRange DateRange) map[string]StepMetric {
    steps, _ := s.repo.GetStepsByJourneyID(journeyID, websiteID)
    stepMetrics := make(map[string]StepMetric)
    
    for _, step := range steps {
        completions, _ := s.repo.GetStepCompletionsByDateRange(step.ID, websiteID, dateRange)
        
        metric := StepMetric{
            StepID:   step.ID,
            StepName: step.Name,
        }
        
        var totalDuration time.Duration
        var retryCount int
        
        for _, completion := range completions {
            metric.StartedCount++
            
            switch completion.Status {
            case "completed":
                metric.CompletedCount++
            case "skipped":
                metric.SkippedCount++
            case "failed":
                metric.FailedCount++
            }
            
            if completion.TimeSpent > 0 {
                totalDuration += time.Duration(completion.TimeSpent) * time.Second
            }
            
            if completion.AttemptCount > 1 {
                retryCount++
            }
        }
        
        // Calculate rates
        if metric.StartedCount > 0 {
            metric.SuccessRate = float64(metric.CompletedCount) / float64(metric.StartedCount)
            metric.RetryRate = float64(retryCount) / float64(metric.StartedCount)
        }
        
        if metric.CompletedCount > 0 {
            metric.AvgDuration = totalDuration / time.Duration(metric.CompletedCount)
        }
        
        stepMetrics[fmt.Sprintf("step_%d", step.ID)] = metric
    }
    
    return stepMetrics
}
```

### Phân tích hành vi người dùng

#### Funnel Analysis

```go
type FunnelAnalysis struct {
    JourneyID    uint                    `json:"journey_id"`
    WebsiteID    uint                    `json:"website_id"`
    Steps        []FunnelStep           `json:"steps"`
    ConversionRate float64              `json:"overall_conversion_rate"`
    DropoffPoints  []FunnelDropoffPoint `json:"dropoff_points"`
}

type FunnelStep struct {
    StepID         uint    `json:"step_id"`
    StepName       string  `json:"step_name"`
    Order          int     `json:"order"`
    UserCount      int     `json:"user_count"`
    ConversionRate float64 `json:"conversion_rate"`
    DropoffRate    float64 `json:"dropoff_rate"`
}

type FunnelDropoffPoint struct {
    FromStepID   uint    `json:"from_step_id"`
    ToStepID     uint    `json:"to_step_id"`
    DropoffCount int     `json:"dropoff_count"`
    DropoffRate  float64 `json:"dropoff_rate"`
    Severity     string  `json:"severity"` // low, medium, high, critical
}

func (s *MetricsService) AnalyzeFunnel(journeyID, websiteID uint, dateRange DateRange) (*FunnelAnalysis, error) {
    steps, err := s.repo.GetStepsByJourneyID(journeyID, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Sort steps by order
    sort.Slice(steps, func(i, j int) bool {
        return steps[i].Order < steps[j].Order
    })
    
    analysis := &FunnelAnalysis{
        JourneyID: journeyID,
        WebsiteID: websiteID,
        Steps:     make([]FunnelStep, len(steps)),
    }
    
    // Get all progress records
    progressList, err := s.repo.GetProgressByJourneyAndDateRange(journeyID, websiteID, dateRange)
    if err != nil {
        return nil, err
    }
    
    totalUsers := len(progressList)
    if totalUsers == 0 {
        return analysis, nil
    }
    
    // Analyze each step
    var previousCount = totalUsers
    for i, step := range steps {
        userCount := s.countUsersReachedStep(progressList, step.ID)
        
        funnelStep := FunnelStep{
            StepID:    step.ID,
            StepName:  step.Name,
            Order:     step.Order,
            UserCount: userCount,
        }
        
        if totalUsers > 0 {
            funnelStep.ConversionRate = float64(userCount) / float64(totalUsers)
        }
        
        if i > 0 && previousCount > 0 {
            funnelStep.DropoffRate = float64(previousCount-userCount) / float64(previousCount)
        }
        
        analysis.Steps[i] = funnelStep
        previousCount = userCount
    }
    
    // Calculate overall conversion rate
    if totalUsers > 0 && len(analysis.Steps) > 0 {
        lastStepUsers := analysis.Steps[len(analysis.Steps)-1].UserCount
        analysis.ConversionRate = float64(lastStepUsers) / float64(totalUsers)
    }
    
    // Identify critical dropoff points
    analysis.DropoffPoints = s.identifyDropoffPoints(analysis.Steps)
    
    return analysis, nil
}
```

#### Cohort Analysis

```go
type CohortAnalysis struct {
    CohortDefinition string                    `json:"cohort_definition"`
    Cohorts         []Cohort                  `json:"cohorts"`
    RetentionMatrix [][]float64               `json:"retention_matrix"`
    Insights        []CohortInsight           `json:"insights"`
}

type Cohort struct {
    Name             string    `json:"name"`
    StartDate        time.Time `json:"start_date"`
    EndDate          time.Time `json:"end_date"`
    UserCount        int       `json:"user_count"`
    CompletionRate   float64   `json:"completion_rate"`
    AvgTimeToComplete time.Duration `json:"avg_time_to_complete"`
}

type CohortInsight struct {
    Type        string      `json:"type"`
    Description string      `json:"description"`
    Impact      string      `json:"impact"` // low, medium, high
    Recommendation string   `json:"recommendation"`
}

func (s *MetricsService) AnalyzeCohorts(journeyID, websiteID uint, cohortType string) (*CohortAnalysis, error) {
    // Define cohort periods (weekly, monthly, etc.)
    cohorts := s.defineCohorts(cohortType)
    
    analysis := &CohortAnalysis{
        CohortDefinition: cohortType,
        Cohorts:         make([]Cohort, len(cohorts)),
    }
    
    // Analyze each cohort
    for i, cohortPeriod := range cohorts {
        progressList, _ := s.repo.GetProgressByJourneyAndDateRange(
            journeyID, websiteID, 
            DateRange{Start: cohortPeriod.Start, End: cohortPeriod.End},
        )
        
        cohort := Cohort{
            Name:      cohortPeriod.Name,
            StartDate: cohortPeriod.Start,
            EndDate:   cohortPeriod.End,
            UserCount: len(progressList),
        }
        
        // Calculate completion metrics for this cohort
        var completedCount int
        var totalTime time.Duration
        var completedUsersTime []time.Duration
        
        for _, progress := range progressList {
            if progress.Status == "completed" && progress.CompletedAt != nil && progress.StartedAt != nil {
                completedCount++
                duration := progress.CompletedAt.Sub(*progress.StartedAt)
                totalTime += duration
                completedUsersTime = append(completedUsersTime, duration)
            }
        }
        
        if cohort.UserCount > 0 {
            cohort.CompletionRate = float64(completedCount) / float64(cohort.UserCount)
        }
        
        if len(completedUsersTime) > 0 {
            cohort.AvgTimeToComplete = totalTime / time.Duration(len(completedUsersTime))
        }
        
        analysis.Cohorts[i] = cohort
    }
    
    // Generate retention matrix
    analysis.RetentionMatrix = s.calculateRetentionMatrix(analysis.Cohorts)
    
    // Generate insights
    analysis.Insights = s.generateCohortInsights(analysis.Cohorts)
    
    return analysis, nil
}
```

#### Heat Map Analysis

```go
type HeatMapData struct {
    JourneyID   uint                 `json:"journey_id"`
    WebsiteID   uint                 `json:"website_id"`
    StepHeatMap map[string]StepHeat  `json:"step_heat_map"`
    UIHeatMap   []UIInteraction      `json:"ui_heat_map"`
}

type StepHeat struct {
    StepID          uint    `json:"step_id"`
    InteractionRate float64 `json:"interaction_rate"`
    CompletionRate  float64 `json:"completion_rate"`
    TimeSpent       float64 `json:"avg_time_spent"`
    HelpRequests    int     `json:"help_requests"`
    Temperature     string  `json:"temperature"` // cold, warm, hot, burning
}

type UIInteraction struct {
    ElementSelector string  `json:"element_selector"`
    InteractionType string  `json:"interaction_type"`
    Count          int     `json:"count"`
    SuccessRate    float64 `json:"success_rate"`
    Position       struct {
        X int `json:"x"`
        Y int `json:"y"`
    } `json:"position"`
}
```

### Real-time Progress Updates

#### WebSocket Integration

```go
type ProgressUpdateService struct {
    socketService SocketService
    repo         OnboardingRepository
}

func (s *ProgressUpdateService) BroadcastProgressUpdate(userID, websiteID uint, update ProgressUpdate) error {
    // Send to user's personal channel
    userChannel := fmt.Sprintf("user:%d:onboarding", userID)
    if err := s.socketService.SendToChannel(userChannel, update); err != nil {
        return err
    }
    
    // Send to admin dashboard if enabled
    adminChannel := fmt.Sprintf("website:%d:onboarding:admin", websiteID)
    adminUpdate := AdminProgressUpdate{
        UserID:   userID,
        Update:   update,
        Timestamp: time.Now(),
    }
    
    return s.socketService.SendToChannel(adminChannel, adminUpdate)
}

type ProgressUpdate struct {
    Type        string      `json:"type"` // step_completed, journey_completed, etc.
    StepID      uint        `json:"step_id,omitempty"`
    JourneyID   uint        `json:"journey_id"`
    Progress    float64     `json:"progress"` // 0.0 to 1.0
    Message     string      `json:"message"`
    Achievement *Achievement `json:"achievement,omitempty"`
    NextStep    *NextStepInfo `json:"next_step,omitempty"`
}

type Achievement struct {
    Type        string `json:"type"` // badge, level, points
    Name        string `json:"name"`
    Description string `json:"description"`
    IconURL     string `json:"icon_url"`
    Points      int    `json:"points"`
}

type NextStepInfo struct {
    StepID      uint   `json:"step_id"`
    Name        string `json:"name"`
    Description string `json:"description"`
    EstimatedDuration int `json:"estimated_duration"`
}
```

### Analytics Dashboard Data

#### Dashboard Metrics

```go
type DashboardMetrics struct {
    WebsiteID        uint                    `json:"website_id"`
    DateRange        DateRange               `json:"date_range"`
    
    // Overview Metrics
    TotalUsers       int                     `json:"total_users"`
    ActiveJourneys   int                     `json:"active_journeys"`
    CompletionRate   float64                 `json:"completion_rate"`
    AvgCompletionTime time.Duration          `json:"avg_completion_time"`
    
    // Journey Performance
    JourneyMetrics   []JourneyPerformance    `json:"journey_metrics"`
    
    // User Segments
    UserSegments     []UserSegment           `json:"user_segments"`
    
    // Trends
    CompletionTrend  []TrendPoint            `json:"completion_trend"`
    UserActivityTrend []TrendPoint           `json:"user_activity_trend"`
    
    // Issues & Alerts
    CriticalIssues   []Issue                 `json:"critical_issues"`
    Recommendations  []Recommendation        `json:"recommendations"`
}

type JourneyPerformance struct {
    JourneyID        uint          `json:"journey_id"`
    JourneyName      string        `json:"journey_name"`
    UserCount        int           `json:"user_count"`
    CompletionRate   float64       `json:"completion_rate"`
    AvgDuration      time.Duration `json:"avg_duration"`
    DropoffRate      float64       `json:"dropoff_rate"`
    Satisfaction     float64       `json:"satisfaction"`
    Status           string        `json:"status"` // healthy, warning, critical
}

type UserSegment struct {
    SegmentName      string  `json:"segment_name"`
    UserCount        int     `json:"user_count"`
    CompletionRate   float64 `json:"completion_rate"`
    Characteristics  []string `json:"characteristics"`
}

type TrendPoint struct {
    Date      time.Time `json:"date"`
    Value     float64   `json:"value"`
    Change    float64   `json:"change"` // percentage change from previous period
}

type Issue struct {
    Type        string    `json:"type"`
    Severity    string    `json:"severity"`
    Description string    `json:"description"`
    AffectedUsers int     `json:"affected_users"`
    JourneyID   *uint     `json:"journey_id,omitempty"`
    StepID      *uint     `json:"step_id,omitempty"`
    DetectedAt  time.Time `json:"detected_at"`
}

type Recommendation struct {
    Type           string    `json:"type"`
    Priority       string    `json:"priority"`
    Title          string    `json:"title"`
    Description    string    `json:"description"`
    ExpectedImpact string    `json:"expected_impact"`
    ActionItems    []string  `json:"action_items"`
}
```

### Performance Optimization

#### Metrics Caching Strategy

```go
type MetricsCacheService struct {
    cache  CacheService
    config CacheConfig
}

type CacheConfig struct {
    RealTimeMetricsTTL  time.Duration // 5 minutes
    HourlyMetricsTTL    time.Duration // 1 hour  
    DailyMetricsTTL     time.Duration // 24 hours
    WeeklyMetricsTTL    time.Duration // 7 days
}

func (s *MetricsCacheService) GetCachedMetrics(key string, metricsType string) (interface{}, bool) {
    var ttl time.Duration
    
    switch metricsType {
    case "realtime":
        ttl = s.config.RealTimeMetricsTTL
    case "hourly":
        ttl = s.config.HourlyMetricsTTL
    case "daily":
        ttl = s.config.DailyMetricsTTL
    case "weekly":
        ttl = s.config.WeeklyMetricsTTL
    default:
        ttl = s.config.DailyMetricsTTL
    }
    
    return s.cache.GetWithTTL(key, ttl)
}

func (s *MetricsCacheService) CacheMetrics(key string, data interface{}, metricsType string) error {
    var ttl time.Duration
    
    switch metricsType {
    case "realtime":
        ttl = s.config.RealTimeMetricsTTL
    case "hourly":
        ttl = s.config.HourlyMetricsTTL
    case "daily":
        ttl = s.config.DailyMetricsTTL
    case "weekly":
        ttl = s.config.WeeklyMetricsTTL
    default:
        ttl = s.config.DailyMetricsTTL
    }
    
    return s.cache.Set(key, data, ttl)
}
```

#### Background Metrics Calculation

```go
type MetricsCalculationJob struct {
    scheduler CronScheduler
    service   *MetricsService
}

func (j *MetricsCalculationJob) ScheduleMetricsJobs() error {
    // Real-time metrics (every 5 minutes)
    if err := j.scheduler.Schedule("*/5 * * * *", j.calculateRealTimeMetrics); err != nil {
        return err
    }
    
    // Hourly metrics
    if err := j.scheduler.Schedule("0 * * * *", j.calculateHourlyMetrics); err != nil {
        return err
    }
    
    // Daily metrics (at 2 AM)
    if err := j.scheduler.Schedule("0 2 * * *", j.calculateDailyMetrics); err != nil {
        return err
    }
    
    // Weekly metrics (Sunday at 3 AM)
    return j.scheduler.Schedule("0 3 * * 0", j.calculateWeeklyMetrics)
}

func (j *MetricsCalculationJob) calculateRealTimeMetrics() {
    websites, _ := j.service.GetActiveWebsites()
    
    for _, website := range websites {
        journeys, _ := j.service.repo.GetJourneysByWebsiteID(website.ID)
        
        for _, journey := range journeys {
            metrics, err := j.service.CalculateJourneyMetrics(
                journey.ID, 
                website.ID,
                DateRange{Start: time.Now().Add(-24 * time.Hour), End: time.Now()},
            )
            
            if err == nil {
                cacheKey := fmt.Sprintf("realtime_metrics:%d:%d", journey.ID, website.ID)
                j.service.cache.Set(cacheKey, metrics, 5*time.Minute)
            }
        }
    }
}
```