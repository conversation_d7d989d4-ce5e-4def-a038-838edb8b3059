# Gamification & Engagement

## H<PERSON> thống tích điểm

```mermaid
flowchart LR
    A[<PERSON><PERSON><PERSON> thành bước] --> B[Nhận điểm]
    B --> C[Cập nhật level]
    C --> D[Unlock badge]
    D --> E[Notification]
    E --> F[Social sharing]
```

### Point System Implementation

```go
type PointSystem struct {
    UserID       uint              `json:"user_id"`
    WebsiteID    uint              `json:"website_id"`
    TotalPoints  int               `json:"total_points"`
    CurrentLevel int               `json:"current_level"`
    PointSources map[string]int    `json:"point_sources"`
    Achievements []Achievement     `json:"achievements"`
}

type PointRule struct {
    ID          uint   `json:"id"`
    WebsiteID   uint   `json:"website_id"`
    ActionType  string `json:"action_type"` // step_completed, journey_completed, etc.
    Points      int    `json:"points"`
    Multiplier  float64 `json:"multiplier"`
    MaxDaily    int    `json:"max_daily"`
    Conditions  JSON   `json:"conditions"`
}

type PointTransaction struct {
    ID          uint      `json:"id"`
    UserID      uint      `json:"user_id"`
    WebsiteID   uint      `json:"website_id"`
    ActionType  string    `json:"action_type"`
    Points      int       `json:"points"`
    Source      string    `json:"source"`
    Reference   string    `json:"reference"` // step_id, journey_id, etc.
    Timestamp   time.Time `json:"timestamp"`
}

// Point calculation service
type GamificationService struct {
    repo    GamificationRepository
    cache   CacheService
    events  EventService
}

func (s *GamificationService) AwardPoints(userID, websiteID uint, actionType, reference string) (*PointTransaction, error) {
    // Check daily limits
    if exceeded, err := s.checkDailyLimit(userID, websiteID, actionType); err != nil || exceeded {
        return nil, errors.New("daily point limit exceeded")
    }
    
    // Get point rule for action
    rule, err := s.repo.GetPointRule(websiteID, actionType)
    if err != nil {
        return nil, err
    }
    
    // Calculate points with conditions and multipliers
    points := s.calculatePoints(rule, userID, websiteID, reference)
    
    // Create transaction
    transaction := &PointTransaction{
        UserID:     userID,
        WebsiteID:  websiteID,
        ActionType: actionType,
        Points:     points,
        Source:     "onboarding",
        Reference:  reference,
        Timestamp:  time.Now(),
    }
    
    if err := s.repo.CreatePointTransaction(transaction); err != nil {
        return nil, err
    }
    
    // Update user's total points
    if err := s.updateUserPoints(userID, websiteID, points); err != nil {
        return nil, err
    }
    
    // Check for level up
    if leveledUp, newLevel := s.checkLevelUp(userID, websiteID); leveledUp {
        s.handleLevelUp(userID, websiteID, newLevel)
    }
    
    // Check for new achievements
    s.checkAchievements(userID, websiteID, actionType, reference)
    
    return transaction, nil
}

func (s *GamificationService) calculatePoints(rule *PointRule, userID, websiteID uint, reference string) int {
    basePoints := rule.Points
    multiplier := rule.Multiplier
    
    // Apply conditions
    if rule.Conditions != nil {
        conditions := make(map[string]interface{})
        json.Unmarshal(rule.Conditions, &conditions)
        
        // Example: First-time completion bonus
        if firstTime, ok := conditions["first_time_bonus"]; ok && firstTime.(bool) {
            if s.isFirstTimeAction(userID, websiteID, rule.ActionType) {
                multiplier *= 1.5
            }
        }
        
        // Example: Streak bonus
        if streakBonus, ok := conditions["streak_bonus"]; ok && streakBonus.(bool) {
            streak := s.getUserStreak(userID, websiteID)
            if streak >= 3 {
                multiplier *= (1.0 + float64(streak)*0.1)
            }
        }
    }
    
    return int(float64(basePoints) * multiplier)
}
```

### Badge System

```go
type Badge struct {
    ID          uint      `json:"id"`
    WebsiteID   uint      `json:"website_id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    IconURL     string    `json:"icon_url"`
    Category    string    `json:"category"`
    Rarity      string    `json:"rarity"` // common, rare, epic, legendary
    Criteria    JSON      `json:"criteria"`
    IsActive    bool      `json:"is_active"`
    CreatedAt   time.Time `json:"created_at"`
}

type UserBadge struct {
    ID        uint      `json:"id"`
    UserID    uint      `json:"user_id"`
    WebsiteID uint      `json:"website_id"`
    BadgeID   uint      `json:"badge_id"`
    EarnedAt  time.Time `json:"earned_at"`
    Progress  float64   `json:"progress"` // 0.0 to 1.0 for partial progress
    
    // Relationships
    Badge Badge `json:"badge"`
}

// Badge criteria examples
var BadgeCriteria = map[string]interface{}{
    "first_steps": map[string]interface{}{
        "type": "step_completion",
        "count": 1,
        "description": "Complete your first onboarding step",
    },
    "journey_master": map[string]interface{}{
        "type": "journey_completion",
        "count": 1,
        "description": "Complete an entire onboarding journey",
    },
    "speedrunner": map[string]interface{}{
        "type": "completion_time",
        "max_duration": "30m",
        "description": "Complete onboarding in under 30 minutes",
    },
    "perfectionist": map[string]interface{}{
        "type": "completion_rate",
        "min_rate": 1.0,
        "no_skips": true,
        "description": "Complete all steps without skipping any",
    },
    "helper": map[string]interface{}{
        "type": "social_action",
        "action": "invite_friend",
        "count": 5,
        "description": "Invite 5 friends to join",
    },
}

func (s *GamificationService) CheckBadgeEligibility(userID, websiteID uint, actionType, reference string) error {
    badges, err := s.repo.GetActiveBadges(websiteID)
    if err != nil {
        return err
    }
    
    for _, badge := range badges {
        if s.evaluateBadgeCriteria(badge, userID, websiteID, actionType, reference) {
            if err := s.awardBadge(userID, websiteID, badge.ID); err != nil {
                log.Printf("Failed to award badge %d to user %d: %v", badge.ID, userID, err)
                continue
            }
        }
    }
    
    return nil
}

func (s *GamificationService) evaluateBadgeCriteria(badge Badge, userID, websiteID uint, actionType, reference string) bool {
    criteria := make(map[string]interface{})
    json.Unmarshal(badge.Criteria, &criteria)
    
    criteriaType := criteria["type"].(string)
    
    switch criteriaType {
    case "step_completion":
        requiredCount := int(criteria["count"].(float64))
        userCompletions := s.getUserStepCompletionCount(userID, websiteID)
        return userCompletions >= requiredCount
        
    case "journey_completion":
        requiredCount := int(criteria["count"].(float64))
        userCompletions := s.getUserJourneyCompletionCount(userID, websiteID)
        return userCompletions >= requiredCount
        
    case "completion_time":
        maxDuration := criteria["max_duration"].(string)
        duration, _ := time.ParseDuration(maxDuration)
        userTime := s.getUserBestCompletionTime(userID, websiteID)
        return userTime <= duration
        
    case "completion_rate":
        minRate := criteria["min_rate"].(float64)
        noSkips := criteria["no_skips"].(bool)
        userRate, hasSkips := s.getUserCompletionRate(userID, websiteID)
        return userRate >= minRate && (!noSkips || !hasSkips)
        
    case "social_action":
        action := criteria["action"].(string)
        requiredCount := int(criteria["count"].(float64))
        userActions := s.getUserSocialActionCount(userID, websiteID, action)
        return userActions >= requiredCount
    }
    
    return false
}
```

### Level System

```go
type LevelSystem struct {
    WebsiteID    uint        `json:"website_id"`
    Levels       []Level     `json:"levels"`
    MaxLevel     int         `json:"max_level"`
    PointsFormula string     `json:"points_formula"` // exponential, linear, custom
}

type Level struct {
    Level        int    `json:"level"`
    RequiredPoints int  `json:"required_points"`
    Title        string `json:"title"`
    Description  string `json:"description"`
    Rewards      JSON   `json:"rewards"`
    IconURL      string `json:"icon_url"`
}

type UserLevel struct {
    UserID         uint      `json:"user_id"`
    WebsiteID      uint      `json:"website_id"`
    CurrentLevel   int       `json:"current_level"`
    TotalPoints    int       `json:"total_points"`
    PointsToNext   int       `json:"points_to_next"`
    LevelProgress  float64   `json:"level_progress"` // 0.0 to 1.0
    LastLevelUp    *time.Time `json:"last_level_up"`
}

func (s *GamificationService) CalculateLevel(points int, websiteID uint) (int, error) {
    levelSystem, err := s.repo.GetLevelSystem(websiteID)
    if err != nil {
        return 1, err
    }
    
    // Find the highest level the user qualifies for
    currentLevel := 1
    for _, level := range levelSystem.Levels {
        if points >= level.RequiredPoints {
            currentLevel = level.Level
        } else {
            break
        }
    }
    
    return currentLevel, nil
}

func (s *GamificationService) GetLevelProgress(userID, websiteID uint) (*UserLevel, error) {
    userPoints, err := s.repo.GetUserTotalPoints(userID, websiteID)
    if err != nil {
        return nil, err
    }
    
    currentLevel, err := s.CalculateLevel(userPoints, websiteID)
    if err != nil {
        return nil, err
    }
    
    levelSystem, err := s.repo.GetLevelSystem(websiteID)
    if err != nil {
        return nil, err
    }
    
    var pointsToNext int
    var levelProgress float64
    
    if currentLevel < levelSystem.MaxLevel {
        nextLevel := currentLevel + 1
        nextLevelPoints := s.getPointsForLevel(nextLevel, levelSystem)
        currentLevelPoints := s.getPointsForLevel(currentLevel, levelSystem)
        
        pointsToNext = nextLevelPoints - userPoints
        if nextLevelPoints > currentLevelPoints {
            progress := float64(userPoints - currentLevelPoints) / float64(nextLevelPoints - currentLevelPoints)
            levelProgress = math.Max(0, math.Min(1, progress))
        }
    } else {
        levelProgress = 1.0
    }
    
    return &UserLevel{
        UserID:        userID,
        WebsiteID:     websiteID,
        CurrentLevel:  currentLevel,
        TotalPoints:   userPoints,
        PointsToNext:  pointsToNext,
        LevelProgress: levelProgress,
    }, nil
}
```

### Achievement System

```go
type Achievement struct {
    ID          uint      `json:"id"`
    WebsiteID   uint      `json:"website_id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Type        string    `json:"type"` // milestone, challenge, secret
    Category    string    `json:"category"`
    IconURL     string    `json:"icon_url"`
    Points      int       `json:"points"`
    Criteria    JSON      `json:"criteria"`
    Rewards     JSON      `json:"rewards"`
    IsHidden    bool      `json:"is_hidden"`
    Rarity      string    `json:"rarity"`
    ValidFrom   *time.Time `json:"valid_from"`
    ValidUntil  *time.Time `json:"valid_until"`
    CreatedAt   time.Time `json:"created_at"`
}

type UserAchievement struct {
    ID            uint      `json:"id"`
    UserID        uint      `json:"user_id"`
    WebsiteID     uint      `json:"website_id"`
    AchievementID uint      `json:"achievement_id"`
    Progress      float64   `json:"progress"` // 0.0 to 1.0
    UnlockedAt    *time.Time `json:"unlocked_at"`
    NotifiedAt    *time.Time `json:"notified_at"`
    
    // Relationships
    Achievement Achievement `json:"achievement"`
}

// Pre-defined achievements
var OnboardingAchievements = []Achievement{
    {
        Name:        "First Steps",
        Description: "Complete your first onboarding step",
        Type:        "milestone",
        Category:    "progress",
        Points:      10,
        Criteria:    `{"type": "step_completion", "count": 1}`,
    },
    {
        Name:        "Quick Learner",
        Description: "Complete 3 steps in under 10 minutes",
        Type:        "challenge",
        Category:    "speed",
        Points:      50,
        Criteria:    `{"type": "speed_challenge", "steps": 3, "max_time": "10m"}`,
    },
    {
        Name:        "Perfectionist",
        Description: "Complete a journey without skipping any optional steps",
        Type:        "challenge",
        Category:    "completion",
        Points:      100,
        Criteria:    `{"type": "perfect_completion", "allow_skips": false}`,
    },
    {
        Name:        "Social Butterfly",
        Description: "Share your progress on social media",
        Type:        "milestone",
        Category:    "social",
        Points:      25,
        Criteria:    `{"type": "social_share", "platforms": ["twitter", "facebook", "linkedin"]}`,
    },
    {
        Name:        "Journey Master",
        Description: "Complete your first onboarding journey",
        Type:        "milestone",
        Category:    "progress",
        Points:      200,
        Criteria:    `{"type": "journey_completion", "count": 1}`,
    },
}
```

### Leaderboard System

```go
type Leaderboard struct {
    ID          uint              `json:"id"`
    WebsiteID   uint              `json:"website_id"`
    Name        string            `json:"name"`
    Type        string            `json:"type"` // points, speed, completion_rate
    Period      string            `json:"period"` // daily, weekly, monthly, all_time
    Entries     []LeaderboardEntry `json:"entries"`
    UpdatedAt   time.Time         `json:"updated_at"`
}

type LeaderboardEntry struct {
    Rank        int       `json:"rank"`
    UserID      uint      `json:"user_id"`
    Username    string    `json:"username"`
    Avatar      string    `json:"avatar"`
    Score       float64   `json:"score"`
    Change      int       `json:"change"` // rank change from previous period
    Badge       *Badge    `json:"badge,omitempty"`
}

func (s *GamificationService) UpdateLeaderboards(websiteID uint) error {
    leaderboards := []string{"points", "speed", "completion_rate"}
    periods := []string{"daily", "weekly", "monthly", "all_time"}
    
    for _, lbType := range leaderboards {
        for _, period := range periods {
            if err := s.updateLeaderboard(websiteID, lbType, period); err != nil {
                log.Printf("Failed to update %s %s leaderboard for website %d: %v", 
                    period, lbType, websiteID, err)
            }
        }
    }
    
    return nil
}

func (s *GamificationService) updateLeaderboard(websiteID uint, lbType, period string) error {
    var entries []LeaderboardEntry
    var err error
    
    switch lbType {
    case "points":
        entries, err = s.getPointsLeaderboard(websiteID, period)
    case "speed":
        entries, err = s.getSpeedLeaderboard(websiteID, period)
    case "completion_rate":
        entries, err = s.getCompletionRateLeaderboard(websiteID, period)
    }
    
    if err != nil {
        return err
    }
    
    // Assign ranks and calculate changes
    for i := range entries {
        entries[i].Rank = i + 1
        entries[i].Change = s.calculateRankChange(entries[i].UserID, websiteID, lbType, period, i+1)
    }
    
    leaderboard := &Leaderboard{
        WebsiteID: websiteID,
        Name:      fmt.Sprintf("%s_%s", lbType, period),
        Type:      lbType,
        Period:    period,
        Entries:   entries,
        UpdatedAt: time.Now(),
    }
    
    return s.repo.SaveLeaderboard(leaderboard)
}
```

### Reward System

```go
type Reward struct {
    ID          uint      `json:"id"`
    WebsiteID   uint      `json:"website_id"`
    Type        string    `json:"type"` // badge, points, unlock, discount, gift
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Value       JSON      `json:"value"`
    Conditions  JSON      `json:"conditions"`
    ExpiresAt   *time.Time `json:"expires_at"`
    IsActive    bool      `json:"is_active"`
}

type UserReward struct {
    ID         uint      `json:"id"`
    UserID     uint      `json:"user_id"`
    WebsiteID  uint      `json:"website_id"`
    RewardID   uint      `json:"reward_id"`
    Status     string    `json:"status"` // pending, claimed, expired
    EarnedAt   time.Time `json:"earned_at"`
    ClaimedAt  *time.Time `json:"claimed_at"`
    ExpiresAt  *time.Time `json:"expires_at"`
    
    // Relationships
    Reward Reward `json:"reward"`
}

// Reward types and examples
var RewardTypes = map[string]interface{}{
    "points": map[string]interface{}{
        "amount": 100,
        "source": "bonus",
    },
    "badge": map[string]interface{}{
        "badge_id": 123,
        "special_edition": true,
    },
    "unlock": map[string]interface{}{
        "feature": "advanced_customization",
        "duration": "30d",
    },
    "discount": map[string]interface{}{
        "percentage": 20,
        "valid_for": "premium_upgrade",
        "code": "ONBOARDING20",
    },
    "gift": map[string]interface{}{
        "type": "digital_asset",
        "asset_url": "https://...",
        "description": "Exclusive wallpaper pack",
    },
}

func (s *GamificationService) ProcessReward(userID, websiteID uint, rewardType string, trigger string) error {
    rewards, err := s.repo.GetEligibleRewards(websiteID, rewardType, trigger)
    if err != nil {
        return err
    }
    
    for _, reward := range rewards {
        if s.evaluateRewardConditions(reward, userID, websiteID) {
            userReward := &UserReward{
                UserID:    userID,
                WebsiteID: websiteID,
                RewardID:  reward.ID,
                Status:    "pending",
                EarnedAt:  time.Now(),
            }
            
            if reward.ExpiresAt != nil {
                userReward.ExpiresAt = reward.ExpiresAt
            }
            
            if err := s.repo.CreateUserReward(userReward); err != nil {
                return err
            }
            
            // Notify user about the reward
            s.notifyUserReward(userID, websiteID, userReward)
        }
    }
    
    return nil
}
```

### Social Features

```go
type SocialChallenge struct {
    ID          uint      `json:"id"`
    WebsiteID   uint      `json:"website_id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Type        string    `json:"type"` // team, individual, community
    StartDate   time.Time `json:"start_date"`
    EndDate     time.Time `json:"end_date"`
    Rules       JSON      `json:"rules"`
    Rewards     JSON      `json:"rewards"`
    IsActive    bool      `json:"is_active"`
}

type ChallengeParticipant struct {
    ID          uint      `json:"id"`
    ChallengeID uint      `json:"challenge_id"`
    UserID      uint      `json:"user_id"`
    TeamID      *uint     `json:"team_id"`
    Progress    float64   `json:"progress"`
    Score       int       `json:"score"`
    JoinedAt    time.Time `json:"joined_at"`
    CompletedAt *time.Time `json:"completed_at"`
}

type SocialShare struct {
    ID        uint      `json:"id"`
    UserID    uint      `json:"user_id"`
    WebsiteID uint      `json:"website_id"`
    Platform  string    `json:"platform"`
    Content   string    `json:"content"`
    ShareURL  string    `json:"share_url"`
    ShareType string    `json:"share_type"` // achievement, progress, completion
    SharedAt  time.Time `json:"shared_at"`
}

func (s *GamificationService) CreateShareContent(userID, websiteID uint, shareType string, data map[string]interface{}) (*SocialShare, error) {
    var content string
    var shareURL string
    
    switch shareType {
    case "achievement":
        achievementName := data["achievement_name"].(string)
        content = fmt.Sprintf("🏆 I just earned the '%s' achievement! #onboarding #achievement", achievementName)
        shareURL = fmt.Sprintf("https://yoursite.com/achievements/%s", data["achievement_id"])
        
    case "progress":
        progress := data["progress"].(float64)
        journeyName := data["journey_name"].(string)
        content = fmt.Sprintf("📈 I'm %.0f%% through my %s journey! #progress #learning", progress*100, journeyName)
        shareURL = fmt.Sprintf("https://yoursite.com/journey/%s", data["journey_id"])
        
    case "completion":
        journeyName := data["journey_name"].(string)
        timeTaken := data["completion_time"].(string)
        content = fmt.Sprintf("🎉 I just completed my %s journey in %s! #completed #success", journeyName, timeTaken)
        shareURL = fmt.Sprintf("https://yoursite.com/completion/%s", data["completion_id"])
    }
    
    share := &SocialShare{
        UserID:    userID,
        WebsiteID: websiteID,
        Content:   content,
        ShareURL:  shareURL,
        ShareType: shareType,
        SharedAt:  time.Now(),
    }
    
    return share, s.repo.CreateSocialShare(share)
}
```

### Notification Integration

```go
type GamificationNotification struct {
    UserID       uint                   `json:"user_id"`
    WebsiteID    uint                   `json:"website_id"`
    Type         string                 `json:"type"`
    Title        string                 `json:"title"`
    Message      string                 `json:"message"`
    Data         map[string]interface{} `json:"data"`
    IconURL      string                 `json:"icon_url"`
    ActionURL    string                 `json:"action_url"`
    Priority     string                 `json:"priority"`
}

func (s *GamificationService) SendAchievementNotification(userID, websiteID uint, achievement Achievement) error {
    notification := &GamificationNotification{
        UserID:    userID,
        WebsiteID: websiteID,
        Type:      "achievement_unlocked",
        Title:     "🏆 Achievement Unlocked!",
        Message:   fmt.Sprintf("You've earned the '%s' achievement!", achievement.Name),
        Data: map[string]interface{}{
            "achievement_id":   achievement.ID,
            "achievement_name": achievement.Name,
            "points":          achievement.Points,
            "rarity":          achievement.Rarity,
        },
        IconURL:   achievement.IconURL,
        ActionURL: fmt.Sprintf("/achievements/%d", achievement.ID),
        Priority:  "high",
    }
    
    return s.notificationService.Send(notification)
}

func (s *GamificationService) SendLevelUpNotification(userID, websiteID uint, newLevel int, rewards JSON) error {
    notification := &GamificationNotification{
        UserID:    userID,
        WebsiteID: websiteID,
        Type:      "level_up",
        Title:     "🚀 Level Up!",
        Message:   fmt.Sprintf("Congratulations! You've reached level %d!", newLevel),
        Data: map[string]interface{}{
            "new_level": newLevel,
            "rewards":   rewards,
        },
        Priority: "high",
    }
    
    return s.notificationService.Send(notification)
}
```