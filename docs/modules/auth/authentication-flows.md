# Authentication Flows

## User Registration Flow với Full Module Integration

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Gateway as API Gateway
    participant Auth as Auth Module
    participant Tenant as Tenant Module
    participant User as User Module
    participant RB<PERSON> as RBAC Module
    participant Notification as Notification Module
    participant Onboarding as Onboarding Module
    participant Media as Media Module
    participant Blog as Blog Module
    participant Socket as Socket Module
    participant Analytics as Analytics Module
    participant Queue as Message Queue
    participant Cache as Redis Cache
    participant DB as Database
    
    Note over Client,Gateway: Step 1: Registration Request
    Client->>Gateway: POST /api/cms/v1/auth/register
    Note over Client,Gateway: {first_name, last_name, email, password, website_id, role?, source?}
    
    Gateway->>Auth: Route to Auth Module
    
    Note over Auth,DB: Step 2: Input Validation & Pre-checks
    Auth->>Auth: Validate input format
    Auth->>Auth: Check password strength
    Auth->>Auth: Sanitize input data
    
    alt Input validation failed
        Auth->>Gateway: 422 Unprocessable Entity
        Gateway->>Client: Validation errors
    end
    
    Note over Auth,Tenant: Step 3: Tenant & Website Validation
    Auth->>Tenant: Validate website_id exists
    Tenant->>DB: SELECT * FROM blog_website WHERE id = ?
    DB->>Tenant: Website record
    
    alt Website not found or inactive
        Tenant->>Auth: Website invalid
        Auth->>Gateway: 404 Website not found
        Gateway->>Client: Error response
    end
    
    Tenant->>Auth: Website validated + tenant_id
    Auth->>Tenant: Check tenant limits
    Tenant->>DB: Get tenant user count
    DB->>Tenant: Current user count
    
    alt Tenant user limit exceeded
        Tenant->>Auth: Limit exceeded
        Auth->>Gateway: 403 Tenant limit reached
        Gateway->>Client: Upgrade plan required
    end
    
    Note over Auth,User: Step 4: Email Uniqueness Check
    Auth->>User: Check email exists in website
    User->>DB: SELECT COUNT(*) FROM blog_user WHERE email = ? AND website_id = ?
    DB->>User: Count result
    
    alt Email already exists
        User->>Auth: Email exists
        Auth->>Gateway: 409 Email already registered
        Gateway->>Client: Conflict error
    end
    
    Note over Auth,RBAC: Step 5: Role Validation
    Auth->>RBAC: Validate requested role (if provided)
    RBAC->>DB: SELECT * FROM blog_role WHERE slug = ? AND website_id = ?
    DB->>RBAC: Role record
    
    alt Invalid role provided
        RBAC->>Auth: Role not found
        Auth->>Gateway: 422 Invalid role
        Gateway->>Client: Validation error
    end
    
    RBAC->>Auth: Role validated or default role assigned
    
    Note over Auth,DB: Step 6: User Creation Transaction
    Auth->>DB: BEGIN TRANSACTION
    Auth->>Auth: Hash password with bcrypt
    Auth->>User: Create user record
    User->>DB: INSERT INTO blog_user (...)
    DB->>User: User created with ID
    User->>Auth: User ID returned
    
    Auth->>RBAC: Assign role to user
    RBAC->>DB: INSERT INTO blog_user_role (...)
    DB->>RBAC: Role assignment created
    
    Auth->>Auth: Generate JWT tokens
    Auth->>DB: INSERT INTO blog_user_session (...)
    DB->>Auth: Session stored
    
    Auth->>DB: COMMIT TRANSACTION
    
    Note over Auth,Cache: Step 7: Cache User Data
    Auth->>Cache: SET website:${website_id}:user:${user_id}
    Cache->>Auth: Cached successfully
    
    Auth->>Cache: SET website:${website_id}:user:email:${email}
    Cache->>Auth: Email mapping cached
    
    Note over Auth,Queue: Step 8: Publish Registration Event
    Auth->>Queue: Publish "user.registered" event
    Note over Queue: Event payload: {user_id, website_id, tenant_id, email, role, source, created_at}
    
    Note over Auth,Gateway: Step 9: Success Response
    Auth->>Gateway: 201 Created + user data + tokens
    Gateway->>Client: Registration successful
    Note over Gateway,Client: {user: {...}, access_token, refresh_token, expires_in}
    
    Note over Queue,Analytics: Step 10: Asynchronous Module Processing
    Queue->>User: user.registered event
    Queue->>Notification: user.registered event
    Queue->>Onboarding: user.registered event
    Queue->>Media: user.registered event
    Queue->>Blog: user.registered event
    Queue->>Socket: user.registered event
    Queue->>Analytics: user.registered event
    
    Note over User,DB: User Module Processing
    User->>DB: CREATE user_profile with defaults
    User->>DB: CREATE user_preferences with defaults
    User->>Cache: Cache user profile
    
    Note over Notification,Queue: Notification Module Processing
    Notification->>DB: CREATE welcome notification
    Notification->>Queue: Queue welcome email job
    Notification->>Queue: Queue onboarding email sequence
    
    Note over Onboarding,DB: Onboarding Module Processing
    Onboarding->>DB: CREATE onboarding_progress record
    Onboarding->>DB: Determine journey type based on role
    Onboarding->>Queue: Queue first onboarding step
    
    Note over Media,DB: Media Module Processing
    Media->>DB: CREATE user media folder structure
    Media->>Media: Set storage quota based on tenant plan
    Media->>Cache: Cache user media settings
    
    Note over Blog,DB: Blog Module Processing (if author role)
    alt User role is author/editor
        Blog->>DB: CREATE author_profile
        Blog->>DB: CREATE author draft workspace
        Blog->>Cache: Cache author settings
    end
    
    Note over Socket,Cache: Socket Module Processing
    Socket->>Cache: CREATE user websocket namespace
    Socket->>Socket: Initialize user notification channel
    Socket->>Socket: Set user online status
    
    Note over Analytics,DB: Analytics Module Processing
    Analytics->>DB: CREATE user_analytics record
    Analytics->>DB: Track registration event
    Analytics->>Analytics: Update tenant user metrics
    Analytics->>Cache: Cache user analytics data
    
    Note over Queue,Notification: Email Sending Process
    Queue->>Notification: Process welcome email job
    Notification->>Notification: Render email template
    Notification->>Notification: Send via email provider
    Notification->>DB: Log email sent status
    
    Note over Socket,Client: Real-time Notifications (if client connected)
    Socket->>Client: Welcome notification via WebSocket
    Socket->>Client: Onboarding progress update
```

### Detailed Module Integration Flow

```mermaid
flowchart TD
    subgraph "Event: user.registered"
        A[Message Queue] --> B{Event Consumers}
    end
    
    subgraph "Synchronous Processing (Registration Request)"
        B --> C[Auth Module]
        C --> C1[Input Validation]
        C --> C2[Website Validation]
        C --> C3[Email Uniqueness Check]
        C --> C4[Role Assignment]
        C --> C5[User Creation]
        C --> C6[Token Generation]
        C --> C7[Session Storage]
        C --> C8[Cache User Data]
        C --> C9[Event Publishing]
    end
    
    subgraph "Asynchronous Processing (Background Jobs)"
        B --> D[User Module]
        D --> D1[Create Profile Record]
        D --> D2[Set Default Preferences]
        D --> D3[Initialize Settings]
        D --> D4[Setup Privacy Settings]
        D --> D5[Create Activity Timeline]
        
        B --> E[Notification Module]
        E --> E1[Send Welcome Email]
        E --> E2[Schedule Onboarding Sequence]
        E --> E3[Enable Default Subscriptions]
        E --> E4[Create Notification Preferences]
        E --> E5[Setup Email Templates]
        
        B --> F[Onboarding Module]
        F --> F1[Create Progress Record]
        F --> F2[Determine Journey Type]
        F --> F3[Start First Step]
        F --> F4[Schedule Follow-up Tasks]
        F --> F5[Track User Source]
        
        B --> G[Media Module]
        G --> G1[Create User Folder Structure]
        G --> G2[Set Storage Quota]
        G --> G3[Initialize Upload Settings]
        G --> G4[Create Default Avatar]
        G --> G5[Setup CDN Access]
        
        B --> H[Blog Module]
        H --> H1{Check Role}
        H1 -->|Author/Editor| H2[Create Author Profile]
        H1 -->|Author/Editor| H3[Setup Draft Workspace]
        H1 -->|Author/Editor| H4[Initialize Categories Access]
        H1 -->|Author/Editor| H5[Setup Writing Preferences]
        H1 -->|Reader| H6[Setup Reading Preferences]
        
        B --> I[Socket Module]
        I --> I1[Create User Namespace]
        I --> I2[Initialize Notification Channel]
        I --> I3[Setup Real-time Subscriptions]
        I --> I4[Create Chat Channels]
        I --> I5[Set Online Status]
        
        B --> J[Analytics Module]
        J --> J1[Create User Analytics Record]
        J --> J2[Track Registration Source]
        J --> J3[Setup Conversion Tracking]
        J --> J4[Initialize Metrics Collection]
        J --> J5[Update Tenant Statistics]
        
        B --> K[RBAC Module]
        K --> K1[Validate Role Assignment]
        K --> K2[Setup Permission Cache]
        K --> K3[Initialize Access Control]
        K --> K4[Create Audit Log]
        K --> K5[Setup Session Permissions]
    end
    
    subgraph "Real-time Updates"
        L[WebSocket Connections]
        I --> L
        L --> L1[Welcome Message]
        L --> L2[Onboarding Notifications]
        L --> L3[System Announcements]
        L --> L4[Tour Guidance]
    end
    
    subgraph "Email Automation"
        M[Email Queue]
        E --> M
        M --> M1[Welcome Email]
        M --> M2[Email Verification]
        M --> M3[Onboarding Series]
        M --> M4[Feature Announcements]
        M --> M5[Tips & Best Practices]
    end
```

## User Login Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Security as Security Monitor
    participant DB as Database
    participant JWT as JWT Service
    participant Cache as Redis Cache
    
    Client->>API: POST /auth/login
    Note over Client,API: {email, password, remember_me?, device_info?}
    
    API->>Security: Check rate limiting
    Security->>API: Rate limit status
    
    alt Rate limit exceeded
        API->>Client: 429 Too Many Requests
    end
    
    API->>DB: Get user by email and website_id
    DB->>API: User data
    
    alt User not found
        API->>Security: Log failed attempt
        API->>Client: 401 Invalid credentials
    end
    
    API->>API: Verify password with bcrypt
    
    alt Password incorrect
        API->>Security: Log failed attempt
        API->>Security: Check account lockout
        
        alt Too many failed attempts
            API->>DB: Lock account temporarily
            API->>Client: 423 Account locked
        else
            API->>Client: 401 Invalid credentials
        end
    end
    
    API->>DB: Check account status
    
    alt Account inactive/banned
        API->>Client: 403 Account not active
    end
    
    API->>JWT: Generate access + refresh tokens
    JWT->>API: Token pair
    
    API->>DB: Update last login time
    API->>DB: Store refresh token
    API->>Cache: Cache user session with website_id prefix
    API->>Security: Log successful login
    
    API->>Client: 200 Login success + tokens
    Note over Client,API: {user, access_token, refresh_token, expires_in}
```

## Token Refresh Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant JWT as JWT Service
    participant DB as Database
    participant Blacklist as Token Blacklist
    
    Client->>API: POST /auth/refresh
    Note over Client,API: {refresh_token}
    
    API->>JWT: Validate refresh token format
    JWT->>API: Token validation result
    
    alt Token format invalid
        API->>Client: 401 Invalid token format
    end
    
    API->>Blacklist: Check if token blacklisted
    Blacklist->>API: Blacklist status
    
    alt Token blacklisted
        API->>Client: 401 Token revoked
    end
    
    API->>DB: Get refresh token from database
    DB->>API: Token record
    
    alt Token not found in DB
        API->>Client: 401 Invalid refresh token
    end
    
    API->>API: Check token expiration
    
    alt Token expired
        API->>DB: Delete expired token
        API->>Client: 401 Refresh token expired
    end
    
    API->>DB: Get user data
    DB->>API: User data
    
    alt User not found or inactive
        API->>DB: Delete token
        API->>Client: 401 User not found
    end
    
    API->>JWT: Generate new access token
    JWT->>API: New access token
    
    opt Token rotation enabled
        API->>JWT: Generate new refresh token
        JWT->>API: New refresh token
        API->>DB: Update refresh token
        API->>Blacklist: Add old refresh token to blacklist
    end
    
    API->>Client: 200 New tokens
    Note over Client,API: {access_token, refresh_token?, expires_in}
```

## Password Reset Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Token as Token Generator
    
    Note over Client,API: Step 1: Request Password Reset
    
    Client->>API: POST /auth/forgot-password
    Note over Client,API: {email}
    
    API->>DB: Find user by email
    DB->>API: User data
    
    alt User not found
        Note over API: Return success anyway (security)
        API->>Client: 200 Reset email sent
    end
    
    API->>Token: Generate reset token
    Token->>API: Secure reset token
    
    API->>DB: Store reset token with expiration
    API->>Email: Send reset email with token
    Email->>API: Email sent confirmation
    
    API->>Client: 200 Reset email sent
    
    Note over Client,API: Step 2: Reset Password with Token
    
    Client->>API: POST /auth/reset-password
    Note over Client,API: {token, new_password, password_confirmation}
    
    API->>DB: Find reset token
    DB->>API: Token record
    
    alt Token not found
        API->>Client: 404 Invalid reset token
    end
    
    API->>API: Check token expiration
    
    alt Token expired
        API->>DB: Delete expired token
        API->>Client: 410 Reset token expired
    end
    
    API->>API: Validate new password
    
    alt Password validation failed
        API->>Client: 422 Password validation error
    end
    
    API->>API: Hash new password
    API->>DB: Update user password
    API->>DB: Delete used reset token
    API->>DB: Invalidate all user sessions
    
    API->>Email: Send password changed notification
    API->>Client: 200 Password reset successful
```

## Password Change Flow (Authenticated)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Auth as Auth Middleware
    participant DB as Database
    participant Email as Email Service
    
    Client->>API: POST /auth/change-password
    Note over Client,API: Authorization: Bearer <token>
    Note over Client,API: {current_password, new_password, new_password_confirmation}
    
    API->>Auth: Validate access token
    Auth->>API: User context
    
    alt Token invalid
        API->>Client: 401 Unauthorized
    end
    
    API->>DB: Get user data
    DB->>API: User record
    
    API->>API: Verify current password
    
    alt Current password incorrect
        API->>Client: 400 Current password incorrect
    end
    
    API->>API: Validate new password
    
    alt Password validation failed
        API->>Client: 422 Password validation error
    end
    
    API->>API: Check password history
    
    alt Password recently used
        API->>Client: 400 Password recently used
    end
    
    API->>API: Hash new password
    API->>DB: Update user password
    API->>DB: Add to password history
    API->>DB: Invalidate all refresh tokens (except current)
    
    API->>Email: Send password changed notification
    API->>Client: 200 Password changed successfully
```

## Multi-Factor Authentication Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant MFA as MFA Service
    participant TOTP as TOTP Generator
    participant SMS as SMS Service
    
    Note over Client,API: Enable MFA
    
    Client->>API: POST /auth/mfa/enable
    Note over Client,API: {method: "totp" | "sms"}
    
    API->>MFA: Generate MFA secret
    MFA->>API: Secret + QR code
    
    API->>DB: Store MFA secret (unconfirmed)
    API->>Client: 200 MFA setup data
    Note over Client,API: {secret, qr_code, backup_codes}
    
    Client->>API: POST /auth/mfa/confirm
    Note over Client,API: {code}
    
    API->>TOTP: Verify TOTP code
    TOTP->>API: Verification result
    
    alt Code invalid
        API->>Client: 400 Invalid MFA code
    end
    
    API->>DB: Confirm MFA setup
    API->>Client: 200 MFA enabled
    
    Note over Client,API: Login with MFA
    
    Client->>API: POST /auth/login
    Note over Client,API: {email, password}
    
    API->>API: Verify credentials
    API->>DB: Check MFA enabled
    
    alt MFA required
        API->>API: Generate temporary MFA token
        API->>Client: 200 MFA required
        Note over Client,API: {mfa_required: true, mfa_token}
        
        Client->>API: POST /auth/mfa/verify
        Note over Client,API: {mfa_token, mfa_code}
        
        API->>TOTP: Verify MFA code
        TOTP->>API: Verification result
        
        alt MFA code invalid
            API->>Client: 400 Invalid MFA code
        end
        
        API->>API: Complete login flow
        API->>Client: 200 Login successful + tokens
    else
        API->>Client: 200 Login successful + tokens
    end
```

## Social Login Flow (OAuth)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant OAuth as OAuth Provider
    participant DB as Database
    participant JWT as JWT Service
    
    Client->>API: GET /auth/oauth/{provider}/redirect
    API->>Client: 302 Redirect to OAuth provider
    
    Client->>OAuth: Authorization request
    OAuth->>Client: Authorization code
    
    Client->>API: GET /auth/oauth/{provider}/callback
    Note over Client,API: ?code=auth_code&state=csrf_token
    
    API->>API: Validate CSRF state
    
    alt Invalid state
        API->>Client: 400 Invalid state parameter
    end
    
    API->>OAuth: Exchange code for access token
    OAuth->>API: Access token + user info
    
    API->>OAuth: Get user profile
    OAuth->>API: User profile data
    
    API->>DB: Find user by OAuth ID
    DB->>API: User lookup result
    
    alt User exists
        API->>DB: Update last login
        API->>JWT: Generate tokens
        JWT->>API: Token pair
        API->>Client: 302 Redirect with tokens
    else User not exists
        alt Account linking enabled
            API->>DB: Find user by email
            DB->>API: Existing user (if any)
            
            alt Email user exists
                API->>DB: Link OAuth account
                API->>JWT: Generate tokens
                JWT->>API: Token pair
                API->>Client: 302 Redirect with tokens
            else Create new user
                API->>DB: Create user with OAuth data
                API->>JWT: Generate tokens
                JWT->>API: Token pair
                API->>Client: 302 Redirect with tokens
            end
        else
            API->>DB: Create user with OAuth data
            API->>JWT: Generate tokens
            JWT->>API: Token pair
            API->>Client: 302 Redirect with tokens
        end
    end
```

## Session Management Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant Cache as Redis Cache
    participant Monitor as Security Monitor
    
    Note over Client,API: List Active Sessions
    
    Client->>API: GET /auth/sessions
    Note over Client,API: Authorization: Bearer <token>
    
    API->>DB: Get user active sessions
    DB->>API: Session list
    
    API->>API: Enrich with device info
    API->>Client: 200 Active sessions
    Note over Client,API: [{session_id, device, location, last_active, current}]
    
    Note over Client,API: Terminate Session
    
    Client->>API: DELETE /auth/sessions/{session_id}
    
    API->>DB: Find session
    DB->>API: Session data
    
    alt Session not found or not owned
        API->>Client: 404 Session not found
    end
    
    alt Terminating current session
        API->>DB: Delete refresh token
        API->>Cache: Clear session cache
        API->>Client: 200 Current session terminated
    else Terminating other session
        API->>DB: Delete refresh token
        API->>Cache: Clear session cache
        API->>Monitor: Log session termination
        API->>Client: 200 Session terminated
    end
    
    Note over Client,API: Terminate All Sessions
    
    Client->>API: POST /auth/sessions/terminate-all
    
    API->>DB: Delete all user refresh tokens
    API->>Cache: Clear all user session caches
    API->>Monitor: Log mass session termination
    
    API->>Client: 200 All sessions terminated
```

## Account Security Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Security as Security Monitor
    participant DB as Database
    participant Email as Email Service
    participant Admin as Admin Panel
    
    Note over Client,API: Suspicious Activity Detection
    
    Client->>API: Multiple failed login attempts
    
    API->>Security: Track failed attempts
    Security->>Security: Analyze patterns
    
    alt Suspicious pattern detected
        Security->>DB: Log security event
        Security->>Email: Send alert to user
        
        alt Severe threat detected
            Security->>DB: Temporarily lock account
            Security->>Admin: Alert administrators
            Security->>Email: Send account locked notification
        end
    end
    
    Note over Client,API: Account Recovery
    
    Client->>API: POST /auth/account-recovery
    Note over Client,API: {email, identity_verification}
    
    API->>Security: Verify recovery request
    Security->>API: Verification result
    
    alt Verification failed
        API->>Client: 403 Recovery verification failed
    end
    
    API->>DB: Generate recovery token
    API->>Email: Send recovery instructions
    API->>Client: 200 Recovery initiated
    
    Note over Client,API: Security Audit
    
    Client->>API: GET /auth/security-log
    
    API->>DB: Get user security events
    DB->>API: Security log
    
    API->>Client: 200 Security events
    Note over Client,API: [{event_type, timestamp, device, location, status}]
```