# Security Features

## Rate Limiting Flow

```mermaid
flowchart TD
    A[Incoming Request] --> B[Extract Client Identifier]
    B --> C[Check Rate Limit]
    C --> D{Limit Exceeded?}
    
    D -->|No| E[Process Request]
    D -->|Yes| F[Apply Backoff Strategy]
    
    F --> G{Backoff Type}
    G -->|Fixed| H[Fixed Delay]
    G -->|Exponential| I[Exponential Backoff]
    G -->|Adaptive| J[Adaptive Delay]
    
    H --> K[Return 429 Too Many Requests]
    I --> K
    J --> K
    
    E --> L[Update Rate Counter]
    L --> M[Process Business Logic]
    M --> N[Return Response]
    
    B --> O[Identifier Types]
    O --> O1[IP Address]
    O --> O2[User ID]
    O --> O3[API Key]
    O --> O4[Session ID]
```

## Account Lockout Flow

```mermaid
stateDiagram-v2
    [*] --> Active
    
    Active --> SuspiciousActivity : Failed login attempts
    SuspiciousActivity --> TemporaryLock : Threshold exceeded
    SuspiciousActivity --> Active : Successful login
    
    TemporaryLock --> Active : Lockout period expired
    TemporaryLock --> PermanentLock : Admin action
    TemporaryLock --> Active : Admin unlock
    
    Active --> PermanentLock : Severe security violation
    PermanentLock --> Active : Admin unlock
    
    Active --> Disabled : User deactivation
    Disabled --> Active : User reactivation
    
    note right of SuspiciousActivity
        - Track failed attempts
        - Analyze patterns
        - Send security alerts
    end note
    
    note right of TemporaryLock
        - Duration: 15min - 24hr
        - Progressive lockout
        - Email notification
    end note
```

## Password Security

### Password Requirements
- **Minimum Length**: 8 characters
- **Character Diversity**: Uppercase, lowercase, numbers, special characters
- **Common Password Prevention**: Block known weak passwords
- **Password History**: Prevent reuse of last 5 passwords
- **Entropy Checking**: Measure password strength

### Password Hashing
```go
type PasswordManager struct {
    minLength    int
    requireUpper bool
    requireLower bool
    requireNumber bool
    requireSpecial bool
    historyLimit int
}

func (pm *PasswordManager) ValidatePassword(password string) error {
    if len(password) < pm.minLength {
        return errors.New("password too short")
    }
    
    var hasUpper, hasLower, hasNumber, hasSpecial bool
    
    for _, char := range password {
        switch {
        case unicode.IsUpper(char):
            hasUpper = true
        case unicode.IsLower(char):
            hasLower = true
        case unicode.IsNumber(char):
            hasNumber = true
        case unicode.IsPunct(char) || unicode.IsSymbol(char):
            hasSpecial = true
        }
    }
    
    if pm.requireUpper && !hasUpper {
        return errors.New("password must contain uppercase letter")
    }
    // ... other validations
    
    return nil
}

func (pm *PasswordManager) HashPassword(password string) (string, error) {
    cost := 12 // bcrypt cost
    hash, err := bcrypt.GenerateFromPassword([]byte(password), cost)
    return string(hash), err
}
```

## Rate Limiting Implementation

### Sliding Window Rate Limiter
```go
type RateLimiter struct {
    redis  *redis.Client
    limits map[string]RateLimit
}

type RateLimit struct {
    Requests int           `json:"requests"`
    Window   time.Duration `json:"window"`
    Burst    int           `json:"burst"`
}

func (rl *RateLimiter) CheckLimit(key string, limitType string) (bool, error) {
    limit, exists := rl.limits[limitType]
    if !exists {
        return true, nil // No limit configured
    }
    
    // Sliding window implementation
    now := time.Now()
    window := now.Add(-limit.Window)
    
    pipe := rl.redis.Pipeline()
    
    // Remove old entries
    pipe.ZRemRangeByScore(key, "0", fmt.Sprintf("%d", window.Unix()))
    
    // Count current requests
    pipe.ZCard(key)
    
    // Add current request
    pipe.ZAdd(key, &redis.Z{
        Score:  float64(now.Unix()),
        Member: fmt.Sprintf("%d", now.UnixNano()),
    })
    
    // Set expiration
    pipe.Expire(key, limit.Window+time.Minute)
    
    results, err := pipe.Exec()
    if err != nil {
        return false, err
    }
    
    count := results[1].(*redis.IntCmd).Val()
    return count < int64(limit.Requests), nil
}
```

### Rate Limit Configuration
```yaml
rate_limiting:
  login:
    requests: 5
    window: "5m"
    burst: 10
  register:
    requests: 3
    window: "10m"
    burst: 5
  password_reset:
    requests: 3
    window: "1h"
    burst: 5
  api_general:
    requests: 1000
    window: "1h"
    burst: 100
```

## Account Lockout System

### Progressive Lockout Strategy
```go
type AccountSecurity struct {
    MaxFailedAttempts    int           `yaml:"max_failed_attempts"`
    LockoutDuration      time.Duration `yaml:"lockout_duration"`
    ProgressiveLockout   bool          `yaml:"progressive_lockout"`
    MaxLockoutDuration   time.Duration `yaml:"max_lockout_duration"`
}

func (as *AccountSecurity) CalculateLockoutDuration(attemptCount int) time.Duration {
    if !as.ProgressiveLockout {
        return as.LockoutDuration
    }
    
    // Progressive: 15min, 30min, 1hr, 2hr, 4hr, 8hr, 24hr
    multiplier := math.Pow(2, float64(attemptCount-as.MaxFailedAttempts))
    duration := time.Duration(float64(as.LockoutDuration) * multiplier)
    
    if duration > as.MaxLockoutDuration {
        return as.MaxLockoutDuration
    }
    
    return duration
}

func (as *AccountSecurity) ShouldLockAccount(failedAttempts int) bool {
    return failedAttempts >= as.MaxFailedAttempts
}
```

### Lockout Flow Implementation
```go
func (s *SecurityService) HandleFailedLogin(userID uint, websiteID uint, ipAddress string) error {
    // Update failed attempts
    user, err := s.repo.GetUser(userID)
    if err != nil {
        return err
    }
    
    user.FailedLoginAttempts++
    user.LastFailedAttempt = time.Now()
    
    // Check if account should be locked
    if s.config.ShouldLockAccount(user.FailedLoginAttempts) {
        lockDuration := s.config.CalculateLockoutDuration(user.FailedLoginAttempts)
        user.LockedUntil = &time.Time{}
        *user.LockedUntil = time.Now().Add(lockDuration)
        
        // Log security event
        s.LogSecurityEvent(userID, websiteID, "account_locked", map[string]interface{}{
            "failed_attempts": user.FailedLoginAttempts,
            "lock_duration":   lockDuration.String(),
            "ip_address":      ipAddress,
        })
        
        // Send notification
        s.notificationService.SendAccountLockedEmail(user)
    }
    
    return s.repo.UpdateUser(user)
}
```

## Multi-Factor Authentication (MFA)

### TOTP (Time-based One-Time Password)
```go
type MFAService struct {
    issuer string
}

func (m *MFAService) GenerateSecret(userEmail string) (*MFASetup, error) {
    secret := make([]byte, 16)
    _, err := rand.Read(secret)
    if err != nil {
        return nil, err
    }
    
    secretBase32 := base32.StdEncoding.EncodeToString(secret)
    
    // Generate QR code URL
    qrURL := fmt.Sprintf(
        "otpauth://totp/%s:%s?secret=%s&issuer=%s",
        url.QueryEscape(m.issuer),
        url.QueryEscape(userEmail),
        secretBase32,
        url.QueryEscape(m.issuer),
    )
    
    // Generate backup codes
    backupCodes := m.generateBackupCodes(8)
    
    return &MFASetup{
        Secret:      secretBase32,
        QRCodeURL:   qrURL,
        BackupCodes: backupCodes,
    }, nil
}

func (m *MFAService) VerifyTOTP(secret, code string) bool {
    return totp.Validate(code, secret)
}

func (m *MFAService) generateBackupCodes(count int) []string {
    codes := make([]string, count)
    for i := 0; i < count; i++ {
        codes[i] = generateRandomCode(8)
    }
    return codes
}
```

### MFA Models
```go
type MFASetup struct {
    Secret      string   `json:"secret"`
    QRCodeURL   string   `json:"qr_code_url"`
    BackupCodes []string `json:"backup_codes"`
}

type UserMFA struct {
    ID          uint      `gorm:"primarykey"`
    UserID      uint      `gorm:"not null;index"`
    WebsiteID   uint      `gorm:"not null;index"`
    Method      string    `gorm:"size:20;not null"` // totp, sms, email
    Secret      string    `gorm:"size:64"`
    BackupCodes JSON      `gorm:"type:json"`
    Enabled     bool      `gorm:"default:false"`
    VerifiedAt  *time.Time
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

## Security Event Logging

### Event Types
- **Authentication Events**: Login, logout, failed attempts
- **Authorization Events**: Permission grants/denials
- **Account Events**: Registration, password changes, lockouts
- **Session Events**: Token refresh, session termination
- **Suspicious Activity**: Unusual login patterns, brute force attempts

### Security Event Model
```go
type SecurityEvent struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    
    EventType   string    `gorm:"size:50;not null" json:"event_type"`
    Description string    `gorm:"size:255" json:"description"`
    
    // Context
    IPAddress   string    `gorm:"size:45" json:"ip_address"`
    UserAgent   string    `gorm:"size:500" json:"user_agent"`
    Location    JSON      `gorm:"type:json" json:"location"`
    
    // Risk assessment
    RiskScore   int       `gorm:"default:0" json:"risk_score"`
    Status      string    `gorm:"size:20;default:'normal'" json:"status"`
    
    // Additional data
    Metadata    JSON      `gorm:"type:json" json:"metadata"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}
```

### Risk Scoring
```go
type RiskAssessment struct {
    BaseScore    int
    LocationRisk int
    DeviceRisk   int
    TimeRisk     int
    FrequencyRisk int
}

func (r *RiskAssessment) CalculateTotal() int {
    total := r.BaseScore + r.LocationRisk + r.DeviceRisk + r.TimeRisk + r.FrequencyRisk
    if total > 100 {
        return 100
    }
    return total
}

func AssessLoginRisk(user *User, loginInfo *LoginInfo) *RiskAssessment {
    assessment := &RiskAssessment{BaseScore: 10}
    
    // Check for unusual location
    if !isKnownLocation(user.ID, loginInfo.IPAddress) {
        assessment.LocationRisk = 30
    }
    
    // Check for new device
    if !isKnownDevice(user.ID, loginInfo.DeviceFingerprint) {
        assessment.DeviceRisk = 20
    }
    
    // Check for unusual time
    if isUnusualTime(user.ID, loginInfo.Timestamp) {
        assessment.TimeRisk = 15
    }
    
    // Check login frequency
    if hasHighFrequency(user.ID, loginInfo.Timestamp) {
        assessment.FrequencyRisk = 25
    }
    
    return assessment
}
```

## Device Tracking

### Device Fingerprinting
```go
type DeviceInfo struct {
    Name        string `json:"device_name"`
    Type        string `json:"device_type"`
    OS          string `json:"os"`
    Browser     string `json:"browser"`
    UserAgent   string `json:"user_agent"`
    Fingerprint string `json:"fingerprint"`
}

func GenerateDeviceFingerprint(deviceInfo *DeviceInfo) string {
    data := fmt.Sprintf("%s|%s|%s|%s", 
        deviceInfo.OS, 
        deviceInfo.Browser, 
        deviceInfo.Type,
        hashUserAgent(deviceInfo.UserAgent),
    )
    
    hash := sha256.Sum256([]byte(data))
    return hex.EncodeToString(hash[:])
}

type KnownDevice struct {
    ID          uint      `gorm:"primarykey"`
    UserID      uint      `gorm:"not null;index"`
    WebsiteID   uint      `gorm:"not null;index"`
    Fingerprint string    `gorm:"size:64;not null"`
    DeviceInfo  JSON      `gorm:"type:json"`
    Trusted     bool      `gorm:"default:false"`
    LastSeenAt  time.Time
    CreatedAt   time.Time
}
```

## IP Geolocation & Blocking

### Geographic Analysis
```go
type LocationInfo struct {
    Country     string  `json:"country"`
    CountryCode string  `json:"country_code"`
    Region      string  `json:"region"`
    City        string  `json:"city"`
    Latitude    float64 `json:"latitude"`
    Longitude   float64 `json:"longitude"`
    ISP         string  `json:"isp"`
}

func GetLocationFromIP(ipAddress string) (*LocationInfo, error) {
    // Integration with GeoIP service
    // Return location information
}

func IsBlockedLocation(countryCode string, blockedCountries []string) bool {
    for _, blocked := range blockedCountries {
        if blocked == countryCode {
            return true
        }
    }
    return false
}
```

### IP Reputation Checking
```go
type IPReputation struct {
    IPAddress   string    `json:"ip_address"`
    Reputation  string    `json:"reputation"` // good, suspicious, malicious
    Confidence  int       `json:"confidence"`
    Sources     []string  `json:"sources"`
    LastChecked time.Time `json:"last_checked"`
}

func CheckIPReputation(ipAddress string) (*IPReputation, error) {
    // Check against multiple threat intelligence sources
    // Return reputation score and details
}
```

## Configuration

### Security Configuration
```yaml
security:
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special: true
    history_limit: 5
    
  account_lockout:
    max_failed_attempts: 5
    lockout_duration: "15m"
    progressive_lockout: true
    max_lockout_duration: "24h"
    
  mfa:
    enabled: true
    required_for_admin: true
    backup_codes_count: 8
    totp_window: 1
    
  rate_limiting:
    login:
      requests: 5
      window: "5m"
    register:
      requests: 3
      window: "10m"
    password_reset:
      requests: 3
      window: "1h"
      
  geo_blocking:
    enabled: false
    blocked_countries: ["XX", "YY"]
    whitelist_mode: false
    
  device_tracking:
    enabled: true
    require_approval: false
    trust_duration: "30d"
```