# Authorization & Permissions

## Role-Based Access Control Flow

```mermaid
flowchart TD
    A[Request with JWT] --> B[Extract User Context]
    B --> C[Load User Roles]
    C --> D[Check Resource Permissions]
    D --> E{Has Permission?}
    
    E -->|Yes| F[Allow Access]
    E -->|No| G[Check Dynamic Rules]
    
    G --> H{Dynamic Rule Match?}
    H -->|Yes| F
    H -->|No| I[Deny Access]
    
    F --> J[Log Access Grant]
    I --> K[Log Access Denial]
    
    C --> L[Role Hierarchy]
    L --> L1[Super Admin]
    L --> L2[Admin]
    L --> L3[Editor]
    L --> L4[Author]
    L --> L5[User]
    
    L1 --> M[All Permissions]
    L2 --> N[Management Permissions]
    L3 --> O[Content Permissions]
    L4 --> P[Own Content Permissions]
    L5 --> Q[Basic Permissions]
```

## Permission Check Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Resource API
    participant Auth as Auth Middleware
    participant RBAC as RBAC Service
    participant Cache as Permission Cache
    participant DB as Database
    
    Client->>API: Request protected resource
    Note over Client,API: Authorization: Bearer <token>
    
    API->>Auth: Validate JWT token
    Auth->>API: User context
    
    API->>RBAC: Check permission
    Note over API,RBAC: checkPermission(user_id, resource, action)
    
    RBAC->>Cache: Check cached permissions
    Cache->>RBAC: Cache result
    
    alt Cache hit
        RBAC->>API: Permission result
    else Cache miss
        RBAC->>DB: Load user roles & permissions
        DB->>RBAC: User permissions
        
        RBAC->>RBAC: Evaluate permission rules
        RBAC->>Cache: Cache permission result
        RBAC->>API: Permission result
    end
    
    alt Permission granted
        API->>Client: 200 Resource data
    else Permission denied
        API->>Client: 403 Forbidden
    end
```

## Authorization Types

### Resource-Based Authorization
- **Blog Posts**: Author can edit own posts, Editor can edit all
- **Media Files**: User can access own uploads, Admin can access all
- **User Profiles**: User can edit own profile, Admin can view all
- **Website Settings**: Only Admin/Super Admin access

### Action-Based Authorization
- **Create**: Who can create new resources
- **Read**: Who can view resources
- **Update**: Who can modify existing resources
- **Delete**: Who can remove resources
- **Publish**: Who can make content public

### Context-Based Authorization
- **Tenant Isolation**: Access limited to website context
- **Time-Based**: Temporary access permissions
- **Location-Based**: Geographic access restrictions
- **Device-Based**: Device-specific permissions

## Implementation Patterns

### Middleware-Based Authorization
```go
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetUint("user_id")
        websiteID := c.GetUint("website_id")
        
        hasPermission, err := rbacService.CheckPermission(
            userID, 
            websiteID, 
            permission,
        )
        
        if err != nil || !hasPermission {
            c.JSON(403, gin.H{"error": "forbidden"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### Resource-Specific Authorization
```go
func RequireResourceAccess(resourceType string, action string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetUint("user_id")
        resourceID := c.Param("id")
        
        canAccess, err := rbacService.CanAccessResource(
            userID,
            resourceType,
            resourceID,
            action,
        )
        
        if err != nil || !canAccess {
            c.JSON(403, gin.H{"error": "access denied"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### Dynamic Permission Rules
```go
type PermissionRule struct {
    Resource   string
    Action     string
    Condition  func(user *User, resource interface{}) bool
}

var DynamicRules = []PermissionRule{
    {
        Resource: "blog_post",
        Action:   "edit",
        Condition: func(user *User, resource interface{}) bool {
            post := resource.(*BlogPost)
            return post.AuthorID == user.ID
        },
    },
    {
        Resource: "comment",
        Action:   "delete",
        Condition: func(user *User, resource interface{}) bool {
            comment := resource.(*Comment)
            return comment.UserID == user.ID || user.HasRole("moderator")
        },
    },
}
```

## Permission Caching Strategy

### Cache Key Patterns
```go
const (
    UserRolesKey       = "website:%d:user:%d:roles"
    UserPermissionsKey = "website:%d:user:%d:permissions"
    RolePermissionsKey = "website:%d:role:%d:permissions"
    ResourceAccessKey  = "website:%d:user:%d:resource:%s:%d"
)
```

### Cache Invalidation
```go
func InvalidateUserPermissions(websiteID, userID uint) {
    keys := []string{
        fmt.Sprintf(UserRolesKey, websiteID, userID),
        fmt.Sprintf(UserPermissionsKey, websiteID, userID),
        fmt.Sprintf("website:%d:user:%d:resource:*", websiteID, userID),
    }
    
    for _, pattern := range keys {
        cache.DeletePattern(pattern)
    }
}
```

## Tenant Isolation in Authorization

### Website-Scoped Permissions
```go
func CheckTenantPermission(userID, websiteID uint, permission string) bool {
    // Ensure user belongs to the website
    if !userBelongsToWebsite(userID, websiteID) {
        return false
    }
    
    // Check permission within website context
    return hasPermissionInWebsite(userID, websiteID, permission)
}
```

### Cross-Tenant Access Prevention
```go
func ValidateTenantAccess(c *gin.Context) {
    tokenWebsiteID := c.GetUint("website_id")
    requestWebsiteID := c.GetUint("request_website_id")
    
    if tokenWebsiteID != requestWebsiteID {
        c.JSON(403, gin.H{
            "error": "cross_tenant_access_denied",
            "message": "Cannot access resources from different websites",
        })
        c.Abort()
        return
    }
}
```

## Best Practices

### Security Principles
- **Principle of Least Privilege**: Grant minimum required permissions
- **Fail Secure**: Deny access by default
- **Defense in Depth**: Multiple authorization layers
- **Audit Trail**: Log all authorization decisions

### Performance Optimization
- **Permission Caching**: Cache frequently checked permissions
- **Bulk Operations**: Check multiple permissions in single call
- **Lazy Loading**: Load permissions only when needed
- **Database Indexing**: Optimize role/permission queries

### Error Handling
- **Consistent Responses**: Standard 403 Forbidden responses
- **No Information Leakage**: Don't reveal why access denied
- **Audit Logging**: Log failed authorization attempts
- **Rate Limiting**: Prevent permission brute force attacks