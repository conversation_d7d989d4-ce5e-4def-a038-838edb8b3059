# Models và Database Schema

## Core Auth Models

### User Model với Security Features
```go
type User struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index;index:idx_website_email,unique" json:"website_id"`
    Email       string    `gorm:"not null;index:idx_website_email,unique" json:"email"`
    Password    string    `gorm:"not null" json:"-"`
    Name        string    `gorm:"size:255;not null" json:"name"`
    
    // Account status
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, locked, disabled
    Role        string    `gorm:"size:50;default:'user'" json:"role"`
    
    // Security fields
    FailedLoginAttempts int       `gorm:"default:0" json:"-"`
    LockedUntil        *time.Time `json:"-"`
    LastLoginAt        *time.Time `json:"last_login_at"`
    LastPasswordChange *time.Time `json:"-"`
    
    // MFA
    MFAEnabled    bool   `gorm:"default:false" json:"mfa_enabled"`
    MFASecret     string `gorm:"size:32" json:"-"`
    BackupCodes   JSON   `gorm:"type:json" json:"-"`
    
    // Note: Profile fields moved to User Module
    
    // Timestamps
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    // Uses status field for soft deletes instead of DeletedAt
    
    // Relationships
    Website         Website          `json:"-"`
    RefreshTokens   []RefreshToken   `json:"-"`
    PasswordResets  []PasswordReset  `json:"-"`
    SecurityEvents  []SecurityEvent  `json:"-"`
    OAuthAccounts   []OAuthAccount   `json:"-"`
    MFADevices      []MFADevice      `json:"-"`
}

// TableName specifies the table name for User model
func (User) TableName() string {
    return "blog_user"
}

// BeforeCreate hash password before creating user
func (u *User) BeforeCreate(tx *gorm.DB) error {
    if u.Password != "" {
        hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), 12)
        if err != nil {
            return err
        }
        u.Password = string(hashedPassword)
    }
    return nil
}

// CheckPassword verifies the provided password
func (u *User) CheckPassword(password string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
    return err == nil
}

// IsLocked checks if the account is currently locked
func (u *User) IsLocked() bool {
    return u.LockedUntil != nil && u.LockedUntil.After(time.Now())
}

// CanLogin checks if user can login
func (u *User) CanLogin() bool {
    return u.Status == "active" && !u.IsLocked()
}
```

### Session Management Models

#### RefreshToken Model
```go
type RefreshToken struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    Token     string    `gorm:"uniqueIndex;not null;size:255" json:"-"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    
    // Device information
    DeviceName   string `gorm:"size:100" json:"device_name"`
    DeviceType   string `gorm:"size:50" json:"device_type"`
    OS           string `gorm:"size:50" json:"os"`
    Browser      string `gorm:"size:50" json:"browser"`
    DeviceID     string `gorm:"size:64;index" json:"device_id"`
    
    // Location information
    IPAddress    string `gorm:"size:45" json:"ip_address"`
    Country      string `gorm:"size:100" json:"country"`
    City         string `gorm:"size:100" json:"city"`
    
    // Security
    IsRevoked    bool      `gorm:"default:false" json:"is_revoked"`
    LastUsedAt   time.Time `json:"last_used_at"`
    ExpiresAt    time.Time `json:"expires_at"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (RefreshToken) TableName() string {
    return "blog_user_session"
}

// IsExpired checks if the refresh token has expired
func (rt *RefreshToken) IsExpired() bool {
    return time.Now().After(rt.ExpiresAt)
}

// IsValid checks if the token is valid (not revoked and not expired)
func (rt *RefreshToken) IsValid() bool {
    return !rt.IsRevoked && !rt.IsExpired()
}
```

#### TokenBlacklist Model
```go
type TokenBlacklist struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    TokenID   string    `gorm:"uniqueIndex;not null;size:255" json:"token_id"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    Reason    string    `gorm:"size:100" json:"reason"`
    ExpiresAt time.Time `json:"expires_at"`
    CreatedAt time.Time `json:"created_at"`
}

func (TokenBlacklist) TableName() string {
    return "blog_token_blacklist"
}
```

### Password Management Models

#### PasswordReset Model
```go
type PasswordReset struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    Token     string    `gorm:"uniqueIndex;not null;size:255" json:"-"`
    Email     string    `gorm:"not null;size:255" json:"email"`
    UsedAt    *time.Time `json:"used_at"`
    ExpiresAt time.Time `json:"expires_at"`
    CreatedAt time.Time `json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (PasswordReset) TableName() string {
    return "blog_password_reset"
}

// IsExpired checks if the reset token has expired
func (pr *PasswordReset) IsExpired() bool {
    return time.Now().After(pr.ExpiresAt)
}

// IsUsed checks if the reset token has been used
func (pr *PasswordReset) IsUsed() bool {
    return pr.UsedAt != nil
}

// IsValid checks if the token is valid (not used and not expired)
func (pr *PasswordReset) IsValid() bool {
    return !pr.IsUsed() && !pr.IsExpired()
}
```

#### PasswordHistory Model
```go
type PasswordHistory struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    Password  string    `gorm:"not null;size:255" json:"-"`
    CreatedAt time.Time `json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (PasswordHistory) TableName() string {
    return "blog_password_history"
}
```

### Security Event Model
```go
type SecurityEvent struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    
    EventType   string    `gorm:"size:50;not null;index" json:"event_type"`
    Description string    `gorm:"size:255" json:"description"`
    
    // Context
    IPAddress   string    `gorm:"size:45;index" json:"ip_address"`
    UserAgent   string    `gorm:"size:500" json:"user_agent"`
    Location    JSON      `gorm:"type:json" json:"location"`
    
    // Risk assessment
    RiskScore   int       `gorm:"default:0;index" json:"risk_score"`
    Status      string    `gorm:"size:20;default:'normal';index" json:"status"`
    
    // Additional data
    Metadata    JSON      `gorm:"type:json" json:"metadata"`
    CreatedAt   time.Time `gorm:"index" json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (SecurityEvent) TableName() string {
    return "blog_security_event"
}

// Security Event Types
const (
    EventTypeLoginSuccess    = "login_success"
    EventTypeLoginFailed     = "login_failed"
    EventTypeLoginSuspicious = "login_suspicious"
    EventTypeLogout          = "logout"
    EventTypePasswordChange  = "password_change"
    EventTypePasswordReset   = "password_reset"
    EventTypeAccountLocked   = "account_locked"
    EventTypeAccountUnlocked = "account_unlocked"
    EventTypeMFAEnabled      = "mfa_enabled"
    EventTypeMFADisabled     = "mfa_disabled"
    EventTypeTokenRefresh    = "token_refresh"
    EventTypeSessionTerminated = "session_terminated"
)

// Risk Score Levels
const (
    RiskScoreLow    = 0
    RiskScoreMedium = 30
    RiskScoreHigh   = 60
    RiskScoreCritical = 90
)
```

### Multi-Factor Authentication Models

#### MFADevice Model
```go
type MFADevice struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    
    DeviceType  string    `gorm:"size:20;not null" json:"device_type"` // totp, sms, email
    DeviceName  string    `gorm:"size:100" json:"device_name"`
    Secret      string    `gorm:"size:64" json:"-"`
    BackupCodes JSON      `gorm:"type:json" json:"-"`
    
    // Status
    IsEnabled   bool      `gorm:"default:false" json:"is_enabled"`
    IsVerified  bool      `gorm:"default:false" json:"is_verified"`
    LastUsedAt  *time.Time `json:"last_used_at"`
    
    // Metadata
    PhoneNumber string    `gorm:"size:20" json:"phone_number,omitempty"`
    EmailAddress string   `gorm:"size:255" json:"email_address,omitempty"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (MFADevice) TableName() string {
    return "blog_mfa_device"
}
```

#### TrustedDevice Model
```go
type TrustedDevice struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    DeviceID    string    `gorm:"size:64;not null;index" json:"device_id"`
    DeviceName  string    `gorm:"size:100" json:"device_name"`
    
    // Device Info
    DeviceType  string    `gorm:"size:50" json:"device_type"`
    OS          string    `gorm:"size:50" json:"os"`
    Browser     string    `gorm:"size:50" json:"browser"`
    UserAgent   string    `gorm:"size:500" json:"user_agent"`
    
    // Trust Info
    TrustedAt   time.Time `json:"trusted_at"`
    ExpiresAt   time.Time `json:"expires_at"`
    LastSeenAt  time.Time `json:"last_seen_at"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (TrustedDevice) TableName() string {
    return "blog_trusted_device"
}

// IsExpired checks if the trusted device has expired
func (td *TrustedDevice) IsExpired() bool {
    return time.Now().After(td.ExpiresAt)
}
```

### OAuth Integration Models

#### OAuthAccount Model
```go
type OAuthAccount struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    
    Provider    string    `gorm:"size:50;not null;index" json:"provider"`
    ProviderID  string    `gorm:"size:255;not null" json:"provider_id"`
    Email       string    `gorm:"size:255" json:"email"`
    Name        string    `gorm:"size:255" json:"name"`
    Avatar      string    `gorm:"size:500" json:"avatar"`
    
    // Tokens
    AccessToken  string   `gorm:"size:1000" json:"-"`
    RefreshToken string   `gorm:"size:1000" json:"-"`
    ExpiresAt    *time.Time `json:"expires_at"`
    
    // Metadata
    Metadata    JSON      `gorm:"type:json" json:"metadata"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

func (OAuthAccount) TableName() string {
    return "blog_oauth_account"
}

// Unique constraint for provider + provider_id + website_id
func (oa *OAuthAccount) BeforeCreate(tx *gorm.DB) error {
    var count int64
    tx.Model(&OAuthAccount{}).Where(
        "provider = ? AND provider_id = ? AND website_id = ?", 
        oa.Provider, oa.ProviderID, oa.WebsiteID,
    ).Count(&count)
    
    if count > 0 {
        return errors.New("oauth account already exists")
    }
    return nil
}
```

## Database Indexes

### User Table Indexes
```sql
-- Primary key
CREATE INDEX idx_blog_user_id ON blog_user(id);

-- Unique constraint for email per website
CREATE UNIQUE INDEX idx_blog_user_website_email ON blog_user(website_id, email);

-- Performance indexes
CREATE INDEX idx_blog_user_website_id ON blog_user(website_id);
CREATE INDEX idx_blog_user_status ON blog_user(status);
CREATE INDEX idx_blog_user_role ON blog_user(role);
CREATE INDEX idx_blog_user_created_at ON blog_user(created_at);
CREATE INDEX idx_blog_user_last_login ON blog_user(last_login_at);

-- Security indexes
CREATE INDEX idx_blog_user_locked_until ON blog_user(locked_until);
CREATE INDEX idx_blog_user_failed_attempts ON blog_user(failed_login_attempts);
```

### Session Table Indexes
```sql
-- Primary key and unique token
CREATE INDEX idx_blog_user_session_id ON blog_user_session(id);
CREATE UNIQUE INDEX idx_blog_user_session_token ON blog_user_session(token);

-- Performance indexes
CREATE INDEX idx_blog_user_session_user_id ON blog_user_session(user_id);
CREATE INDEX idx_blog_user_session_website_id ON blog_user_session(website_id);
CREATE INDEX idx_blog_user_session_device_id ON blog_user_session(device_id);
CREATE INDEX idx_blog_user_session_expires_at ON blog_user_session(expires_at);
CREATE INDEX idx_blog_user_session_created_at ON blog_user_session(created_at);

-- Cleanup indexes
CREATE INDEX idx_blog_user_session_revoked ON blog_user_session(is_revoked);
```

### Security Event Table Indexes
```sql
-- Primary key
CREATE INDEX idx_blog_security_event_id ON blog_security_event(id);

-- Performance indexes
CREATE INDEX idx_blog_security_event_user_id ON blog_security_event(user_id);
CREATE INDEX idx_blog_security_event_website_id ON blog_security_event(website_id);
CREATE INDEX idx_blog_security_event_type ON blog_security_event(event_type);
CREATE INDEX idx_blog_security_event_created_at ON blog_security_event(created_at);

-- Security analysis indexes
CREATE INDEX idx_blog_security_event_ip ON blog_security_event(ip_address);
CREATE INDEX idx_blog_security_event_risk_score ON blog_security_event(risk_score);
CREATE INDEX idx_blog_security_event_status ON blog_security_event(status);

-- Composite indexes for common queries
CREATE INDEX idx_blog_security_event_user_type ON blog_security_event(user_id, event_type);
CREATE INDEX idx_blog_security_event_website_type_date ON blog_security_event(website_id, event_type, created_at);
```

## Database Constraints

### Foreign Key Constraints
```sql
-- User relationships
ALTER TABLE blog_user_session 
ADD CONSTRAINT fk_blog_user_session_user 
FOREIGN KEY (user_id) REFERENCES blog_user(id) ON DELETE CASCADE;

ALTER TABLE blog_password_reset 
ADD CONSTRAINT fk_blog_password_reset_user 
FOREIGN KEY (user_id) REFERENCES blog_user(id) ON DELETE CASCADE;

ALTER TABLE blog_security_event 
ADD CONSTRAINT fk_blog_security_event_user 
FOREIGN KEY (user_id) REFERENCES blog_user(id) ON DELETE CASCADE;

ALTER TABLE blog_mfa_device 
ADD CONSTRAINT fk_blog_mfa_device_user 
FOREIGN KEY (user_id) REFERENCES blog_user(id) ON DELETE CASCADE;

ALTER TABLE blog_oauth_account 
ADD CONSTRAINT fk_blog_oauth_account_user 
FOREIGN KEY (user_id) REFERENCES blog_user(id) ON DELETE CASCADE;

-- Website relationships
ALTER TABLE blog_user 
ADD CONSTRAINT fk_blog_user_website 
FOREIGN KEY (website_id) REFERENCES blog_website(id) ON DELETE CASCADE;
```

### Check Constraints
```sql
-- User status validation
ALTER TABLE blog_user 
ADD CONSTRAINT chk_blog_user_status 
CHECK (status IN ('active', 'locked', 'disabled', 'pending'));

-- User role validation
ALTER TABLE blog_user 
ADD CONSTRAINT chk_blog_user_role 
CHECK (role IN ('super_admin', 'admin', 'editor', 'author', 'user'));

-- Security event type validation
ALTER TABLE blog_security_event 
ADD CONSTRAINT chk_blog_security_event_type 
CHECK (event_type IN (
    'login_success', 'login_failed', 'login_suspicious', 'logout',
    'password_change', 'password_reset', 'account_locked', 'account_unlocked',
    'mfa_enabled', 'mfa_disabled', 'token_refresh', 'session_terminated'
));

-- Risk score validation
ALTER TABLE blog_security_event 
ADD CONSTRAINT chk_blog_security_event_risk_score 
CHECK (risk_score >= 0 AND risk_score <= 100);
```

## Cache Schema

### Redis Key Patterns
```go
const (
    // User cache keys
    UserCacheKey          = "website:%d:user:%d"
    UserEmailCacheKey     = "website:%d:user:email:%s"
    UserRolesCacheKey     = "website:%d:user:%d:roles"
    UserPermissionsCacheKey = "website:%d:user:%d:permissions"
    
    // Session cache keys
    SessionCacheKey       = "website:%d:session:%s"
    UserSessionsKey       = "website:%d:user:%d:sessions"
    ActiveSessionsKey     = "website:%d:sessions:active"
    
    // Security cache keys
    FailedAttemptsKey     = "website:%d:failed_attempts:%s"
    RateLimitKey          = "website:%d:ratelimit:%s:%s"
    MFASecretKey          = "website:%d:mfa:%d"
    TrustedDeviceKey      = "website:%d:trusted_device:%s"
    
    // Password reset cache keys
    PasswordResetKey      = "website:%d:pwreset:%s"
    PasswordResetCountKey = "website:%d:pwreset:count:%s"
    
    // OAuth cache keys
    OAuthStateKey         = "oauth:state:%s"
    OAuthTokenKey         = "website:%d:oauth:%d:%s"
)
```

### Cache TTL Configuration
```go
const (
    UserCacheTTL          = 1 * time.Hour
    SessionCacheTTL       = 7 * 24 * time.Hour
    PermissionCacheTTL    = 30 * time.Minute
    RateLimitTTL          = 15 * time.Minute
    FailedAttemptsTTL     = 1 * time.Hour
    PasswordResetTTL      = 1 * time.Hour
    MFASecretTTL          = 10 * time.Minute
    OAuthStateTTL         = 10 * time.Minute
    TrustedDeviceTTL      = 30 * 24 * time.Hour
)
```