# Tenant Isolation

## Multi-tenancy Implementation

### Repository Level Isolation
```go
type AuthRepository struct {
    db *gorm.DB
}

// GetUserByEmail với tenant isolation
func (r *AuthRepository) GetUserByEmail(websiteID uint, email string) (*User, error) {
    var user User
    err := r.db.Where("website_id = ? AND email = ?", websiteID, email).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, ErrUserNotFound
        }
        return nil, err
    }
    return &user, nil
}

// CreateUser với tenant validation
func (r *AuthRepository) CreateUser(user *User) error {
    // Validate website exists and is active
    var website Website
    if err := r.db.First(&website, user.WebsiteID).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return ErrInvalidWebsite
        }
        return err
    }
    
    if website.Status != "active" {
        return ErrWebsiteInactive
    }
    
    // Check website user limits
    var userCount int64
    r.db.Model(&User{}).Where("website_id = ? AND status != ?", user.WebsiteID, "deleted").Count(&userCount)
    
    if userCount >= int64(website.MaxUsers) {
        return ErrWebsiteUserLimitExceeded
    }
    
    // Check email uniqueness within website
    var existingCount int64
    r.db.Model(&User{}).Where("website_id = ? AND email = ?", user.WebsiteID, user.Email).Count(&existingCount)
    
    if existingCount > 0 {
        return ErrEmailAlreadyExists
    }
    
    return r.db.Create(user).Error
}

// GetUserSessions với tenant filtering
func (r *AuthRepository) GetUserSessions(websiteID, userID uint) ([]RefreshToken, error) {
    var tokens []RefreshToken
    err := r.db.Where("website_id = ? AND user_id = ? AND is_revoked = ?", websiteID, userID, false).
        Order("last_used_at DESC").
        Find(&tokens).Error
    return tokens, err
}

// UpdateUserInWebsite ensures user belongs to website
func (r *AuthRepository) UpdateUserInWebsite(websiteID, userID uint, updates map[string]interface{}) error {
    result := r.db.Model(&User{}).
        Where("id = ? AND website_id = ?", userID, websiteID).
        Updates(updates)
    
    if result.Error != nil {
        return result.Error
    }
    
    if result.RowsAffected == 0 {
        return ErrUserNotFoundInWebsite
    }
    
    return nil
}

// DeleteUserFromWebsite soft deletes user only in specific website
func (r *AuthRepository) DeleteUserFromWebsite(websiteID, userID uint) error {
    result := r.db.Where("id = ? AND website_id = ?", userID, websiteID).Delete(&User{})
    
    if result.Error != nil {
        return result.Error
    }
    
    if result.RowsAffected == 0 {
        return ErrUserNotFoundInWebsite
    }
    
    return nil
}
```

### Service Level Tenant Context

#### Tenant Context Middleware
```go
type TenantContext struct {
    WebsiteID uint
    TenantID  uint
    UserID    uint
}

func TenantContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract website_id from header
        websiteIDStr := c.GetHeader("X-Website-ID")
        if websiteIDStr == "" {
            c.JSON(400, gin.H{
                "error": "missing_website_id",
                "message": "X-Website-ID header is required",
            })
            c.Abort()
            return
        }
        
        websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
        if err != nil {
            c.JSON(400, gin.H{
                "error": "invalid_website_id",
                "message": "X-Website-ID must be a valid number",
            })
            c.Abort()
            return
        }
        
        // Validate website exists and is active
        website, err := websiteService.GetWebsite(uint(websiteID))
        if err != nil {
            if errors.Is(err, ErrWebsiteNotFound) {
                c.JSON(404, gin.H{
                    "error": "website_not_found",
                    "message": "The specified website does not exist",
                })
            } else {
                c.JSON(500, gin.H{
                    "error": "internal_error",
                    "message": "Failed to validate website",
                })
            }
            c.Abort()
            return
        }
        
        if website.Status != "active" {
            c.JSON(403, gin.H{
                "error": "website_inactive",
                "message": "The specified website is not active",
            })
            c.Abort()
            return
        }
        
        // Set tenant context
        c.Set("website_id", uint(websiteID))
        c.Set("tenant_id", website.TenantID)
        c.Next()
    }
}
```

#### Authenticated Tenant Middleware
```go
func AuthenticatedTenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get token from Authorization header
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(401, gin.H{
                "error": "unauthorized",
                "message": "Authorization header is required",
            })
            c.Abort()
            return
        }
        
        // Extract token from "Bearer <token>"
        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.JSON(401, gin.H{
                "error": "invalid_token_format",
                "message": "Authorization header must be in format 'Bearer <token>'",
            })
            c.Abort()
            return
        }
        
        token := tokenParts[1]
        
        // Validate token
        claims, err := tokenService.ValidateToken(token)
        if err != nil {
            c.JSON(401, gin.H{
                "error": "invalid_token",
                "message": "The provided token is invalid or expired",
            })
            c.Abort()
            return
        }
        
        // Check if token's website_id matches request's website_id
        requestWebsiteID := c.GetUint("website_id")
        if claims.WebsiteID != requestWebsiteID {
            c.JSON(403, gin.H{
                "error": "cross_tenant_access_denied",
                "message": "Cannot access resources from different websites",
                "details": map[string]interface{}{
                    "token_website_id":   claims.WebsiteID,
                    "request_website_id": requestWebsiteID,
                },
            })
            c.Abort()
            return
        }
        
        // Verify user still exists and is active in the website
        user, err := userService.GetUserInWebsite(claims.WebsiteID, claims.UserID)
        if err != nil {
            if errors.Is(err, ErrUserNotFoundInWebsite) {
                c.JSON(401, gin.H{
                    "error": "user_not_found",
                    "message": "User no longer exists in this website",
                })
            } else {
                c.JSON(500, gin.H{
                    "error": "internal_error",
                    "message": "Failed to validate user",
                })
            }
            c.Abort()
            return
        }
        
        if !user.CanLogin() {
            c.JSON(401, gin.H{
                "error": "user_inactive",
                "message": "User account is not active",
            })
            c.Abort()
            return
        }
        
        // Set user context
        c.Set("user_id", claims.UserID)
        c.Set("user_role", claims.Role)
        c.Set("user_email", claims.Email)
        
        c.Next()
    }
}
```

### Cache Key Patterns với Tenant Isolation

#### Cache Key Strategy
```go
// Cache keys với website_id prefix để ensure tenant isolation
const (
    UserCacheKey          = "website:%d:user:%d"                    // website:1:user:123
    UserEmailCacheKey     = "website:%d:user:email:%s"             // website:1:user:email:<EMAIL>
    UserSessionsCacheKey  = "website:%d:user:%d:sessions"          // website:1:user:123:sessions
    SessionCacheKey       = "website:%d:session:%s"               // website:1:session:abc123
    RateLimitCacheKey     = "website:%d:ratelimit:%s:%s"           // website:1:ratelimit:login:192.168.1.1
    MFASecretCacheKey     = "website:%d:mfa:%d:secret"             // website:1:mfa:123:secret
    PasswordResetCacheKey = "website:%d:pwreset:%s"               // website:1:pwreset:token123
    SecurityEventCacheKey = "website:%d:security:%d:events"       // website:1:security:123:events
)

// Helper functions for cache key generation
func GetUserCacheKey(websiteID, userID uint) string {
    return fmt.Sprintf(UserCacheKey, websiteID, userID)
}

func GetUserEmailCacheKey(websiteID uint, email string) string {
    return fmt.Sprintf(UserEmailCacheKey, websiteID, email)
}

func GetSessionCacheKey(websiteID uint, sessionID string) string {
    return fmt.Sprintf(SessionCacheKey, websiteID, sessionID)
}

func GetRateLimitCacheKey(websiteID uint, limitType, identifier string) string {
    return fmt.Sprintf(RateLimitCacheKey, websiteID, limitType, identifier)
}
```

#### Cache Service với Tenant Isolation
```go
type TenantAwareCacheService struct {
    redis  *redis.Client
    config CacheConfig
}

func (cs *TenantAwareCacheService) GetUser(websiteID, userID uint) (*User, error) {
    key := GetUserCacheKey(websiteID, userID)
    
    data, err := cs.redis.Get(key).Result()
    if err != nil {
        if err == redis.Nil {
            return nil, ErrCacheMiss
        }
        return nil, err
    }
    
    var user User
    if err := json.Unmarshal([]byte(data), &user); err != nil {
        return nil, err
    }
    
    // Verify cached user belongs to the correct website
    if user.WebsiteID != websiteID {
        // Cache corruption - delete invalid entry
        cs.redis.Del(key)
        return nil, ErrCacheCorruption
    }
    
    return &user, nil
}

func (cs *TenantAwareCacheService) SetUser(user *User) error {
    key := GetUserCacheKey(user.WebsiteID, user.ID)
    
    data, err := json.Marshal(user)
    if err != nil {
        return err
    }
    
    return cs.redis.Set(key, data, cs.config.UserTTL).Err()
}

func (cs *TenantAwareCacheService) InvalidateUserCache(websiteID, userID uint) error {
    keys := []string{
        GetUserCacheKey(websiteID, userID),
        GetUserSessionsCacheKey(websiteID, userID),
        fmt.Sprintf("website:%d:user:%d:permissions", websiteID, userID),
        fmt.Sprintf("website:%d:user:%d:roles", websiteID, userID),
    }
    
    return cs.redis.Del(keys...).Err()
}

func (cs *TenantAwareCacheService) InvalidateWebsiteCache(websiteID uint) error {
    // Get all keys for this website
    pattern := fmt.Sprintf("website:%d:*", websiteID)
    
    keys, err := cs.redis.Keys(pattern).Result()
    if err != nil {
        return err
    }
    
    if len(keys) == 0 {
        return nil
    }
    
    return cs.redis.Del(keys...).Err()
}
```

### JWT Token với Tenant Context

#### Token Claims với Website ID
```go
type TenantAwareTokenClaims struct {
    jwt.StandardClaims
    UserID    uint   `json:"user_id"`
    WebsiteID uint   `json:"website_id"`
    TenantID  uint   `json:"tenant_id"`
    Email     string `json:"email"`
    Role      string `json:"role"`
    SessionID string `json:"session_id"`
}

func (ts *TokenService) GenerateTokenPair(user *User, sessionID string, website *Website) (*TokenPair, error) {
    now := time.Now()
    
    // Generate access token với tenant context
    accessClaims := &TenantAwareTokenClaims{
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: now.Add(ts.config.AccessTokenTTL).Unix(),
            IssuedAt:  now.Unix(),
            Id:        generateTokenID(),
            Issuer:    fmt.Sprintf("blog-api-v3-website-%d", user.WebsiteID),
            Subject:   fmt.Sprintf("user-%d", user.ID),
            Audience:  fmt.Sprintf("website-%d", user.WebsiteID),
        },
        UserID:    user.ID,
        WebsiteID: user.WebsiteID,
        TenantID:  website.TenantID,
        Email:     user.Email,
        Role:      user.Role,
        SessionID: sessionID,
    }
    
    accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
    accessTokenString, err := accessToken.SignedString([]byte(ts.config.Secret))
    if err != nil {
        return nil, err
    }
    
    // Generate refresh token
    refreshClaims := &TenantAwareTokenClaims{
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: now.Add(ts.config.RefreshTokenTTL).Unix(),
            IssuedAt:  now.Unix(),
            Id:        generateTokenID(),
            Issuer:    fmt.Sprintf("blog-api-v3-website-%d", user.WebsiteID),
            Subject:   fmt.Sprintf("user-%d", user.ID),
            Audience:  fmt.Sprintf("website-%d", user.WebsiteID),
        },
        UserID:    user.ID,
        WebsiteID: user.WebsiteID,
        TenantID:  website.TenantID,
        SessionID: sessionID,
    }
    
    refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
    refreshTokenString, err := refreshToken.SignedString([]byte(ts.config.Secret))
    if err != nil {
        return nil, err
    }
    
    return &TokenPair{
        AccessToken:  accessTokenString,
        RefreshToken: refreshTokenString,
        TokenType:    "Bearer",
        ExpiresIn:    int(ts.config.AccessTokenTTL.Seconds()),
    }, nil
}

func (ts *TokenService) ValidateTokenForWebsite(tokenString string, expectedWebsiteID uint) (*TenantAwareTokenClaims, error) {
    claims, err := ts.ValidateToken(tokenString)
    if err != nil {
        return nil, err
    }
    
    // Verify token belongs to the expected website
    if claims.WebsiteID != expectedWebsiteID {
        return nil, ErrCrossTenantAccess
    }
    
    // Verify audience matches
    expectedAudience := fmt.Sprintf("website-%d", expectedWebsiteID)
    if claims.Audience != expectedAudience {
        return nil, ErrInvalidTokenAudience
    }
    
    return claims, nil
}
```

### Database Queries với Tenant Filtering

#### GORM Scopes for Tenant Isolation
```go
// TenantScope adds website_id filter to all queries
func TenantScope(websiteID uint) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("website_id = ?", websiteID)
    }
}

// ActiveUserScope adds active user filter
func ActiveUserScope() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("status = ?", "active")
    }
}

// Repository methods using scopes
func (r *userRepository) GetActiveUsersInWebsite(websiteID uint, limit, offset int) ([]*User, error) {
    var users []*User
    err := r.db.
        Scopes(TenantScope(websiteID), ActiveUserScope()).
        Limit(limit).
        Offset(offset).
        Find(&users).Error
    return users, err
}

func (r *userRepository) GetUserByEmailInWebsite(websiteID uint, email string) (*User, error) {
    var user User
    err := r.db.
        Scopes(TenantScope(websiteID)).
        Where("email = ?", email).
        First(&user).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, ErrUserNotFound
        }
        return nil, err
    }
    
    return &user, nil
}

func (r *sessionRepository) GetUserSessionsInWebsite(websiteID, userID uint) ([]*RefreshToken, error) {
    var sessions []*RefreshToken
    err := r.db.
        Scopes(TenantScope(websiteID)).
        Where("user_id = ? AND is_revoked = ?", userID, false).
        Order("last_used_at DESC").
        Find(&sessions).Error
    return sessions, err
}
```

### Security Event Logging với Tenant Context

#### Tenant-Aware Security Events
```go
func (ss *SecurityService) LogSecurityEvent(websiteID, userID uint, eventType string, metadata map[string]interface{}) error {
    event := &SecurityEvent{
        WebsiteID:   websiteID,
        UserID:      userID,
        EventType:   eventType,
        Description: ss.getEventDescription(eventType),
        RiskScore:   ss.calculateRiskScore(eventType, metadata),
        Status:      "normal",
        Metadata:    metadata,
        CreatedAt:   time.Now(),
    }
    
    // Add request context if available
    if ipAddress, exists := metadata["ip_address"].(string); exists {
        event.IPAddress = ipAddress
    }
    if userAgent, exists := metadata["user_agent"].(string); exists {
        event.UserAgent = userAgent
    }
    
    // Log to database
    if err := ss.eventRepo.Create(event); err != nil {
        return err
    }
    
    // Cache recent events for quick access
    cacheKey := fmt.Sprintf("website:%d:security:%d:recent_events", websiteID, userID)
    ss.cacheRecentEvent(cacheKey, event)
    
    // Check for suspicious patterns within this tenant
    return ss.analyzeSecurityPattern(websiteID, userID, event)
}

func (ss *SecurityService) GetUserSecurityEventsInWebsite(websiteID, userID uint, limit int) ([]*SecurityEvent, error) {
    var events []*SecurityEvent
    err := ss.eventRepo.db.
        Scopes(TenantScope(websiteID)).
        Where("user_id = ?", userID).
        Order("created_at DESC").
        Limit(limit).
        Find(&events).Error
    return events, err
}
```

### API Route Organization

#### Tenant-Scoped Routes
```go
func SetupAuthRoutes(r *gin.Engine) {
    // Public routes (no authentication required)
    public := r.Group("/api/cms/v1")
    public.Use(TenantContextMiddleware())
    {
        public.POST("/auth/register", authController.Register)
        public.POST("/auth/login", authController.Login)
        public.POST("/auth/forgot-password", authController.ForgotPassword)
        public.POST("/auth/reset-password", authController.ResetPassword)
        public.GET("/auth/oauth/:provider/redirect", authController.OAuthRedirect)
        public.GET("/auth/oauth/:provider/callback", authController.OAuthCallback)
    }
    
    // Protected routes (authentication required)
    protected := r.Group("/api/cms/v1")
    protected.Use(TenantContextMiddleware())
    protected.Use(AuthenticatedTenantMiddleware())
    {
        protected.POST("/auth/logout", authController.Logout)
        protected.POST("/auth/refresh", authController.RefreshToken)
        protected.POST("/auth/change-password", authController.ChangePassword)
        
        // Session management
        protected.GET("/auth/sessions", authController.GetSessions)
        protected.DELETE("/auth/sessions/:sessionId", authController.TerminateSession)
        protected.POST("/auth/sessions/terminate-all", authController.TerminateAllSessions)
        
        // MFA management
        protected.POST("/auth/mfa/enable", authController.EnableMFA)
        protected.POST("/auth/mfa/confirm", authController.ConfirmMFA)
        protected.POST("/auth/mfa/disable", authController.DisableMFA)
        protected.POST("/auth/mfa/backup-codes/regenerate", authController.RegenerateBackupCodes)
        
        // Security
        protected.GET("/auth/security-log", authController.GetSecurityLog)
        protected.POST("/auth/security/report", authController.ReportSuspiciousActivity)
    }
}
```

### Error Handling và Tenant Validation

#### Tenant-Specific Errors
```go
var (
    ErrWebsiteNotFound         = errors.New("website not found")
    ErrWebsiteInactive         = errors.New("website is not active")
    ErrCrossTenantAccess       = errors.New("cross-tenant access denied")
    ErrUserNotFoundInWebsite   = errors.New("user not found in website")
    ErrWebsiteUserLimitExceeded = errors.New("website user limit exceeded")
    ErrInvalidTokenAudience    = errors.New("invalid token audience")
    ErrCacheCorruption         = errors.New("cache data corruption detected")
)

func HandleTenantError(c *gin.Context, err error) {
    switch {
    case errors.Is(err, ErrWebsiteNotFound):
        c.JSON(404, gin.H{
            "error": "website_not_found",
            "message": "The specified website does not exist",
        })
    case errors.Is(err, ErrWebsiteInactive):
        c.JSON(403, gin.H{
            "error": "website_inactive",
            "message": "The specified website is not active",
        })
    case errors.Is(err, ErrCrossTenantAccess):
        c.JSON(403, gin.H{
            "error": "cross_tenant_access_denied",
            "message": "Cannot access resources from different websites",
        })
    case errors.Is(err, ErrUserNotFoundInWebsite):
        c.JSON(404, gin.H{
            "error": "user_not_found_in_website",
            "message": "User not found in the specified website",
        })
    case errors.Is(err, ErrWebsiteUserLimitExceeded):
        c.JSON(403, gin.H{
            "error": "user_limit_exceeded",
            "message": "Website has reached its user limit",
        })
    default:
        c.JSON(500, gin.H{
            "error": "internal_error",
            "message": "An internal error occurred",
        })
    }
}
```

## Best Practices for Tenant Isolation

### Security Principles
1. **Always Include Website ID**: Every database query must include website_id filter
2. **Validate Token Context**: Always verify token's website_id matches request context
3. **Scope Cache Keys**: Prefix all cache keys with website_id
4. **Isolate Sessions**: Sessions are scoped to specific websites
5. **Cross-Tenant Prevention**: Explicitly check and prevent cross-tenant access

### Performance Optimization
1. **Database Indexing**: Create composite indexes with website_id as first column
2. **Cache Partitioning**: Separate cache namespaces by website
3. **Connection Pooling**: Consider separate pools for high-traffic tenants
4. **Query Optimization**: Use GORM scopes for consistent tenant filtering

### Monitoring và Auditing
1. **Access Logging**: Log all cross-tenant access attempts
2. **Performance Metrics**: Track per-tenant performance metrics
3. **Security Monitoring**: Monitor for tenant isolation violations
4. **Compliance Auditing**: Maintain audit trails per tenant