# API Endpoints

## Authentication Endpoints

### Register User
```http
POST /api/cms/v1/auth/register
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "website_id": 1
}
```

**Response:**
```json
{
  "status": {
    "code": 201,
    "message": "User registered successfully",
    "success": true,
    "path": "/api/cms/v1/auth/register",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "user": {
      "id": 123,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "user",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "def50200abcd...",
      "token_type": "Bearer",
      "expires_in": 3600
    }
  }
}
```

### Login User
```http
POST /api/cms/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "remember_me": true,
  "device_info": {
    "device_name": "iPhone 13",
    "device_type": "mobile",
    "os": "iOS 15.0",
    "browser": "Safari"
  }
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Login successful",
    "success": true,
    "path": "/api/cms/v1/auth/login",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "last_login_at": "2024-01-15T10:30:00Z"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "def50200abcd...",
      "token_type": "Bearer",
      "expires_in": 3600
    }
  }
}
```

### Logout User
```http
POST /api/cms/v1/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "all_devices": false
}
```

### Refresh Token
```http
POST /api/cms/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "def50200abcd..."
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Token refreshed successfully",
    "success": true,
    "path": "/api/cms/v1/auth/refresh",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "def50200abcd...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

## Password Management

### Change Password
```http
POST /api/cms/v1/auth/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "current_password": "SecurePass123!",
  "new_password": "NewSecurePass456!",
  "new_password_confirmation": "NewSecurePass456!"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Password changed successfully",
    "success": true,
    "path": "/api/cms/v1/auth/change-password",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "Password has been updated. All other sessions have been terminated."
  }
}
```

### Forgot Password
```http
POST /api/cms/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Password reset email sent",
    "success": true,
    "path": "/api/cms/v1/auth/forgot-password",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "If an account with that email exists, we've sent password reset instructions."
  }
}
```

### Reset Password
```http
POST /api/cms/v1/auth/reset-password
Content-Type: application/json

{
  "token": "reset_token_xyz789",
  "password": "NewSecurePass456!",
  "password_confirmation": "NewSecurePass456!"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Password reset successful",
    "success": true,
    "path": "/api/cms/v1/auth/reset-password",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "Password has been reset successfully. Please log in with your new password."
  }
}
```

## Session Management

### List Sessions
```http
GET /api/cms/v1/auth/sessions
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Sessions retrieved successfully",
    "success": true,
    "path": "/api/cms/v1/auth/sessions",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": [
    {
      "id": "session_123",
      "device": {
        "name": "iPhone 13",
        "type": "mobile",
        "os": "iOS 15.0",
        "browser": "Safari"
      },
      "location": {
        "country": "Vietnam",
        "city": "Ho Chi Minh City",
        "ip": "***********"
      },
      "last_active": "2024-01-15T10:25:00Z",
      "created_at": "2024-01-15T09:00:00Z",
      "is_current": true
    },
    {
      "id": "session_456",
      "device": {
        "name": "MacBook Pro",
        "type": "desktop",
        "os": "macOS 12.0",
        "browser": "Chrome"
      },
      "location": {
        "country": "Vietnam",
        "city": "Hanoi",
        "ip": "***********"
      },
      "last_active": "2024-01-14T15:30:00Z",
      "created_at": "2024-01-14T15:00:00Z",
      "is_current": false
    }
  ]
}
```

### Terminate Session
```http
DELETE /api/cms/v1/auth/sessions/{session_id}
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Session terminated successfully",
    "success": true,
    "path": "/api/cms/v1/auth/sessions/session_456",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "Session has been terminated"
  }
}
```

### Terminate All Sessions
```http
POST /api/cms/v1/auth/sessions/terminate-all
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "All sessions terminated successfully",
    "success": true,
    "path": "/api/cms/v1/auth/sessions/terminate-all",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "All sessions have been terminated",
    "sessions_terminated": 3
  }
}
```

## Multi-Factor Authentication

### Enable MFA
```http
POST /api/cms/v1/auth/mfa/enable
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "method": "totp"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "MFA setup initiated",
    "success": true,
    "path": "/api/cms/v1/auth/mfa/enable",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qr_code_url": "otpauth://totp/Blog%20API%20v3:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Blog%20API%20v3",
    "backup_codes": [
      "12345678",
      "87654321",
      "11223344",
      "44332211",
      "55667788",
      "88776655",
      "********",
      "********"
    ]
  }
}
```

### Confirm MFA Setup
```http
POST /api/cms/v1/auth/mfa/confirm
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "code": "123456"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "MFA enabled successfully",
    "success": true,
    "path": "/api/cms/v1/auth/mfa/confirm",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "message": "Two-factor authentication has been enabled for your account"
  }
}
```

### Verify MFA Code (During Login)
```http
POST /api/cms/v1/auth/mfa/verify
Content-Type: application/json

{
  "mfa_token": "temp_token_abc123",
  "mfa_code": "123456",
  "remember_device": true
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "MFA verification successful",
    "success": true,
    "path": "/api/cms/v1/auth/mfa/verify",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "def50200abcd...",
      "token_type": "Bearer",
      "expires_in": 3600
    }
  }
}
```

### Disable MFA
```http
POST /api/cms/v1/auth/mfa/disable
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "password": "SecurePass123!",
  "mfa_code": "123456"
}
```

### Generate New Backup Codes
```http
POST /api/cms/v1/auth/mfa/backup-codes/regenerate
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "password": "SecurePass123!"
}
```

## Security Features

### Get Security Log
```http
GET /api/cms/v1/auth/security-log?cursor=abc123&limit=20&event_type=login
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Security log retrieved successfully",
    "success": true,
    "path": "/api/cms/v1/auth/security-log",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "events": [
      {
        "id": 1,
        "event_type": "login_success",
        "description": "Successful login",
        "ip_address": "***********",
        "location": {
          "country": "Vietnam",
          "city": "Ho Chi Minh City"
        },
        "device": {
          "name": "iPhone 13",
          "type": "mobile"
        },
        "risk_score": 10,
        "status": "normal",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "has_more": true,
      "next_cursor": "def456",
      "limit": 20
    }
  }
}
```

### Report Suspicious Activity
```http
POST /api/cms/v1/auth/security/report
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "event_type": "suspicious_login",
  "description": "Login from unusual location",
  "metadata": {
    "ip": "***********00",
    "user_agent": "Suspicious Bot"
  }
}
```

### Account Recovery
```http
POST /api/cms/v1/auth/account-recovery
Content-Type: application/json

{
  "email": "<EMAIL>",
  "identity_verification": {
    "type": "security_questions",
    "answers": {
      "question_1": "answer_1",
      "question_2": "answer_2"
    }
  }
}
```

## OAuth Endpoints

### OAuth Redirect
```http
GET /api/cms/v1/auth/oauth/{provider}/redirect?website_id=1&state=csrf_token
```

**Response:**
```http
HTTP/1.1 302 Found
Location: https://accounts.google.com/oauth/authorize?client_id=...&redirect_uri=...&scope=...&state=csrf_token
```

### OAuth Callback
```http
GET /api/cms/v1/auth/oauth/{provider}/callback?code=auth_code&state=csrf_token
```

**Response (Redirect):**
```http
HTTP/1.1 302 Found
Location: https://yourapp.com/dashboard?token=eyJhbGciOiJIUzI1NiIs...&refresh_token=def50200abcd...
```

## Error Responses

### Validation Error
```json
{
  "status": {
    "code": 422,
    "message": "Validation failed",
    "success": false,
    "path": "/api/cms/v1/auth/register",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "error": {
    "type": "validation_error",
    "details": {
      "email": ["The email field is required"],
      "password": ["Password must be at least 8 characters"]
    }
  }
}
```

### Authentication Error
```json
{
  "status": {
    "code": 401,
    "message": "Invalid credentials",
    "success": false,
    "path": "/api/cms/v1/auth/login",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "error": {
    "type": "authentication_error",
    "message": "The provided credentials are invalid"
  }
}
```

### Rate Limit Error
```json
{
  "status": {
    "code": 429,
    "message": "Too many requests",
    "success": false,
    "path": "/api/cms/v1/auth/login",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "error": {
    "type": "rate_limit_error",
    "message": "Too many login attempts. Please try again in 5 minutes.",
    "retry_after": 300
  }
}
```

### Account Locked Error
```json
{
  "status": {
    "code": 423,
    "message": "Account locked",
    "success": false,
    "path": "/api/cms/v1/auth/login",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "error": {
    "type": "account_locked",
    "message": "Account is temporarily locked due to multiple failed login attempts",
    "unlock_at": "2024-01-15T11:00:00Z"
  }
}
```

## Request Headers

### Required Headers
```http
Content-Type: application/json
X-Website-ID: 1
User-Agent: MyApp/1.0
X-Request-ID: unique-request-id
```

### Optional Headers
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Device-ID: device-fingerprint-hash
X-Real-IP: ***********
Accept-Language: en-US,en;q=0.9
```

## Rate Limiting Headers

All responses include rate limiting information:

```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: **********
X-RateLimit-Window: 300
```