# Implementation Details

## Service Layer Architecture

### Auth Service Structure
```go
type AuthService struct {
    userRepo         UserRepository
    sessionRepo      SessionRepository
    securityRepo     SecurityRepository
    passwordService  PasswordService
    tokenService     TokenService
    mfaService       MFAService
    emailService     EmailService
    cacheService     CacheService
    eventPublisher   EventPublisher
    config          *AuthConfig
}

func NewAuthService(deps AuthServiceDependencies) *AuthService {
    return &AuthService{
        userRepo:        deps.UserRepo,
        sessionRepo:     deps.SessionRepo,
        securityRepo:    deps.SecurityRepo,
        passwordService: deps.PasswordService,
        tokenService:    deps.TokenService,
        mfaService:      deps.MFAService,
        emailService:    deps.EmailService,
        cacheService:    deps.CacheService,
        eventPublisher:  deps.EventPublisher,
        config:          deps.Config,
    }
}
```

### Repository Pattern Implementation

#### UserRepository Interface
```go
type UserRepository interface {
    // CRUD operations
    Create(user *User) error
    GetByID(websiteID, userID uint) (*User, error)
    GetByEmail(websiteID uint, email string) (*User, error)
    Update(user *User) error
    Delete(websiteID, userID uint) error
    
    // Security operations
    UpdateFailedAttempts(userID uint, attempts int) error
    LockAccount(userID uint, until time.Time) error
    UnlockAccount(userID uint) error
    UpdateLastLogin(userID uint) error
    
    // Query operations
    List(websiteID uint, filters UserFilters) ([]*User, error)
    Count(websiteID uint, filters UserFilters) (int64, error)
    
    // Tenant operations
    GetWebsiteUserCount(websiteID uint) (int64, error)
    CheckEmailExists(websiteID uint, email string) (bool, error)
}
```

#### UserRepository Implementation
```go
type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{db: db}
}

func (r *userRepository) Create(user *User) error {
    return r.db.Create(user).Error
}

func (r *userRepository) GetByEmail(websiteID uint, email string) (*User, error) {
    var user User
    err := r.db.Where("website_id = ? AND email = ?", websiteID, email).
        First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, ErrUserNotFound
        }
        return nil, err
    }
    return &user, nil
}

func (r *userRepository) UpdateFailedAttempts(userID uint, attempts int) error {
    return r.db.Model(&User{}).
        Where("id = ?", userID).
        Update("failed_login_attempts", attempts).Error
}

func (r *userRepository) LockAccount(userID uint, until time.Time) error {
    return r.db.Model(&User{}).
        Where("id = ?", userID).
        Updates(map[string]interface{}{
            "locked_until": until,
            "status":       "locked",
        }).Error
}
```

### Password Service Implementation

#### Password Manager
```go
type PasswordService struct {
    config     PasswordConfig
    historyRepo PasswordHistoryRepository
}

type PasswordConfig struct {
    MinLength      int    `yaml:"min_length"`
    RequireUpper   bool   `yaml:"require_uppercase"`
    RequireLower   bool   `yaml:"require_lowercase"`
    RequireNumbers bool   `yaml:"require_numbers"`
    RequireSpecial bool   `yaml:"require_special"`
    HistoryLimit   int    `yaml:"history_limit"`
    Cost           int    `yaml:"bcrypt_cost"`
}

func (ps *PasswordService) ValidatePassword(password string, userID uint) error {
    // Length check
    if len(password) < ps.config.MinLength {
        return ErrPasswordTooShort
    }
    
    // Character requirements
    var hasUpper, hasLower, hasNumber, hasSpecial bool
    for _, char := range password {
        switch {
        case unicode.IsUpper(char):
            hasUpper = true
        case unicode.IsLower(char):
            hasLower = true
        case unicode.IsNumber(char):
            hasNumber = true
        case unicode.IsPunct(char) || unicode.IsSymbol(char):
            hasSpecial = true
        }
    }
    
    if ps.config.RequireUpper && !hasUpper {
        return ErrPasswordNeedsUpper
    }
    if ps.config.RequireLower && !hasLower {
        return ErrPasswordNeedsLower
    }
    if ps.config.RequireNumbers && !hasNumber {
        return ErrPasswordNeedsNumber
    }
    if ps.config.RequireSpecial && !hasSpecial {
        return ErrPasswordNeedsSpecial
    }
    
    // Check against password history
    if userID > 0 {
        isReused, err := ps.CheckPasswordHistory(userID, password)
        if err != nil {
            return err
        }
        if isReused {
            return ErrPasswordRecentlyUsed
        }
    }
    
    return nil
}

func (ps *PasswordService) HashPassword(password string) (string, error) {
    hash, err := bcrypt.GenerateFromPassword([]byte(password), ps.config.Cost)
    return string(hash), err
}

func (ps *PasswordService) CheckPasswordHistory(userID uint, password string) (bool, error) {
    history, err := ps.historyRepo.GetRecentPasswords(userID, ps.config.HistoryLimit)
    if err != nil {
        return false, err
    }
    
    for _, oldPassword := range history {
        if ps.VerifyPassword(password, oldPassword.Password) {
            return true, nil
        }
    }
    
    return false, nil
}

func (ps *PasswordService) VerifyPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### Token Service Implementation

#### JWT Token Manager
```go
type TokenService struct {
    config TokenConfig
    cache  CacheService
}

type TokenConfig struct {
    Secret           string        `yaml:"secret"`
    AccessTokenTTL   time.Duration `yaml:"access_token_ttl"`
    RefreshTokenTTL  time.Duration `yaml:"refresh_token_ttl"`
    TokenRotation    bool          `yaml:"token_rotation"`
}

type TokenClaims struct {
    jwt.StandardClaims
    UserID    uint   `json:"user_id"`
    WebsiteID uint   `json:"website_id"`
    Email     string `json:"email"`
    Role      string `json:"role"`
    SessionID string `json:"session_id"`
}

func (ts *TokenService) GenerateTokenPair(user *User, sessionID string) (*TokenPair, error) {
    // Generate access token
    accessClaims := &TokenClaims{
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(ts.config.AccessTokenTTL).Unix(),
            IssuedAt:  time.Now().Unix(),
            Id:        generateTokenID(),
        },
        UserID:    user.ID,
        WebsiteID: user.WebsiteID,
        Email:     user.Email,
        Role:      user.Role,
        SessionID: sessionID,
    }
    
    accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
    accessTokenString, err := accessToken.SignedString([]byte(ts.config.Secret))
    if err != nil {
        return nil, err
    }
    
    // Generate refresh token
    refreshClaims := &TokenClaims{
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(ts.config.RefreshTokenTTL).Unix(),
            IssuedAt:  time.Now().Unix(),
            Id:        generateTokenID(),
        },
        UserID:    user.ID,
        WebsiteID: user.WebsiteID,
        SessionID: sessionID,
    }
    
    refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
    refreshTokenString, err := refreshToken.SignedString([]byte(ts.config.Secret))
    if err != nil {
        return nil, err
    }
    
    return &TokenPair{
        AccessToken:  accessTokenString,
        RefreshToken: refreshTokenString,
        TokenType:    "Bearer",
        ExpiresIn:    int(ts.config.AccessTokenTTL.Seconds()),
    }, nil
}

func (ts *TokenService) ValidateToken(tokenString string) (*TokenClaims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
        if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
            return nil, ErrInvalidSigningMethod
        }
        return []byte(ts.config.Secret), nil
    })
    
    if err != nil {
        return nil, err
    }
    
    claims, ok := token.Claims.(*TokenClaims)
    if !ok || !token.Valid {
        return nil, ErrInvalidToken
    }
    
    // Check if token is blacklisted
    isBlacklisted, err := ts.cache.IsTokenBlacklisted(claims.Id)
    if err != nil {
        return nil, err
    }
    if isBlacklisted {
        return nil, ErrTokenBlacklisted
    }
    
    return claims, nil
}

func (ts *TokenService) BlacklistToken(tokenID string, expiresAt time.Time) error {
    return ts.cache.BlacklistToken(tokenID, expiresAt)
}
```

### MFA Service Implementation

#### TOTP Manager
```go
type MFAService struct {
    config MFAConfig
    repo   MFARepository
}

type MFAConfig struct {
    Issuer           string `yaml:"issuer"`
    BackupCodesCount int    `yaml:"backup_codes_count"`
    TOTPWindow       int    `yaml:"totp_window"`
}

func (ms *MFAService) GenerateSecret(userID uint, email string) (*MFASetup, error) {
    // Generate 16-byte secret
    secret := make([]byte, 16)
    _, err := rand.Read(secret)
    if err != nil {
        return nil, err
    }
    
    secretBase32 := base32.StdEncoding.EncodeToString(secret)
    
    // Generate QR code URL for TOTP apps
    qrURL := fmt.Sprintf(
        "otpauth://totp/%s:%s?secret=%s&issuer=%s",
        url.QueryEscape(ms.config.Issuer),
        url.QueryEscape(email),
        secretBase32,
        url.QueryEscape(ms.config.Issuer),
    )
    
    // Generate backup codes
    backupCodes := ms.generateBackupCodes(ms.config.BackupCodesCount)
    
    return &MFASetup{
        Secret:      secretBase32,
        QRCodeURL:   qrURL,
        BackupCodes: backupCodes,
    }, nil
}

func (ms *MFAService) VerifyTOTP(secret, code string) bool {
    // Use the configured window for time drift tolerance
    for i := -ms.config.TOTPWindow; i <= ms.config.TOTPWindow; i++ {
        timestamp := time.Now().Add(time.Duration(i) * 30 * time.Second)
        if totp.GenerateCode(secret, timestamp.Unix()) == code {
            return true
        }
    }
    return false
}

func (ms *MFAService) VerifyBackupCode(userID uint, code string) (bool, error) {
    device, err := ms.repo.GetUserMFADevice(userID)
    if err != nil {
        return false, err
    }
    
    var backupCodes []string
    if err := json.Unmarshal(device.BackupCodes, &backupCodes); err != nil {
        return false, err
    }
    
    // Check if code exists and remove it
    for i, backupCode := range backupCodes {
        if backupCode == code {
            // Remove the used backup code
            backupCodes = append(backupCodes[:i], backupCodes[i+1:]...)
            
            // Update the device with remaining codes
            updatedCodes, _ := json.Marshal(backupCodes)
            device.BackupCodes = updatedCodes
            
            return true, ms.repo.UpdateMFADevice(device)
        }
    }
    
    return false, nil
}

func (ms *MFAService) generateBackupCodes(count int) []string {
    codes := make([]string, count)
    for i := 0; i < count; i++ {
        codes[i] = ms.generateRandomCode(8)
    }
    return codes
}

func (ms *MFAService) generateRandomCode(length int) string {
    const charset = "0123456789"
    b := make([]byte, length)
    for i := range b {
        b[i] = charset[rand.Intn(len(charset))]
    }
    return string(b)
}
```

### Security Service Implementation

#### Rate Limiting
```go
type SecurityService struct {
    redis       *redis.Client
    config      SecurityConfig
    eventRepo   SecurityEventRepository
}

type SecurityConfig struct {
    MaxFailedAttempts  int           `yaml:"max_failed_attempts"`
    LockoutDuration    time.Duration `yaml:"lockout_duration"`
    ProgressiveLockout bool          `yaml:"progressive_lockout"`
    RateLimits         map[string]RateLimit `yaml:"rate_limits"`
}

func (ss *SecurityService) CheckRateLimit(key, limitType string) (bool, time.Duration, error) {
    limit, exists := ss.config.RateLimits[limitType]
    if !exists {
        return true, 0, nil // No limit configured
    }
    
    // Sliding window rate limiting with Redis
    now := time.Now()
    window := now.Add(-limit.Window)
    
    pipe := ss.redis.Pipeline()
    
    // Remove expired entries
    pipe.ZRemRangeByScore(key, "0", fmt.Sprintf("%d", window.Unix()))
    
    // Count current requests
    countCmd := pipe.ZCard(key)
    
    // Add current request
    pipe.ZAdd(key, &redis.Z{
        Score:  float64(now.Unix()),
        Member: fmt.Sprintf("%d:%d", now.Unix(), rand.Int63()),
    })
    
    // Set expiration
    pipe.Expire(key, limit.Window+time.Minute)
    
    _, err := pipe.Exec()
    if err != nil {
        return false, 0, err
    }
    
    count := countCmd.Val()
    if count >= int64(limit.Requests) {
        // Calculate retry after duration
        retryAfter := limit.Window - time.Since(window)
        return false, retryAfter, nil
    }
    
    return true, 0, nil
}

func (ss *SecurityService) RecordFailedLogin(userID uint, websiteID uint, ipAddress, userAgent string) error {
    // Get current failed attempts
    user, err := ss.userRepo.GetByID(websiteID, userID)
    if err != nil {
        return err
    }
    
    user.FailedLoginAttempts++
    
    // Check if account should be locked
    if user.FailedLoginAttempts >= ss.config.MaxFailedAttempts {
        lockDuration := ss.calculateLockoutDuration(user.FailedLoginAttempts)
        lockUntil := time.Now().Add(lockDuration)
        user.LockedUntil = &lockUntil
        user.Status = "locked"
        
        // Log security event
        event := &SecurityEvent{
            WebsiteID:   websiteID,
            UserID:      userID,
            EventType:   EventTypeAccountLocked,
            Description: fmt.Sprintf("Account locked after %d failed attempts", user.FailedLoginAttempts),
            IPAddress:   ipAddress,
            UserAgent:   userAgent,
            RiskScore:   RiskScoreHigh,
            Status:      "high",
            Metadata: map[string]interface{}{
                "failed_attempts": user.FailedLoginAttempts,
                "lock_duration":   lockDuration.String(),
            },
        }
        
        if err := ss.eventRepo.Create(event); err != nil {
            log.Printf("Failed to log security event: %v", err)
        }
    }
    
    return ss.userRepo.Update(user)
}

func (ss *SecurityService) calculateLockoutDuration(attempts int) time.Duration {
    if !ss.config.ProgressiveLockout {
        return ss.config.LockoutDuration
    }
    
    // Progressive lockout: 15min, 30min, 1hr, 2hr, 4hr, 8hr, 24hr (max)
    base := ss.config.LockoutDuration
    multiplier := math.Pow(2, float64(attempts-ss.config.MaxFailedAttempts))
    duration := time.Duration(float64(base) * multiplier)
    
    maxDuration := 24 * time.Hour
    if duration > maxDuration {
        return maxDuration
    }
    
    return duration
}
```

### Cache Service Implementation

#### Redis Cache Manager
```go
type CacheService struct {
    redis  *redis.Client
    config CacheConfig
}

type CacheConfig struct {
    UserTTL        time.Duration `yaml:"user_ttl"`
    SessionTTL     time.Duration `yaml:"session_ttl"`
    PermissionTTL  time.Duration `yaml:"permission_ttl"`
    RateLimitTTL   time.Duration `yaml:"rate_limit_ttl"`
}

func (cs *CacheService) GetUser(websiteID, userID uint) (*User, error) {
    key := fmt.Sprintf(UserCacheKey, websiteID, userID)
    
    data, err := cs.redis.Get(key).Result()
    if err != nil {
        if err == redis.Nil {
            return nil, ErrCacheMiss
        }
        return nil, err
    }
    
    var user User
    if err := json.Unmarshal([]byte(data), &user); err != nil {
        return nil, err
    }
    
    return &user, nil
}

func (cs *CacheService) SetUser(user *User) error {
    key := fmt.Sprintf(UserCacheKey, user.WebsiteID, user.ID)
    
    data, err := json.Marshal(user)
    if err != nil {
        return err
    }
    
    return cs.redis.Set(key, data, cs.config.UserTTL).Err()
}

func (cs *CacheService) InvalidateUser(websiteID, userID uint) error {
    keys := []string{
        fmt.Sprintf(UserCacheKey, websiteID, userID),
        fmt.Sprintf(UserRolesCacheKey, websiteID, userID),
        fmt.Sprintf(UserPermissionsCacheKey, websiteID, userID),
    }
    
    return cs.redis.Del(keys...).Err()
}

func (cs *CacheService) IsTokenBlacklisted(tokenID string) (bool, error) {
    key := fmt.Sprintf("blacklist:token:%s", tokenID)
    
    exists, err := cs.redis.Exists(key).Result()
    if err != nil {
        return false, err
    }
    
    return exists > 0, nil
}

func (cs *CacheService) BlacklistToken(tokenID string, expiresAt time.Time) error {
    key := fmt.Sprintf("blacklist:token:%s", tokenID)
    ttl := time.Until(expiresAt)
    
    if ttl <= 0 {
        return nil // Token already expired
    }
    
    return cs.redis.Set(key, "1", ttl).Err()
}
```

### Event Publishing Implementation

#### Event Publisher
```go
type EventPublisher struct {
    publisher MessageQueue
    config    EventConfig
}

type EventConfig struct {
    Enabled     bool   `yaml:"enabled"`
    TopicPrefix string `yaml:"topic_prefix"`
}

func (ep *EventPublisher) PublishUserRegistered(user *User) error {
    if !ep.config.Enabled {
        return nil
    }
    
    event := &UserRegisteredEvent{
        EventType:   "user.registered",
        UserID:      user.ID,
        WebsiteID:   user.WebsiteID,
        Email:       user.Email,
        Role:        user.Role,
        Source:      "auth_module",
        OccurredAt:  time.Now(),
    }
    
    topic := fmt.Sprintf("%s.user.registered", ep.config.TopicPrefix)
    return ep.publisher.Publish(topic, event)
}

func (ep *EventPublisher) PublishUserLoggedIn(user *User, sessionInfo *SessionInfo) error {
    if !ep.config.Enabled {
        return nil
    }
    
    event := &UserLoggedInEvent{
        EventType:   "user.logged_in",
        UserID:      user.ID,
        WebsiteID:   user.WebsiteID,
        SessionID:   sessionInfo.SessionID,
        IPAddress:   sessionInfo.IPAddress,
        UserAgent:   sessionInfo.UserAgent,
        OccurredAt:  time.Now(),
    }
    
    topic := fmt.Sprintf("%s.user.logged_in", ep.config.TopicPrefix)
    return ep.publisher.Publish(topic, event)
}
```

### Testing Implementation

#### Unit Tests
```go
func TestAuthService_Login_Success(t *testing.T) {
    // Setup mocks
    mockUserRepo := new(MockUserRepository)
    mockSessionRepo := new(MockSessionRepository)
    mockTokenService := new(MockTokenService)
    mockSecurityService := new(MockSecurityService)
    mockEventPublisher := new(MockEventPublisher)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:        mockUserRepo,
        SessionRepo:     mockSessionRepo,
        TokenService:    mockTokenService,
        SecurityService: mockSecurityService,
        EventPublisher:  mockEventPublisher,
    })
    
    // Setup test data
    user := &User{
        ID:        1,
        WebsiteID: 1,
        Email:     "<EMAIL>",
        Password:  hashPassword("password123"),
        Status:    "active",
    }
    
    request := &LoginRequest{
        Email:    "<EMAIL>",
        Password: "password123",
        DeviceInfo: &DeviceInfo{
            DeviceName: "Test Device",
            DeviceType: "mobile",
        },
    }
    
    // Setup expectations
    mockUserRepo.On("GetByEmail", uint(1), "<EMAIL>").Return(user, nil)
    mockSecurityService.On("CheckRateLimit", mock.Anything, "login").Return(true, time.Duration(0), nil)
    mockSessionRepo.On("Create", mock.AnythingOfType("*RefreshToken")).Return(nil)
    mockTokenService.On("GenerateTokenPair", user, mock.Anything).Return(&TokenPair{
        AccessToken:  "access_token",
        RefreshToken: "refresh_token",
        TokenType:    "Bearer",
        ExpiresIn:    3600,
    }, nil)
    mockUserRepo.On("UpdateLastLogin", uint(1)).Return(nil)
    mockEventPublisher.On("PublishUserLoggedIn", user, mock.Anything).Return(nil)
    
    // Execute
    response, err := service.Login(1, request)
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, response)
    assert.Equal(t, user.Email, response.User.Email)
    assert.Equal(t, "access_token", response.AccessToken)
    assert.Equal(t, "refresh_token", response.RefreshToken)
    
    // Verify all expectations
    mockUserRepo.AssertExpectations(t)
    mockSessionRepo.AssertExpectations(t)
    mockTokenService.AssertExpectations(t)
    mockSecurityService.AssertExpectations(t)
    mockEventPublisher.AssertExpectations(t)
}
```

#### Integration Tests
```go
func TestAuth_LoginFlow_Integration(t *testing.T) {
    // Setup test database and services
    db := setupTestDB(t)
    redis := setupTestRedis(t)
    
    userRepo := NewUserRepository(db)
    sessionRepo := NewSessionRepository(db)
    tokenService := NewTokenService(testTokenConfig)
    securityService := NewSecurityService(redis, testSecurityConfig)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:        userRepo,
        SessionRepo:     sessionRepo,
        TokenService:    tokenService,
        SecurityService: securityService,
    })
    
    // Create test user
    user := &User{
        WebsiteID: 1,
        Email:     "<EMAIL>",
        Password:  "password123",
        Name:      "Integration Test",
        Status:    "active",
        Role:      "user",
    }
    
    err := userRepo.Create(user)
    require.NoError(t, err)
    
    // Test login
    request := &LoginRequest{
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    response, err := service.Login(1, request)
    
    // Assertions
    assert.NoError(t, err)
    assert.NotNil(t, response)
    assert.Equal(t, user.Email, response.User.Email)
    assert.NotEmpty(t, response.AccessToken)
    assert.NotEmpty(t, response.RefreshToken)
    
    // Verify session was created
    sessions, err := sessionRepo.GetUserSessions(1, user.ID)
    assert.NoError(t, err)
    assert.Len(t, sessions, 1)
    
    // Verify token is valid
    claims, err := tokenService.ValidateToken(response.AccessToken)
    assert.NoError(t, err)
    assert.Equal(t, user.ID, claims.UserID)
    assert.Equal(t, user.WebsiteID, claims.WebsiteID)
    
    // Cleanup
    cleanupTestData(t, db, redis)
}
```

## Configuration Management

### Environment Configuration
```yaml
# config/auth.yaml
auth:
  jwt:
    secret: "${JWT_SECRET}"
    access_token_ttl: "1h"
    refresh_token_ttl: "24h"
    token_rotation: true
    
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special: true
    history_limit: 5
    bcrypt_cost: 12
    
  security:
    max_failed_attempts: 5
    lockout_duration: "15m"
    progressive_lockout: true
    
  rate_limiting:
    login:
      requests: 5
      window: "5m"
    register:
      requests: 3
      window: "10m"
    password_reset:
      requests: 3
      window: "1h"
      
  mfa:
    enabled: true
    issuer: "Blog API v3"
    backup_codes_count: 8
    totp_window: 1
    
  cache:
    user_ttl: "1h"
    session_ttl: "7d"
    permission_ttl: "30m"
    rate_limit_ttl: "15m"
    
  events:
    enabled: true
    topic_prefix: "blog_api_v3"
```