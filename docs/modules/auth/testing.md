# Testing Guidelines

## Unit Testing Strategies

### Service Layer Testing

#### Auth Service Unit Tests
```go
package auth

import (
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/require"
)

func TestAuthService_Register_Success(t *testing.T) {
    // Setup mocks
    mockUserRepo := new(MockUserRepository)
    mockWebsiteRepo := new(MockWebsiteRepository)
    mockPasswordService := new(MockPasswordService)
    mockTokenService := new(MockTokenService)
    mockEventPublisher := new(MockEventPublisher)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:        mockUserRepo,
        WebsiteRepo:     mockWebsiteRepo,
        PasswordService: mockPasswordService,
        TokenService:    mockTokenService,
        EventPublisher:  mockEventPublisher,
    })
    
    // Test data
    request := &RegisterRequest{
        Name:                "<PERSON>",
        Email:               "<EMAIL>",
        Password:            "SecurePass123!",
        PasswordConfirmation: "SecurePass123!",
        WebsiteID:          1,
    }
    
    website := &Website{
        ID:       1,
        Status:   "active",
        MaxUsers: 1000,
    }
    
    hashedPassword := "$2a$12$hashedpassword"
    
    user := &User{
        ID:        123,
        Name:      request.Name,
        Email:     request.Email,
        Password:  hashedPassword,
        WebsiteID: request.WebsiteID,
        Status:    "active",
        Role:      "user",
    }
    
    tokenPair := &TokenPair{
        AccessToken:  "access_token_123",
        RefreshToken: "refresh_token_123",
        TokenType:    "Bearer",
        ExpiresIn:    3600,
    }
    
    // Setup expectations
    mockWebsiteRepo.On("GetByID", uint(1)).Return(website, nil)
    mockWebsiteRepo.On("GetUserCount", uint(1)).Return(int64(50), nil)
    mockUserRepo.On("CheckEmailExists", uint(1), "<EMAIL>").Return(false, nil)
    mockPasswordService.On("ValidatePassword", "SecurePass123!", uint(0)).Return(nil)
    mockPasswordService.On("HashPassword", "SecurePass123!").Return(hashedPassword, nil)
    mockUserRepo.On("Create", mock.AnythingOfType("*User")).Return(nil).Run(func(args mock.Arguments) {
        arg := args.Get(0).(*User)
        arg.ID = 123
    })
    mockTokenService.On("GenerateTokenPair", mock.AnythingOfType("*User"), mock.AnythingOfType("string"), website).Return(tokenPair, nil)
    mockEventPublisher.On("PublishUserRegistered", mock.AnythingOfType("*User")).Return(nil)
    
    // Execute
    response, err := service.Register(request)
    
    // Assert
    require.NoError(t, err)
    assert.NotNil(t, response)
    assert.Equal(t, user.ID, response.User.ID)
    assert.Equal(t, user.Email, response.User.Email)
    assert.Equal(t, tokenPair.AccessToken, response.AccessToken)
    assert.Equal(t, tokenPair.RefreshToken, response.RefreshToken)
    
    // Verify all expectations
    mockUserRepo.AssertExpectations(t)
    mockWebsiteRepo.AssertExpectations(t)
    mockPasswordService.AssertExpectations(t)
    mockTokenService.AssertExpectations(t)
    mockEventPublisher.AssertExpectations(t)
}

func TestAuthService_Register_EmailAlreadyExists(t *testing.T) {
    // Setup mocks
    mockUserRepo := new(MockUserRepository)
    mockWebsiteRepo := new(MockWebsiteRepository)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:    mockUserRepo,
        WebsiteRepo: mockWebsiteRepo,
    })
    
    request := &RegisterRequest{
        Name:      "John Doe",
        Email:     "<EMAIL>",
        Password:  "SecurePass123!",
        WebsiteID: 1,
    }
    
    website := &Website{ID: 1, Status: "active", MaxUsers: 1000}
    
    // Setup expectations
    mockWebsiteRepo.On("GetByID", uint(1)).Return(website, nil)
    mockWebsiteRepo.On("GetUserCount", uint(1)).Return(int64(50), nil)
    mockUserRepo.On("CheckEmailExists", uint(1), "<EMAIL>").Return(true, nil)
    
    // Execute
    response, err := service.Register(request)
    
    // Assert
    assert.Error(t, err)
    assert.Nil(t, response)
    assert.Equal(t, ErrEmailAlreadyExists, err)
    
    mockUserRepo.AssertExpectations(t)
    mockWebsiteRepo.AssertExpectations(t)
}

func TestAuthService_Login_Success(t *testing.T) {
    // Setup mocks
    mockUserRepo := new(MockUserRepository)
    mockSessionRepo := new(MockSessionRepository)
    mockPasswordService := new(MockPasswordService)
    mockTokenService := new(MockTokenService)
    mockSecurityService := new(MockSecurityService)
    mockEventPublisher := new(MockEventPublisher)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:        mockUserRepo,
        SessionRepo:     mockSessionRepo,
        PasswordService: mockPasswordService,
        TokenService:    mockTokenService,
        SecurityService: mockSecurityService,
        EventPublisher:  mockEventPublisher,
    })
    
    // Test data
    request := &LoginRequest{
        Email:    "<EMAIL>",
        Password: "SecurePass123!",
        DeviceInfo: &DeviceInfo{
            DeviceName: "iPhone 13",
            DeviceType: "mobile",
            OS:         "iOS 15.0",
            Browser:    "Safari",
        },
    }
    
    user := &User{
        ID:        123,
        Email:     "<EMAIL>",
        Password:  "$2a$12$hashedpassword",
        WebsiteID: 1,
        Status:    "active",
        FailedLoginAttempts: 0,
        LockedUntil: nil,
    }
    
    website := &Website{ID: 1, Status: "active"}
    sessionID := "session_123"
    
    tokenPair := &TokenPair{
        AccessToken:  "access_token_123",
        RefreshToken: "refresh_token_123",
        TokenType:    "Bearer",
        ExpiresIn:    3600,
    }
    
    // Setup expectations
    mockSecurityService.On("CheckRateLimit", mock.Anything, "login").Return(true, time.Duration(0), nil)
    mockUserRepo.On("GetByEmail", uint(1), "<EMAIL>").Return(user, nil)
    mockPasswordService.On("VerifyPassword", "SecurePass123!", user.Password).Return(true)
    mockSessionRepo.On("Create", mock.AnythingOfType("*RefreshToken")).Return(nil).Run(func(args mock.Arguments) {
        session := args.Get(0).(*RefreshToken)
        session.ID = 1
    })
    mockTokenService.On("GenerateTokenPair", user, mock.AnythingOfType("string"), website).Return(tokenPair, nil)
    mockUserRepo.On("UpdateLastLogin", user.ID).Return(nil)
    mockUserRepo.On("ResetFailedAttempts", user.ID).Return(nil)
    mockEventPublisher.On("PublishUserLoggedIn", user, mock.AnythingOfType("*SessionInfo")).Return(nil)
    
    // Execute
    response, err := service.Login(1, request)
    
    // Assert
    require.NoError(t, err)
    assert.NotNil(t, response)
    assert.Equal(t, user.Email, response.User.Email)
    assert.Equal(t, tokenPair.AccessToken, response.AccessToken)
    assert.Equal(t, tokenPair.RefreshToken, response.RefreshToken)
    
    mockUserRepo.AssertExpectations(t)
    mockSessionRepo.AssertExpectations(t)
    mockPasswordService.AssertExpectations(t)
    mockTokenService.AssertExpectations(t)
    mockSecurityService.AssertExpectations(t)
    mockEventPublisher.AssertExpectations(t)
}

func TestAuthService_Login_InvalidPassword(t *testing.T) {
    // Setup mocks
    mockUserRepo := new(MockUserRepository)
    mockPasswordService := new(MockPasswordService)
    mockSecurityService := new(MockSecurityService)
    
    service := NewAuthService(AuthServiceDependencies{
        UserRepo:        mockUserRepo,
        PasswordService: mockPasswordService,
        SecurityService: mockSecurityService,
    })
    
    request := &LoginRequest{
        Email:    "<EMAIL>",
        Password: "wrongpassword",
    }
    
    user := &User{
        ID:       123,
        Email:    "<EMAIL>",
        Password: "$2a$12$hashedpassword",
        Status:   "active",
        FailedLoginAttempts: 2,
    }
    
    // Setup expectations
    mockSecurityService.On("CheckRateLimit", mock.Anything, "login").Return(true, time.Duration(0), nil)
    mockUserRepo.On("GetByEmail", uint(1), "<EMAIL>").Return(user, nil)
    mockPasswordService.On("VerifyPassword", "wrongpassword", user.Password).Return(false)
    mockSecurityService.On("RecordFailedLogin", user.ID, uint(1), mock.Anything, mock.Anything).Return(nil)
    
    // Execute
    response, err := service.Login(1, request)
    
    // Assert
    assert.Error(t, err)
    assert.Nil(t, response)
    assert.Equal(t, ErrInvalidCredentials, err)
    
    mockUserRepo.AssertExpectations(t)
    mockPasswordService.AssertExpectations(t)
    mockSecurityService.AssertExpectations(t)
}
```

### Repository Layer Testing

#### User Repository Tests
```go
func TestUserRepository_Create_Success(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    user := &User{
        Name:      "John Doe",
        Email:     "<EMAIL>",
        Password:  "hashedpassword",
        WebsiteID: 1,
        Status:    "active",
        Role:      "user",
    }
    
    err := repo.Create(user)
    
    assert.NoError(t, err)
    assert.Greater(t, user.ID, uint(0))
    assert.NotZero(t, user.CreatedAt)
    assert.NotZero(t, user.UpdatedAt)
}

func TestUserRepository_GetByEmail_Success(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    // Create test user
    user := &User{
        Name:      "John Doe",
        Email:     "<EMAIL>",
        Password:  "hashedpassword",
        WebsiteID: 1,
        Status:    "active",
        Role:      "user",
    }
    
    err := repo.Create(user)
    require.NoError(t, err)
    
    // Test retrieval
    foundUser, err := repo.GetByEmail(1, "<EMAIL>")
    
    assert.NoError(t, err)
    assert.NotNil(t, foundUser)
    assert.Equal(t, user.ID, foundUser.ID)
    assert.Equal(t, user.Email, foundUser.Email)
    assert.Equal(t, user.WebsiteID, foundUser.WebsiteID)
}

func TestUserRepository_GetByEmail_NotFound(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    user, err := repo.GetByEmail(1, "<EMAIL>")
    
    assert.Error(t, err)
    assert.Nil(t, user)
    assert.Equal(t, ErrUserNotFound, err)
}

func TestUserRepository_TenantIsolation(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    // Create users in different websites
    user1 := &User{
        Name:      "User 1",
        Email:     "<EMAIL>",
        Password:  "hashedpassword",
        WebsiteID: 1,
        Status:    "active",
        Role:      "user",
    }
    
    user2 := &User{
        Name:      "User 2",
        Email:     "<EMAIL>", // Same email, different website
        Password:  "hashedpassword",
        WebsiteID: 2,
        Status:    "active",
        Role:      "user",
    }
    
    err := repo.Create(user1)
    require.NoError(t, err)
    
    err = repo.Create(user2)
    require.NoError(t, err)
    
    // Test tenant isolation
    foundUser1, err := repo.GetByEmail(1, "<EMAIL>")
    assert.NoError(t, err)
    assert.Equal(t, user1.ID, foundUser1.ID)
    assert.Equal(t, uint(1), foundUser1.WebsiteID)
    
    foundUser2, err := repo.GetByEmail(2, "<EMAIL>")
    assert.NoError(t, err)
    assert.Equal(t, user2.ID, foundUser2.ID)
    assert.Equal(t, uint(2), foundUser2.WebsiteID)
    
    // Cross-tenant access should not return user from different website
    notFound, err := repo.GetByEmail(1, "<EMAIL>")
    assert.NoError(t, err)
    assert.NotEqual(t, user2.ID, notFound.ID) // Should find user1, not user2
}
```

### Security Testing

#### Rate Limiting Tests
```go
func TestSecurityService_RateLimit_Success(t *testing.T) {
    redis := setupTestRedis(t)
    defer cleanupTestRedis(t, redis)
    
    config := SecurityConfig{
        RateLimits: map[string]RateLimit{
            "login": {
                Requests: 5,
                Window:   5 * time.Minute,
            },
        },
    }
    
    service := NewSecurityService(redis, config, nil)
    
    key := "test_key"
    
    // Test first 5 requests should pass
    for i := 0; i < 5; i++ {
        allowed, _, err := service.CheckRateLimit(key, "login")
        assert.NoError(t, err)
        assert.True(t, allowed)
    }
    
    // 6th request should be blocked
    allowed, retryAfter, err := service.CheckRateLimit(key, "login")
    assert.NoError(t, err)
    assert.False(t, allowed)
    assert.Greater(t, retryAfter, time.Duration(0))
}

func TestSecurityService_AccountLockout(t *testing.T) {
    redis := setupTestRedis(t)
    defer cleanupTestRedis(t, redis)
    
    mockUserRepo := new(MockUserRepository)
    mockEventRepo := new(MockSecurityEventRepository)
    
    config := SecurityConfig{
        MaxFailedAttempts:  3,
        LockoutDuration:    15 * time.Minute,
        ProgressiveLockout: false,
    }
    
    service := NewSecurityService(redis, config, mockEventRepo)
    service.userRepo = mockUserRepo
    
    user := &User{
        ID:                  123,
        FailedLoginAttempts: 2,
        Status:              "active",
    }
    
    // Setup expectations for account lockout
    mockUserRepo.On("GetByID", uint(1), uint(123)).Return(user, nil)
    mockUserRepo.On("Update", mock.AnythingOfType("*User")).Return(nil).Run(func(args mock.Arguments) {
        updatedUser := args.Get(0).(*User)
        assert.Equal(t, 3, updatedUser.FailedLoginAttempts)
        assert.Equal(t, "locked", updatedUser.Status)
        assert.NotNil(t, updatedUser.LockedUntil)
    })
    mockEventRepo.On("Create", mock.AnythingOfType("*SecurityEvent")).Return(nil)
    
    // Execute
    err := service.RecordFailedLogin(123, 1, "***********", "test-agent")
    
    // Assert
    assert.NoError(t, err)
    mockUserRepo.AssertExpectations(t)
    mockEventRepo.AssertExpectations(t)
}
```

#### Password Security Tests
```go
func TestPasswordService_ValidatePassword(t *testing.T) {
    config := PasswordConfig{
        MinLength:      8,
        RequireUpper:   true,
        RequireLower:   true,
        RequireNumbers: true,
        RequireSpecial: true,
        HistoryLimit:   5,
    }
    
    mockHistoryRepo := new(MockPasswordHistoryRepository)
    service := NewPasswordService(config, mockHistoryRepo)
    
    tests := []struct {
        name     string
        password string
        userID   uint
        setup    func()
        wantErr  error
    }{
        {
            name:     "valid password",
            password: "SecurePass123!",
            userID:   0,
            setup:    func() {},
            wantErr:  nil,
        },
        {
            name:     "too short",
            password: "Short1!",
            userID:   0,
            setup:    func() {},
            wantErr:  ErrPasswordTooShort,
        },
        {
            name:     "no uppercase",
            password: "securepass123!",
            userID:   0,
            setup:    func() {},
            wantErr:  ErrPasswordNeedsUpper,
        },
        {
            name:     "no lowercase",
            password: "SECUREPASS123!",
            userID:   0,
            setup:    func() {},
            wantErr:  ErrPasswordNeedsLower,
        },
        {
            name:     "no numbers",
            password: "SecurePassword!",
            userID:   0,
            setup:    func() {},
            wantErr:  ErrPasswordNeedsNumber,
        },
        {
            name:     "no special characters",
            password: "SecurePass123",
            userID:   0,
            setup:    func() {},
            wantErr:  ErrPasswordNeedsSpecial,
        },
        {
            name:     "recently used password",
            password: "SecurePass123!",
            userID:   123,
            setup: func() {
                mockHistoryRepo.On("GetRecentPasswords", uint(123), 5).Return([]*PasswordHistory{
                    {Password: hashPassword("SecurePass123!")},
                }, nil)
            },
            wantErr: ErrPasswordRecentlyUsed,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mockHistoryRepo.ExpectedCalls = nil // Reset mock
            tt.setup()
            
            err := service.ValidatePassword(tt.password, tt.userID)
            
            if tt.wantErr != nil {
                assert.Error(t, err)
                assert.Equal(t, tt.wantErr, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

### MFA Testing

#### TOTP Tests
```go
func TestMFAService_GenerateSecret(t *testing.T) {
    config := MFAConfig{
        Issuer:           "Blog API v3",
        BackupCodesCount: 8,
        TOTPWindow:       1,
    }
    
    service := NewMFAService(config, nil)
    
    setup, err := service.GenerateSecret(123, "<EMAIL>")
    
    assert.NoError(t, err)
    assert.NotNil(t, setup)
    assert.NotEmpty(t, setup.Secret)
    assert.NotEmpty(t, setup.QRCodeURL)
    assert.Len(t, setup.BackupCodes, 8)
    
    // Verify QR code URL format
    assert.Contains(t, setup.QRCodeURL, "otpauth://totp/")
    assert.Contains(t, setup.QRCodeURL, "Blog%20API%20v3")
    assert.Contains(t, setup.QRCodeURL, "<EMAIL>")
    assert.Contains(t, setup.QRCodeURL, setup.Secret)
}

func TestMFAService_VerifyTOTP(t *testing.T) {
    config := MFAConfig{
        TOTPWindow: 1,
    }
    
    service := NewMFAService(config, nil)
    
    // Generate a test secret
    secret := "JBSWY3DPEHPK3PXP"
    
    // Generate valid TOTP code for current time
    validCode := totp.GenerateCode(secret, time.Now().Unix())
    
    // Test valid code
    isValid := service.VerifyTOTP(secret, validCode)
    assert.True(t, isValid)
    
    // Test invalid code
    isValid = service.VerifyTOTP(secret, "000000")
    assert.False(t, isValid)
}
```

## Integration Testing

### API Integration Tests
```go
func TestAuthAPI_RegisterLogin_Integration(t *testing.T) {
    // Setup test server
    server := setupTestServer(t)
    defer server.Cleanup()
    
    client := server.Client()
    
    // Test registration
    registerReq := RegisterRequest{
        Name:                "Integration Test",
        Email:               "<EMAIL>",
        Password:            "SecurePass123!",
        PasswordConfirmation: "SecurePass123!",
        WebsiteID:          1,
    }
    
    registerResp, err := client.Register(registerReq)
    require.NoError(t, err)
    assert.Equal(t, 201, registerResp.Status.Code)
    assert.NotEmpty(t, registerResp.Data.AccessToken)
    assert.NotEmpty(t, registerResp.Data.RefreshToken)
    
    userID := registerResp.Data.User.ID
    
    // Test login with registered credentials
    loginReq := LoginRequest{
        Email:    "<EMAIL>",
        Password: "SecurePass123!",
    }
    
    loginResp, err := client.Login(loginReq)
    require.NoError(t, err)
    assert.Equal(t, 200, loginResp.Status.Code)
    assert.Equal(t, userID, loginResp.Data.User.ID)
    assert.NotEmpty(t, loginResp.Data.AccessToken)
    
    // Test authenticated request
    sessionsResp, err := client.GetSessions(loginResp.Data.AccessToken)
    require.NoError(t, err)
    assert.Equal(t, 200, sessionsResp.Status.Code)
    assert.Len(t, sessionsResp.Data, 1) // Should have one active session
    
    // Test logout
    logoutResp, err := client.Logout(loginResp.Data.AccessToken)
    require.NoError(t, err)
    assert.Equal(t, 200, logoutResp.Status.Code)
    
    // Test that token is now invalid
    _, err = client.GetSessions(loginResp.Data.AccessToken)
    assert.Error(t, err)
}

func TestAuthAPI_TenantIsolation_Integration(t *testing.T) {
    server := setupTestServer(t)
    defer server.Cleanup()
    
    client := server.Client()
    
    // Register user in website 1
    user1Req := RegisterRequest{
        Name:                "User 1",
        Email:               "<EMAIL>",
        Password:            "SecurePass123!",
        PasswordConfirmation: "SecurePass123!",
        WebsiteID:          1,
    }
    
    user1Resp, err := client.Register(user1Req)
    require.NoError(t, err)
    
    // Register user in website 2 with same email
    user2Req := RegisterRequest{
        Name:                "User 2",
        Email:               "<EMAIL>", // Same email, different website
        Password:            "SecurePass123!",
        PasswordConfirmation: "SecurePass123!",
        WebsiteID:          2,
    }
    
    user2Resp, err := client.Register(user2Req)
    require.NoError(t, err)
    
    // Verify users have different IDs
    assert.NotEqual(t, user1Resp.Data.User.ID, user2Resp.Data.User.ID)
    
    // Test cross-tenant access prevention
    // Try to use user1's token with website 2
    client.SetWebsiteID(2)
    _, err = client.GetSessions(user1Resp.Data.AccessToken)
    assert.Error(t, err)
    
    // Reset website and verify user1's token works with website 1
    client.SetWebsiteID(1)
    sessionsResp, err := client.GetSessions(user1Resp.Data.AccessToken)
    require.NoError(t, err)
    assert.Equal(t, 200, sessionsResp.Status.Code)
}
```

### Database Integration Tests
```go
func TestAuthRepository_DatabaseIntegration(t *testing.T) {
    // Setup real database connection for integration testing
    db := setupIntegrationDB(t)
    defer cleanupIntegrationDB(t, db)
    
    userRepo := NewUserRepository(db)
    sessionRepo := NewSessionRepository(db)
    
    // Test user creation
    user := &User{
        Name:      "DB Integration Test",
        Email:     "<EMAIL>",
        Password:  "hashedpassword",
        WebsiteID: 1,
        Status:    "active",
        Role:      "user",
    }
    
    err := userRepo.Create(user)
    require.NoError(t, err)
    assert.Greater(t, user.ID, uint(0))
    
    // Test session creation
    session := &RefreshToken{
        Token:       "test_refresh_token",
        UserID:      user.ID,
        WebsiteID:   user.WebsiteID,
        DeviceName:  "Test Device",
        DeviceType:  "mobile",
        IPAddress:   "***********",
        ExpiresAt:   time.Now().Add(24 * time.Hour),
        LastUsedAt:  time.Now(),
    }
    
    err = sessionRepo.Create(session)
    require.NoError(t, err)
    assert.Greater(t, session.ID, uint(0))
    
    // Test retrieval with relationships
    foundUser, err := userRepo.GetByID(user.WebsiteID, user.ID)
    require.NoError(t, err)
    assert.Equal(t, user.Email, foundUser.Email)
    
    sessions, err := sessionRepo.GetUserSessions(user.WebsiteID, user.ID)
    require.NoError(t, err)
    assert.Len(t, sessions, 1)
    assert.Equal(t, session.Token, sessions[0].Token)
    
    // Test cascade delete
    err = userRepo.Delete(user.WebsiteID, user.ID)
    require.NoError(t, err)
    
    // Verify user is soft deleted
    _, err = userRepo.GetByID(user.WebsiteID, user.ID)
    assert.Error(t, err)
    assert.Equal(t, ErrUserNotFound, err)
    
    // Verify sessions are also cleaned up
    sessions, err = sessionRepo.GetUserSessions(user.WebsiteID, user.ID)
    require.NoError(t, err)
    assert.Len(t, sessions, 0)
}
```

## Load Testing

### Performance Tests
```go
func TestAuthService_LoginPerformance(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping performance test in short mode")
    }
    
    // Setup
    server := setupTestServer(t)
    defer server.Cleanup()
    
    // Create test users
    users := createTestUsers(t, server, 100)
    
    // Concurrent login test
    concurrency := 50
    iterations := 1000
    
    start := time.Now()
    
    var wg sync.WaitGroup
    results := make(chan error, iterations)
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            
            for j := 0; j < iterations/concurrency; j++ {
                user := users[j%len(users)]
                
                loginReq := LoginRequest{
                    Email:    user.Email,
                    Password: "testpassword",
                }
                
                _, err := server.Client().Login(loginReq)
                results <- err
            }
        }()
    }
    
    wg.Wait()
    close(results)
    
    duration := time.Since(start)
    
    // Analyze results
    var errors int
    for err := range results {
        if err != nil {
            errors++
        }
    }
    
    successRate := float64(iterations-errors) / float64(iterations) * 100
    requestsPerSecond := float64(iterations) / duration.Seconds()
    
    t.Logf("Performance Results:")
    t.Logf("- Duration: %v", duration)
    t.Logf("- Total requests: %d", iterations)
    t.Logf("- Errors: %d", errors)
    t.Logf("- Success rate: %.2f%%", successRate)
    t.Logf("- Requests per second: %.2f", requestsPerSecond)
    
    // Assertions
    assert.GreaterOrEqual(t, successRate, 95.0, "Success rate should be at least 95%")
    assert.GreaterOrEqual(t, requestsPerSecond, 100.0, "Should handle at least 100 requests per second")
}
```

## Test Helpers và Utilities

### Test Setup Helpers
```go
// Test database setup
func setupTestDB(t *testing.T) *gorm.DB {
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    require.NoError(t, err)
    
    // Run migrations
    err = db.AutoMigrate(
        &User{},
        &RefreshToken{},
        &PasswordReset{},
        &SecurityEvent{},
        &MFADevice{},
        &OAuthAccount{},
    )
    require.NoError(t, err)
    
    return db
}

func cleanupTestDB(t *testing.T, db *gorm.DB) {
    sqlDB, err := db.DB()
    require.NoError(t, err)
    sqlDB.Close()
}

// Test Redis setup
func setupTestRedis(t *testing.T) *redis.Client {
    client := redis.NewClient(&redis.Options{
        Addr:     "localhost:6379",
        DB:       15, // Use separate DB for testing
    })
    
    // Clear test database
    err := client.FlushDB().Err()
    require.NoError(t, err)
    
    return client
}

func cleanupTestRedis(t *testing.T, client *redis.Client) {
    client.FlushDB()
    client.Close()
}

// Test server setup
type TestServer struct {
    server   *gin.Engine
    db       *gorm.DB
    redis    *redis.Client
    services *Services
}

func setupTestServer(t *testing.T) *TestServer {
    db := setupTestDB(t)
    redis := setupTestRedis(t)
    
    // Initialize services
    services := initializeTestServices(db, redis)
    
    // Setup routes
    server := gin.New()
    setupAuthRoutes(server, services)
    
    return &TestServer{
        server:   server,
        db:       db,
        redis:    redis,
        services: services,
    }
}

func (ts *TestServer) Client() *TestClient {
    return &TestClient{
        server:    ts.server,
        websiteID: 1, // Default website ID
    }
}

func (ts *TestServer) Cleanup() {
    cleanupTestRedis(nil, ts.redis)
    cleanupTestDB(nil, ts.db)
}

// Test client for API calls
type TestClient struct {
    server    *gin.Engine
    websiteID uint
}

func (tc *TestClient) SetWebsiteID(websiteID uint) {
    tc.websiteID = websiteID
}

func (tc *TestClient) Register(req RegisterRequest) (*RegisterResponse, error) {
    // Implementation for test HTTP client
    // ...
}

func (tc *TestClient) Login(req LoginRequest) (*LoginResponse, error) {
    // Implementation for test HTTP client
    // ...
}
```

### Mock Implementations
```go
// MockUserRepository implements UserRepository interface for testing
type MockUserRepository struct {
    mock.Mock
}

func (m *MockUserRepository) Create(user *User) error {
    args := m.Called(user)
    return args.Error(0)
}

func (m *MockUserRepository) GetByID(websiteID, userID uint) (*User, error) {
    args := m.Called(websiteID, userID)
    return args.Get(0).(*User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(websiteID uint, email string) (*User, error) {
    args := m.Called(websiteID, email)
    if args.Get(0) == nil {
        return nil, args.Error(1)
    }
    return args.Get(0).(*User), args.Error(1)
}

func (m *MockUserRepository) Update(user *User) error {
    args := m.Called(user)
    return args.Error(0)
}

func (m *MockUserRepository) CheckEmailExists(websiteID uint, email string) (bool, error) {
    args := m.Called(websiteID, email)
    return args.Bool(0), args.Error(1)
}

// Additional mock methods...
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Auth Module Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'modules/auth/**'
      - 'pkg/auth/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'modules/auth/**'
      - 'pkg/auth/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: blog_api_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install dependencies
      run: go mod download
    
    - name: Run unit tests
      run: go test -v -race -coverprofile=coverage.out ./modules/auth/... ./pkg/auth/...
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: blog_api_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        JWT_SECRET: test-secret-key
    
    - name: Run integration tests
      run: go test -v -tags=integration ./modules/auth/...
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: blog_api_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        JWT_SECRET: test-secret-key
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: auth-module
```

## Best Practices

### Testing Guidelines
1. **Test Structure**: Follow AAA pattern (Arrange, Act, Assert)
2. **Isolation**: Each test should be independent and isolated
3. **Mocking**: Use mocks for external dependencies
4. **Coverage**: Aim for 80%+ code coverage
5. **Performance**: Include performance tests for critical paths
6. **Security**: Test security features thoroughly
7. **Tenant Isolation**: Always test multi-tenant scenarios