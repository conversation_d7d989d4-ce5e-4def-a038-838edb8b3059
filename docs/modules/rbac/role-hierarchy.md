# Role Hierarchy và Definitions

## Phân cấp vai trò

### Hierarchy Structure

```mermaid
graph TD
    A[Super Admin] --> B[Tenant Admin]
    B --> C[Content Manager]
    B --> D[User Manager]
    C --> E[Editor]
    C --> F[Author]
    D --> G[Moderator]
    E --> H[Contributor]
    F --> H
    G --> I[Member]
    H --> I
    I --> J[Guest]
```

### Role Definitions

#### Super Admin
- **Level**: 0 (Highest)
- **Scope**: Global
- **Description**: Quản lý toàn bộ hệ thống
- **Permissions**:
  - Tạo và quản lý tenant
  - C<PERSON>u hình hệ thống global
  - Truy cập tất cả tài nguyên
  - Quản lý system-level settings

**Use Cases**:
- Platform administration
- System maintenance
- Global configuration
- Emergency access

#### Tenant Admin  
- **Level**: 1
- **Scope**: Tenant-wide
- **Description**: Quản lý tenant của mình
- **Permissions**:
  - Cấu hình tenant settings
  - Quản lý users trong tenant
  - Tạo và quản lý websites
  - Assign roles trong tenant

**Use Cases**:
- Organization management
- Tenant configuration
- User provisioning
- Website management

#### Content Manager
- **Level**: 2
- **Scope**: Tenant/Website
- **Description**: Quản lý toàn bộ nội dung
- **Permissions**:
  - Approve/reject posts
  - Quản lý categories và tags
  - Content workflow management
  - Publishing oversight

**Use Cases**:
- Editorial oversight
- Content strategy
- Publishing workflow
- Content quality control

#### User Manager
- **Level**: 2
- **Scope**: Tenant/Website
- **Description**: Quản lý người dùng và quyền hạn
- **Permissions**:
  - Manage user accounts
  - Assign/revoke roles
  - User onboarding
  - Access control management

**Use Cases**:
- User administration
- Role assignment
- Access management
- User support

#### Editor
- **Level**: 3
- **Scope**: Website
- **Description**: Chỉnh sửa tất cả bài viết
- **Permissions**:
  - Chỉnh sửa tất cả bài viết
  - Publish/unpublish posts
  - Quản lý comments
  - Content moderation

**Use Cases**:
- Content editing
- Publication management
- Quality assurance
- Content review

#### Author
- **Level**: 4
- **Scope**: Website
- **Description**: Tạo và quản lý bài viết của mình
- **Permissions**:
  - Tạo bài viết mới
  - Chỉnh sửa bài viết của mình
  - Submit for review
  - Trả lời comments

**Use Cases**:
- Content creation
- Article writing
- Draft management
- Reader engagement

#### Moderator
- **Level**: 4
- **Scope**: Website
- **Description**: Kiểm duyệt nội dung và tương tác
- **Permissions**:
  - Quản lý comments
  - Moderate user-generated content
  - Handle reports
  - Community management

**Use Cases**:
- Comment moderation
- Community oversight
- Content flagging
- User behavior management

#### Contributor
- **Level**: 5
- **Scope**: Website
- **Description**: Đóng góp nội dung với quyền hạn hạn chế
- **Permissions**:
  - Create draft posts
  - Submit for review
  - Basic interaction
  - Limited editing

**Use Cases**:
- Guest posting
- Content contribution
- Draft creation
- Collaborative writing

#### Member
- **Level**: 6
- **Scope**: Website
- **Description**: Thành viên thông thường
- **Permissions**:
  - Đọc nội dung
  - Comment trên bài viết
  - Like và share
  - Personal profile management

**Use Cases**:
- Content consumption
- Community participation
- Social interaction
- Profile management

#### Guest
- **Level**: 7 (Lowest)
- **Scope**: Public
- **Description**: Khách vãng lai
- **Permissions**:
  - Chỉ đọc nội dung public
  - Không thể tương tác
  - Limited access

**Use Cases**:
- Anonymous browsing
- Public content access
- No interaction capabilities

## Role Inheritance Rules

### Inheritance Mechanism

```mermaid
flowchart TD
    A[Parent Role Permissions] --> B[Child Role Base Permissions]
    B --> C[Child Role Additional Permissions]
    C --> D[Final Permission Set]
    
    E[Permission Conflicts] --> F{Resolution Strategy}
    F -->|Explicit Deny| G[DENY]
    F -->|Explicit Allow| H[ALLOW]
    F -->|Not Specified| I[Inherit from Parent]
```

### Inheritance Examples

#### Editor inherits from Content Manager
```yaml
content_manager:
  permissions:
    - posts.approve
    - posts.reject
    - categories.manage
    - workflow.manage

editor:
  inherits_from: content_manager
  additional_permissions:
    - posts.edit.all
    - comments.moderate
  restrictions:
    - posts.approve: false  # Override inheritance
```

#### Author inherits from Editor
```yaml
editor:
  permissions:
    - posts.edit.all
    - posts.publish
    - comments.moderate

author:
  inherits_from: editor
  permission_scope: own_only  # Restrict inherited permissions
  additional_permissions:
    - posts.create
  restrictions:
    - posts.edit.all: posts.edit.own
    - posts.publish: false
```

## Role Assignment Rules

### Assignment Constraints

```mermaid
flowchart TD
    A[Role Assignment Request] --> B[Check Assigner Permission]
    B --> C{Can assign this role?}
    C -->|No| D[Deny Assignment]
    C -->|Yes| E[Check Hierarchy Rules]
    E --> F{Valid hierarchy?}
    F -->|No| G[Invalid Assignment]
    F -->|Yes| H[Check Context]
    H --> I{Valid context?}
    I -->|No| J[Context Mismatch]
    I -->|Yes| K[Create Assignment]
```

### Assignment Rules

1. **Hierarchy Rule**: User can only assign roles at or below their level
2. **Context Rule**: Assignment must match tenant/website context
3. **Permission Rule**: Assigner must have user management permission
4. **Conflict Rule**: Check for conflicting role assignments

### Assignment Examples

```go
// Valid assignment: Tenant Admin assigns Editor role
func (s *RoleService) AssignRole(assignerID, userID, roleID uint, context AssignmentContext) error {
    assigner := s.GetUserHighestRole(assignerID, context.TenantID, context.WebsiteID)
    targetRole := s.GetRole(roleID)
    
    // Check hierarchy
    if assigner.Level >= targetRole.Level {
        return ErrInsufficientLevel
    }
    
    // Check permission
    if !s.HasPermission(assignerID, "users.assign_roles", context) {
        return ErrNoPermission
    }
    
    // Valid assignment
    return s.CreateUserRole(userID, roleID, context)
}
```

## Role Context và Scoping

### Context Types

#### Global Context
```yaml
context: global
scope: all_tenants
description: System-wide roles (Super Admin only)
restrictions:
  - requires_system_permission: true
  - tenant_id: null
  - website_id: null
```

#### Tenant Context
```yaml
context: tenant
scope: single_tenant
description: Tenant-wide roles
restrictions:
  - tenant_id: required
  - website_id: null
  - inherits_to_websites: true
```

#### Website Context
```yaml
context: website
scope: single_website
description: Website-specific roles
restrictions:
  - tenant_id: required
  - website_id: required
  - website_isolation: true
```

### Role Scoping Logic

```mermaid
flowchart LR
    A[User Request] --> B[Extract Context]
    B --> C[Get User Roles]
    C --> D[Filter by Context]
    D --> E[Apply Scope Rules]
    E --> F[Return Applicable Roles]
    
    subgraph "Scope Priority"
        G[Website Specific] --> H[Tenant Wide] --> I[Global]
    end
```

## Dynamic Role Features

### Temporary Role Assignment

```yaml
temporary_assignment:
  user_id: 123
  role_id: 456
  expires_at: "2024-12-31T23:59:59Z"
  auto_revoke: true
  notification:
    - before_expiry: "24h"
    - on_expiry: true
```

### Conditional Role Activation

```yaml
conditional_role:
  role_id: 789
  conditions:
    time_based:
      start_time: "09:00"
      end_time: "17:00"
      timezone: "Asia/Ho_Chi_Minh"
    location_based:
      allowed_ips: ["***********/24"]
    device_based:
      require_mfa: true
```

### Role Elevation

```yaml
role_elevation:
  base_role: "author"
  elevated_role: "editor"
  duration: "2h"
  approval_required: true
  justification: "Emergency content update"
  auto_demote: true
```

## Role Management API

### Role CRUD Operations

```go
type RoleService interface {
    CreateRole(role *Role) error
    GetRole(id uint) (*Role, error)
    UpdateRole(id uint, updates *Role) error
    DeleteRole(id uint) error
    
    ListRolesByContext(tenantID, websiteID uint) ([]Role, error)
    GetRoleHierarchy(rootID uint) (*RoleTree, error)
    
    AssignUserRole(userID, roleID uint, context AssignmentContext) error
    RevokeUserRole(userID, roleID uint, context AssignmentContext) error
    
    GetUserRoles(userID uint, context QueryContext) ([]Role, error)
    GetEffectivePermissions(userID uint, context QueryContext) ([]Permission, error)
}
```

### Role Validation

```go
func (s *RoleService) ValidateRoleAssignment(assignerID, userID, roleID uint, context AssignmentContext) error {
    // Check assigner permissions
    if !s.CanAssignRole(assignerID, roleID, context) {
        return ErrInsufficientPermissions
    }
    
    // Check hierarchy constraints
    if !s.IsValidHierarchy(assignerID, roleID, context) {
        return ErrInvalidHierarchy
    }
    
    // Check context constraints
    if !s.IsValidContext(roleID, context) {
        return ErrInvalidContext
    }
    
    // Check for conflicts
    if conflicts := s.CheckRoleConflicts(userID, roleID, context); len(conflicts) > 0 {
        return ErrRoleConflicts{Conflicts: conflicts}
    }
    
    return nil
}
```

## Best Practices

### Role Design Principles

1. **Principle of Least Privilege**: Cấp quyền tối thiểu cần thiết
2. **Clear Separation**: Tách biệt vai trò rõ ràng
3. **Logical Grouping**: Nhóm quyền theo chức năng
4. **Scalable Hierarchy**: Thiết kế phân cấp có thể mở rộng
5. **Context Awareness**: Vai trò phù hợp với ngữ cảnh

### Role Naming Conventions

```yaml
naming_patterns:
  system_roles: "system_*"      # system_admin
  tenant_roles: "tenant_*"      # tenant_admin
  website_roles: "website_*"    # website_editor
  functional_roles: "*_manager" # content_manager
  level_roles: "senior_*"       # senior_author
```

### Role Documentation

```yaml
role_documentation:
  required_fields:
    - description
    - permissions_summary
    - use_cases
    - assignment_criteria
    - inheritance_rules
  
  examples:
    - typical_workflows
    - permission_scenarios
    - assignment_examples
```