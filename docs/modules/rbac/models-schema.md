# RBAC Models và Database Schema

## Mô hình dữ liệu

### Role (Vai trò)
- **ID**: Định danh vai trò
- **TenantID**: ID tenant
- **WebsiteID**: ID website (cho website-specific roles)
- **Name**: Tên vai trò
- **DisplayName**: <PERSON><PERSON><PERSON> hiển thị
- **Description**: <PERSON><PERSON> tả vai trò
- **Level**: Cấp độ vai trò (hierarchy)
- **ParentID**: ID vai trò cha
- **IsSystem**: <PERSON><PERSON> trò hệ thống hay custom
- **Status**: <PERSON><PERSON><PERSON><PERSON> thá<PERSON> (active, inactive)

### Permission (Quyền)
- **ID**: <PERSON><PERSON><PERSON> danh quyền
- **Name**: Tên quyền
- **Resource**: Tài nguyên được bảo vệ
- **Action**: <PERSON><PERSON><PERSON> động (create, read, update, delete)
- **Scope**: Phạm vi (own, team, all)
- **Conditions**: <PERSON><PERSON><PERSON><PERSON> kiện áp dụng (JSON)
- **Description**: <PERSON><PERSON> tả quyền

### RolePermission (<PERSON><PERSON> quyền cho vai trò)
- **RoleID**: ID vai trò
- **PermissionID**: ID quyền
- **GrantedAt**: Thời gian cấp quyền
- **GrantedBy**: Người cấp quyền
- **ExpiresAt**: Thời gian hết hạn
- **Conditions**: Điều kiện bổ sung

### UserRole (Gán vai trò cho user)
- **UserID**: ID người dùng
- **RoleID**: ID vai trò
- **TenantID**: ID tenant
- **WebsiteID**: ID website (cho website-specific roles)
- **AssignedAt**: Thời gian gán
- **AssignedBy**: Người gán vai trò
- **ExpiresAt**: Thời gian hết hạn
- **Context**: Ngữ cảnh gán vai trò

### Resource (Tài nguyên)
- **ID**: Định danh tài nguyên
- **Name**: Tên tài nguyên
- **Type**: Loại tài nguyên (api, ui, data)
- **Pattern**: Pattern matching
- **Attributes**: Thuộc tính tài nguyên
- **Protected**: Có được bảo vệ không

## Database Schema

### Roles Table
```sql
CREATE TABLE roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    level INT DEFAULT 0,
    parent_id INT UNSIGNED NULL,
    is_system BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_role_per_context (tenant_id, website_id, name),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES roles(id) ON DELETE SET NULL,
    
    INDEX idx_roles_tenant (tenant_id),
    INDEX idx_roles_website (website_id),
    INDEX idx_roles_hierarchy (parent_id, level),
    INDEX idx_roles_system (is_system, status)
);
```

### Permissions Table
```sql
CREATE TABLE permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    scope ENUM('own', 'team', 'all') DEFAULT 'own',
    conditions JSON NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_permission (resource, action, scope),
    INDEX idx_permissions_resource (resource),
    INDEX idx_permissions_action (action),
    INDEX idx_permissions_scope (scope)
);
```

### Role Permissions Table
```sql
CREATE TABLE role_permissions (
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT UNSIGNED NOT NULL,
    expires_at TIMESTAMP NULL,
    conditions JSON NULL,
    
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_role_permissions_expires (expires_at),
    INDEX idx_role_permissions_granted_by (granted_by)
);
```

### User Roles Table
```sql
CREATE TABLE user_roles (
    user_id INT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT UNSIGNED NOT NULL,
    expires_at TIMESTAMP NULL,
    context JSON NULL,
    
    PRIMARY KEY (user_id, role_id, tenant_id, COALESCE(website_id, 0)),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_user_roles_user (user_id),
    INDEX idx_user_roles_role (role_id),
    INDEX idx_user_roles_tenant (tenant_id),
    INDEX idx_user_roles_website (website_id),
    INDEX idx_user_roles_expires (expires_at)
);
```

### Resources Table
```sql
CREATE TABLE resources (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('api', 'ui', 'data') NOT NULL,
    pattern VARCHAR(255) NOT NULL,
    attributes JSON NULL,
    protected BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_resources_type (type),
    INDEX idx_resources_protected (protected),
    INDEX idx_resources_pattern (pattern)
);
```

## Go Models

### Role Model
```go
type Role struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    TenantID    uint      `json:"tenant_id" gorm:"not null"`
    WebsiteID   *uint     `json:"website_id,omitempty"`
    Name        string    `json:"name" gorm:"size:100;not null"`
    DisplayName string    `json:"display_name" gorm:"size:255;not null"`
    Description string    `json:"description" gorm:"type:text"`
    Level       int       `json:"level" gorm:"default:0"`
    ParentID    *uint     `json:"parent_id,omitempty"`
    IsSystem    bool      `json:"is_system" gorm:"default:false"`
    Status      string    `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relations
    Tenant      Tenant        `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
    Website     *Website      `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
    Parent      *Role         `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
    Children    []Role        `json:"children,omitempty" gorm:"foreignKey:ParentID"`
    Permissions []Permission  `json:"permissions,omitempty" gorm:"many2many:role_permissions"`
    Users       []User        `json:"users,omitempty" gorm:"many2many:user_roles"`
}
```

### Permission Model
```go
type Permission struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null"`
    Resource    string    `json:"resource" gorm:"size:100;not null"`
    Action      string    `json:"action" gorm:"size:50;not null"`
    Scope       string    `json:"scope" gorm:"type:enum('own','team','all');default:'own'"`
    Conditions  datatypes.JSON `json:"conditions,omitempty" gorm:"type:json"`
    Description string    `json:"description" gorm:"type:text"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relations
    Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions"`
}
```

### RolePermission Model
```go
type RolePermission struct {
    RoleID       uint           `json:"role_id" gorm:"primaryKey"`
    PermissionID uint           `json:"permission_id" gorm:"primaryKey"`
    GrantedAt    time.Time      `json:"granted_at"`
    GrantedBy    uint           `json:"granted_by" gorm:"not null"`
    ExpiresAt    *time.Time     `json:"expires_at,omitempty"`
    Conditions   datatypes.JSON `json:"conditions,omitempty" gorm:"type:json"`
    
    // Relations
    Role       Role       `json:"role,omitempty" gorm:"foreignKey:RoleID"`
    Permission Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
    GrantedByUser User    `json:"granted_by_user,omitempty" gorm:"foreignKey:GrantedBy"`
}
```

### UserRole Model
```go
type UserRole struct {
    UserID     uint           `json:"user_id" gorm:"primaryKey"`
    RoleID     uint           `json:"role_id" gorm:"primaryKey"`
    TenantID   uint           `json:"tenant_id" gorm:"primaryKey"`
    WebsiteID  *uint          `json:"website_id,omitempty" gorm:"primaryKey"`
    AssignedAt time.Time      `json:"assigned_at"`
    AssignedBy uint           `json:"assigned_by" gorm:"not null"`
    ExpiresAt  *time.Time     `json:"expires_at,omitempty"`
    Context    datatypes.JSON `json:"context,omitempty" gorm:"type:json"`
    
    // Relations
    User         User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
    Role         Role     `json:"role,omitempty" gorm:"foreignKey:RoleID"`
    Tenant       Tenant   `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
    Website      *Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
    AssignedByUser User   `json:"assigned_by_user,omitempty" gorm:"foreignKey:AssignedBy"`
}
```

### Resource Model
```go
type Resource struct {
    ID         uint           `json:"id" gorm:"primaryKey"`
    Name       string         `json:"name" gorm:"size:100;uniqueIndex;not null"`
    Type       string         `json:"type" gorm:"type:enum('api','ui','data');not null"`
    Pattern    string         `json:"pattern" gorm:"size:255;not null"`
    Attributes datatypes.JSON `json:"attributes,omitempty" gorm:"type:json"`
    Protected  bool           `json:"protected" gorm:"default:true"`
    CreatedAt  time.Time      `json:"created_at"`
    UpdatedAt  time.Time      `json:"updated_at"`
}
```

## Migration Scripts

### Create Tables Migration
```go
func CreateRBACTables(db *gorm.DB) error {
    return db.AutoMigrate(
        &Role{},
        &Permission{},
        &RolePermission{},
        &UserRole{},
        &Resource{},
    )
}
```

### Seed Default Data
```go
func SeedDefaultRBAC(db *gorm.DB) error {
    // Create default permissions
    permissions := []Permission{
        {Name: "posts.create", Resource: "posts", Action: "create", Scope: "own"},
        {Name: "posts.read", Resource: "posts", Action: "read", Scope: "all"},
        {Name: "posts.update", Resource: "posts", Action: "update", Scope: "own"},
        {Name: "posts.delete", Resource: "posts", Action: "delete", Scope: "own"},
        {Name: "posts.publish", Resource: "posts", Action: "publish", Scope: "own"},
        {Name: "comments.create", Resource: "comments", Action: "create", Scope: "own"},
        {Name: "comments.moderate", Resource: "comments", Action: "moderate", Scope: "all"},
        {Name: "users.read", Resource: "users", Action: "read", Scope: "team"},
        {Name: "users.manage", Resource: "users", Action: "manage", Scope: "all"},
    }
    
    for _, permission := range permissions {
        db.FirstOrCreate(&permission, Permission{Name: permission.Name})
    }
    
    // Create default resources
    resources := []Resource{
        {Name: "posts", Type: "api", Pattern: "/api/*/posts/*", Protected: true},
        {Name: "comments", Type: "api", Pattern: "/api/*/comments/*", Protected: true},
        {Name: "users", Type: "api", Pattern: "/api/*/users/*", Protected: true},
        {Name: "dashboard", Type: "ui", Pattern: "/dashboard/*", Protected: true},
    }
    
    for _, resource := range resources {
        db.FirstOrCreate(&resource, Resource{Name: resource.Name})
    }
    
    return nil
}
```

## Database Relationships

```mermaid
erDiagram
    ROLES {
        int id PK
        int tenant_id FK
        int website_id FK
        varchar name
        varchar display_name
        text description
        int level
        int parent_id FK
        boolean is_system
        enum status
    }
    
    PERMISSIONS {
        int id PK
        varchar name UK
        varchar resource
        varchar action
        enum scope
        json conditions
        text description
    }
    
    ROLE_PERMISSIONS {
        int role_id PK,FK
        int permission_id PK,FK
        timestamp granted_at
        int granted_by FK
        timestamp expires_at
        json conditions
    }
    
    USER_ROLES {
        int user_id PK,FK
        int role_id PK,FK
        int tenant_id PK,FK
        int website_id PK,FK
        timestamp assigned_at
        int assigned_by FK
        timestamp expires_at
        json context
    }
    
    RESOURCES {
        int id PK
        varchar name UK
        enum type
        varchar pattern
        json attributes
        boolean protected
    }
    
    USERS {
        int id PK
        varchar email
        varchar name
    }
    
    TENANTS {
        int id PK
        varchar name
    }
    
    WEBSITES {
        int id PK
        int tenant_id FK
        varchar domain
    }
    
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : granted_to
    ROLES ||--o{ USER_ROLES : assigned_to
    USERS ||--o{ USER_ROLES : has
    TENANTS ||--o{ ROLES : contains
    WEBSITES ||--o{ ROLES : contains
    TENANTS ||--o{ USER_ROLES : scoped_to
    WEBSITES ||--o{ USER_ROLES : scoped_to
    ROLES ||--o{ ROLES : parent_of
```

## Indexes và Performance

### Composite Indexes
```sql
-- Role lookups by context
CREATE INDEX idx_roles_context ON roles (tenant_id, website_id, status);

-- User role lookups
CREATE INDEX idx_user_roles_lookup ON user_roles (user_id, tenant_id, website_id);

-- Permission checks
CREATE INDEX idx_role_permissions_check ON role_permissions (role_id, expires_at);

-- Hierarchy queries
CREATE INDEX idx_roles_hierarchy_lookup ON roles (parent_id, level, status);
```

### Query Optimization
```sql
-- Efficient user permission query
SELECT DISTINCT p.* 
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN user_roles ur ON rp.role_id = ur.role_id
WHERE ur.user_id = ? 
  AND ur.tenant_id = ?
  AND (ur.website_id = ? OR ur.website_id IS NULL)
  AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
  AND (ur.expires_at IS NULL OR ur.expires_at > NOW());
```