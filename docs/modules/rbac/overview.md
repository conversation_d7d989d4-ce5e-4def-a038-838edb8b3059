# RBAC Module Overview

## Tổng quan

Module RBAC (Role-Based Access Control) cung cấp hệ thống phân quyền linh hoạt và mạnh mẽ cho ứng dụng blog, cho phép quản lý vai trò, quyền hạn và kiểm soát truy cập chi tiết đến từng tài nguyên.

## Mục tiêu

- **Flexible Permission System**: <PERSON><PERSON> thống phân quyền linh hoạt
- **Role Management**: Quản lý vai trò người dùng
- **Resource-based Access**: <PERSON><PERSON><PERSON> soát truy cập theo tài nguyên
- **Hierarchical Roles**: <PERSON><PERSON> trò có thứ bậc
- **Dynamic Permissions**: Quyền động theo context
- **Audit Trail**: <PERSON> dõi lịch sử truy cập

## Tính năng chính

- **Role Definition**: <PERSON><PERSON><PERSON> nghĩa vai trò tùy chỉnh
- **Permission Assignment**: <PERSON><PERSON> quyền cho vai trò
- **User Role Mapping**: <PERSON><PERSON> vai trò cho người dùng
- **Resource Protection**: Bảo vệ tài nguyên API
- **Permission Inheritance**: Kế thừa quyền từ vai trò cha
- **Conditional Access**: Truy cập có điều kiện
- **Temporary Permissions**: Quyền tạm thời
- **Permission Caching**: Cache quyền để tối ưu performance

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/rbac/
├── models/                   # Các model RBAC
│   ├── role.go              # Model vai trò
│   ├── permission.go        # Model quyền
│   ├── resource.go          # Model tài nguyên
│   ├── policy.go            # Model chính sách
│   └── assignment.go        # Model phân quyền
├── services/                # Logic nghiệp vụ
│   ├── role_service.go      # Dịch vụ vai trò
│   ├── permission_service.go # Dịch vụ quyền
│   ├── policy_service.go    # Dịch vụ chính sách
│   ├── enforcement_service.go # Dịch vụ thực thi
│   └── cache_service.go     # Dịch vụ cache quyền
├── handlers/                # HTTP handlers
├── repositories/            # Truy cập dữ liệu
├── middleware/              # RBAC middleware
│   ├── permission_middleware.go # Kiểm tra quyền
│   └── role_middleware.go   # Kiểm tra vai trò
├── policies/                # Policy definitions
│   ├── blog_policies.go     # Chính sách blog
│   ├── user_policies.go     # Chính sách user
│   └── admin_policies.go    # Chính sách admin
└── utils/                   # Utilities
    ├── permission_checker.go # Kiểm tra quyền
    └── role_hierarchy.go    # Thứ bậc vai trò
```

## Workflow chính

### 1. Kiểm tra quyền truy cập

```mermaid
sequenceDiagram
    participant User as User
    participant Middleware as RBAC Middleware
    participant Cache as Permission Cache
    participant Service as RBAC Service
    participant DB as Database
    
    User->>Middleware: API Request
    Middleware->>Cache: Check cached permissions
    Cache->>Middleware: Cache miss
    Middleware->>Service: Get user permissions
    Service->>DB: Query user roles & permissions
    DB->>Service: Return permission data
    Service->>Cache: Cache permissions
    Service->>Middleware: Return permission result
    Middleware->>User: Allow/Deny access
```

### 2. Gán vai trò cho người dùng

```mermaid
flowchart TD
    A[Admin assigns role] --> B[Validate permission]
    B --> C{Admin has permission?}
    C -->|No| D[Deny assignment]
    C -->|Yes| E[Check role hierarchy]
    E --> F{Valid hierarchy?}
    F -->|No| G[Invalid assignment]
    F -->|Yes| H[Create assignment]
    H --> I[Clear user cache]
    I --> J[Send notification]
    J --> K[Log assignment]
```

### 3. Dynamic Permission Evaluation

```mermaid
flowchart TD
    A[Permission Check] --> B[Get Base Permissions]
    B --> C[Apply Role Hierarchy]
    C --> D[Check Context Conditions]
    D --> E{Conditions Met?}
    E -->|No| F[Deny Access]
    E -->|Yes| G[Check Resource Ownership]
    G --> H{Owns Resource?}
    H -->|No| I[Check Scope Permissions]
    H -->|Yes| J[Allow Access]
    I --> K{Has Scope Access?}
    K -->|No| F
    K -->|Yes| J
```

## Liên kết tài liệu

- [Models và Database Schema](./models-schema.md)
- [Role Hierarchy](./role-hierarchy.md)
- [Permission System](./permission-system.md)
- [Permission Resolution](./permission-resolution.md)
- [Multi-tenancy](./multi-tenancy.md)
- [Performance Optimization](./performance.md)
- [API Endpoints](./api-endpoints.md)
- [Security & Monitoring](./security-monitoring.md)
- [Integration Patterns](./integration.md)

## Tài liệu liên quan

- [Module System Overview](../overview.md)
- [Auth Module](../auth/overview.md)
- [Tenant Module](../tenant/overview.md)
- [Security Best Practices](../../best-practices/security.md)
- [Performance Optimization](../../best-practices/performance.md)