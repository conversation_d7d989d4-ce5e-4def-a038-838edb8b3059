# Website Management

## Overview

Website management is a core component of the tenant system, allowing each tenant to create and manage multiple websites with independent configurations, themes, and settings. Each website operates as an isolated environment within its parent tenant.

## Website Features

### Core Website Capabilities
- **Independent Configuration**: Each website has its own settings and preferences
- **Theme Management**: Customizable themes per website
- **Domain Management**: Support for both subdomains and custom domains
- **Content Isolation**: Complete separation of content between websites
- **Resource Monitoring**: Per-website resource usage tracking

### Website Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Creating: Create website request
    Creating --> Active: Setup completed
    Creating --> Failed: Setup error
    Active --> Suspended: Violation or payment issue
    Active --> Maintenance: Scheduled maintenance
    Suspended --> Active: Issue resolved
    Maintenance --> Active: Maintenance completed
    Active --> Deleted: Deletion request
    Suspended --> Deleted: Permanent suspension
    Failed --> Deleted: Cleanup failed creation
    Deleted --> [*]: Resource cleanup
```

## Website Configuration

### Basic Settings
```json
{
  "website_id": "ws_123456789",
  "tenant_id": "tn_987654321",
  "name": "My Company Blog",
  "slug": "company-blog",
  "status": "active",
  "settings": {
    "theme": "corporate-theme",
    "language": "en",
    "timezone": "UTC",
    "seo": {
      "meta_title": "Company Blog - Latest Updates",
      "meta_description": "Stay updated with our latest news and insights",
      "meta_keywords": ["company", "blog", "news", "updates"]
    },
    "features": {
      "comments_enabled": true,
      "social_sharing": true,
      "newsletter_signup": true,
      "search_enabled": true
    },
    "branding": {
      "logo_url": "https://cdn.example.com/logo.png",
      "favicon_url": "https://cdn.example.com/favicon.ico",
      "color_scheme": {
        "primary": "#007bff",
        "secondary": "#6c757d",
        "accent": "#28a745"
      }
    }
  }
}
```

### Advanced Settings
```json
{
  "advanced_settings": {
    "custom_css": ".header { background-color: #custom; }",
    "custom_js": "// Custom JavaScript code",
    "analytics": {
      "google_analytics_id": "GA-XXXXXX-X",
      "facebook_pixel_id": "**********",
      "custom_tracking_code": "<!-- Custom tracking -->"
    },
    "integrations": {
      "payment_gateway": "stripe",
      "email_service": "sendgrid",
      "storage_provider": "aws-s3"
    },
    "security": {
      "ssl_required": true,
      "rate_limiting": {
        "requests_per_minute": 100,
        "burst_limit": 200
      },
      "content_security_policy": "default-src 'self'",
      "allowed_domains": ["*.trusted-domain.com"]
    }
  }
}
```

## Website Creation Process

### 1. Validation and Setup
```go
func CreateWebsite(ctx context.Context, tenantID string, req CreateWebsiteRequest) (*Website, error) {
    // Validate tenant permissions
    if !canCreateWebsite(ctx, tenantID) {
        return nil, ErrInsufficientPermissions
    }
    
    // Check subscription limits
    if !hasWebsiteQuota(ctx, tenantID) {
        return nil, ErrQuotaExceeded
    }
    
    // Validate website data
    if err := validateWebsiteRequest(req); err != nil {
        return nil, err
    }
    
    // Create website
    website := &Website{
        ID:       generateWebsiteID(),
        TenantID: tenantID,
        Name:     req.Name,
        Slug:     generateSlug(req.Name),
        Status:   StatusActive,
        Settings: req.Settings,
        Theme:    req.Theme,
    }
    
    // Save to database
    if err := repo.CreateWebsite(ctx, website); err != nil {
        return nil, err
    }
    
    // Setup default content
    if err := setupDefaultContent(ctx, website.ID); err != nil {
        // Log error but don't fail creation
        log.Errorf("Failed to setup default content for website %s: %v", website.ID, err)
    }
    
    return website, nil
}
```

### 2. Default Content Setup
```go
func setupDefaultContent(ctx context.Context, websiteID string) error {
    // Create default categories
    defaultCategories := []Category{
        {Name: "General", Slug: "general", IsDefault: true},
        {Name: "News", Slug: "news"},
        {Name: "Updates", Slug: "updates"},
    }
    
    for _, category := range defaultCategories {
        if err := createCategory(ctx, websiteID, category); err != nil {
            return err
        }
    }
    
    // Create welcome post
    welcomePost := Post{
        Title:     "Welcome to Your New Website",
        Content:   getWelcomeContent(),
        Status:    StatusPublished,
        AuthorID:  getSystemUserID(),
        WebsiteID: websiteID,
    }
    
    return createPost(ctx, welcomePost)
}
```

## Website Context Resolution

### Middleware Implementation
```go
func WebsiteMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        var website *Website
        var err error
        
        // Try to get website from header
        if websiteID := c.GetHeader("Website-ID"); websiteID != "" {
            website, err = getWebsiteByID(c, websiteID)
        } else {
            // Try subdomain resolution
            host := c.Request.Host
            subdomain := extractSubdomain(host)
            
            if subdomain != "" {
                website, err = getWebsiteBySubdomain(c, subdomain)
            } else {
                // Try custom domain resolution
                website, err = getWebsiteByDomain(c, host)
            }
        }
        
        if err != nil || website == nil {
            // Use default website or return error
            website, err = getDefaultWebsite(c)
            if err != nil {
                c.JSON(404, gin.H{"error": "Website not found"})
                c.Abort()
                return
            }
        }
        
        // Set website context
        c.Set("website", website)
        c.Set("website_id", website.ID)
        c.Set("tenant_id", website.TenantID)
        
        c.Next()
    }
}
```

### Context Utilities
```go
func GetWebsiteFromContext(c *gin.Context) (*Website, error) {
    website, exists := c.Get("website")
    if !exists {
        return nil, ErrWebsiteNotFound
    }
    
    return website.(*Website), nil
}

func GetWebsiteID(c *gin.Context) string {
    websiteID, _ := c.Get("website_id")
    return websiteID.(string)
}

func GetTenantID(c *gin.Context) string {
    tenantID, _ := c.Get("tenant_id")
    return tenantID.(string)
}
```

## Theme Management

### Theme Structure
```json
{
  "theme_id": "corporate-theme",
  "name": "Corporate Theme",
  "version": "1.2.0",
  "description": "Professional corporate theme",
  "author": "Theme Developer",
  "templates": {
    "home": "templates/home.html",
    "post": "templates/post.html",
    "category": "templates/category.html",
    "page": "templates/page.html"
  },
  "assets": {
    "css": ["assets/style.css", "assets/responsive.css"],
    "js": ["assets/main.js", "assets/components.js"],
    "images": ["assets/images/"]
  },
  "customizable": {
    "colors": {
      "primary": "#007bff",
      "secondary": "#6c757d"
    },
    "fonts": {
      "heading": "Arial, sans-serif",
      "body": "Georgia, serif"
    },
    "layout": {
      "sidebar": "right",
      "header_style": "fixed"
    }
  }
}
```

### Theme Installation
```go
func InstallTheme(ctx context.Context, websiteID, themeID string) error {
    // Validate theme exists and is compatible
    theme, err := getTheme(themeID)
    if err != nil {
        return err
    }
    
    // Check website permissions
    website, err := getWebsite(ctx, websiteID)
    if err != nil {
        return err
    }
    
    // Backup current theme settings
    if err := backupThemeSettings(ctx, websiteID); err != nil {
        return err
    }
    
    // Install theme files
    if err := installThemeFiles(ctx, websiteID, theme); err != nil {
        return err
    }
    
    // Update website theme setting
    website.Theme = themeID
    return updateWebsite(ctx, website)
}
```

## Website-specific File Management

### File Organization
```
/storage/websites/{website_id}/
├── themes/
│   ├── current/                # Active theme files
│   └── backups/               # Theme backups
├── uploads/
│   ├── images/                # Image uploads
│   ├── documents/             # Document uploads
│   └── media/                 # Media files
├── cache/
│   ├── pages/                 # Cached pages
│   └── assets/                # Cached assets
└── logs/
    ├── access.log             # Access logs
    └── error.log              # Error logs
```

### File Upload Handler
```go
func UploadFile(c *gin.Context) {
    websiteID := GetWebsiteID(c)
    
    file, header, err := c.Request.FormFile("file")
    if err != nil {
        c.JSON(400, gin.H{"error": "No file uploaded"})
        return
    }
    defer file.Close()
    
    // Validate file type and size
    if err := validateUpload(header); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // Generate unique filename
    filename := generateUniqueFilename(header.Filename)
    filepath := fmt.Sprintf("websites/%s/uploads/%s", websiteID, filename)
    
    // Save file
    if err := saveFile(file, filepath); err != nil {
        c.JSON(500, gin.H{"error": "Failed to save file"})
        return
    }
    
    // Create file record
    fileRecord := &File{
        ID:        generateFileID(),
        WebsiteID: websiteID,
        Filename:  filename,
        Path:      filepath,
        Size:      header.Size,
        MimeType:  header.Header.Get("Content-Type"),
    }
    
    if err := saveFileRecord(c, fileRecord); err != nil {
        c.JSON(500, gin.H{"error": "Failed to save file record"})
        return
    }
    
    c.JSON(200, gin.H{
        "file_id": fileRecord.ID,
        "url":     generateFileURL(filepath),
    })
}
```

## Website Analytics and Monitoring

### Usage Tracking
```go
type WebsiteUsage struct {
    WebsiteID     string    `json:"website_id" db:"website_id"`
    Date          time.Time `json:"date" db:"date"`
    PageViews     int64     `json:"page_views" db:"page_views"`
    UniqueVisitors int64    `json:"unique_visitors" db:"unique_visitors"`
    BandwidthUsed int64     `json:"bandwidth_used" db:"bandwidth_used"`
    StorageUsed   int64     `json:"storage_used" db:"storage_used"`
    APIRequests   int64     `json:"api_requests" db:"api_requests"`
}

func TrackUsage(c *gin.Context) {
    websiteID := GetWebsiteID(c)
    
    // Track page view
    go func() {
        usage := &WebsiteUsage{
            WebsiteID:  websiteID,
            Date:       time.Now().Truncate(24 * time.Hour),
            PageViews:  1,
            APIRequests: 1,
        }
        
        // Get client IP for unique visitor tracking
        clientIP := c.ClientIP()
        if !isUniqueVisitor(websiteID, clientIP) {
            usage.UniqueVisitors = 1
            markUniqueVisitor(websiteID, clientIP)
        }
        
        // Track bandwidth usage
        responseSize := c.Writer.Size()
        usage.BandwidthUsed = int64(responseSize)
        
        // Save usage data
        saveUsageData(usage)
    }()
}
```

### Performance Monitoring
```go
func WebsitePerformanceMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        websiteID := GetWebsiteID(c)
        
        c.Next()
        
        // Calculate response time
        duration := time.Since(start)
        
        // Log performance metrics
        metrics := PerformanceMetric{
            WebsiteID:    websiteID,
            Endpoint:     c.Request.URL.Path,
            Method:       c.Request.Method,
            ResponseTime: duration,
            StatusCode:   c.Writer.Status(),
            Timestamp:    time.Now(),
        }
        
        go savePerformanceMetric(metrics)
        
        // Alert on slow responses
        if duration > 5*time.Second {
            go alertSlowResponse(websiteID, c.Request.URL.Path, duration)
        }
    }
}
```

## Website Backup and Recovery

### Automated Backup
```go
func ScheduleWebsiteBackup(websiteID string) error {
    // Create backup job
    job := &BackupJob{
        WebsiteID: websiteID,
        Type:      "full",
        Schedule:  "0 2 * * *", // Daily at 2 AM
    }
    
    return scheduleJob(job, func() error {
        return performWebsiteBackup(websiteID)
    })
}

func performWebsiteBackup(websiteID string) error {
    // Backup database
    if err := backupWebsiteDatabase(websiteID); err != nil {
        return err
    }
    
    // Backup files
    if err := backupWebsiteFiles(websiteID); err != nil {
        return err
    }
    
    // Backup configuration
    if err := backupWebsiteConfig(websiteID); err != nil {
        return err
    }
    
    return nil
}
```

## Best Practices

### Website Management
1. **Slug Generation**: Always generate unique, SEO-friendly slugs
2. **Settings Validation**: Validate all settings before saving
3. **Resource Cleanup**: Properly clean up resources when deleting websites
4. **Version Control**: Keep track of configuration changes

### Performance Optimization
1. **Caching**: Implement aggressive caching for website settings
2. **CDN Integration**: Use CDN for static assets
3. **Image Optimization**: Automatic image compression and resizing
4. **Lazy Loading**: Implement lazy loading for improved performance

### Security Considerations
1. **Input Validation**: Strict validation of all user inputs
2. **File Upload Security**: Proper file type and size validation
3. **XSS Prevention**: Sanitize all user-generated content
4. **CSRF Protection**: Implement CSRF tokens for all forms