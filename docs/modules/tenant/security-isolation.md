# Security and Data Isolation

## Overview

The security and data isolation system ensures complete separation of tenant and website data while maintaining strong access controls and security boundaries. This document covers data isolation strategies, access control mechanisms, tenant boundaries, and security best practices.

## Data Isolation Architecture

### Multi-level Isolation Strategy

```mermaid
flowchart TB
    subgraph "Database Layer"
        A[Shared Infrastructure]
        B[Tenant Schema Separation]
        C[Row-level Security]
        D[Connection Pooling]
    end
    
    subgraph "Application Layer"
        E[Tenant Context Middleware]
        F[Website Context Middleware]
        G[Permission Checks]
        H[Query Filtering]
    end
    
    subgraph "API Layer"
        I[Authentication]
        J[Authorization]
        K[Rate Limiting]
        L[Input Validation]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
```

### Database-level Isolation

#### Schema Separation
```sql
-- Each tenant gets a dedicated schema
CREATE SCHEMA tenant_tn_1234567890;

-- Website data within tenant schema
CREATE TABLE tenant_tn_1234567890.websites (
    id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) NOT NULL DEFAULT 'tn_1234567890',
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    settings JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Posts within website context
CREATE TABLE tenant_tn_1234567890.posts (
    id VARCHAR(32) PRIMARY KEY,
    website_id VARCHAR(32) NOT NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (website_id) REFERENCES tenant_tn_1234567890.websites(id)
);

-- Row-level security policies
ALTER TABLE tenant_tn_1234567890.posts ENABLE ROW LEVEL SECURITY;

CREATE POLICY posts_isolation_policy ON tenant_tn_1234567890.posts
    USING (website_id = current_setting('app.current_website_id')::VARCHAR);
```

#### Connection Context Management
```go
type DatabaseManager struct {
    pools map[string]*sql.DB
    mutex sync.RWMutex
}

func (dm *DatabaseManager) GetTenantConnection(ctx context.Context, tenantID string) (*sql.DB, error) {
    dm.mutex.RLock()
    pool, exists := dm.pools[tenantID]
    dm.mutex.RUnlock()
    
    if !exists {
        return dm.createTenantConnection(tenantID)
    }
    
    return pool, nil
}

func (dm *DatabaseManager) createTenantConnection(tenantID string) (*sql.DB, error) {
    dm.mutex.Lock()
    defer dm.mutex.Unlock()
    
    // Double-check pattern
    if pool, exists := dm.pools[tenantID]; exists {
        return pool, nil
    }
    
    // Create new connection pool for tenant
    config := dm.getConnectionConfig(tenantID)
    db, err := sql.Open("postgres", config.DSN)
    if err != nil {
        return nil, err
    }
    
    // Configure connection pool
    db.SetMaxOpenConns(config.MaxOpenConns)
    db.SetMaxIdleConns(config.MaxIdleConns)
    db.SetConnMaxLifetime(config.ConnMaxLifetime)
    
    // Set default schema
    _, err = db.Exec(fmt.Sprintf("SET search_path = tenant_%s, public", tenantID))
    if err != nil {
        return nil, err
    }
    
    dm.pools[tenantID] = db
    return db, nil
}

func (dm *DatabaseManager) SetWebsiteContext(ctx context.Context, db *sql.DB, websiteID string) error {
    _, err := db.ExecContext(ctx, "SET app.current_website_id = $1", websiteID)
    return err
}
```

## Access Control and Authorization

### Role-Based Access Control (RBAC)

```go
type Role string
const (
    RoleSystemAdmin    Role = "system_admin"
    RoleTenantAdmin    Role = "tenant_admin"
    RoleWebsiteAdmin   Role = "website_admin"
    RoleEditor         Role = "editor"
    RoleAuthor         Role = "author"
    RoleSubscriber     Role = "subscriber"
)

type Permission string
const (
    PermissionTenantCreate      Permission = "tenant:create"
    PermissionTenantRead        Permission = "tenant:read"
    PermissionTenantUpdate      Permission = "tenant:update"
    PermissionTenantDelete      Permission = "tenant:delete"
    PermissionWebsiteCreate     Permission = "website:create"
    PermissionWebsiteRead       Permission = "website:read"
    PermissionWebsiteUpdate     Permission = "website:update"
    PermissionWebsiteDelete     Permission = "website:delete"
    PermissionPostCreate        Permission = "post:create"
    PermissionPostRead          Permission = "post:read"
    PermissionPostUpdate        Permission = "post:update"
    PermissionPostDelete        Permission = "post:delete"
    PermissionUserCreate        Permission = "user:create"
    PermissionUserRead          Permission = "user:read"
    PermissionUserUpdate        Permission = "user:update"
    PermissionUserDelete        Permission = "user:delete"
)

type UserRole struct {
    UserID     string `json:"user_id" db:"user_id"`
    TenantID   string `json:"tenant_id" db:"tenant_id"`
    WebsiteID  *string `json:"website_id,omitempty" db:"website_id"`
    Role       Role   `json:"role" db:"role"`
    GrantedBy  string `json:"granted_by" db:"granted_by"`
    GrantedAt  time.Time `json:"granted_at" db:"granted_at"`
    ExpiresAt  *time.Time `json:"expires_at,omitempty" db:"expires_at"`
}

var RolePermissions = map[Role][]Permission{
    RoleSystemAdmin: {
        PermissionTenantCreate, PermissionTenantRead, PermissionTenantUpdate, PermissionTenantDelete,
        PermissionWebsiteCreate, PermissionWebsiteRead, PermissionWebsiteUpdate, PermissionWebsiteDelete,
        PermissionPostCreate, PermissionPostRead, PermissionPostUpdate, PermissionPostDelete,
        PermissionUserCreate, PermissionUserRead, PermissionUserUpdate, PermissionUserDelete,
    },
    RoleTenantAdmin: {
        PermissionTenantRead, PermissionTenantUpdate,
        PermissionWebsiteCreate, PermissionWebsiteRead, PermissionWebsiteUpdate, PermissionWebsiteDelete,
        PermissionPostCreate, PermissionPostRead, PermissionPostUpdate, PermissionPostDelete,
        PermissionUserCreate, PermissionUserRead, PermissionUserUpdate, PermissionUserDelete,
    },
    RoleWebsiteAdmin: {
        PermissionWebsiteRead, PermissionWebsiteUpdate,
        PermissionPostCreate, PermissionPostRead, PermissionPostUpdate, PermissionPostDelete,
        PermissionUserCreate, PermissionUserRead, PermissionUserUpdate,
    },
    RoleEditor: {
        PermissionPostCreate, PermissionPostRead, PermissionPostUpdate, PermissionPostDelete,
        PermissionUserRead,
    },
    RoleAuthor: {
        PermissionPostCreate, PermissionPostRead, PermissionPostUpdate,
        PermissionUserRead,
    },
    RoleSubscriber: {
        PermissionPostRead, PermissionUserRead,
    },
}
```

### Authorization Middleware
```go
type AuthorizationMiddleware struct {
    authService AuthService
    rbacService RBACService
}

func (m *AuthorizationMiddleware) RequirePermission(permission Permission) gin.HandlerFunc {
    return func(c *gin.Context) {
        user, exists := c.Get("user")
        if !exists {
            c.JSON(401, gin.H{"error": "Authentication required"})
            c.Abort()
            return
        }
        
        tenantID := GetTenantID(c)
        websiteID := GetWebsiteID(c)
        
        if !m.rbacService.HasPermission(c, user.(*User).ID, tenantID, websiteID, permission) {
            c.JSON(403, gin.H{"error": "Insufficient permissions"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

func (m *AuthorizationMiddleware) RequireRole(role Role) gin.HandlerFunc {
    return func(c *gin.Context) {
        user, exists := c.Get("user")
        if !exists {
            c.JSON(401, gin.H{"error": "Authentication required"})
            c.Abort()
            return
        }
        
        tenantID := GetTenantID(c)
        websiteID := GetWebsiteID(c)
        
        if !m.rbacService.HasRole(c, user.(*User).ID, tenantID, websiteID, role) {
            c.JSON(403, gin.H{"error": "Role required: " + string(role)})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

type RBACService struct {
    userRoleRepo UserRoleRepository
    cache        Cache
}

func (r *RBACService) HasPermission(ctx context.Context, userID, tenantID string, websiteID *string, permission Permission) bool {
    // Get user roles for tenant/website
    roles := r.getUserRoles(ctx, userID, tenantID, websiteID)
    
    // Check if any role has the required permission
    for _, role := range roles {
        if r.roleHasPermission(role, permission) {
            return true
        }
    }
    
    return false
}

func (r *RBACService) getUserRoles(ctx context.Context, userID, tenantID string, websiteID *string) []Role {
    // Try cache first
    cacheKey := fmt.Sprintf("user_roles:%s:%s:%v", userID, tenantID, websiteID)
    if roles, found := r.cache.Get(cacheKey); found {
        return roles.([]Role)
    }
    
    // Get from database
    userRoles, err := r.userRoleRepo.GetUserRoles(ctx, userID, tenantID, websiteID)
    if err != nil {
        log.Errorf("Failed to get user roles: %v", err)
        return []Role{}
    }
    
    roles := make([]Role, len(userRoles))
    for i, ur := range userRoles {
        roles[i] = ur.Role
    }
    
    // Cache for 15 minutes
    r.cache.Set(cacheKey, roles, 15*time.Minute)
    
    return roles
}

func (r *RBACService) roleHasPermission(role Role, permission Permission) bool {
    permissions, exists := RolePermissions[role]
    if !exists {
        return false
    }
    
    for _, p := range permissions {
        if p == permission {
            return true
        }
    }
    
    return false
}
```

## Data Access Patterns

### Repository Pattern with Isolation
```go
type PostRepository struct {
    db      *sql.DB
    cache   Cache
}

func (r *PostRepository) GetByID(ctx context.Context, websiteID, postID string) (*Post, error) {
    // Ensure website context is set
    if err := r.setWebsiteContext(ctx, websiteID); err != nil {
        return nil, err
    }
    
    query := `
        SELECT id, website_id, title, content, status, author_id, created_at, updated_at
        FROM posts 
        WHERE id = $1 AND website_id = $2
    `
    
    var post Post
    err := r.db.QueryRowContext(ctx, query, postID, websiteID).Scan(
        &post.ID, &post.WebsiteID, &post.Title, &post.Content,
        &post.Status, &post.AuthorID, &post.CreatedAt, &post.UpdatedAt,
    )
    
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrPostNotFound
        }
        return nil, err
    }
    
    return &post, nil
}

func (r *PostRepository) List(ctx context.Context, websiteID string, filters PostFilters) ([]*Post, error) {
    // Ensure website context is set
    if err := r.setWebsiteContext(ctx, websiteID); err != nil {
        return nil, err
    }
    
    query := `
        SELECT id, website_id, title, content, status, author_id, created_at, updated_at
        FROM posts 
        WHERE website_id = $1
    `
    args := []interface{}{websiteID}
    
    // Apply filters
    if filters.Status != "" {
        query += " AND status = $" + strconv.Itoa(len(args)+1)
        args = append(args, filters.Status)
    }
    
    if filters.AuthorID != "" {
        query += " AND author_id = $" + strconv.Itoa(len(args)+1)
        args = append(args, filters.AuthorID)
    }
    
    query += " ORDER BY created_at DESC LIMIT $" + strconv.Itoa(len(args)+1)
    args = append(args, filters.Limit)
    
    rows, err := r.db.QueryContext(ctx, query, args...)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var posts []*Post
    for rows.Next() {
        var post Post
        err := rows.Scan(
            &post.ID, &post.WebsiteID, &post.Title, &post.Content,
            &post.Status, &post.AuthorID, &post.CreatedAt, &post.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }
        posts = append(posts, &post)
    }
    
    return posts, nil
}

func (r *PostRepository) setWebsiteContext(ctx context.Context, websiteID string) error {
    _, err := r.db.ExecContext(ctx, "SET app.current_website_id = $1", websiteID)
    return err
}
```

### Query Validation and Sanitization
```go
type QueryValidator struct {
    allowedFields map[string][]string
    maxLimit      int
}

func NewQueryValidator() *QueryValidator {
    return &QueryValidator{
        allowedFields: map[string][]string{
            "posts": {"id", "title", "status", "author_id", "created_at", "updated_at"},
            "users": {"id", "email", "first_name", "last_name", "created_at"},
        },
        maxLimit: 100,
    }
}

func (v *QueryValidator) ValidateFilters(table string, filters map[string]interface{}) error {
    allowedFields := v.allowedFields[table]
    if allowedFields == nil {
        return fmt.Errorf("table %s not allowed", table)
    }
    
    for field := range filters {
        if !v.isFieldAllowed(field, allowedFields) {
            return fmt.Errorf("field %s not allowed for table %s", field, table)
        }
    }
    
    return nil
}

func (v *QueryValidator) ValidateLimit(limit int) int {
    if limit <= 0 || limit > v.maxLimit {
        return v.maxLimit
    }
    return limit
}

func (v *QueryValidator) SanitizeOrderBy(table, orderBy string) (string, error) {
    allowedFields := v.allowedFields[table]
    if allowedFields == nil {
        return "", fmt.Errorf("table %s not allowed", table)
    }
    
    // Extract field name (remove ASC/DESC)
    field := strings.Fields(orderBy)[0]
    
    if !v.isFieldAllowed(field, allowedFields) {
        return "", fmt.Errorf("field %s not allowed for ordering", field)
    }
    
    // Only allow ASC/DESC
    parts := strings.Fields(orderBy)
    if len(parts) > 1 {
        direction := strings.ToUpper(parts[1])
        if direction != "ASC" && direction != "DESC" {
            direction = "ASC"
        }
        return fmt.Sprintf("%s %s", field, direction), nil
    }
    
    return field + " ASC", nil
}

func (v *QueryValidator) isFieldAllowed(field string, allowedFields []string) bool {
    for _, allowed := range allowedFields {
        if field == allowed {
            return true
        }
    }
    return false
}
```

## Security Middleware Stack

### Complete Security Middleware Chain
```go
func SetupSecurityMiddleware(router *gin.Engine) {
    // CORS configuration
    router.Use(cors.New(cors.Config{
        AllowOrigins:     []string{"https://*.yourblog.com"},
        AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowHeaders:     []string{"Authorization", "Content-Type", "Website-ID"},
        ExposeHeaders:    []string{"X-RateLimit-Limit", "X-RateLimit-Remaining"},
        AllowCredentials: true,
        MaxAge:           12 * time.Hour,
    }))
    
    // Security headers
    router.Use(SecurityHeadersMiddleware())
    
    // Rate limiting
    router.Use(RateLimitMiddleware())
    
    // Request size limiting
    router.Use(RequestSizeLimitMiddleware(10 * 1024 * 1024)) // 10MB
    
    // Input validation and sanitization
    router.Use(InputValidationMiddleware())
    
    // Authentication
    router.Use(AuthenticationMiddleware())
    
    // Tenant/Website context
    router.Use(TenantContextMiddleware())
    router.Use(WebsiteContextMiddleware())
    
    // Resource limits checking
    router.Use(ResourceLimitsMiddleware())
    
    // Audit logging
    router.Use(AuditLoggingMiddleware())
}

func SecurityHeadersMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        c.Header("Content-Security-Policy", "default-src 'self'")
        c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
        c.Next()
    }
}

func InputValidationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Validate request headers
        if err := validateHeaders(c.Request.Header); err != nil {
            c.JSON(400, gin.H{"error": "Invalid headers"})
            c.Abort()
            return
        }
        
        // Validate query parameters
        if err := validateQueryParams(c.Request.URL.Query()); err != nil {
            c.JSON(400, gin.H{"error": "Invalid query parameters"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

func AuditLoggingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        // Process request
        c.Next()
        
        // Log security-relevant events
        auditLog := AuditLog{
            UserID:     getUserID(c),
            TenantID:   GetTenantID(c),
            WebsiteID:  GetWebsiteID(c),
            Action:     c.Request.Method + " " + c.Request.URL.Path,
            IPAddress:  c.ClientIP(),
            UserAgent:  c.Request.UserAgent(),
            StatusCode: c.Writer.Status(),
            Duration:   time.Since(start),
            Timestamp:  time.Now(),
        }
        
        // Log to audit system
        go logAuditEvent(auditLog)
    }
}
```

## Data Encryption and Protection

### Sensitive Data Encryption
```go
type Encryptor struct {
    key []byte
}

func NewEncryptor(key string) (*Encryptor, error) {
    keyBytes, err := base64.StdEncoding.DecodeString(key)
    if err != nil {
        return nil, err
    }
    
    if len(keyBytes) != 32 {
        return nil, errors.New("key must be 32 bytes")
    }
    
    return &Encryptor{key: keyBytes}, nil
}

func (e *Encryptor) Encrypt(plaintext string) (string, error) {
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (e *Encryptor) Decrypt(ciphertext string) (string, error) {
    data, err := base64.StdEncoding.DecodeString(ciphertext)
    if err != nil {
        return "", err
    }
    
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return "", errors.New("ciphertext too short")
    }
    
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return "", err
    }
    
    return string(plaintext), nil
}

// Usage in models
type User struct {
    ID        string `json:"id" db:"id"`
    Email     string `json:"email" db:"email"`
    FirstName string `json:"first_name" db:"first_name"`
    LastName  string `json:"last_name" db:"last_name"`
    // Sensitive data stored encrypted
    Phone     *EncryptedString `json:"phone,omitempty" db:"phone"`
    Address   *EncryptedString `json:"address,omitempty" db:"address"`
}

type EncryptedString struct {
    value     string
    encryptor *Encryptor
}

func NewEncryptedString(value string, encryptor *Encryptor) (*EncryptedString, error) {
    encrypted, err := encryptor.Encrypt(value)
    if err != nil {
        return nil, err
    }
    
    return &EncryptedString{
        value:     encrypted,
        encryptor: encryptor,
    }, nil
}

func (es *EncryptedString) String() (string, error) {
    return es.encryptor.Decrypt(es.value)
}

func (es *EncryptedString) Scan(value interface{}) error {
    if value == nil {
        return nil
    }
    
    switch v := value.(type) {
    case string:
        es.value = v
    case []byte:
        es.value = string(v)
    default:
        return errors.New("cannot scan into EncryptedString")
    }
    
    return nil
}

func (es EncryptedString) Value() (driver.Value, error) {
    return es.value, nil
}
```

## Security Monitoring and Alerting

### Security Event Detection
```go
type SecurityMonitor struct {
    alertManager AlertManager
    metrics      MetricsCollector
}

func (sm *SecurityMonitor) MonitorSecurityEvents() {
    // Monitor failed authentication attempts
    go sm.monitorFailedLogins()
    
    // Monitor suspicious API usage
    go sm.monitorAPIAnomalies()
    
    // Monitor privilege escalation attempts
    go sm.monitorPrivilegeEscalation()
    
    // Monitor data access patterns
    go sm.monitorDataAccess()
}

func (sm *SecurityMonitor) monitorFailedLogins() {
    ticker := time.NewTicker(1 * time.Minute)
    for range ticker.C {
        // Check for multiple failed logins from same IP
        suspiciousIPs := sm.getIPsWithExcessiveFailedLogins()
        
        for _, ip := range suspiciousIPs {
            sm.alertManager.SendSecurityAlert(SecurityAlert{
                Type:        "suspicious_login_attempts",
                Severity:    "high",
                IPAddress:   ip.Address,
                Description: fmt.Sprintf("Multiple failed login attempts from IP %s", ip.Address),
                Count:       ip.FailedAttempts,
            })
            
            // Temporarily block IP
            sm.blockIP(ip.Address, 30*time.Minute)
        }
    }
}

func (sm *SecurityMonitor) monitorDataAccess() {
    // Detect unusual data access patterns
    // - Access to data outside user's tenant
    // - Bulk data downloads
    // - Access during unusual hours
    // - Geographic anomalies
}

type SecurityAlert struct {
    Type        string    `json:"type"`
    Severity    string    `json:"severity"`
    IPAddress   string    `json:"ip_address"`
    UserID      string    `json:"user_id,omitempty"`
    TenantID    string    `json:"tenant_id,omitempty"`
    Description string    `json:"description"`
    Count       int       `json:"count,omitempty"`
    Timestamp   time.Time `json:"timestamp"`
}
```

## Best Practices

### Security Implementation Guidelines

1. **Defense in Depth**
   - Multiple layers of security controls
   - Fail-safe defaults
   - Principle of least privilege

2. **Data Protection**
   - Encrypt sensitive data at rest and in transit
   - Use strong encryption algorithms (AES-256)
   - Regularly rotate encryption keys

3. **Access Control**
   - Implement fine-grained RBAC
   - Regular access reviews
   - Time-limited access tokens

4. **Monitoring and Alerting**
   - Comprehensive audit logging
   - Real-time security monitoring
   - Automated incident response

5. **Secure Development**
   - Input validation and sanitization
   - Output encoding
   - Secure coding practices
   - Regular security testing