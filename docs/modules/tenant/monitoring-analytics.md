# Monitoring and Analytics

## Overview

The monitoring and analytics system provides comprehensive usage tracking, resource monitoring, performance metrics, and business intelligence for tenants and their websites. This system enables data-driven decision making, resource optimization, and proactive issue detection.

## Analytics Architecture

### Data Collection Pipeline

```mermaid
flowchart LR
    A[HTTP Request] --> B[Middleware]
    B --> C[Event Collector]
    C --> D[Event Queue]
    D --> E[Stream Processor]
    E --> F[Time Series DB]
    E --> G[Analytics DB]
    F --> H[Monitoring Dashboard]
    G --> I[Analytics Dashboard]
    H --> J[Alerts]
    I --> K[Reports]
```

### Real-time vs Batch Processing

```mermaid
flowchart TB
    A[Events] --> B{Event Type}
    B -->|Real-time| C[Stream Processing]
    B -->|Batch| D[Batch Processing]
    
    C --> E[Redis/MemoryDB]
    C --> F[Live Dashboards]
    C --> G[Instant Alerts]
    
    D --> H[Data Warehouse]
    D --> I[Daily Reports]
    D --> J[Historical Analysis]
```

## Usage Tracking Implementation

### Event Collection Middleware
```go
type AnalyticsCollector struct {
    eventQueue   EventQueue
    redis        redis.Client
    config       AnalyticsConfig
}

func (a *AnalyticsCollector) TrackingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        // Capture request info
        event := &AnalyticsEvent{
            ID:        generateEventID(),
            Type:      "api_request",
            TenantID:  GetTenantID(c),
            WebsiteID: GetWebsiteID(c),
            UserID:    getUserID(c),
            Timestamp: start,
            Request: RequestInfo{
                Method:     c.Request.Method,
                Path:       c.Request.URL.Path,
                UserAgent:  c.Request.UserAgent(),
                IPAddress:  c.ClientIP(),
                Headers:    getRelevantHeaders(c.Request.Header),
            },
        }
        
        // Process request
        c.Next()
        
        // Capture response info
        event.Response = ResponseInfo{
            StatusCode:   c.Writer.Status(),
            Size:         c.Writer.Size(),
            Duration:     time.Since(start),
        }
        
        // Track in real-time cache
        go a.trackRealTimeMetrics(event)
        
        // Queue for batch processing
        go a.queueEvent(event)
    }
}

func (a *AnalyticsCollector) trackRealTimeMetrics(event *AnalyticsEvent) {
    ctx := context.Background()
    
    // Increment API request counter
    key := fmt.Sprintf("api_requests:%s:%s", event.TenantID, time.Now().Format("2006-01-02:15"))
    a.redis.Incr(ctx, key)
    a.redis.Expire(ctx, key, 24*time.Hour)
    
    // Track response times
    latencyKey := fmt.Sprintf("response_times:%s:%s", event.TenantID, time.Now().Format("2006-01-02:15"))
    a.redis.LPush(ctx, latencyKey, event.Response.Duration.Milliseconds())
    a.redis.LTrim(ctx, latencyKey, 0, 999) // Keep last 1000 values
    a.redis.Expire(ctx, latencyKey, 24*time.Hour)
    
    // Track bandwidth usage
    bandwidthKey := fmt.Sprintf("bandwidth:%s:%s", event.TenantID, time.Now().Format("2006-01-02"))
    a.redis.IncrBy(ctx, bandwidthKey, int64(event.Response.Size))
    a.redis.Expire(ctx, bandwidthKey, 30*24*time.Hour)
    
    // Track errors
    if event.Response.StatusCode >= 400 {
        errorKey := fmt.Sprintf("errors:%s:%s", event.TenantID, time.Now().Format("2006-01-02:15"))
        a.redis.Incr(ctx, errorKey)
        a.redis.Expire(ctx, errorKey, 24*time.Hour)
    }
}

type AnalyticsEvent struct {
    ID        string      `json:"id"`
    Type      string      `json:"type"`
    TenantID  string      `json:"tenant_id"`
    WebsiteID string      `json:"website_id"`
    UserID    string      `json:"user_id,omitempty"`
    Timestamp time.Time   `json:"timestamp"`
    Request   RequestInfo `json:"request"`
    Response  ResponseInfo `json:"response"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type RequestInfo struct {
    Method     string            `json:"method"`
    Path       string            `json:"path"`
    UserAgent  string            `json:"user_agent"`
    IPAddress  string            `json:"ip_address"`
    Headers    map[string]string `json:"headers"`
    Size       int               `json:"size"`
}

type ResponseInfo struct {
    StatusCode int           `json:"status_code"`
    Size       int           `json:"size"`
    Duration   time.Duration `json:"duration"`
}
```

### Website Activity Tracking
```go
type WebsiteAnalytics struct {
    db           database.DB
    redis        redis.Client
    eventQueue   EventQueue
}

func (wa *WebsiteAnalytics) TrackPageView(ctx context.Context, websiteID, path string, visitor VisitorInfo) {
    event := PageViewEvent{
        ID:        generateEventID(),
        WebsiteID: websiteID,
        Path:      path,
        Visitor:   visitor,
        Timestamp: time.Now(),
    }
    
    // Real-time tracking
    go wa.trackRealTimePageView(event)
    
    // Queue for batch processing
    go wa.queuePageViewEvent(event)
}

func (wa *WebsiteAnalytics) trackRealTimePageView(event PageViewEvent) {
    ctx := context.Background()
    today := time.Now().Format("2006-01-02")
    hour := time.Now().Format("2006-01-02:15")
    
    // Daily page views
    dailyKey := fmt.Sprintf("page_views:%s:%s", event.WebsiteID, today)
    wa.redis.Incr(ctx, dailyKey)
    wa.redis.Expire(ctx, dailyKey, 30*24*time.Hour)
    
    // Hourly page views
    hourlyKey := fmt.Sprintf("page_views_hourly:%s:%s", event.WebsiteID, hour)
    wa.redis.Incr(ctx, hourlyKey)
    wa.redis.Expire(ctx, hourlyKey, 7*24*time.Hour)
    
    // Unique visitors (using HyperLogLog for memory efficiency)
    visitorsKey := fmt.Sprintf("unique_visitors:%s:%s", event.WebsiteID, today)
    wa.redis.PFAdd(ctx, visitorsKey, event.Visitor.ID)
    wa.redis.Expire(ctx, visitorsKey, 30*24*time.Hour)
    
    // Popular pages
    pageKey := fmt.Sprintf("popular_pages:%s:%s", event.WebsiteID, today)
    wa.redis.ZIncrBy(ctx, pageKey, 1, event.Path)
    wa.redis.Expire(ctx, pageKey, 30*24*time.Hour)
    
    // Referrer tracking
    if event.Visitor.Referrer != "" {
        referrerKey := fmt.Sprintf("referrers:%s:%s", event.WebsiteID, today)
        wa.redis.ZIncrBy(ctx, referrerKey, 1, event.Visitor.Referrer)
        wa.redis.Expire(ctx, referrerKey, 30*24*time.Hour)
    }
}

type PageViewEvent struct {
    ID        string      `json:"id"`
    WebsiteID string      `json:"website_id"`
    Path      string      `json:"path"`
    Visitor   VisitorInfo `json:"visitor"`
    Timestamp time.Time   `json:"timestamp"`
}

type VisitorInfo struct {
    ID           string `json:"id"`
    IPAddress    string `json:"ip_address"`
    UserAgent    string `json:"user_agent"`
    Referrer     string `json:"referrer"`
    Country      string `json:"country"`
    Region       string `json:"region"`
    City         string `json:"city"`
    Browser      string `json:"browser"`
    OS           string `json:"os"`
    Device       string `json:"device"`
    IsReturning  bool   `json:"is_returning"`
}
```

## Resource Monitoring

### Resource Usage Tracking
```go
type ResourceMonitor struct {
    db         database.DB
    redis      redis.Client
    scheduler  Scheduler
}

func (rm *ResourceMonitor) StartMonitoring() {
    // Monitor every 5 minutes
    rm.scheduler.AddJob("resource-monitoring", "*/5 * * * *", func() {
        rm.collectResourceUsage()
    })
    
    // Aggregate hourly
    rm.scheduler.AddJob("hourly-aggregation", "0 * * * *", func() {
        rm.aggregateHourlyUsage()
    })
    
    // Generate daily reports
    rm.scheduler.AddJob("daily-reports", "0 2 * * *", func() {
        rm.generateDailyReports()
    })
}

func (rm *ResourceMonitor) collectResourceUsage() {
    tenants, err := rm.getActiveTenants()
    if err != nil {
        log.Errorf("Failed to get active tenants: %v", err)
        return
    }
    
    for _, tenant := range tenants {
        go rm.collectTenantResourceUsage(tenant.ID)
    }
}

func (rm *ResourceMonitor) collectTenantResourceUsage(tenantID string) {
    usage := &ResourceUsage{
        TenantID:  tenantID,
        Timestamp: time.Now(),
    }
    
    // Collect storage usage
    storageUsage, err := rm.getStorageUsage(tenantID)
    if err != nil {
        log.Errorf("Failed to get storage usage for tenant %s: %v", tenantID, err)
    } else {
        usage.Storage = storageUsage
    }
    
    // Collect bandwidth usage
    bandwidthUsage, err := rm.getBandwidthUsage(tenantID)
    if err != nil {
        log.Errorf("Failed to get bandwidth usage for tenant %s: %v", tenantID, err)
    } else {
        usage.Bandwidth = bandwidthUsage
    }
    
    // Collect database usage
    dbUsage, err := rm.getDatabaseUsage(tenantID)
    if err != nil {
        log.Errorf("Failed to get database usage for tenant %s: %v", tenantID, err)
    } else {
        usage.Database = dbUsage
    }
    
    // Collect API usage
    apiUsage, err := rm.getAPIUsage(tenantID)
    if err != nil {
        log.Errorf("Failed to get API usage for tenant %s: %v", tenantID, err)
    } else {
        usage.API = apiUsage
    }
    
    // Store usage data
    if err := rm.storeResourceUsage(usage); err != nil {
        log.Errorf("Failed to store resource usage for tenant %s: %v", tenantID, err)
    }
    
    // Check limits and send alerts if needed
    rm.checkResourceLimits(tenantID, usage)
}

type ResourceUsage struct {
    TenantID   string             `json:"tenant_id"`
    Timestamp  time.Time          `json:"timestamp"`
    Storage    StorageUsage       `json:"storage"`
    Bandwidth  BandwidthUsage     `json:"bandwidth"`
    Database   DatabaseUsage      `json:"database"`
    API        APIUsage           `json:"api"`
}

type StorageUsage struct {
    TotalBytes int64            `json:"total_bytes"`
    ByType     map[string]int64 `json:"by_type"`
    FileCount  int64            `json:"file_count"`
}

type BandwidthUsage struct {
    TotalBytes   int64 `json:"total_bytes"`
    InboundBytes int64 `json:"inbound_bytes"`
    OutboundBytes int64 `json:"outbound_bytes"`
}

type DatabaseUsage struct {
    TableSizes   map[string]int64 `json:"table_sizes"`
    QueryCount   int64            `json:"query_count"`
    SlowQueries  int64            `json:"slow_queries"`
    Connections  int              `json:"connections"`
}

type APIUsage struct {
    TotalRequests    int64            `json:"total_requests"`
    SuccessfulRequests int64          `json:"successful_requests"`
    ErrorRequests    int64            `json:"error_requests"`
    ByEndpoint       map[string]int64 `json:"by_endpoint"`
    AverageLatency   time.Duration    `json:"average_latency"`
}

func (rm *ResourceMonitor) getStorageUsage(tenantID string) (StorageUsage, error) {
    var usage StorageUsage
    
    // Get total storage from file system
    storagePath := fmt.Sprintf("/storage/tenants/%s", tenantID)
    
    err := filepath.Walk(storagePath, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return nil // Skip errors
        }
        
        if !info.IsDir() {
            usage.TotalBytes += info.Size()
            usage.FileCount++
            
            // Categorize by file type
            ext := strings.ToLower(filepath.Ext(path))
            if usage.ByType == nil {
                usage.ByType = make(map[string]int64)
            }
            usage.ByType[ext] += info.Size()
        }
        
        return nil
    })
    
    return usage, err
}

func (rm *ResourceMonitor) getBandwidthUsage(tenantID string) (BandwidthUsage, error) {
    ctx := context.Background()
    today := time.Now().Format("2006-01-02")
    
    // Get bandwidth usage from Redis
    key := fmt.Sprintf("bandwidth:%s:%s", tenantID, today)
    totalBytes, err := rm.redis.Get(ctx, key).Int64()
    if err != nil && err != redis.Nil {
        return BandwidthUsage{}, err
    }
    
    return BandwidthUsage{
        TotalBytes: totalBytes,
        // Add more detailed tracking if needed
    }, nil
}
```

### Performance Monitoring
```go
type PerformanceMonitor struct {
    metrics MetricsCollector
    alerts  AlertManager
}

func (pm *PerformanceMonitor) TrackPerformance(ctx context.Context, operation string, duration time.Duration, metadata map[string]interface{}) {
    metric := PerformanceMetric{
        Operation: operation,
        Duration:  duration,
        TenantID:  GetTenantID(ctx),
        WebsiteID: GetWebsiteID(ctx),
        Timestamp: time.Now(),
        Metadata:  metadata,
    }
    
    // Record metric
    pm.metrics.RecordDuration(metric.Operation, metric.Duration, map[string]string{
        "tenant_id":  metric.TenantID,
        "website_id": metric.WebsiteID,
    })
    
    // Check for performance issues
    if duration > 5*time.Second {
        pm.alerts.SendAlert(Alert{
            Type:        "performance_degradation",
            Severity:    "warning",
            Title:       "Slow Operation Detected",
            Description: fmt.Sprintf("Operation %s took %v", operation, duration),
            TenantID:    metric.TenantID,
            WebsiteID:   metric.WebsiteID,
            Metadata:    metadata,
        })
    }
}

func (pm *PerformanceMonitor) GetPerformanceStats(ctx context.Context, tenantID string, period time.Duration) (*PerformanceStats, error) {
    endTime := time.Now()
    startTime := endTime.Add(-period)
    
    stats := &PerformanceStats{
        TenantID:  tenantID,
        Period:    period,
        StartTime: startTime,
        EndTime:   endTime,
    }
    
    // Get response time percentiles
    responseTypes, err := pm.metrics.GetPercentiles("api_response_time", map[string]string{
        "tenant_id": tenantID,
    }, startTime, endTime, []float64{50, 90, 95, 99})
    if err != nil {
        return nil, err
    }
    
    stats.ResponseTime = ResponseTimeStats{
        P50: responseTypes[50],
        P90: responseTypes[90],
        P95: responseTypes[95],
        P99: responseTypes[99],
    }
    
    // Get error rates
    totalRequests, err := pm.metrics.GetCounter("api_requests_total", map[string]string{
        "tenant_id": tenantID,
    }, startTime, endTime)
    if err != nil {
        return nil, err
    }
    
    errorRequests, err := pm.metrics.GetCounter("api_requests_total", map[string]string{
        "tenant_id": tenantID,
        "status":    "error",
    }, startTime, endTime)
    if err != nil {
        return nil, err
    }
    
    if totalRequests > 0 {
        stats.ErrorRate = float64(errorRequests) / float64(totalRequests) * 100
    }
    
    return stats, nil
}

type PerformanceStats struct {
    TenantID     string            `json:"tenant_id"`
    Period       time.Duration     `json:"period"`
    StartTime    time.Time         `json:"start_time"`
    EndTime      time.Time         `json:"end_time"`
    ResponseTime ResponseTimeStats `json:"response_time"`
    ErrorRate    float64           `json:"error_rate"`
    Throughput   float64           `json:"throughput"`
}

type ResponseTimeStats struct {
    P50 time.Duration `json:"p50"`
    P90 time.Duration `json:"p90"`
    P95 time.Duration `json:"p95"`
    P99 time.Duration `json:"p99"`
}
```

## Reporting and Analytics

### Analytics Service
```go
type AnalyticsService struct {
    db         database.DB
    redis      redis.Client
    cache      Cache
}

func (as *AnalyticsService) GetWebsiteAnalytics(ctx context.Context, websiteID string, period AnalyticsPeriod) (*WebsiteAnalytics, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("analytics:%s:%s", websiteID, period.String())
    if cached, found := as.cache.Get(cacheKey); found {
        return cached.(*WebsiteAnalytics), nil
    }
    
    analytics := &WebsiteAnalytics{
        WebsiteID: websiteID,
        Period:    period,
    }
    
    // Get page views
    pageViews, err := as.getPageViews(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.PageViews = pageViews
    
    // Get unique visitors
    uniqueVisitors, err := as.getUniqueVisitors(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.UniqueVisitors = uniqueVisitors
    
    // Get top pages
    topPages, err := as.getTopPages(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.TopPages = topPages
    
    // Get traffic sources
    trafficSources, err := as.getTrafficSources(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.TrafficSources = trafficSources
    
    // Get device breakdown
    deviceBreakdown, err := as.getDeviceBreakdown(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.DeviceBreakdown = deviceBreakdown
    
    // Get geographic data
    geoData, err := as.getGeographicData(ctx, websiteID, period)
    if err != nil {
        return nil, err
    }
    analytics.GeographicData = geoData
    
    // Cache for 15 minutes
    as.cache.Set(cacheKey, analytics, 15*time.Minute)
    
    return analytics, nil
}

func (as *AnalyticsService) getPageViews(ctx context.Context, websiteID string, period AnalyticsPeriod) ([]DataPoint, error) {
    var dataPoints []DataPoint
    
    switch period.Interval {
    case "hour":
        // Get hourly data from Redis
        for i := 0; i < period.Duration; i++ {
            hour := time.Now().Add(-time.Duration(i) * time.Hour).Format("2006-01-02:15")
            key := fmt.Sprintf("page_views_hourly:%s:%s", websiteID, hour)
            
            count, err := as.redis.Get(ctx, key).Int64()
            if err == redis.Nil {
                count = 0
            } else if err != nil {
                return nil, err
            }
            
            dataPoints = append(dataPoints, DataPoint{
                Timestamp: time.Now().Add(-time.Duration(i) * time.Hour),
                Value:     count,
            })
        }
        
    case "day":
        // Get daily data from database
        query := `
            SELECT DATE(timestamp) as date, COUNT(*) as views
            FROM page_views 
            WHERE website_id = $1 AND timestamp >= $2
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        `
        
        startTime := time.Now().AddDate(0, 0, -period.Duration)
        rows, err := as.db.QueryContext(ctx, query, websiteID, startTime)
        if err != nil {
            return nil, err
        }
        defer rows.Close()
        
        for rows.Next() {
            var date time.Time
            var views int64
            
            if err := rows.Scan(&date, &views); err != nil {
                return nil, err
            }
            
            dataPoints = append(dataPoints, DataPoint{
                Timestamp: date,
                Value:     views,
            })
        }
    }
    
    return dataPoints, nil
}

func (as *AnalyticsService) GenerateReport(ctx context.Context, tenantID string, reportType ReportType, period ReportPeriod) (*Report, error) {
    report := &Report{
        ID:         generateReportID(),
        TenantID:   tenantID,
        Type:       reportType,
        Period:     period,
        GeneratedAt: time.Now(),
    }
    
    switch reportType {
    case ReportTypeUsage:
        data, err := as.generateUsageReport(ctx, tenantID, period)
        if err != nil {
            return nil, err
        }
        report.Data = data
        
    case ReportTypeAnalytics:
        data, err := as.generateAnalyticsReport(ctx, tenantID, period)
        if err != nil {
            return nil, err
        }
        report.Data = data
        
    case ReportTypePerformance:
        data, err := as.generatePerformanceReport(ctx, tenantID, period)
        if err != nil {
            return nil, err
        }
        report.Data = data
    }
    
    // Store report
    if err := as.storeReport(ctx, report); err != nil {
        return nil, err
    }
    
    return report, nil
}

type WebsiteAnalytics struct {
    WebsiteID       string          `json:"website_id"`
    Period          AnalyticsPeriod `json:"period"`
    PageViews       []DataPoint     `json:"page_views"`
    UniqueVisitors  []DataPoint     `json:"unique_visitors"`
    TopPages        []PageStats     `json:"top_pages"`
    TrafficSources  []SourceStats   `json:"traffic_sources"`
    DeviceBreakdown []DeviceStats   `json:"device_breakdown"`
    GeographicData  []GeoStats      `json:"geographic_data"`
}

type DataPoint struct {
    Timestamp time.Time `json:"timestamp"`
    Value     int64     `json:"value"`
}

type PageStats struct {
    Path      string `json:"path"`
    Views     int64  `json:"views"`
    Visitors  int64  `json:"visitors"`
    BounceRate float64 `json:"bounce_rate"`
}

type SourceStats struct {
    Source   string  `json:"source"`
    Visits   int64   `json:"visits"`
    Percent  float64 `json:"percent"`
}

type DeviceStats struct {
    Device  string  `json:"device"`
    Count   int64   `json:"count"`
    Percent float64 `json:"percent"`
}

type GeoStats struct {
    Country string  `json:"country"`
    Visits  int64   `json:"visits"`
    Percent float64 `json:"percent"`
}
```

### Dashboard Data API
```go
func (as *AnalyticsService) GetDashboardData(ctx context.Context, tenantID string) (*DashboardData, error) {
    dashboard := &DashboardData{
        TenantID:    tenantID,
        RefreshedAt: time.Now(),
    }
    
    // Get tenant overview
    overview, err := as.getTenantOverview(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    dashboard.Overview = overview
    
    // Get recent activity
    activity, err := as.getRecentActivity(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    dashboard.RecentActivity = activity
    
    // Get alerts
    alerts, err := as.getActiveAlerts(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    dashboard.Alerts = alerts
    
    // Get performance metrics
    performance, err := as.getPerformanceMetrics(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    dashboard.Performance = performance
    
    return dashboard, nil
}

type DashboardData struct {
    TenantID       string              `json:"tenant_id"`
    RefreshedAt    time.Time           `json:"refreshed_at"`
    Overview       TenantOverview      `json:"overview"`
    RecentActivity []ActivityEvent     `json:"recent_activity"`
    Alerts         []Alert             `json:"alerts"`
    Performance    PerformanceOverview `json:"performance"`
}

type TenantOverview struct {
    WebsiteCount    int     `json:"website_count"`
    UserCount       int     `json:"user_count"`
    PostCount       int     `json:"post_count"`
    StorageUsed     int64   `json:"storage_used"`
    BandwidthUsed   int64   `json:"bandwidth_used"`
    MonthlyPageViews int64  `json:"monthly_page_views"`
    GrowthRate      float64 `json:"growth_rate"`
}

type ActivityEvent struct {
    Type        string    `json:"type"`
    Description string    `json:"description"`
    UserID      string    `json:"user_id"`
    WebsiteID   string    `json:"website_id"`
    Timestamp   time.Time `json:"timestamp"`
}
```

## Alert System

### Alert Configuration and Management
```go
type AlertManager struct {
    rules       []AlertRule
    channels    []AlertChannel
    db          database.DB
    scheduler   Scheduler
}

type AlertRule struct {
    ID          string           `json:"id"`
    Name        string           `json:"name"`
    Type        AlertType        `json:"type"`
    Conditions  []AlertCondition `json:"conditions"`
    Threshold   float64          `json:"threshold"`
    Period      time.Duration    `json:"period"`
    Severity    AlertSeverity    `json:"severity"`
    Enabled     bool             `json:"enabled"`
    TenantID    string           `json:"tenant_id,omitempty"`
    WebsiteID   string           `json:"website_id,omitempty"`
}

type AlertCondition struct {
    Metric    string  `json:"metric"`
    Operator  string  `json:"operator"`
    Value     float64 `json:"value"`
    Period    string  `json:"period"`
}

func (am *AlertManager) SetupDefaultAlerts() {
    // High error rate alert
    am.AddRule(AlertRule{
        ID:   "high_error_rate",
        Name: "High Error Rate",
        Type: AlertTypePerformance,
        Conditions: []AlertCondition{
            {
                Metric:   "error_rate",
                Operator: ">",
                Value:    5.0, // 5%
                Period:   "5m",
            },
        },
        Severity: AlertSeverityWarning,
        Period:   5 * time.Minute,
        Enabled:  true,
    })
    
    // Storage quota exceeded
    am.AddRule(AlertRule{
        ID:   "storage_quota_exceeded",
        Name: "Storage Quota Exceeded",
        Type: AlertTypeResource,
        Conditions: []AlertCondition{
            {
                Metric:   "storage_usage_percent",
                Operator: ">",
                Value:    90.0,
                Period:   "1m",
            },
        },
        Severity: AlertSeverityCritical,
        Period:   1 * time.Minute,
        Enabled:  true,
    })
    
    // Bandwidth limit approaching
    am.AddRule(AlertRule{
        ID:   "bandwidth_limit_warning",
        Name: "Bandwidth Limit Warning",
        Type: AlertTypeResource,
        Conditions: []AlertCondition{
            {
                Metric:   "bandwidth_usage_percent",
                Operator: ">",
                Value:    80.0,
                Period:   "1h",
            },
        },
        Severity: AlertSeverityWarning,
        Period:   1 * time.Hour,
        Enabled:  true,
    })
}

func (am *AlertManager) EvaluateAlerts() {
    for _, rule := range am.rules {
        if !rule.Enabled {
            continue
        }
        
        go am.evaluateRule(rule)
    }
}

func (am *AlertManager) evaluateRule(rule AlertRule) {
    // Get metric values
    conditionsMet := true
    
    for _, condition := range rule.Conditions {
        value, err := am.getMetricValue(condition.Metric, rule.TenantID, rule.WebsiteID, condition.Period)
        if err != nil {
            log.Errorf("Failed to get metric %s: %v", condition.Metric, err)
            return
        }
        
        if !am.evaluateCondition(condition, value) {
            conditionsMet = false
            break
        }
    }
    
    if conditionsMet {
        alert := Alert{
            ID:        generateAlertID(),
            RuleID:    rule.ID,
            Type:      rule.Type,
            Severity:  rule.Severity,
            Title:     rule.Name,
            TenantID:  rule.TenantID,
            WebsiteID: rule.WebsiteID,
            CreatedAt: time.Now(),
            Status:    AlertStatusOpen,
        }
        
        am.SendAlert(alert)
    }
}

func (am *AlertManager) SendAlert(alert Alert) {
    // Store alert
    if err := am.storeAlert(alert); err != nil {
        log.Errorf("Failed to store alert: %v", err)
        return
    }
    
    // Send to configured channels
    for _, channel := range am.channels {
        go channel.Send(alert)
    }
}

type Alert struct {
    ID          string        `json:"id"`
    RuleID      string        `json:"rule_id"`
    Type        AlertType     `json:"type"`
    Severity    AlertSeverity `json:"severity"`
    Title       string        `json:"title"`
    Description string        `json:"description"`
    TenantID    string        `json:"tenant_id"`
    WebsiteID   string        `json:"website_id"`
    CreatedAt   time.Time     `json:"created_at"`
    ResolvedAt  *time.Time    `json:"resolved_at,omitempty"`
    Status      AlertStatus   `json:"status"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type AlertType string
const (
    AlertTypePerformance AlertType = "performance"
    AlertTypeResource    AlertType = "resource"
    AlertTypeSecurity    AlertType = "security"
    AlertTypeSystem      AlertType = "system"
)

type AlertSeverity string
const (
    AlertSeverityInfo     AlertSeverity = "info"
    AlertSeverityWarning  AlertSeverity = "warning"
    AlertSeverityCritical AlertSeverity = "critical"
)

type AlertStatus string
const (
    AlertStatusOpen     AlertStatus = "open"
    AlertStatusResolved AlertStatus = "resolved"
    AlertStatusSilenced AlertStatus = "silenced"
)
```

## Best Practices

### Performance Optimization
1. **Efficient Data Collection**: Use sampling for high-volume events
2. **Caching Strategy**: Cache frequently accessed analytics data
3. **Batch Processing**: Process analytics data in batches to reduce load
4. **Data Retention**: Implement data retention policies for old analytics data

### Data Accuracy
1. **Deduplication**: Implement bot detection and filtering
2. **Validation**: Validate incoming data before processing
3. **Consistency**: Ensure consistent data collection across all endpoints
4. **Backup**: Regular backups of analytics data

### Privacy and Compliance
1. **Data Anonymization**: Anonymize personal data in analytics
2. **GDPR Compliance**: Implement data deletion and export capabilities
3. **Consent Management**: Respect user consent preferences
4. **Data Minimization**: Collect only necessary data

### Scalability
1. **Horizontal Scaling**: Design for horizontal scaling of analytics infrastructure
2. **Data Partitioning**: Partition data by tenant and time period
3. **Load Balancing**: Distribute analytics load across multiple servers
4. **Resource Monitoring**: Monitor analytics system performance and resource usage