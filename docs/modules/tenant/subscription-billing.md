# Subscription and Billing Management

## Overview

The subscription and billing system manages service plans, billing cycles, resource limits, and payment processing for tenants and their websites. Each subscription defines the available resources and features for a tenant or individual website.

## Subscription Plans

### Plan Structure
```yaml
plans:
  free:
    name: "<PERSON>ễn phí"
    price: 0
    billing_cycle: "monthly"
    trial_days: 0
    limits:
      users: 3
      posts: 50
      storage: "1GB"
      bandwidth: "10GB"
      websites: 1
      custom_domains: 0
      api_requests_per_day: 1000
    features:
      - "basic_themes"
      - "email_support"
    restrictions:
      - "watermark_required"
      - "limited_customization"
  
  basic:
    name: "Cơ bản"
    price: 29
    billing_cycle: "monthly"
    trial_days: 14
    limits:
      users: 10
      posts: 500
      storage: "10GB"
      bandwidth: "100GB"
      websites: 3
      custom_domains: 1
      api_requests_per_day: 10000
    features:
      - "premium_themes"
      - "priority_support"
      - "custom_css"
      - "analytics"
    restrictions: []
  
  premium:
    name: "Cao cấp"
    price: 99
    billing_cycle: "monthly"
    trial_days: 30
    limits:
      users: 50
      posts: 5000
      storage: "100GB"
      bandwidth: "1TB"
      websites: 10
      custom_domains: 5
      api_requests_per_day: 100000
    features:
      - "all_themes"
      - "priority_support"
      - "custom_css"
      - "custom_js"
      - "analytics"
      - "white_label"
      - "api_access"
    restrictions: []
    
  enterprise:
    name: "Doanh nghiệp"
    price: 299
    billing_cycle: "monthly"
    trial_days: 30
    limits:
      users: -1          # Unlimited
      posts: -1          # Unlimited
      storage: "1TB"
      bandwidth: "10TB"
      websites: -1       # Unlimited
      custom_domains: -1 # Unlimited
      api_requests_per_day: -1 # Unlimited
    features:
      - "all_themes"
      - "dedicated_support"
      - "custom_css"
      - "custom_js"
      - "analytics"
      - "white_label"
      - "api_access"
      - "sso_integration"
      - "custom_integrations"
    restrictions: []
```

## Subscription States and Lifecycle

### Subscription Status Flow
```mermaid
stateDiagram-v2
    [*] --> Trial: New signup
    Trial --> Active: Payment successful
    Trial --> Expired: Trial period ended
    Active --> Suspended: Payment failed
    Active --> Cancelled: User cancellation
    Suspended --> Active: Payment resolved
    Suspended --> Cancelled: Grace period ended
    Expired --> Active: Upgrade to paid plan
    Cancelled --> [*]: Account deletion
    Active --> Upgrading: Plan upgrade
    Upgrading --> Active: Upgrade completed
    Active --> Downgrading: Plan downgrade
    Downgrading --> Active: Downgrade completed
```

### Subscription Model
```go
type Subscription struct {
    ID              string                 `json:"id" db:"id"`
    TenantID        string                 `json:"tenant_id" db:"tenant_id"`
    WebsiteID       *string                `json:"website_id,omitempty" db:"website_id"`
    Plan            SubscriptionPlan       `json:"plan" db:"plan"`
    Status          SubscriptionStatus     `json:"status" db:"status"`
    BillingCycle    BillingCycle          `json:"billing_cycle" db:"billing_cycle"`
    Price           decimal.Decimal        `json:"price" db:"price"`
    Currency        string                 `json:"currency" db:"currency"`
    StartDate       time.Time              `json:"start_date" db:"start_date"`
    EndDate         *time.Time             `json:"end_date,omitempty" db:"end_date"`
    TrialEndsAt     *time.Time             `json:"trial_ends_at,omitempty" db:"trial_ends_at"`
    NextBillingDate *time.Time             `json:"next_billing_date,omitempty" db:"next_billing_date"`
    Limits          SubscriptionLimits     `json:"limits" db:"limits"`
    Features        []string               `json:"features" db:"features"`
    Metadata        map[string]interface{} `json:"metadata" db:"metadata"`
    CreatedAt       time.Time              `json:"created_at" db:"created_at"`
    UpdatedAt       time.Time              `json:"updated_at" db:"updated_at"`
}

type SubscriptionLimits struct {
    Users              int    `json:"users"`
    Posts              int    `json:"posts"`
    Storage            string `json:"storage"`
    Bandwidth          string `json:"bandwidth"`
    Websites           int    `json:"websites"`
    CustomDomains      int    `json:"custom_domains"`
    APIRequestsPerDay  int    `json:"api_requests_per_day"`
}

type SubscriptionPlan string
const (
    PlanFree       SubscriptionPlan = "free"
    PlanBasic      SubscriptionPlan = "basic"
    PlanPremium    SubscriptionPlan = "premium"
    PlanEnterprise SubscriptionPlan = "enterprise"
)

type SubscriptionStatus string
const (
    StatusTrial      SubscriptionStatus = "trial"
    StatusActive     SubscriptionStatus = "active"
    StatusSuspended  SubscriptionStatus = "suspended"
    StatusCancelled  SubscriptionStatus = "cancelled"
    StatusExpired    SubscriptionStatus = "expired"
    StatusUpgrading  SubscriptionStatus = "upgrading"
    StatusDowngrading SubscriptionStatus = "downgrading"
)
```

## Billing and Payment Processing

### Payment Integration
```go
type PaymentService interface {
    CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*PaymentSubscription, error)
    UpdateSubscription(ctx context.Context, subscriptionID string, req UpdateSubscriptionRequest) error
    CancelSubscription(ctx context.Context, subscriptionID string) error
    ProcessPayment(ctx context.Context, req PaymentRequest) (*PaymentResult, error)
    GetPaymentMethods(ctx context.Context, customerID string) ([]PaymentMethod, error)
    CreateCustomer(ctx context.Context, req CreateCustomerRequest) (*Customer, error)
}

type CreateSubscriptionRequest struct {
    CustomerID      string          `json:"customer_id"`
    PlanID          string          `json:"plan_id"`
    PaymentMethodID string          `json:"payment_method_id"`
    TrialDays       int             `json:"trial_days"`
    Metadata        map[string]string `json:"metadata"`
}

type PaymentResult struct {
    ID            string          `json:"id"`
    Status        PaymentStatus   `json:"status"`
    Amount        decimal.Decimal `json:"amount"`
    Currency      string          `json:"currency"`
    FailureReason string          `json:"failure_reason,omitempty"`
    Metadata      map[string]string `json:"metadata"`
}
```

### Billing Service Implementation
```go
type BillingService struct {
    subscriptionRepo SubscriptionRepository
    paymentService   PaymentService
    limitService     LimitService
    emailService     EmailService
}

func (s *BillingService) CreateSubscription(ctx context.Context, tenantID string, planID string, paymentMethodID string) (*Subscription, error) {
    // Get plan configuration
    plan, err := s.getPlanConfig(planID)
    if err != nil {
        return nil, err
    }
    
    // Create payment subscription
    paymentReq := CreateSubscriptionRequest{
        CustomerID:      tenantID,
        PlanID:          planID,
        PaymentMethodID: paymentMethodID,
        TrialDays:       plan.TrialDays,
        Metadata: map[string]string{
            "tenant_id": tenantID,
        },
    }
    
    paymentSub, err := s.paymentService.CreateSubscription(ctx, paymentReq)
    if err != nil {
        return nil, err
    }
    
    // Create internal subscription
    subscription := &Subscription{
        ID:           generateSubscriptionID(),
        TenantID:     tenantID,
        Plan:         SubscriptionPlan(planID),
        Status:       StatusTrial,
        BillingCycle: plan.BillingCycle,
        Price:        plan.Price,
        Currency:     "USD",
        StartDate:    time.Now(),
        Limits:       plan.Limits,
        Features:     plan.Features,
        Metadata: map[string]interface{}{
            "payment_subscription_id": paymentSub.ID,
        },
    }
    
    if plan.TrialDays > 0 {
        trialEnd := time.Now().AddDate(0, 0, plan.TrialDays)
        subscription.TrialEndsAt = &trialEnd
    } else {
        subscription.Status = StatusActive
        nextBilling := s.calculateNextBillingDate(time.Now(), plan.BillingCycle)
        subscription.NextBillingDate = &nextBilling
    }
    
    // Save subscription
    if err := s.subscriptionRepo.Create(ctx, subscription); err != nil {
        return nil, err
    }
    
    // Update limits
    if err := s.limitService.UpdateLimits(ctx, tenantID, subscription.Limits); err != nil {
        log.Errorf("Failed to update limits for tenant %s: %v", tenantID, err)
    }
    
    return subscription, nil
}

func (s *BillingService) UpgradeSubscription(ctx context.Context, tenantID string, newPlanID string) error {
    // Get current subscription
    currentSub, err := s.subscriptionRepo.GetByTenantID(ctx, tenantID)
    if err != nil {
        return err
    }
    
    // Get new plan
    newPlan, err := s.getPlanConfig(newPlanID)
    if err != nil {
        return err
    }
    
    // Calculate prorated amount
    proratedAmount := s.calculateProratedAmount(currentSub, newPlan)
    
    // Process upgrade payment
    if proratedAmount > 0 {
        paymentReq := PaymentRequest{
            CustomerID: tenantID,
            Amount:     proratedAmount,
            Currency:   currentSub.Currency,
            Description: fmt.Sprintf("Upgrade to %s plan", newPlanID),
        }
        
        result, err := s.paymentService.ProcessPayment(ctx, paymentReq)
        if err != nil || result.Status != PaymentStatusSucceeded {
            return fmt.Errorf("payment failed: %v", err)
        }
    }
    
    // Update subscription
    currentSub.Plan = SubscriptionPlan(newPlanID)
    currentSub.Price = newPlan.Price
    currentSub.Limits = newPlan.Limits
    currentSub.Features = newPlan.Features
    currentSub.Status = StatusActive
    
    if err := s.subscriptionRepo.Update(ctx, currentSub); err != nil {
        return err
    }
    
    // Update limits
    return s.limitService.UpdateLimits(ctx, tenantID, currentSub.Limits)
}
```

## Resource Limit Management

### Limit Enforcement Middleware
```go
func ResourceLimitMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := GetWebsiteID(c)
        tenantID := GetTenantID(c)
        
        // Check various limits based on endpoint
        endpoint := c.Request.URL.Path
        method := c.Request.Method
        
        switch {
        case strings.Contains(endpoint, "/posts") && method == "POST":
            if !checkPostLimit(c, websiteID) {
                c.JSON(429, gin.H{"error": "Post limit exceeded"})
                c.Abort()
                return
            }
            
        case strings.Contains(endpoint, "/users") && method == "POST":
            if !checkUserLimit(c, tenantID) {
                c.JSON(429, gin.H{"error": "User limit exceeded"})
                c.Abort()
                return
            }
            
        case strings.Contains(endpoint, "/upload"):
            if !checkStorageLimit(c, websiteID) {
                c.JSON(429, gin.H{"error": "Storage limit exceeded"})
                c.Abort()
                return
            }
            
        default:
            // Check API rate limits
            if !checkAPIRateLimit(c, tenantID) {
                c.JSON(429, gin.H{"error": "API rate limit exceeded"})
                c.Abort()
                return
            }
        }
        
        c.Next()
    }
}

func checkPostLimit(c *gin.Context, websiteID string) bool {
    subscription := getSubscriptionFromContext(c)
    if subscription.Limits.Posts == -1 { // Unlimited
        return true
    }
    
    currentCount, err := getPostCount(c, websiteID)
    if err != nil {
        log.Errorf("Failed to get post count: %v", err)
        return false
    }
    
    return currentCount < subscription.Limits.Posts
}

func checkStorageLimit(c *gin.Context, websiteID string) bool {
    subscription := getSubscriptionFromContext(c)
    
    // Parse storage limit
    maxStorage, err := parseStorageLimit(subscription.Limits.Storage)
    if err != nil {
        return false
    }
    
    currentUsage, err := getStorageUsage(c, websiteID)
    if err != nil {
        log.Errorf("Failed to get storage usage: %v", err)
        return false
    }
    
    return currentUsage < maxStorage
}
```

### Usage Tracking
```go
type UsageTracker struct {
    redis  redis.Client
    db     database.DB
}

func (t *UsageTracker) TrackAPIRequest(tenantID string) error {
    key := fmt.Sprintf("api_usage:%s:%s", tenantID, time.Now().Format("2006-01-02"))
    
    // Increment daily counter
    count, err := t.redis.Incr(context.Background(), key).Result()
    if err != nil {
        return err
    }
    
    // Set expiry if first request of the day
    if count == 1 {
        t.redis.Expire(context.Background(), key, 24*time.Hour)
    }
    
    return nil
}

func (t *UsageTracker) GetAPIUsage(tenantID string, date time.Time) (int64, error) {
    key := fmt.Sprintf("api_usage:%s:%s", tenantID, date.Format("2006-01-02"))
    
    count, err := t.redis.Get(context.Background(), key).Int64()
    if err == redis.Nil {
        return 0, nil
    }
    
    return count, err
}

func (t *UsageTracker) TrackBandwidth(websiteID string, bytes int64) error {
    // Track daily bandwidth
    dailyKey := fmt.Sprintf("bandwidth:%s:%s", websiteID, time.Now().Format("2006-01-02"))
    t.redis.IncrBy(context.Background(), dailyKey, bytes)
    t.redis.Expire(context.Background(), dailyKey, 24*time.Hour)
    
    // Track monthly bandwidth
    monthlyKey := fmt.Sprintf("bandwidth:%s:%s", websiteID, time.Now().Format("2006-01"))
    t.redis.IncrBy(context.Background(), monthlyKey, bytes)
    t.redis.Expire(context.Background(), monthlyKey, 31*24*time.Hour)
    
    return nil
}
```

## Billing Automation and Webhooks

### Subscription Lifecycle Automation
```go
type BillingAutomation struct {
    billingService  *BillingService
    emailService    EmailService
    scheduler       Scheduler
}

func (b *BillingAutomation) SetupAutomation() {
    // Trial ending notifications
    b.scheduler.AddJob("trial-ending-warning", "0 9 * * *", func() {
        b.notifyTrialEnding()
    })
    
    // Expired trial cleanup
    b.scheduler.AddJob("trial-cleanup", "0 2 * * *", func() {
        b.cleanupExpiredTrials()
    })
    
    // Failed payment retry
    b.scheduler.AddJob("payment-retry", "0 */6 * * *", func() {
        b.retryFailedPayments()
    })
    
    // Usage reports
    b.scheduler.AddJob("usage-reports", "0 1 1 * *", func() {
        b.generateMonthlyUsageReports()
    })
}

func (b *BillingAutomation) notifyTrialEnding() {
    // Find trials ending in 3 days
    endDate := time.Now().AddDate(0, 0, 3)
    subscriptions, err := b.billingService.GetTrialsEndingOn(endDate)
    if err != nil {
        log.Errorf("Failed to get ending trials: %v", err)
        return
    }
    
    for _, sub := range subscriptions {
        tenant, err := b.billingService.GetTenant(sub.TenantID)
        if err != nil {
            continue
        }
        
        // Send trial ending email
        b.emailService.SendTrialEndingNotification(tenant.Email, tenant.Name, sub.TrialEndsAt)
    }
}

func (b *BillingAutomation) cleanupExpiredTrials() {
    // Find expired trials
    subscriptions, err := b.billingService.GetExpiredTrials()
    if err != nil {
        log.Errorf("Failed to get expired trials: %v", err)
        return
    }
    
    for _, sub := range subscriptions {
        // Update status to expired
        sub.Status = StatusExpired
        b.billingService.UpdateSubscription(context.Background(), sub)
        
        // Suspend tenant access
        b.billingService.SuspendTenant(sub.TenantID)
        
        // Send expiration notification
        tenant, _ := b.billingService.GetTenant(sub.TenantID)
        b.emailService.SendTrialExpiredNotification(tenant.Email, tenant.Name)
    }
}
```

### Payment Webhook Handler
```go
func HandlePaymentWebhook(c *gin.Context) {
    var webhook PaymentWebhook
    if err := c.ShouldBindJSON(&webhook); err != nil {
        c.JSON(400, gin.H{"error": "Invalid webhook payload"})
        return
    }
    
    // Verify webhook signature
    if !verifyWebhookSignature(c.Request, webhook) {
        c.JSON(401, gin.H{"error": "Invalid signature"})
        return
    }
    
    switch webhook.Type {
    case "subscription.payment_succeeded":
        handlePaymentSucceeded(webhook.Data)
        
    case "subscription.payment_failed":
        handlePaymentFailed(webhook.Data)
        
    case "subscription.cancelled":
        handleSubscriptionCancelled(webhook.Data)
        
    case "subscription.updated":
        handleSubscriptionUpdated(webhook.Data)
    }
    
    c.JSON(200, gin.H{"status": "processed"})
}

func handlePaymentSucceeded(data WebhookData) {
    subscription, err := getSubscriptionByPaymentID(data.SubscriptionID)
    if err != nil {
        log.Errorf("Failed to find subscription: %v", err)
        return
    }
    
    // Update subscription status
    subscription.Status = StatusActive
    subscription.NextBillingDate = calculateNextBillingDate(time.Now(), subscription.BillingCycle)
    
    updateSubscription(context.Background(), subscription)
    
    // Send payment confirmation
    tenant, _ := getTenant(subscription.TenantID)
    sendPaymentConfirmation(tenant.Email, data.Amount, data.Currency)
}

func handlePaymentFailed(data WebhookData) {
    subscription, err := getSubscriptionByPaymentID(data.SubscriptionID)
    if err != nil {
        return
    }
    
    // Update subscription status
    subscription.Status = StatusSuspended
    updateSubscription(context.Background(), subscription)
    
    // Suspend tenant access
    suspendTenant(subscription.TenantID)
    
    // Send payment failed notification
    tenant, _ := getTenant(subscription.TenantID)
    sendPaymentFailedNotification(tenant.Email, data.FailureReason)
    
    // Schedule retry
    schedulePaymentRetry(subscription.ID, data.AttemptCount)
}
```

## Reporting and Analytics

### Subscription Analytics
```go
type SubscriptionAnalytics struct {
    db database.DB
}

func (s *SubscriptionAnalytics) GetMRR(ctx context.Context) (*MRRReport, error) {
    query := `
        SELECT 
            DATE_TRUNC('month', created_at) as month,
            plan,
            COUNT(*) as new_subscriptions,
            SUM(price) as mrr
        FROM subscriptions 
        WHERE status IN ('active', 'trial') 
            AND created_at >= NOW() - INTERVAL '12 months'
        GROUP BY month, plan
        ORDER BY month DESC
    `
    
    rows, err := s.db.Query(ctx, query)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var report MRRReport
    for rows.Next() {
        var entry MRREntry
        err := rows.Scan(&entry.Month, &entry.Plan, &entry.NewSubscriptions, &entry.MRR)
        if err != nil {
            return nil, err
        }
        report.Entries = append(report.Entries, entry)
    }
    
    return &report, nil
}

func (s *SubscriptionAnalytics) GetChurnRate(ctx context.Context, period time.Duration) (float64, error) {
    startDate := time.Now().Add(-period)
    
    query := `
        SELECT 
            COUNT(CASE WHEN status = 'cancelled' AND updated_at >= $1 THEN 1 END) as churned,
            COUNT(*) as total
        FROM subscriptions 
        WHERE created_at < $1
    `
    
    var churned, total int
    err := s.db.QueryRow(ctx, query, startDate).Scan(&churned, &total)
    if err != nil {
        return 0, err
    }
    
    if total == 0 {
        return 0, nil
    }
    
    return float64(churned) / float64(total) * 100, nil
}
```

## Best Practices

### Subscription Management
1. **Grace Periods**: Provide grace periods for failed payments
2. **Clear Communication**: Send clear notifications about billing events
3. **Flexible Upgrades**: Allow seamless plan upgrades and downgrades
4. **Trial Management**: Properly handle trial periods and conversions

### Payment Security
1. **PCI Compliance**: Use PCI-compliant payment processors
2. **Webhook Verification**: Always verify webhook signatures
3. **Secure Storage**: Never store sensitive payment information
4. **Audit Trails**: Maintain comprehensive audit logs

### Performance Optimization
1. **Caching**: Cache subscription and limit information
2. **Batch Processing**: Process billing operations in batches
3. **Async Operations**: Use background jobs for heavy operations
4. **Rate Limiting**: Implement proper rate limiting based on plans