# Integration Patterns

## Overview

This document describes how the Tenant module integrates with other modules in the Blog API v3 system, including communication patterns, data flow, event handling, and cross-module dependencies.

## Integration Architecture

### Module Dependency Graph

```mermaid
flowchart TD
    A[Tenant Module] --> B[Auth Module]
    A --> C[User Module]
    A --> D[Website Module]
    A --> E[Blog Module]
    A --> F[Notification Module]
    A --> G[Payment Module]
    A --> H[Media Module]
    A --> I[SEO Module]
    A --> J[RBAC Module]
    
    B --> K[Database]
    C --> K
    D --> K
    E --> K
    F --> L[Message Queue]
    G --> M[Payment Gateway]
    H --> N[File Storage]
    I --> O[Search Engine]
    J --> P[Permission Store]
```

### Communication Patterns

```mermaid
sequenceDiagram
    participant T as Tenant Module
    participant A as Auth Module
    participant U as User Module
    participant B as Blog Module
    participant N as Notification Module
    
    T->>A: Tenant Created Event
    A->>U: Create Default Admin User
    U->>T: User Created Event
    T->>B: Initialize Blog Structure
    B->>N: Send Welcome Notification
    N->>T: Notification Sent Event
```

## Auth Module Integration

### Authentication Context
```go
type AuthContext struct {
    UserID    string `json:"user_id"`
    TenantID  string `json:"tenant_id"`
    WebsiteID string `json:"website_id"`
    Roles     []Role `json:"roles"`
    SessionID string `json:"session_id"`
}

type TenantAuthService struct {
    authService  AuthService
    tenantRepo   TenantRepository
    userRoleRepo UserRoleRepository
}

func (tas *TenantAuthService) AuthenticateWithTenantContext(ctx context.Context, token string) (*AuthContext, error) {
    // Validate token with auth service
    authResult, err := tas.authService.ValidateToken(ctx, token)
    if err != nil {
        return nil, err
    }
    
    // Extract tenant/website context from token claims
    tenantID := authResult.Claims["tenant_id"].(string)
    websiteID := authResult.Claims["website_id"].(string)
    
    // Verify tenant is active
    tenant, err := tas.tenantRepo.GetByID(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    if tenant.Status != TenantStatusActive {
        return nil, ErrTenantInactive
    }
    
    // Get user roles for tenant
    roles, err := tas.userRoleRepo.GetUserRoles(ctx, authResult.UserID, tenantID, &websiteID)
    if err != nil {
        return nil, err
    }
    
    return &AuthContext{
        UserID:    authResult.UserID,
        TenantID:  tenantID,
        WebsiteID: websiteID,
        Roles:     roles,
        SessionID: authResult.SessionID,
    }, nil
}

func (tas *TenantAuthService) GenerateTenantToken(ctx context.Context, userID, tenantID, websiteID string) (string, error) {
    // Get user roles
    roles, err := tas.userRoleRepo.GetUserRoles(ctx, userID, tenantID, &websiteID)
    if err != nil {
        return "", err
    }
    
    // Create token with tenant context
    claims := map[string]interface{}{
        "user_id":    userID,
        "tenant_id":  tenantID,
        "website_id": websiteID,
        "roles":      roles,
        "iss":        "blog-api-v3",
        "aud":        tenantID,
        "exp":        time.Now().Add(24 * time.Hour).Unix(),
        "iat":        time.Now().Unix(),
    }
    
    return tas.authService.GenerateToken(claims)
}
```

### Cross-tenant Access Prevention
```go
func TenantIsolationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authCtx, exists := c.Get("auth_context")
        if !exists {
            c.JSON(401, gin.H{"error": "Authentication required"})
            c.Abort()
            return
        }
        
        auth := authCtx.(*AuthContext)
        
        // Extract resource tenant from URL
        resourceTenantID := c.Param("tenant_id")
        if resourceTenantID != "" && resourceTenantID != auth.TenantID {
            // Check if user has cross-tenant access (system admin)
            if !hasSystemAdminRole(auth.Roles) {
                c.JSON(403, gin.H{"error": "Access denied to tenant resources"})
                c.Abort()
                return
            }
        }
        
        // Extract resource website from URL
        resourceWebsiteID := c.Param("website_id")
        if resourceWebsiteID != "" && resourceWebsiteID != auth.WebsiteID {
            // Check if user has cross-website access within tenant
            if !hasAccessToWebsite(auth.UserID, auth.TenantID, resourceWebsiteID) {
                c.JSON(403, gin.H{"error": "Access denied to website resources"})
                c.Abort()
                return
            }
        }
        
        c.Next()
    }
}
```

## User Module Integration

### User Management with Tenant Context
```go
type TenantUserService struct {
    userService    UserService
    tenantService  TenantService
    rbacService    RBACService
    eventPublisher EventPublisher
}

func (tus *TenantUserService) CreateUser(ctx context.Context, tenantID, websiteID string, req CreateUserRequest) (*User, error) {
    // Verify tenant and website exist
    tenant, err := tus.tenantService.GetByID(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    website, err := tus.tenantService.GetWebsite(ctx, websiteID)
    if err != nil {
        return nil, err
    }
    
    if website.TenantID != tenantID {
        return nil, ErrWebsiteNotInTenant
    }
    
    // Check user limit for tenant
    if !tus.canCreateUser(ctx, tenantID) {
        return nil, ErrUserLimitExceeded
    }
    
    // Create user with tenant context
    user, err := tus.userService.CreateUser(ctx, UserRequest{
        Email:      req.Email,
        FirstName:  req.FirstName,
        LastName:   req.LastName,
        Password:   req.Password,
        TenantID:   tenantID,
        WebsiteID:  websiteID,
    })
    if err != nil {
        return nil, err
    }
    
    // Assign default role
    if err := tus.rbacService.AssignRole(ctx, user.ID, tenantID, websiteID, req.Role); err != nil {
        // Rollback user creation
        tus.userService.DeleteUser(ctx, user.ID)
        return nil, err
    }
    
    // Publish user created event
    tus.eventPublisher.Publish(UserCreatedEvent{
        UserID:    user.ID,
        TenantID:  tenantID,
        WebsiteID: websiteID,
        Role:      req.Role,
        CreatedAt: time.Now(),
    })
    
    return user, nil
}

func (tus *TenantUserService) GetTenantUsers(ctx context.Context, tenantID string, filters UserFilters) ([]*User, error) {
    // Get all users for tenant
    users, err := tus.userService.GetUsersByTenant(ctx, tenantID, filters)
    if err != nil {
        return nil, err
    }
    
    // Enrich with role information
    for _, user := range users {
        roles, err := tus.rbacService.GetUserRoles(ctx, user.ID, tenantID, nil)
        if err != nil {
            log.Errorf("Failed to get roles for user %s: %v", user.ID, err)
            continue
        }
        user.Roles = roles
    }
    
    return users, nil
}

func (tus *TenantUserService) TransferUser(ctx context.Context, userID, fromTenantID, toTenantID string) error {
    // Verify both tenants exist and are active
    fromTenant, err := tus.tenantService.GetByID(ctx, fromTenantID)
    if err != nil {
        return err
    }
    
    toTenant, err := tus.tenantService.GetByID(ctx, toTenantID)
    if err != nil {
        return err
    }
    
    if fromTenant.Status != TenantStatusActive || toTenant.Status != TenantStatusActive {
        return ErrTenantInactive
    }
    
    // Check user limit in target tenant
    if !tus.canCreateUser(ctx, toTenantID) {
        return ErrUserLimitExceeded
    }
    
    // Begin transaction
    tx, err := tus.userService.BeginTx(ctx)
    if err != nil {
        return err
    }
    defer tx.Rollback()
    
    // Remove user from source tenant
    if err := tus.rbacService.RemoveUserFromTenant(ctx, userID, fromTenantID); err != nil {
        return err
    }
    
    // Add user to target tenant
    if err := tus.rbacService.AssignRole(ctx, userID, toTenantID, "", RoleSubscriber); err != nil {
        return err
    }
    
    // Update user's default tenant
    if err := tus.userService.UpdateUserTenant(ctx, userID, toTenantID); err != nil {
        return err
    }
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        return err
    }
    
    // Publish transfer event
    tus.eventPublisher.Publish(UserTransferredEvent{
        UserID:       userID,
        FromTenantID: fromTenantID,
        ToTenantID:   toTenantID,
        TransferredAt: time.Now(),
    })
    
    return nil
}
```

## Blog Module Integration

### Content Management with Multi-tenancy
```go
type TenantBlogService struct {
    blogService    BlogService
    tenantService  TenantService
    mediaService   MediaService
    eventPublisher EventPublisher
}

func (tbs *TenantBlogService) CreatePost(ctx context.Context, websiteID string, req CreatePostRequest) (*Post, error) {
    // Get website context
    website, err := tbs.tenantService.GetWebsite(ctx, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Check post limit for website
    if !tbs.canCreatePost(ctx, websiteID) {
        return nil, ErrPostLimitExceeded
    }
    
    // Process content with tenant-specific settings
    processedContent, err := tbs.processContent(req.Content, website)
    if err != nil {
        return nil, err
    }
    
    // Create post with website context
    post, err := tbs.blogService.CreatePost(ctx, PostRequest{
        Title:     req.Title,
        Content:   processedContent,
        Slug:      req.Slug,
        Status:    req.Status,
        AuthorID:  req.AuthorID,
        WebsiteID: websiteID,
        TenantID:  website.TenantID,
        Categories: req.Categories,
        Tags:      req.Tags,
        SEO:       req.SEO,
    })
    if err != nil {
        return nil, err
    }
    
    // Process media uploads
    if len(req.MediaFiles) > 0 {
        if err := tbs.processMediaFiles(ctx, post.ID, websiteID, req.MediaFiles); err != nil {
            log.Errorf("Failed to process media files: %v", err)
        }
    }
    
    // Publish post created event
    tbs.eventPublisher.Publish(PostCreatedEvent{
        PostID:    post.ID,
        WebsiteID: websiteID,
        TenantID:  website.TenantID,
        AuthorID:  req.AuthorID,
        Status:    post.Status,
        CreatedAt: time.Now(),
    })
    
    return post, nil
}

func (tbs *TenantBlogService) GetWebsitePosts(ctx context.Context, websiteID string, filters PostFilters) ([]*Post, error) {
    // Verify website access
    if !tbs.hasWebsiteAccess(ctx, websiteID) {
        return nil, ErrAccessDenied
    }
    
    // Get posts with website filter
    posts, err := tbs.blogService.GetPosts(ctx, PostFilters{
        WebsiteID: websiteID,
        Status:    filters.Status,
        AuthorID:  filters.AuthorID,
        Category:  filters.Category,
        Tag:       filters.Tag,
        Limit:     filters.Limit,
        Offset:    filters.Offset,
    })
    if err != nil {
        return nil, err
    }
    
    // Enrich with website-specific data
    for _, post := range posts {
        post.Website = tbs.getWebsiteInfo(websiteID)
        post.URL = tbs.generatePostURL(post, websiteID)
    }
    
    return posts, nil
}

func (tbs *TenantBlogService) processContent(content string, website *Website) (string, error) {
    // Apply website-specific content transformations
    processed := content
    
    // Replace placeholders with website-specific values
    processed = strings.ReplaceAll(processed, "{{WEBSITE_NAME}}", website.Name)
    processed = strings.ReplaceAll(processed, "{{WEBSITE_URL}}", website.PrimaryDomain)
    
    // Apply content filters based on website settings
    if website.Settings["content_filter"] != nil {
        filters := website.Settings["content_filter"].(map[string]interface{})
        if filters["strip_html"].(bool) {
            processed = stripHTML(processed)
        }
        if filters["auto_link"].(bool) {
            processed = autoLink(processed)
        }
    }
    
    return processed, nil
}
```

## Notification Module Integration

### Multi-tenant Notification System
```go
type TenantNotificationService struct {
    notificationService NotificationService
    tenantService      TenantService
    templateService    TemplateService
    eventSubscriber    EventSubscriber
}

func (tns *TenantNotificationService) SendNotification(ctx context.Context, req NotificationRequest) error {
    // Get tenant and website context
    tenant, err := tns.tenantService.GetByID(ctx, req.TenantID)
    if err != nil {
        return err
    }
    
    website, err := tns.tenantService.GetWebsite(ctx, req.WebsiteID)
    if err != nil {
        return err
    }
    
    // Get tenant-specific notification template
    template, err := tns.getNotificationTemplate(req.Type, tenant.ID, website.ID)
    if err != nil {
        return err
    }
    
    // Render template with tenant/website context
    content, err := tns.renderTemplate(template, map[string]interface{}{
        "tenant":    tenant,
        "website":   website,
        "recipient": req.Recipient,
        "data":      req.Data,
    })
    if err != nil {
        return err
    }
    
    // Send notification with tenant branding
    notification := &Notification{
        Type:      req.Type,
        Recipient: req.Recipient,
        Subject:   content.Subject,
        Body:      content.Body,
        Sender: NotificationSender{
            Name:  website.Name,
            Email: fmt.Sprintf("noreply@%s", website.PrimaryDomain),
        },
        Branding: NotificationBranding{
            LogoURL:      website.Settings["logo_url"].(string),
            PrimaryColor: website.Settings["primary_color"].(string),
            FooterText:   fmt.Sprintf("© %s %s", time.Now().Year(), website.Name),
        },
    }
    
    return tns.notificationService.Send(ctx, notification)
}

func (tns *TenantNotificationService) SetupEventHandlers() {
    // Handle tenant creation
    tns.eventSubscriber.Subscribe("tenant.created", func(event Event) {
        tenantEvent := event.(*TenantCreatedEvent)
        tns.sendWelcomeNotification(tenantEvent.TenantID, tenantEvent.AdminUserID)
    })
    
    // Handle website creation
    tns.eventSubscriber.Subscribe("website.created", func(event Event) {
        websiteEvent := event.(*WebsiteCreatedEvent)
        tns.sendWebsiteCreatedNotification(websiteEvent.WebsiteID, websiteEvent.TenantID)
    })
    
    // Handle subscription changes
    tns.eventSubscriber.Subscribe("subscription.changed", func(event Event) {
        subEvent := event.(*SubscriptionChangedEvent)
        tns.sendSubscriptionNotification(subEvent.TenantID, subEvent.OldPlan, subEvent.NewPlan)
    })
    
    // Handle payment failures
    tns.eventSubscriber.Subscribe("payment.failed", func(event Event) {
        paymentEvent := event.(*PaymentFailedEvent)
        tns.sendPaymentFailureNotification(paymentEvent.TenantID, paymentEvent.Amount)
    })
}

func (tns *TenantNotificationService) getNotificationTemplate(templateType, tenantID, websiteID string) (*NotificationTemplate, error) {
    // Try website-specific template first
    template, err := tns.templateService.GetTemplate(templateType, websiteID)
    if err == nil {
        return template, nil
    }
    
    // Fallback to tenant-specific template
    template, err = tns.templateService.GetTemplate(templateType, tenantID)
    if err == nil {
        return template, nil
    }
    
    // Fallback to system default template
    return tns.templateService.GetDefaultTemplate(templateType)
}
```

## Payment Module Integration

### Subscription and Billing Integration
```go
type TenantPaymentService struct {
    paymentService PaymentService
    tenantService  TenantService
    eventPublisher EventPublisher
}

func (tps *TenantPaymentService) ProcessSubscriptionPayment(ctx context.Context, tenantID string, paymentRequest PaymentRequest) (*PaymentResult, error) {
    // Get tenant and subscription info
    tenant, err := tps.tenantService.GetByID(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    subscription, err := tps.tenantService.GetSubscription(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    // Process payment with tenant context
    paymentRequest.CustomerID = tenant.PaymentCustomerID
    paymentRequest.Metadata = map[string]string{
        "tenant_id":       tenantID,
        "subscription_id": subscription.ID,
        "plan":           string(subscription.Plan),
    }
    
    result, err := tps.paymentService.ProcessPayment(ctx, paymentRequest)
    if err != nil {
        // Handle payment failure
        tps.handlePaymentFailure(ctx, tenantID, subscription.ID, err)
        return nil, err
    }
    
    // Update subscription on successful payment
    if result.Status == PaymentStatusSucceeded {
        err = tps.updateSubscriptionAfterPayment(ctx, tenantID, subscription, result)
        if err != nil {
            log.Errorf("Failed to update subscription after payment: %v", err)
        }
        
        // Publish payment success event
        tps.eventPublisher.Publish(PaymentSucceededEvent{
            TenantID:      tenantID,
            PaymentID:     result.ID,
            Amount:        result.Amount,
            Currency:      result.Currency,
            ProcessedAt:   time.Now(),
        })
    }
    
    return result, nil
}

func (tps *TenantPaymentService) HandleWebhook(ctx context.Context, webhook PaymentWebhook) error {
    switch webhook.Type {
    case "subscription.payment_succeeded":
        return tps.handlePaymentSuccess(ctx, webhook.Data)
        
    case "subscription.payment_failed":
        return tps.handlePaymentFailure(ctx, webhook.Data.TenantID, webhook.Data.SubscriptionID, 
            fmt.Errorf("payment failed: %s", webhook.Data.FailureReason))
        
    case "subscription.cancelled":
        return tps.handleSubscriptionCancellation(ctx, webhook.Data)
        
    case "customer.subscription.updated":
        return tps.handleSubscriptionUpdate(ctx, webhook.Data)
    }
    
    return nil
}

func (tps *TenantPaymentService) handlePaymentFailure(ctx context.Context, tenantID, subscriptionID string, paymentError error) error {
    // Update subscription status
    subscription, err := tps.tenantService.GetSubscription(ctx, tenantID)
    if err != nil {
        return err
    }
    
    subscription.Status = SubscriptionStatusSuspended
    if err := tps.tenantService.UpdateSubscription(ctx, subscription); err != nil {
        return err
    }
    
    // Suspend tenant access
    if err := tps.tenantService.SuspendTenant(ctx, tenantID, "Payment failure"); err != nil {
        return err
    }
    
    // Publish payment failure event
    tps.eventPublisher.Publish(PaymentFailedEvent{
        TenantID:      tenantID,
        SubscriptionID: subscriptionID,
        Error:         paymentError.Error(),
        FailedAt:      time.Now(),
    })
    
    return nil
}
```

## Media Module Integration

### Tenant-specific Media Management
```go
type TenantMediaService struct {
    mediaService   MediaService
    tenantService  TenantService
    storageService StorageService
}

func (tms *TenantMediaService) UploadFile(ctx context.Context, websiteID string, req FileUploadRequest) (*MediaFile, error) {
    // Get website context
    website, err := tms.tenantService.GetWebsite(ctx, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Check storage limits
    if !tms.canUploadFile(ctx, website.TenantID, req.Size) {
        return nil, ErrStorageLimitExceeded
    }
    
    // Generate tenant-specific storage path
    storagePath := fmt.Sprintf("tenants/%s/websites/%s/%s", 
        website.TenantID, websiteID, req.Category)
    
    // Upload file with tenant isolation
    file, err := tms.mediaService.UploadFile(ctx, UploadRequest{
        File:        req.File,
        Filename:    req.Filename,
        ContentType: req.ContentType,
        Path:        storagePath,
        WebsiteID:   websiteID,
        TenantID:    website.TenantID,
        Metadata: map[string]interface{}{
            "uploaded_by": req.UserID,
            "website_id":  websiteID,
            "tenant_id":   website.TenantID,
        },
    })
    if err != nil {
        return nil, err
    }
    
    // Update storage usage
    tms.updateStorageUsage(ctx, website.TenantID, req.Size)
    
    return file, nil
}

func (tms *TenantMediaService) GetWebsiteFiles(ctx context.Context, websiteID string, filters MediaFilters) ([]*MediaFile, error) {
    // Verify website access
    if !tms.hasWebsiteAccess(ctx, websiteID) {
        return nil, ErrAccessDenied
    }
    
    // Get files with website filter
    files, err := tms.mediaService.GetFiles(ctx, MediaFilters{
        WebsiteID: websiteID,
        Category:  filters.Category,
        FileType:  filters.FileType,
        Limit:     filters.Limit,
        Offset:    filters.Offset,
    })
    if err != nil {
        return nil, err
    }
    
    // Generate website-specific URLs
    for _, file := range files {
        file.URL = tms.generateFileURL(file, websiteID)
        file.ThumbnailURL = tms.generateThumbnailURL(file, websiteID)
    }
    
    return files, nil
}

func (tms *TenantMediaService) canUploadFile(ctx context.Context, tenantID string, fileSize int64) bool {
    subscription, err := tms.tenantService.GetSubscription(ctx, tenantID)
    if err != nil {
        return false
    }
    
    // Get current storage usage
    usage, err := tms.getStorageUsage(ctx, tenantID)
    if err != nil {
        return false
    }
    
    // Parse storage limit
    storageLimit, err := parseStorageLimit(subscription.Limits.Storage)
    if err != nil {
        return false
    }
    
    return usage+fileSize <= storageLimit
}
```

## Event-Driven Integration

### Event System Implementation
```go
type TenantEventSystem struct {
    eventBus    EventBus
    subscribers map[string][]EventHandler
}

func (tes *TenantEventSystem) PublishEvent(event Event) error {
    return tes.eventBus.Publish(event.Type(), event)
}

func (tes *TenantEventSystem) SubscribeToEvent(eventType string, handler EventHandler) {
    if tes.subscribers[eventType] == nil {
        tes.subscribers[eventType] = []EventHandler{}
    }
    tes.subscribers[eventType] = append(tes.subscribers[eventType], handler)
}

// Define event types
type TenantCreatedEvent struct {
    TenantID    string    `json:"tenant_id"`
    TenantName  string    `json:"tenant_name"`
    AdminUserID string    `json:"admin_user_id"`
    Plan        string    `json:"plan"`
    CreatedAt   time.Time `json:"created_at"`
}

func (e TenantCreatedEvent) Type() string { return "tenant.created" }

type WebsiteCreatedEvent struct {
    WebsiteID   string    `json:"website_id"`
    TenantID    string    `json:"tenant_id"`
    WebsiteName string    `json:"website_name"`
    Domain      string    `json:"domain"`
    CreatedAt   time.Time `json:"created_at"`
}

func (e WebsiteCreatedEvent) Type() string { return "website.created" }

type SubscriptionChangedEvent struct {
    TenantID    string    `json:"tenant_id"`
    OldPlan     string    `json:"old_plan"`
    NewPlan     string    `json:"new_plan"`
    ChangedAt   time.Time `json:"changed_at"`
}

func (e SubscriptionChangedEvent) Type() string { return "subscription.changed" }

// Event handlers
func (tes *TenantEventSystem) SetupEventHandlers() {
    // Tenant creation handlers
    tes.SubscribeToEvent("tenant.created", func(event Event) error {
        e := event.(*TenantCreatedEvent)
        
        // Initialize default blog structure
        if err := tes.initializeBlogStructure(e.TenantID); err != nil {
            log.Errorf("Failed to initialize blog structure: %v", err)
        }
        
        // Setup default notification templates
        if err := tes.setupNotificationTemplates(e.TenantID); err != nil {
            log.Errorf("Failed to setup notification templates: %v", err)
        }
        
        // Send welcome notifications
        if err := tes.sendWelcomeNotifications(e.TenantID, e.AdminUserID); err != nil {
            log.Errorf("Failed to send welcome notifications: %v", err)
        }
        
        return nil
    })
    
    // Website creation handlers
    tes.SubscribeToEvent("website.created", func(event Event) error {
        e := event.(*WebsiteCreatedEvent)
        
        // Initialize website content
        if err := tes.initializeWebsiteContent(e.WebsiteID); err != nil {
            log.Errorf("Failed to initialize website content: %v", err)
        }
        
        // Setup SEO defaults
        if err := tes.setupSEODefaults(e.WebsiteID); err != nil {
            log.Errorf("Failed to setup SEO defaults: %v", err)
        }
        
        return nil
    })
    
    // Subscription change handlers
    tes.SubscribeToEvent("subscription.changed", func(event Event) error {
        e := event.(*SubscriptionChangedEvent)
        
        // Update resource limits
        if err := tes.updateResourceLimits(e.TenantID, e.NewPlan); err != nil {
            log.Errorf("Failed to update resource limits: %v", err)
        }
        
        // Enable/disable features based on plan
        if err := tes.updateFeatureAccess(e.TenantID, e.NewPlan); err != nil {
            log.Errorf("Failed to update feature access: %v", err)
        }
        
        return nil
    })
}
```

## Cross-Module Data Consistency

### Distributed Transaction Management
```go
type TenantTransactionManager struct {
    db              database.DB
    eventPublisher  EventPublisher
    compensationSvc CompensationService
}

func (ttm *TenantTransactionManager) CreateTenantWithDependencies(ctx context.Context, req CreateTenantRequest) (*Tenant, error) {
    // Start distributed transaction
    tx, err := ttm.db.BeginTx(ctx, nil)
    if err != nil {
        return nil, err
    }
    
    compensation := NewCompensationChain()
    
    // Step 1: Create tenant
    tenant, err := ttm.createTenant(ctx, tx, req)
    if err != nil {
        tx.Rollback()
        return nil, err
    }
    compensation.Add(func() error {
        return ttm.deleteTenant(context.Background(), tenant.ID)
    })
    
    // Step 2: Create default website
    website, err := ttm.createDefaultWebsite(ctx, tx, tenant.ID, req.WebsiteName)
    if err != nil {
        tx.Rollback()
        compensation.Execute()
        return nil, err
    }
    compensation.Add(func() error {
        return ttm.deleteWebsite(context.Background(), website.ID)
    })
    
    // Step 3: Create admin user
    adminUser, err := ttm.createAdminUser(ctx, tx, tenant.ID, website.ID, req.AdminUser)
    if err != nil {
        tx.Rollback()
        compensation.Execute()
        return nil, err
    }
    compensation.Add(func() error {
        return ttm.deleteUser(context.Background(), adminUser.ID)
    })
    
    // Step 4: Setup subscription
    subscription, err := ttm.createSubscription(ctx, tx, tenant.ID, req.Plan)
    if err != nil {
        tx.Rollback()
        compensation.Execute()
        return nil, err
    }
    compensation.Add(func() error {
        return ttm.cancelSubscription(context.Background(), subscription.ID)
    })
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        compensation.Execute()
        return nil, err
    }
    
    // Publish success events
    ttm.eventPublisher.Publish(TenantCreatedEvent{
        TenantID:    tenant.ID,
        TenantName:  tenant.Name,
        AdminUserID: adminUser.ID,
        Plan:        req.Plan,
        CreatedAt:   time.Now(),
    })
    
    return tenant, nil
}

type CompensationChain struct {
    actions []func() error
}

func NewCompensationChain() *CompensationChain {
    return &CompensationChain{actions: []func() error{}}
}

func (cc *CompensationChain) Add(action func() error) {
    cc.actions = append(cc.actions, action)
}

func (cc *CompensationChain) Execute() {
    // Execute compensation actions in reverse order
    for i := len(cc.actions) - 1; i >= 0; i-- {
        if err := cc.actions[i](); err != nil {
            log.Errorf("Compensation action failed: %v", err)
        }
    }
}
```

## Best Practices

### Integration Guidelines

1. **Loose Coupling**
   - Use events for async communication
   - Define clear interfaces between modules
   - Avoid direct database access across modules

2. **Data Consistency**
   - Use distributed transactions sparingly
   - Implement compensation patterns for failure scenarios
   - Design for eventual consistency

3. **Error Handling**
   - Implement circuit breakers for external dependencies
   - Use retry mechanisms with exponential backoff
   - Log errors with correlation IDs

4. **Performance**
   - Cache frequently accessed data
   - Use batch operations where possible
   - Implement proper connection pooling

5. **Security**
   - Validate all cross-module requests
   - Implement proper authentication context passing
   - Use encrypted communication for sensitive data

6. **Monitoring**
   - Track cross-module communication metrics
   - Monitor integration points for failures
   - Implement distributed tracing