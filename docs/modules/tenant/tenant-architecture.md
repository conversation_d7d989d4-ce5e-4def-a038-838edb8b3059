# Tenant Architecture - Multi-tenancy System Design

## Overview

The Tenant module provides a comprehensive multi-tenancy system for Blog API v3, enabling multiple organizations to use the same system with completely isolated data. Each tenant can have multiple websites, with each website having its own domain and settings.

## Core Architecture Principles

- **Data Isolation**: Complete separation of data between tenants and websites
- **Scalable Design**: Horizontal scaling capabilities with database sharding potential
- **Resource Management**: Per-website resource limits and monitoring
- **Security First**: Multi-layer security with proper access controls

## Data Models

### Tenant Model
```go
type Tenant struct {
    ID       string                 `json:"id" db:"id"`
    Name     string                 `json:"name" db:"name"`
    Slug     string                 `json:"slug" db:"slug"`
    Status   TenantStatus          `json:"status" db:"status"`
    Plan     string                 `json:"plan" db:"plan"`
    Settings map[string]interface{} `json:"settings" db:"settings"`
    CreatedAt time.Time            `json:"created_at" db:"created_at"`
    UpdatedAt time.Time            `json:"updated_at" db:"updated_at"`
}
```

**Fields:**
- **ID**: Unique tenant identifier
- **Name**: Display name of the tenant
- **Slug**: Short name used for subdomain
- **Status**: Current status (active, suspended, deleted)
- **Plan**: Current service plan
- **Settings**: Custom tenant settings (JSON)
- **Created/Updated**: Creation and modification timestamps

### Website Model
```go
type Website struct {
    ID       string                 `json:"id" db:"id"`
    TenantID string                 `json:"tenant_id" db:"tenant_id"`
    Name     string                 `json:"name" db:"name"`
    Slug     string                 `json:"slug" db:"slug"`
    Status   WebsiteStatus          `json:"status" db:"status"`
    Settings map[string]interface{} `json:"settings" db:"settings"`
    Theme    string                 `json:"theme" db:"theme"`
    CreatedAt time.Time            `json:"created_at" db:"created_at"`
    UpdatedAt time.Time            `json:"updated_at" db:"updated_at"`
}
```

**Fields:**
- **ID**: Unique website identifier
- **TenantID**: ID of the owning tenant
- **Name**: Display name of the website
- **Slug**: Short name used for subdomain
- **Status**: Current status (active, suspended, deleted)
- **Settings**: Custom website settings (JSON)
- **Theme**: Currently used theme
- **Created/Updated**: Creation and modification timestamps

### Subscription Model
```go
type Subscription struct {
    TenantID  string                 `json:"tenant_id" db:"tenant_id"`
    WebsiteID *string                `json:"website_id,omitempty" db:"website_id"`
    Plan      SubscriptionPlan       `json:"plan" db:"plan"`
    Status    SubscriptionStatus     `json:"status" db:"status"`
    StartDate time.Time              `json:"start_date" db:"start_date"`
    EndDate   *time.Time             `json:"end_date,omitempty" db:"end_date"`
    Limits    map[string]interface{} `json:"limits" db:"limits"`
    CreatedAt time.Time              `json:"created_at" db:"created_at"`
    UpdatedAt time.Time              `json:"updated_at" db:"updated_at"`
}
```

**Fields:**
- **TenantID**: ID of the tenant
- **WebsiteID**: ID of the website (if subscription is per website)
- **Plan**: Plan type (free, basic, premium, enterprise)
- **Status**: Subscription status
- **StartDate/EndDate**: Subscription period
- **Limits**: Resource limits configuration

### Domain Model
```go
type Domain struct {
    ID        string      `json:"id" db:"id"`
    TenantID  string      `json:"tenant_id" db:"tenant_id"`
    WebsiteID string      `json:"website_id" db:"website_id"`
    Domain    string      `json:"domain" db:"domain"`
    Type      DomainType  `json:"type" db:"type"`
    Status    DomainStatus `json:"status" db:"status"`
    SSL       SSLConfig   `json:"ssl" db:"ssl"`
    CreatedAt time.Time   `json:"created_at" db:"created_at"`
    UpdatedAt time.Time   `json:"updated_at" db:"updated_at"`
}
```

**Fields:**
- **TenantID**: ID of the tenant
- **WebsiteID**: ID of the website
- **Domain**: Domain name
- **Type**: Domain type (subdomain, custom)
- **Status**: Domain status (pending, verified, active)
- **SSL**: SSL configuration

## Module Structure

```
internal/modules/tenant/
├── models/                    # Data models
│   ├── tenant.go             # Main tenant model
│   ├── website.go            # Website model
│   ├── subscription.go       # Subscription model
│   └── domain.go            # Domain model
├── services/                 # Business logic
│   ├── tenant_service.go     # Tenant management service
│   ├── website_service.go    # Website management service
│   ├── domain_service.go     # Domain management service
│   └── billing_service.go    # Billing service
├── handlers/                 # HTTP handlers
│   ├── tenant_handler.go     # Tenant API handlers
│   ├── website_handler.go    # Website API handlers
│   ├── domain_handler.go     # Domain API handlers
│   └── subscription_handler.go # Subscription API handlers
├── repositories/             # Data access layer
│   ├── tenant_repository.go  # Tenant data access
│   ├── website_repository.go # Website data access
│   ├── domain_repository.go  # Domain data access
│   └── subscription_repository.go # Subscription data access
├── middleware/               # Tenant processing middleware
│   ├── tenant_middleware.go  # Tenant identification
│   ├── website_middleware.go # Website context
│   └── limits_middleware.go  # Resource limits checking
└── validators/               # Input validation
    ├── tenant_validator.go   # Tenant validation rules
    ├── website_validator.go  # Website validation rules
    └── domain_validator.go   # Domain validation rules
```

## System Design Patterns

### Multi-tenancy Architecture Pattern
The system uses a **hybrid multi-tenancy** approach:
- **Schema-per-tenant**: Each tenant can have separate database schemas
- **Row-level isolation**: Website-level data isolation within tenant schemas
- **Shared infrastructure**: Common services and middleware

### Website Context Resolution Flow

```mermaid
flowchart TD
    A[HTTP Request] --> B{Check Header}
    B -->|Has Website-ID| C[Use Website-ID]
    B -->|No Header| D{Check Subdomain}
    D -->|Has subdomain| E[Find website from subdomain]
    D -->|No subdomain| F{Check Custom Domain}
    F -->|Has custom domain| G[Find website from domain]
    F -->|No custom domain| H[Use default website]
    C --> I[Set website context]
    E --> I
    G --> I
    H --> I
    I --> J[Set tenant context from website]
    J --> K[Continue request processing]
```

### Tenant Creation Workflow

```mermaid
sequenceDiagram
    participant Admin as Administrator
    participant API as Tenant API
    participant DB as Database
    participant DNS as DNS Service
    participant Schema as Schema Service
    
    Admin->>API: Create new tenant
    API->>API: Validate tenant information
    API->>DB: Save tenant information
    API->>DNS: Create subdomain
    API->>Schema: Create dedicated database schema
    API->>DB: Initialize tenant data structure
    API->>Admin: Return tenant information
```

## Performance Considerations

### Database Design
- **Primary Keys**: Use UUIDs for global uniqueness
- **Indexing Strategy**: 
  - Primary index on website_id for data isolation
  - Secondary indexes on tenant_id for cross-website queries
  - Composite indexes for common query patterns

### Caching Strategy
- **Tenant Cache**: Cache tenant information with 1-hour TTL
- **Website Cache**: Cache website context with 30-minute TTL
- **Domain Cache**: Cache domain-to-website mapping with 15-minute TTL

### Connection Pooling
- **Per-tenant connections**: Separate connection pools for tenant schemas
- **Shared connections**: Common pool for metadata operations
- **Connection limits**: Configurable limits per tenant based on plan

## Scalability Design

### Horizontal Scaling
- **Database Sharding**: Potential for sharding by tenant_id or website_id
- **Load Balancing**: Website-aware load balancing
- **Service Separation**: Independent scaling of tenant services

### Resource Management
- **Per-website limits**: CPU, memory, storage, and bandwidth limits
- **Queue management**: Separate queues for different tenant plans
- **Auto-scaling**: Automatic scaling based on tenant load patterns

## Data Isolation Strategies

### Schema-level Isolation
- Each tenant gets a dedicated database schema
- Cross-tenant data access is impossible at the database level
- Schema migration management per tenant

### Row-level Isolation
- All queries automatically filtered by website_id
- Middleware ensures proper context setting
- Audit trails per website and tenant

### Access Control Layers
1. **Network Level**: VPC and firewall rules
2. **Application Level**: Middleware validation
3. **Database Level**: Schema and row-level security
4. **API Level**: Endpoint-specific authorization