# Domain Management

## Overview

Domain management is a critical component of the multi-tenant system, handling both subdomain and custom domain configurations for websites. Each website can have multiple domains, with support for automatic SSL certificate provisioning, domain verification, and DNS management.

## Domain Types and Architecture

### Domain Types
1. **Subdomain**: Automatically provided subdomains (e.g., `company.yourblog.com`)
2. **Custom Domain**: Customer-owned domains (e.g., `blog.company.com`)
3. **Apex Domain**: Root domains (e.g., `company.com`)
4. **Wildcard Subdomain**: Catch-all subdomains (e.g., `*.company.com`)

### Domain Model
```go
type Domain struct {
    ID            string                 `json:"id" db:"id"`
    TenantID      string                 `json:"tenant_id" db:"tenant_id"`
    WebsiteID     string                 `json:"website_id" db:"website_id"`
    Domain        string                 `json:"domain" db:"domain"`
    Type          DomainType            `json:"type" db:"type"`
    Status        DomainStatus          `json:"status" db:"status"`
    IsPrimary     bool                  `json:"is_primary" db:"is_primary"`
    SSL           SSLConfig             `json:"ssl" db:"ssl"`
    DNSConfig     DNSConfig             `json:"dns_config" db:"dns_config"`
    VerificationToken string            `json:"verification_token" db:"verification_token"`
    VerifiedAt    *time.Time            `json:"verified_at,omitempty" db:"verified_at"`
    CreatedAt     time.Time             `json:"created_at" db:"created_at"`
    UpdatedAt     time.Time             `json:"updated_at" db:"updated_at"`
}

type DomainType string
const (
    DomainTypeSubdomain     DomainType = "subdomain"
    DomainTypeCustom        DomainType = "custom"
    DomainTypeApex          DomainType = "apex"
    DomainTypeWildcard      DomainType = "wildcard"
)

type DomainStatus string
const (
    DomainStatusPending     DomainStatus = "pending"
    DomainStatusVerifying   DomainStatus = "verifying"
    DomainStatusVerified    DomainStatus = "verified"
    DomainStatusActive      DomainStatus = "active"
    DomainStatusFailed      DomainStatus = "failed"
    DomainStatusSuspended   DomainStatus = "suspended"
)

type SSLConfig struct {
    Provider      string    `json:"provider"`
    Status        string    `json:"status"`
    CertificateID string    `json:"certificate_id,omitempty"`
    IssuedAt      *time.Time `json:"issued_at,omitempty"`
    ExpiresAt     *time.Time `json:"expires_at,omitempty"`
    AutoRenew     bool      `json:"auto_renew"`
}

type DNSConfig struct {
    Records []DNSRecord `json:"records"`
    TTL     int         `json:"ttl"`
}

type DNSRecord struct {
    Type    string `json:"type"`
    Name    string `json:"name"`
    Value   string `json:"value"`
    TTL     int    `json:"ttl"`
    Priority int   `json:"priority,omitempty"`
}
```

## Domain Verification Process

### Verification Methods
1. **DNS TXT Record**: Add a TXT record to domain DNS
2. **Meta Tag**: Add a meta tag to website HTML
3. **File Upload**: Upload a verification file to website root
4. **Email**: Send verification link to domain admin email

### Domain Verification Flow
```mermaid
sequenceDiagram
    participant User as User
    participant API as Domain API
    participant DNS as DNS Provider
    participant Verifier as Domain Verifier
    participant SSL as SSL Provider
    
    User->>API: Add custom domain
    API->>API: Generate verification token
    API->>User: Return verification instructions
    User->>DNS: Add TXT record
    User->>API: Request verification
    API->>Verifier: Start verification process
    Verifier->>DNS: Check TXT record
    DNS-->>Verifier: TXT record found
    Verifier->>API: Verification successful
    API->>SSL: Request SSL certificate
    SSL-->>API: Certificate issued
    API->>DNS: Update DNS records
    API->>User: Domain verified and active
```

### Verification Service Implementation
```go
type DomainVerifier struct {
    dnsResolver   DNSResolver
    httpClient    HTTPClient
    emailService  EmailService
}

func (v *DomainVerifier) VerifyDomain(ctx context.Context, domain *Domain) error {
    switch domain.Type {
    case DomainTypeSubdomain:
        return v.verifySubdomain(ctx, domain)
    case DomainTypeCustom:
        return v.verifyCustomDomain(ctx, domain)
    case DomainTypeApex:
        return v.verifyApexDomain(ctx, domain)
    case DomainTypeWildcard:
        return v.verifyWildcardDomain(ctx, domain)
    default:
        return ErrUnsupportedDomainType
    }
}

func (v *DomainVerifier) verifyCustomDomain(ctx context.Context, domain *Domain) error {
    // Method 1: DNS TXT Record Verification
    txtRecord := fmt.Sprintf("yourblog-verification=%s", domain.VerificationToken)
    
    records, err := v.dnsResolver.LookupTXT(ctx, domain.Domain)
    if err != nil {
        return fmt.Errorf("DNS lookup failed: %w", err)
    }
    
    for _, record := range records {
        if record == txtRecord {
            return v.markDomainVerified(ctx, domain)
        }
    }
    
    // Method 2: Meta Tag Verification
    if v.verifyMetaTag(ctx, domain) {
        return v.markDomainVerified(ctx, domain)
    }
    
    // Method 3: File Verification
    if v.verifyFile(ctx, domain) {
        return v.markDomainVerified(ctx, domain)
    }
    
    return ErrVerificationFailed
}

func (v *DomainVerifier) verifyMetaTag(ctx context.Context, domain *Domain) bool {
    url := fmt.Sprintf("https://%s", domain.Domain)
    
    resp, err := v.httpClient.Get(url)
    if err != nil {
        return false
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return false
    }
    
    expectedMeta := fmt.Sprintf(`<meta name="yourblog-verification" content="%s">`, domain.VerificationToken)
    return strings.Contains(string(body), expectedMeta)
}

func (v *DomainVerifier) verifyFile(ctx context.Context, domain *Domain) bool {
    url := fmt.Sprintf("https://%s/.well-known/yourblog-verification.txt", domain.Domain)
    
    resp, err := v.httpClient.Get(url)
    if err != nil {
        return false
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != 200 {
        return false
    }
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return false
    }
    
    return strings.TrimSpace(string(body)) == domain.VerificationToken
}

func (v *DomainVerifier) markDomainVerified(ctx context.Context, domain *Domain) error {
    now := time.Now()
    domain.Status = DomainStatusVerified
    domain.VerifiedAt = &now
    
    // Update domain in database
    if err := updateDomain(ctx, domain); err != nil {
        return err
    }
    
    // Trigger SSL certificate issuance
    go v.requestSSLCertificate(domain)
    
    return nil
}
```

## SSL Certificate Management

### SSL Configuration and Automation
```go
type SSLManager struct {
    provider     SSLProvider
    storage      CertificateStorage
    scheduler    Scheduler
}

type SSLProvider interface {
    IssueCertificate(ctx context.Context, domain string, options CertificateOptions) (*Certificate, error)
    RenewCertificate(ctx context.Context, certificateID string) (*Certificate, error)
    RevokeCertificate(ctx context.Context, certificateID string) error
    GetCertificate(ctx context.Context, certificateID string) (*Certificate, error)
}

type Certificate struct {
    ID          string    `json:"id"`
    Domain      string    `json:"domain"`
    Certificate string    `json:"certificate"`
    PrivateKey  string    `json:"private_key"`
    Chain       string    `json:"chain"`
    IssuedAt    time.Time `json:"issued_at"`
    ExpiresAt   time.Time `json:"expires_at"`
}

func (m *SSLManager) RequestCertificate(ctx context.Context, domain *Domain) error {
    // Check if domain is verified
    if domain.Status != DomainStatusVerified {
        return ErrDomainNotVerified
    }
    
    // Request certificate from provider
    cert, err := m.provider.IssueCertificate(ctx, domain.Domain, CertificateOptions{
        Type: "domain_validated",
        SANs: []string{fmt.Sprintf("www.%s", domain.Domain)},
    })
    if err != nil {
        return fmt.Errorf("certificate issuance failed: %w", err)
    }
    
    // Store certificate
    if err := m.storage.StoreCertificate(ctx, cert); err != nil {
        return fmt.Errorf("certificate storage failed: %w", err)
    }
    
    // Update domain SSL config
    domain.SSL = SSLConfig{
        Provider:      m.provider.Name(),
        Status:        "active",
        CertificateID: cert.ID,
        IssuedAt:      &cert.IssuedAt,
        ExpiresAt:     &cert.ExpiresAt,
        AutoRenew:     true,
    }
    
    // Update domain status to active
    domain.Status = DomainStatusActive
    
    return updateDomain(ctx, domain)
}

func (m *SSLManager) SetupAutoRenewal() {
    m.scheduler.AddJob("ssl-renewal-check", "0 2 * * *", func() {
        m.checkExpiringCertificates()
    })
}

func (m *SSLManager) checkExpiringCertificates() {
    // Find certificates expiring in 30 days
    expiringDate := time.Now().AddDate(0, 0, 30)
    
    domains, err := getDomainsWithExpiringSSL(expiringDate)
    if err != nil {
        log.Errorf("Failed to get expiring certificates: %v", err)
        return
    }
    
    for _, domain := range domains {
        go func(d *Domain) {
            if err := m.renewCertificate(context.Background(), d); err != nil {
                log.Errorf("Failed to renew certificate for domain %s: %v", d.Domain, err)
                
                // Send alert if renewal fails
                m.sendRenewalAlert(d)
            }
        }(domain)
    }
}

func (m *SSLManager) renewCertificate(ctx context.Context, domain *Domain) error {
    cert, err := m.provider.RenewCertificate(ctx, domain.SSL.CertificateID)
    if err != nil {
        return err
    }
    
    // Update certificate storage
    if err := m.storage.StoreCertificate(ctx, cert); err != nil {
        return err
    }
    
    // Update domain SSL config
    domain.SSL.IssuedAt = &cert.IssuedAt
    domain.SSL.ExpiresAt = &cert.ExpiresAt
    
    return updateDomain(ctx, domain)
}
```

## DNS Management

### DNS Service Integration
```go
type DNSManager struct {
    providers map[string]DNSProvider
    resolver  DNSResolver
}

type DNSProvider interface {
    CreateRecord(ctx context.Context, zone string, record DNSRecord) error
    UpdateRecord(ctx context.Context, zone string, recordID string, record DNSRecord) error
    DeleteRecord(ctx context.Context, zone string, recordID string) error
    ListRecords(ctx context.Context, zone string) ([]DNSRecord, error)
}

func (m *DNSManager) SetupDomainDNS(ctx context.Context, domain *Domain, websiteConfig WebsiteConfig) error {
    // Determine required DNS records
    records := m.generateDNSRecords(domain, websiteConfig)
    
    // Get appropriate DNS provider
    provider, err := m.getProviderForDomain(domain.Domain)
    if err != nil {
        return err
    }
    
    // Create DNS records
    for _, record := range records {
        if err := provider.CreateRecord(ctx, domain.Domain, record); err != nil {
            return fmt.Errorf("failed to create DNS record %s: %w", record.Name, err)
        }
    }
    
    // Update domain DNS config
    domain.DNSConfig = DNSConfig{
        Records: records,
        TTL:     300,
    }
    
    return updateDomain(ctx, domain)
}

func (m *DNSManager) generateDNSRecords(domain *Domain, config WebsiteConfig) []DNSRecord {
    var records []DNSRecord
    
    // A record pointing to load balancer
    records = append(records, DNSRecord{
        Type:  "A",
        Name:  "@",
        Value: config.LoadBalancerIP,
        TTL:   300,
    })
    
    // CNAME for www subdomain
    if domain.Type != DomainTypeWildcard {
        records = append(records, DNSRecord{
            Type:  "CNAME",
            Name:  "www",
            Value: domain.Domain + ".",
            TTL:   300,
        })
    }
    
    // TXT record for verification
    records = append(records, DNSRecord{
        Type:  "TXT",
        Name:  "@",
        Value: fmt.Sprintf("yourblog-verification=%s", domain.VerificationToken),
        TTL:   300,
    })
    
    // MX records for email (if enabled)
    if config.EmailEnabled {
        records = append(records, DNSRecord{
            Type:     "MX",
            Name:     "@",
            Value:    "mail.yourblog.com.",
            TTL:      300,
            Priority: 10,
        })
    }
    
    return records
}
```

## Subdomain vs Custom Domain Handling

### Subdomain Management
```go
type SubdomainManager struct {
    dnsProvider DNSProvider
    baseDomain  string
    patterns    map[string]string
}

func (s *SubdomainManager) CreateSubdomain(ctx context.Context, website *Website) (*Domain, error) {
    // Generate subdomain from website slug
    subdomain := s.generateSubdomain(website.Slug)
    fullDomain := fmt.Sprintf("%s.%s", subdomain, s.baseDomain)
    
    // Check if subdomain is available
    if !s.isSubdomainAvailable(ctx, subdomain) {
        return nil, ErrSubdomainTaken
    }
    
    // Create domain record
    domain := &Domain{
        ID:        generateDomainID(),
        TenantID:  website.TenantID,
        WebsiteID: website.ID,
        Domain:    fullDomain,
        Type:      DomainTypeSubdomain,
        Status:    DomainStatusActive, // Subdomains are automatically verified
        IsPrimary: true,
        SSL: SSLConfig{
            Provider:  "letsencrypt",
            Status:    "active",
            AutoRenew: true,
        },
    }
    
    // Create DNS record
    record := DNSRecord{
        Type:  "CNAME",
        Name:  subdomain,
        Value: "app.yourblog.com.",
        TTL:   300,
    }
    
    if err := s.dnsProvider.CreateRecord(ctx, s.baseDomain, record); err != nil {
        return nil, fmt.Errorf("failed to create DNS record: %w", err)
    }
    
    // Request wildcard SSL certificate if needed
    if err := s.ensureWildcardSSL(ctx); err != nil {
        log.Errorf("Failed to ensure wildcard SSL: %v", err)
    }
    
    return domain, nil
}

func (s *SubdomainManager) generateSubdomain(slug string) string {
    // Sanitize slug for DNS compliance
    subdomain := strings.ToLower(slug)
    subdomain = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(subdomain, "-")
    subdomain = strings.Trim(subdomain, "-")
    
    // Ensure minimum length
    if len(subdomain) < 3 {
        subdomain = subdomain + "-site"
    }
    
    // Ensure maximum length (63 characters for DNS)
    if len(subdomain) > 63 {
        subdomain = subdomain[:60] + randomString(3)
    }
    
    return subdomain
}

func (s *SubdomainManager) isSubdomainAvailable(ctx context.Context, subdomain string) bool {
    // Check in database
    exists, err := domainExists(ctx, fmt.Sprintf("%s.%s", subdomain, s.baseDomain))
    if err != nil || exists {
        return false
    }
    
    // Check reserved subdomains
    reserved := []string{"www", "api", "admin", "app", "mail", "ftp", "blog", "shop"}
    for _, r := range reserved {
        if subdomain == r {
            return false
        }
    }
    
    return true
}
```

### Custom Domain Validation
```go
func ValidateCustomDomain(domain string) error {
    // Basic format validation
    if len(domain) == 0 {
        return ErrEmptyDomain
    }
    
    if len(domain) > 253 {
        return ErrDomainTooLong
    }
    
    // Check for valid characters
    validDomain := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`)
    if !validDomain.MatchString(domain) {
        return ErrInvalidDomainFormat
    }
    
    // Check for blocked domains
    if isBlockedDomain(domain) {
        return ErrDomainBlocked
    }
    
    // Check if domain already exists
    if domainExists(context.Background(), domain) {
        return ErrDomainAlreadyExists
    }
    
    // Check domain reputation
    if !hasGoodReputation(domain) {
        return ErrDomainBadReputation
    }
    
    return nil
}

func isBlockedDomain(domain string) bool {
    blockedDomains := []string{
        "localhost",
        "127.0.0.1",
        "0.0.0.0",
        "*.local",
        "*.test",
        "*.invalid",
    }
    
    for _, blocked := range blockedDomains {
        if matched, _ := filepath.Match(blocked, domain); matched {
            return true
        }
    }
    
    return false
}
```

## Domain Health Monitoring

### Health Check System
```go
type DomainHealthChecker struct {
    httpClient   HTTPClient
    dnsResolver  DNSResolver
    sslChecker   SSLChecker
    alertManager AlertManager
}

func (h *DomainHealthChecker) StartMonitoring() {
    // Check domain health every 5 minutes
    ticker := time.NewTicker(5 * time.Minute)
    go func() {
        for range ticker.C {
            h.checkAllDomains()
        }
    }()
}

func (h *DomainHealthChecker) checkAllDomains() {
    domains, err := getActiveDomains()
    if err != nil {
        log.Errorf("Failed to get active domains: %v", err)
        return
    }
    
    for _, domain := range domains {
        go h.checkDomainHealth(domain)
    }
}

func (h *DomainHealthChecker) checkDomainHealth(domain *Domain) {
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    health := DomainHealth{
        DomainID:  domain.ID,
        CheckedAt: time.Now(),
    }
    
    // DNS resolution check
    health.DNSResolution = h.checkDNSResolution(ctx, domain.Domain)
    
    // HTTP availability check
    health.HTTPAvailability = h.checkHTTPAvailability(ctx, domain.Domain)
    
    // SSL certificate check
    health.SSLStatus = h.checkSSLStatus(ctx, domain.Domain)
    
    // Calculate overall health score
    health.Score = h.calculateHealthScore(health)
    
    // Save health status
    saveHealthStatus(ctx, health)
    
    // Send alerts if needed
    if health.Score < 70 {
        h.alertManager.SendDomainHealthAlert(domain, health)
    }
}

func (h *DomainHealthChecker) checkDNSResolution(ctx context.Context, domain string) DNSHealthStatus {
    start := time.Now()
    
    ips, err := h.dnsResolver.LookupIPAddr(ctx, domain)
    if err != nil {
        return DNSHealthStatus{
            Status:     "failed",
            Error:      err.Error(),
            Latency:    time.Since(start),
        }
    }
    
    return DNSHealthStatus{
        Status:      "healthy",
        ResolvedIPs: ipsToStrings(ips),
        Latency:     time.Since(start),
    }
}

func (h *DomainHealthChecker) checkHTTPAvailability(ctx context.Context, domain string) HTTPHealthStatus {
    url := fmt.Sprintf("https://%s", domain)
    start := time.Now()
    
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return HTTPHealthStatus{Status: "failed", Error: err.Error()}
    }
    
    resp, err := h.httpClient.Do(req)
    if err != nil {
        return HTTPHealthStatus{
            Status:   "failed",
            Error:    err.Error(),
            Latency:  time.Since(start),
        }
    }
    defer resp.Body.Close()
    
    return HTTPHealthStatus{
        Status:     "healthy",
        StatusCode: resp.StatusCode,
        Latency:    time.Since(start),
    }
}
```

## Best Practices

### Domain Security
1. **Verification**: Always verify domain ownership before activation
2. **SSL Enforcement**: Require SSL certificates for all custom domains
3. **DNS Security**: Implement DNSSEC where possible
4. **Rate Limiting**: Limit domain verification attempts to prevent abuse

### Performance Optimization
1. **DNS Caching**: Implement intelligent DNS caching strategies
2. **Certificate Caching**: Cache SSL certificates to reduce lookup time
3. **Health Monitoring**: Continuous monitoring of domain health
4. **Load Balancing**: Distribute traffic across multiple endpoints

### Operational Excellence
1. **Automated Renewal**: Automate SSL certificate renewal
2. **Monitoring**: Set up comprehensive monitoring and alerting
3. **Backup DNS**: Maintain backup DNS providers for redundancy
4. **Documentation**: Keep detailed records of domain configurations