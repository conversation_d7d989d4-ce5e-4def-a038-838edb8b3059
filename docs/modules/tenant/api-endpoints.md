# API Endpoints Documentation

## Overview

This document provides comprehensive documentation for all Tenant module API endpoints, including request/response examples, authentication requirements, and error handling.

## Authentication

All API endpoints require authentication via JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

The JWT token must contain:
- `tenant_id`: ID of the current tenant
- `website_id`: ID of the current website (for website-specific operations)
- `user_id`: ID of the authenticated user
- `roles`: User roles and permissions

## Base URLs

- **CMS API**: `/api/cms/v1`
- **Frontend API**: `/api/frontend/v1`
- **Admin API**: `/api/admin/v1`

## Tenant Management Endpoints

### List Tenants
```http
GET /api/cms/v1/tenants
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20, max: 100)
- `status` (string): Filter by status (active, suspended, deleted)
- `plan` (string): Filter by subscription plan
- `search` (string): Search by name or slug

**Response:**
```json
{
  "data": [
    {
      "id": "tn_1234567890",
      "name": "Acme Corporation",
      "slug": "acme-corp",
      "status": "active",
      "plan": "premium",
      "settings": {
        "timezone": "UTC",
        "language": "en",
        "features": {
          "analytics": true,
          "custom_domains": true
        }
      },
      "subscription": {
        "plan": "premium",
        "status": "active",
        "next_billing_date": "2024-02-15T00:00:00Z"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z"
    }
  ],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": null,
    "has_more": true,
    "has_previous": false,
    "limit": 20
  }
}
```

### Create Tenant
```http
POST /api/cms/v1/tenants
```

**Request Body:**
```json
{
  "name": "New Company",
  "slug": "new-company",
  "plan": "basic",
  "settings": {
    "timezone": "UTC",
    "language": "en"
  },
  "admin_user": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "password": "secure_password"
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "tn_9876543210",
    "name": "New Company",
    "slug": "new-company",
    "status": "active",
    "plan": "basic",
    "settings": {
      "timezone": "UTC",
      "language": "en"
    },
    "default_website": {
      "id": "ws_1111111111",
      "name": "New Company Blog",
      "slug": "main",
      "domain": "new-company.yourblog.com"
    },
    "admin_user": {
      "id": "usr_2222222222",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "created_at": "2024-01-25T09:15:00Z"
  }
}
```

### Get Tenant Details
```http
GET /api/cms/v1/tenants/{tenant_id}
```

**Response:**
```json
{
  "data": {
    "id": "tn_1234567890",
    "name": "Acme Corporation",
    "slug": "acme-corp",
    "status": "active",
    "plan": "premium",
    "settings": {
      "timezone": "UTC",
      "language": "en",
      "branding": {
        "logo_url": "https://cdn.example.com/logo.png",
        "primary_color": "#007bff"
      }
    },
    "subscription": {
      "id": "sub_1111111111",
      "plan": "premium",
      "status": "active",
      "current_period_start": "2024-01-01T00:00:00Z",
      "current_period_end": "2024-02-01T00:00:00Z",
      "limits": {
        "users": 50,
        "posts": 5000,
        "storage": "100GB",
        "bandwidth": "1TB",
        "websites": 10
      }
    },
    "websites": [
      {
        "id": "ws_1111111111",
        "name": "Main Blog",
        "slug": "main",
        "domain": "acme-corp.yourblog.com",
        "status": "active"
      }
    ],
    "usage": {
      "users": 25,
      "posts": 1250,
      "storage": "45GB",
      "bandwidth": "250GB",
      "websites": 3
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

### Update Tenant
```http
PUT /api/cms/v1/tenants/{tenant_id}
```

**Request Body:**
```json
{
  "name": "Updated Company Name",
  "settings": {
    "timezone": "America/New_York",
    "language": "en",
    "branding": {
      "logo_url": "https://cdn.example.com/new-logo.png",
      "primary_color": "#28a745"
    }
  }
}
```

### Delete Tenant
```http
DELETE /api/cms/v1/tenants/{tenant_id}
```

**Query Parameters:**
- `force` (boolean): Permanently delete (default: false for status-based soft delete)

**Response:**
```json
{
  "message": "Tenant deleted successfully",
  "data": {
    "id": "tn_1234567890",
    "status": "deleted",
    "status_updated_at": "2024-01-25T16:30:00Z"
  }
}
```

## Website Management Endpoints

### List Websites
```http
GET /api/cms/v1/tenants/{tenant_id}/websites
```

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status` (string): Filter by status
- `search` (string): Search by name or slug

**Response:**
```json
{
  "data": [
    {
      "id": "ws_1111111111",
      "tenant_id": "tn_1234567890",
      "name": "Main Blog",
      "slug": "main",
      "status": "active",
      "theme": "corporate-theme",
      "domains": [
        {
          "id": "dm_1111111111",
          "domain": "acme-corp.yourblog.com",
          "type": "subdomain",
          "is_primary": true,
          "status": "active"
        }
      ],
      "settings": {
        "language": "en",
        "seo": {
          "meta_title": "Acme Corporation Blog"
        }
      },
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Create Website
```http
POST /api/cms/v1/tenants/{tenant_id}/websites
```

**Request Body:**
```json
{
  "name": "Product Blog",
  "slug": "products",
  "theme": "modern-theme",
  "settings": {
    "language": "en",
    "timezone": "UTC",
    "seo": {
      "meta_title": "Product Updates & News",
      "meta_description": "Latest product updates and company news"
    },
    "features": {
      "comments_enabled": true,
      "social_sharing": true
    }
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "ws_2222222222",
    "tenant_id": "tn_1234567890",
    "name": "Product Blog",
    "slug": "products",
    "status": "active",
    "theme": "modern-theme",
    "domains": [
      {
        "id": "dm_2222222222",
        "domain": "products-acme-corp.yourblog.com",
        "type": "subdomain",
        "is_primary": true,
        "status": "active"
      }
    ],
    "created_at": "2024-01-25T11:00:00Z"
  }
}
```

### Get Website Details
```http
GET /api/cms/v1/websites/{website_id}
```

### Update Website
```http
PUT /api/cms/v1/websites/{website_id}
```

### Delete Website
```http
DELETE /api/cms/v1/websites/{website_id}
```

## Domain Management Endpoints

### List Domains
```http
GET /api/cms/v1/tenants/{tenant_id}/domains
```

**Query Parameters:**
- `website_id` (string): Filter by website
- `type` (string): Filter by domain type (subdomain, custom)
- `status` (string): Filter by status

**Response:**
```json
{
  "data": [
    {
      "id": "dm_1111111111",
      "tenant_id": "tn_1234567890",
      "website_id": "ws_1111111111",
      "domain": "acme-corp.yourblog.com",
      "type": "subdomain",
      "status": "active",
      "is_primary": true,
      "ssl": {
        "provider": "letsencrypt",
        "status": "active",
        "issued_at": "2024-01-15T10:30:00Z",
        "expires_at": "2024-04-15T10:30:00Z"
      },
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Add Custom Domain
```http
POST /api/cms/v1/tenants/{tenant_id}/domains
```

**Request Body:**
```json
{
  "website_id": "ws_1111111111",
  "domain": "blog.acme.com",
  "type": "custom"
}
```

**Response:**
```json
{
  "data": {
    "id": "dm_3333333333",
    "tenant_id": "tn_1234567890",
    "website_id": "ws_1111111111",
    "domain": "blog.acme.com",
    "type": "custom",
    "status": "pending",
    "verification_token": "abc123xyz789",
    "verification_methods": [
      {
        "type": "dns_txt",
        "instructions": "Add this TXT record to your DNS: yourblog-verification=abc123xyz789"
      },
      {
        "type": "meta_tag",
        "instructions": "Add this meta tag to your website: <meta name=\"yourblog-verification\" content=\"abc123xyz789\">"
      },
      {
        "type": "file_upload",
        "instructions": "Upload a file with content 'abc123xyz789' to https://blog.acme.com/.well-known/yourblog-verification.txt"
      }
    ],
    "created_at": "2024-01-25T12:00:00Z"
  }
}
```

### Verify Domain
```http
POST /api/cms/v1/domains/{domain_id}/verify
```

**Response:**
```json
{
  "data": {
    "id": "dm_3333333333",
    "status": "verified",
    "verified_at": "2024-01-25T12:30:00Z",
    "ssl": {
      "status": "provisioning"
    }
  }
}
```

### Update Domain
```http
PUT /api/cms/v1/domains/{domain_id}
```

**Request Body:**
```json
{
  "is_primary": true
}
```

### Delete Domain
```http
DELETE /api/cms/v1/domains/{domain_id}
```

## Subscription Management Endpoints

### Get Subscription
```http
GET /api/cms/v1/tenants/{tenant_id}/subscription
```

**Response:**
```json
{
  "data": {
    "id": "sub_1111111111",
    "tenant_id": "tn_1234567890",
    "plan": "premium",
    "status": "active",
    "billing_cycle": "monthly",
    "price": 99.00,
    "currency": "USD",
    "current_period_start": "2024-01-01T00:00:00Z",
    "current_period_end": "2024-02-01T00:00:00Z",
    "next_billing_date": "2024-02-01T00:00:00Z",
    "limits": {
      "users": 50,
      "posts": 5000,
      "storage": "100GB",
      "bandwidth": "1TB",
      "websites": 10,
      "custom_domains": 5
    },
    "features": [
      "all_themes",
      "priority_support",
      "custom_css",
      "analytics",
      "white_label"
    ],
    "usage": {
      "users": 25,
      "posts": 1250,
      "storage": "45GB",
      "bandwidth": "250GB",
      "websites": 3,
      "custom_domains": 2
    }
  }
}
```

### Create Subscription
```http
POST /api/cms/v1/tenants/{tenant_id}/subscription
```

**Request Body:**
```json
{
  "plan": "premium",
  "billing_cycle": "monthly",
  "payment_method_id": "pm_1234567890"
}
```

### Update Subscription
```http
PUT /api/cms/v1/tenants/{tenant_id}/subscription
```

**Request Body:**
```json
{
  "plan": "enterprise",
  "billing_cycle": "yearly"
}
```

### Cancel Subscription
```http
DELETE /api/cms/v1/tenants/{tenant_id}/subscription
```

**Request Body:**
```json
{
  "reason": "switching_providers",
  "feedback": "Found a better solution for our needs",
  "cancel_immediately": false
}
```

## Usage and Analytics Endpoints

### Get Usage Statistics
```http
GET /api/cms/v1/tenants/{tenant_id}/usage
```

**Query Parameters:**
- `website_id` (string): Filter by specific website
- `period` (string): Time period (day, week, month, year)
- `start_date` (string): Start date (ISO 8601)
- `end_date` (string): End date (ISO 8601)

**Response:**
```json
{
  "data": {
    "tenant_id": "tn_1234567890",
    "period": "month",
    "current_usage": {
      "users": 25,
      "posts": 1250,
      "storage": "45GB",
      "bandwidth": "250GB",
      "api_requests": 125000
    },
    "limits": {
      "users": 50,
      "posts": 5000,
      "storage": "100GB",
      "bandwidth": "1TB",
      "api_requests": 1000000
    },
    "usage_percentage": {
      "users": 50,
      "posts": 25,
      "storage": 45,
      "bandwidth": 25,
      "api_requests": 12.5
    },
    "websites": [
      {
        "website_id": "ws_1111111111",
        "name": "Main Blog",
        "usage": {
          "posts": 800,
          "storage": "30GB",
          "bandwidth": "180GB",
          "page_views": 50000,
          "unique_visitors": 12000
        }
      }
    ]
  }
}
```

### Get Analytics Data
```http
GET /api/cms/v1/websites/{website_id}/analytics
```

**Query Parameters:**
- `metric` (string): Metric type (page_views, visitors, bandwidth, etc.)
- `period` (string): Time period
- `start_date` (string): Start date
- `end_date` (string): End date

**Response:**
```json
{
  "data": {
    "website_id": "ws_1111111111",
    "metric": "page_views",
    "period": "month",
    "total": 50000,
    "data_points": [
      {
        "date": "2024-01-01",
        "value": 1500
      },
      {
        "date": "2024-01-02",
        "value": 1800
      }
    ]
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request validation failed",
    "details": [
      {
        "field": "name",
        "message": "Name is required"
      }
    ],
    "request_id": "req_1234567890"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTHENTICATION_REQUIRED` | 401 | Authentication token missing or invalid |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource not found |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `QUOTA_EXCEEDED` | 429 | Resource quota exceeded |
| `DOMAIN_ALREADY_EXISTS` | 409 | Domain already exists |
| `SUBSCRIPTION_REQUIRED` | 402 | Valid subscription required |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Rate Limiting

API endpoints are rate limited based on subscription plan:

| Plan | Rate Limit | Burst Limit |
|------|------------|-------------|
| Free | 100 req/min | 200 req |
| Basic | 500 req/min | 1000 req |
| Premium | 2000 req/min | 4000 req |
| Enterprise | 10000 req/min | 20000 req |

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 500
X-RateLimit-Remaining: 487
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page`: Page number (starting from 1)
- `limit`: Items per page (max 100)

**Response Format:**
```json
{
  "data": [...],
  "meta": {
    "next_cursor": "eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==",
    "previous_cursor": null,
    "has_more": true,
    "has_previous": false,
    "limit": 20
  }
}
```

## Filtering and Sorting

Many endpoints support filtering and sorting:

**Filter Parameters:**
- `status`: Filter by status
- `created_after`: Items created after date
- `created_before`: Items created before date

**Sort Parameters:**
- `sort`: Field to sort by
- `order`: Sort order (asc, desc)

Example:
```
GET /api/cms/v1/tenants?status=active&sort=created_at&order=desc
```