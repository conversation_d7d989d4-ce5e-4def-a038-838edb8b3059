# Frontend Rendering

## Tổng quan

Frontend Rendering system chịu trách nhiệm render website cho visitors với hi<PERSON>u suất cao, hỗ trợ static site generation, caching strategies, và performance optimization. Hệ thống này đảm bảo website load nhanh và SEO-friendly.

## Rendering Strategies

### Static Site Generation (SSG)

```mermaid
flowchart LR
    A[Content thay đổi] --> B[Trigger build]
    B --> C[Generate static files]
    C --> D[Optimize assets]
    D --> E[Deploy to CDN]
    E --> F[Update cache]
```

### Server-Side Rendering (SSR)

```go
type SSRRenderer struct {
    templateEngine TemplateEngine
    cache         CacheService
    websiteRepo   WebsiteRepository
}

func (r *SSRRenderer) RenderPage(websiteID int64, slug string, context RenderContext) (*RenderedPage, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("page:ssr:%d:%s", websiteID, slug)
    if cached, found := r.cache.Get(cacheKey); found {
        return cached.(*RenderedPage), nil
    }
    
    // Get page data
    page, err := r.pageService.GetPageBySlugForWebsite(websiteID, slug)
    if err != nil {
        return nil, err
    }
    
    // Get website theme
    theme, err := r.themeService.GetActiveThemeForWebsite(websiteID)
    if err != nil {
        return nil, err
    }
    
    // Render page
    html, err := r.renderPageWithTheme(page, theme, context)
    if err != nil {
        return nil, err
    }
    
    rendered := &RenderedPage{
        HTML:         html,
        StatusCode:   200,
        Headers:      r.buildHeaders(page),
        CacheControl: "public, max-age=300",
    }
    
    // Cache the result
    r.cache.Set(cacheKey, rendered, 5*time.Minute)
    
    return rendered, nil
}
```

### Client-Side Rendering (CSR)

```javascript
class WebsiteRenderer {
    constructor(websiteConfig) {
        this.config = websiteConfig;
        this.blockRenderers = new Map();
        this.init();
    }
    
    init() {
        this.registerBlockRenderers();
        this.setupLazyLoading();
        this.initializeAnalytics();
    }
    
    async renderPage(pageData) {
        const container = document.getElementById('page-content');
        container.innerHTML = '';
        
        // Render blocks sequentially
        for (const block of pageData.content.blocks) {
            const blockElement = await this.renderBlock(block);
            container.appendChild(blockElement);
        }
        
        // Initialize interactive components
        this.initializeComponents(container);
        
        // Update meta tags
        this.updateMetaTags(pageData.seo);
        
        // Track page view
        this.trackPageView(pageData);
    }
    
    async renderBlock(block) {
        const renderer = this.blockRenderers.get(block.type);
        if (!renderer) {
            console.warn(`No renderer found for block type: ${block.type}`);
            return this.createErrorBlock(block);
        }
        
        return await renderer.render(block);
    }
}
```

## Template System

### Template Engine Implementation

```go
type TemplateEngine struct {
    templates *template.Template
    functions template.FuncMap
    cache     map[string]*template.Template
}

func NewTemplateEngine() *TemplateEngine {
    engine := &TemplateEngine{
        cache: make(map[string]*template.Template),
        functions: template.FuncMap{
            "formatDate": formatDate,
            "truncate":   truncateText,
            "markdown":   renderMarkdown,
            "asset":      getAssetURL,
            "json":       toJSON,
        },
    }
    
    return engine
}

func (e *TemplateEngine) RenderTemplate(templateName string, data interface{}) (string, error) {
    // Check cache
    if tmpl, exists := e.cache[templateName]; exists {
        return e.executeTemplate(tmpl, data)
    }
    
    // Load and parse template
    tmpl, err := e.loadTemplate(templateName)
    if err != nil {
        return "", err
    }
    
    // Cache compiled template
    e.cache[templateName] = tmpl
    
    return e.executeTemplate(tmpl, data)
}

func (e *TemplateEngine) executeTemplate(tmpl *template.Template, data interface{}) (string, error) {
    var buf bytes.Buffer
    if err := tmpl.Execute(&buf, data); err != nil {
        return "", err
    }
    return buf.String(), nil
}
```

### Block Rendering System

```go
type BlockRenderer interface {
    Render(block Block, context RenderContext) (string, error)
    CanRender(blockType string) bool
}

type TextBlockRenderer struct {
    markdown MarkdownProcessor
}

func (r *TextBlockRenderer) Render(block Block, context RenderContext) (string, error) {
    content := block.Attributes["content"].(string)
    alignment := block.Attributes["alignment"].(string)
    
    // Process markdown if needed
    if strings.Contains(content, "**") || strings.Contains(content, "*") {
        content = r.markdown.Process(content)
    }
    
    return fmt.Sprintf(`<div class="text-block text-%s">%s</div>`, alignment, content), nil
}

type ImageBlockRenderer struct {
    mediaService MediaService
}

func (r *ImageBlockRenderer) Render(block Block, context RenderContext) (string, error) {
    src := block.Attributes["src"].(string)
    alt := block.Attributes["alt"].(string)
    caption := block.Attributes["caption"].(string)
    
    // Get optimized image URLs
    srcset := r.mediaService.GetResponsiveSrcSet(src, context.DeviceInfo)
    
    html := fmt.Sprintf(`
        <figure class="image-block">
            <img src="%s" srcset="%s" alt="%s" loading="lazy">
            %s
        </figure>
    `, src, srcset, alt, r.renderCaption(caption))
    
    return html, nil
}
```

## Caching Strategy

### Multi-Level Caching

```go
type CacheManager struct {
    levels []CacheLevel
}

type CacheLevel interface {
    Get(key string) (interface{}, bool)
    Set(key string, value interface{}, ttl time.Duration)
    Delete(key string)
}

// L1: In-memory cache
type MemoryCache struct {
    data map[string]*CacheItem
    mu   sync.RWMutex
}

// L2: Redis cache
type RedisCache struct {
    client *redis.Client
}

// L3: Database cache
type DatabaseCache struct {
    db *gorm.DB
}

func (m *CacheManager) Get(key string) (interface{}, bool) {
    for _, level := range m.levels {
        if value, found := level.Get(key); found {
            // Propagate to higher levels
            m.propagateUp(key, value, level)
            return value, true
        }
    }
    return nil, false
}

func (m *CacheManager) Set(key string, value interface{}, ttl time.Duration) {
    // Set in all levels
    for _, level := range m.levels {
        level.Set(key, value, ttl)
    }
}
```

### Cache Invalidation

```go
type CacheInvalidator struct {
    cache    CacheManager
    patterns map[string][]string
}

func (c *CacheInvalidator) InvalidatePageCache(pageID int64) error {
    patterns := []string{
        fmt.Sprintf("page:*:%d", pageID),
        fmt.Sprintf("page:html:%d:*", pageID),
        fmt.Sprintf("block:*:%d:*", pageID),
    }
    
    for _, pattern := range patterns {
        if err := c.cache.DeleteByPattern(pattern); err != nil {
            return err
        }
    }
    
    return nil
}

func (c *CacheInvalidator) InvalidateWebsiteCache(websiteID int64) error {
    patterns := []string{
        fmt.Sprintf("website:%d:*", websiteID),
        fmt.Sprintf("page:*:%d:*", websiteID),
        fmt.Sprintf("theme:%d:*", websiteID),
        fmt.Sprintf("menu:%d:*", websiteID),
    }
    
    for _, pattern := range patterns {
        if err := c.cache.DeleteByPattern(pattern); err != nil {
            return err
        }
    }
    
    return nil
}
```

## Asset Optimization

### Asset Pipeline

```go
type AssetPipeline struct {
    processors []AssetProcessor
    minifier   Minifier
    hasher     AssetHasher
}

type AssetProcessor interface {
    Process(asset *Asset) (*Asset, error)
    CanProcess(mimeType string) bool
}

func (p *AssetPipeline) ProcessAssets(assets []*Asset) ([]*Asset, error) {
    var processed []*Asset
    
    for _, asset := range assets {
        // Find appropriate processor
        processor := p.findProcessor(asset.MimeType)
        if processor == nil {
            processed = append(processed, asset)
            continue
        }
        
        // Process asset
        processedAsset, err := processor.Process(asset)
        if err != nil {
            return nil, err
        }
        
        // Minify if needed
        if p.shouldMinify(asset.MimeType) {
            processedAsset, err = p.minifier.Minify(processedAsset)
            if err != nil {
                return nil, err
            }
        }
        
        // Generate hash for cache busting
        processedAsset.Hash = p.hasher.Generate(processedAsset.Content)
        processedAsset.URL = fmt.Sprintf("%s?v=%s", processedAsset.URL, processedAsset.Hash)
        
        processed = append(processed, processedAsset)
    }
    
    return processed, nil
}
```

### CSS Optimization

```go
type CSSProcessor struct {
    purger    CSSPurger
    autoprefixer Autoprefixer
}

func (p *CSSProcessor) Process(asset *Asset) (*Asset, error) {
    css := string(asset.Content)
    
    // Remove unused CSS
    purged, err := p.purger.Purge(css, asset.UsageContext)
    if err != nil {
        return nil, err
    }
    
    // Add vendor prefixes
    prefixed, err := p.autoprefixer.AddPrefixes(purged)
    if err != nil {
        return nil, err
    }
    
    return &Asset{
        Name:     asset.Name,
        MimeType: asset.MimeType,
        Content:  []byte(prefixed),
        URL:      asset.URL,
    }, nil
}
```

### JavaScript Optimization

```go
type JSProcessor struct {
    bundler   JSBundler
    treeshaker TreeShaker
}

func (p *JSProcessor) Process(asset *Asset) (*Asset, error) {
    // Parse JavaScript
    ast, err := p.parse(asset.Content)
    if err != nil {
        return nil, err
    }
    
    // Remove dead code
    shaken, err := p.treeshaker.Shake(ast)
    if err != nil {
        return nil, err
    }
    
    // Bundle with dependencies
    bundled, err := p.bundler.Bundle(shaken)
    if err != nil {
        return nil, err
    }
    
    return &Asset{
        Name:     asset.Name,
        MimeType: asset.MimeType,
        Content:  bundled,
        URL:      asset.URL,
    }, nil
}
```

## Performance Optimization

### Critical Path Optimization

```go
type CriticalPathOptimizer struct {
    analyzer CriticalResourceAnalyzer
}

func (o *CriticalPathOptimizer) OptimizePage(page *Page) (*OptimizedPage, error) {
    // Analyze critical resources
    critical, err := o.analyzer.AnalyzeCriticalPath(page)
    if err != nil {
        return nil, err
    }
    
    optimized := &OptimizedPage{
        HTML: page.HTML,
    }
    
    // Inline critical CSS
    if critical.CSS != nil {
        inlined := o.inlineCriticalCSS(page.HTML, critical.CSS)
        optimized.HTML = inlined
    }
    
    // Preload critical resources
    optimized.PreloadHeaders = o.generatePreloadHeaders(critical.Resources)
    
    // Defer non-critical JavaScript
    optimized.HTML = o.deferNonCriticalJS(optimized.HTML, critical.Scripts)
    
    return optimized, nil
}

func (o *CriticalPathOptimizer) inlineCriticalCSS(html string, css string) string {
    style := fmt.Sprintf("<style>%s</style>", css)
    return strings.Replace(html, "</head>", style+"</head>", 1)
}
```

### Resource Hints

```go
func (r *SSRRenderer) generateResourceHints(page *Page) []string {
    var hints []string
    
    // DNS prefetch for external domains
    for _, domain := range r.extractExternalDomains(page) {
        hints = append(hints, fmt.Sprintf(`<link rel="dns-prefetch" href="//%s">`, domain))
    }
    
    // Preconnect for critical third parties
    for _, origin := range r.getCriticalOrigins(page) {
        hints = append(hints, fmt.Sprintf(`<link rel="preconnect" href="%s">`, origin))
    }
    
    // Prefetch for likely next pages
    for _, link := range r.getPrefetchCandidates(page) {
        hints = append(hints, fmt.Sprintf(`<link rel="prefetch" href="%s">`, link))
    }
    
    return hints
}
```

## Progressive Enhancement

### Service Worker Implementation

```javascript
class WebsiteServiceWorker {
    constructor() {
        this.cacheName = 'website-cache-v1';
        this.staticAssets = [
            '/css/critical.css',
            '/js/app.js',
            '/fonts/main.woff2'
        ];
    }
    
    async install() {
        const cache = await caches.open(this.cacheName);
        return cache.addAll(this.staticAssets);
    }
    
    async fetch(request) {
        // Cache-first strategy for static assets
        if (this.isStaticAsset(request.url)) {
            return this.cacheFirst(request);
        }
        
        // Network-first strategy for HTML pages
        if (this.isHTMLRequest(request)) {
            return this.networkFirst(request);
        }
        
        // Stale-while-revalidate for API calls
        return this.staleWhileRevalidate(request);
    }
    
    async cacheFirst(request) {
        const cached = await caches.match(request);
        if (cached) {
            return cached;
        }
        
        const response = await fetch(request);
        const cache = await caches.open(this.cacheName);
        cache.put(request, response.clone());
        return response;
    }
}
```

### Lazy Loading Implementation

```javascript
class LazyLoader {
    constructor() {
        this.observer = new IntersectionObserver(this.handleIntersect.bind(this));
        this.init();
    }
    
    init() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.observer.observe(img));
        
        // Lazy load blocks
        const blocks = document.querySelectorAll('.lazy-block');
        blocks.forEach(block => this.observer.observe(block));
    }
    
    handleIntersect(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target.tagName === 'IMG') {
                    this.loadImage(entry.target);
                } else {
                    this.loadBlock(entry.target);
                }
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    async loadBlock(element) {
        const blockData = JSON.parse(element.dataset.block);
        const rendered = await this.renderBlock(blockData);
        element.innerHTML = rendered;
        element.classList.add('loaded');
    }
}
```

## Mobile Optimization

### Responsive Images

```go
func (r *ImageRenderer) GenerateResponsiveHTML(image *Media, sizes string) string {
    srcset := []string{}
    
    // Generate different sizes
    for _, size := range []int{320, 640, 960, 1280, 1920} {
        if size <= image.Width {
            url := r.getResizedURL(image, size)
            srcset = append(srcset, fmt.Sprintf("%s %dw", url, size))
        }
    }
    
    return fmt.Sprintf(`
        <img src="%s" 
             srcset="%s" 
             sizes="%s" 
             alt="%s" 
             loading="lazy">
    `, r.getResizedURL(image, 640), strings.Join(srcset, ", "), sizes, image.AltText)
}
```

### Adaptive Loading

```javascript
class AdaptiveLoader {
    constructor() {
        this.connectionInfo = this.getConnectionInfo();
        this.deviceInfo = this.getDeviceInfo();
    }
    
    getConnectionInfo() {
        if ('connection' in navigator) {
            return {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                saveData: navigator.connection.saveData
            };
        }
        return { effectiveType: '4g' };
    }
    
    shouldLoadHighQuality() {
        if (this.connectionInfo.saveData) return false;
        if (this.connectionInfo.effectiveType === 'slow-2g') return false;
        if (this.connectionInfo.effectiveType === '2g') return false;
        if (this.deviceInfo.memory && this.deviceInfo.memory < 4) return false;
        return true;
    }
    
    adaptImageQuality(imageElement) {
        const highQualitySrc = imageElement.dataset.srcHq;
        const lowQualitySrc = imageElement.dataset.srcLq;
        
        if (this.shouldLoadHighQuality() && highQualitySrc) {
            imageElement.src = highQualitySrc;
        } else {
            imageElement.src = lowQualitySrc;
        }
    }
}
```

## Error Handling

### Fallback Rendering

```go
func (r *SSRRenderer) RenderWithFallback(websiteID int64, slug string, context RenderContext) (*RenderedPage, error) {
    // Try normal rendering
    page, err := r.RenderPage(websiteID, slug, context)
    if err == nil {
        return page, nil
    }
    
    // Log error for monitoring
    r.logger.Error("Page rendering failed", "error", err, "website_id", websiteID, "slug", slug)
    
    // Try cached version
    if cached := r.getCachedVersion(websiteID, slug); cached != nil {
        r.logger.Info("Serving cached version due to render error")
        return cached, nil
    }
    
    // Render error page
    errorPage, err := r.renderErrorPage(404, websiteID, context)
    if err != nil {
        // Fallback to static error page
        return r.getStaticErrorPage(404), nil
    }
    
    return errorPage, nil
}
```

## Best Practices

### Performance Guidelines
- Implement critical CSS inlining
- Use resource hints (preload, prefetch)
- Optimize images for different viewports
- Minimize JavaScript execution
- Leverage browser caching

### Rendering Strategy
- Use SSG for static content
- Use SSR for dynamic content
- Implement progressive enhancement
- Cache aggressively at multiple levels
- Monitor Core Web Vitals

### Mobile Optimization
- Implement responsive images
- Use adaptive loading strategies
- Optimize for touch interactions
- Consider connection quality
- Test across devices

## Tài liệu liên quan

- [Theme Management](./theme-management.md)
- [Page Builder](./page-builder.md)
- [SEO Optimization](./seo-optimization.md)
- [Media Management](./media-management.md)