# Page Builder System

## Tổng quan

Page Builder là hệ thống drag & drop cho phép người dùng tạo và tùy chỉnh trang web mà không cần kiến thức lập trình. Hệ thống này sử dụng kiến trúc block-based để cung cấp tính linh hoạt và dễ sử dụng.

## Page Builder Flow

```mermaid
flowchart TD
    A[Bắt đầu tạo trang] --> B[Chọn template]
    B --> C[Drag & Drop blocks]
    C --> D{Thêm block mới?}
    D -->|Có| E[Chọn loại block]
    E --> F[Cấu hình block]
    F --> C
    D -->|Không| G[Preview trang]
    G --> H{Hài lòng?}
    H -->|Không| C
    H -->|Có| I[Lưu trang]
    I --> J[Publish trang]
```

## Block System Architecture

### Block Categories

#### Content Blocks
- **Text Block**: <PERSON>ăn bản với rich editor
- **Heading Block**: Ti<PERSON>u đề H1-H6
- **Image Block**: Hình ảnh với caption
- **Video Block**: Embed video
- **Quote Block**: Trích dẫn
- **Code Block**: Hiển thị code

#### Layout Blocks
- **Container Block**: Wrapper cho các block khác
- **Columns Block**: Chia cột layout
- **Spacer Block**: Khoảng trống
- **Divider Block**: Đường chia
- **Grid Block**: Layout dạng lưới

#### Media Blocks
- **Gallery Block**: Thư viện ảnh
- **Slider Block**: Slideshow
- **Audio Block**: Player nhạc
- **File Download Block**: Link download

#### Interactive Blocks
- **Button Block**: Nút call-to-action
- **Form Block**: Form liên hệ
- **Social Share Block**: Chia sẻ mạng xã hội
- **Comment Block**: Hệ thống comment

#### Dynamic Blocks
- **Blog Posts Block**: Hiển thị bài viết
- **Categories Block**: Danh mục bài viết
- **Tags Block**: Thẻ tag
- **Recent Posts Block**: Bài viết mới nhất
- **Popular Posts Block**: Bài viết phổ biến

## Page Model

### Page Data Structure

- **ID**: Định danh trang
- **WebsiteID**: ID website (multi-tenancy)
- **TenantID**: ID tenant
- **Title**: Tiêu đề trang
- **Slug**: URL slug
- **Content**: Nội dung trang (JSON blocks)
- **Template**: Template sử dụng
- **SEO**: Metadata SEO
- **Status**: Trạng thái publish

### Page Content JSON Structure

```json
{
  "blocks": [
    {
      "id": "block-1",
      "type": "heading",
      "attributes": {
        "level": 1,
        "text": "Welcome to Our Website",
        "alignment": "center"
      }
    },
    {
      "id": "block-2",
      "type": "text",
      "attributes": {
        "content": "<p>This is a paragraph with <strong>bold text</strong>.</p>",
        "alignment": "left"
      }
    },
    {
      "id": "block-3",
      "type": "image",
      "attributes": {
        "src": "/media/hero-image.jpg",
        "alt": "Hero Image",
        "caption": "Beautiful landscape photo",
        "alignment": "center"
      }
    }
  ],
  "layout": {
    "container": "wide",
    "sidebar": "none"
  }
}
```

## Block Components

### Base Block Interface

```typescript
interface Block {
  id: string;
  type: string;
  attributes: Record<string, any>;
  children?: Block[];
}

interface BlockConfig {
  name: string;
  category: string;
  icon: string;
  settings: BlockSetting[];
  preview: string;
}

interface BlockSetting {
  key: string;
  label: string;
  type: 'text' | 'number' | 'color' | 'image' | 'select';
  default?: any;
  options?: Array<{label: string, value: any}>;
}
```

### Text Block Implementation

```typescript
const TextBlockConfig: BlockConfig = {
  name: 'Text',
  category: 'content',
  icon: 'text',
  settings: [
    {
      key: 'content',
      label: 'Content',
      type: 'text',
      default: ''
    },
    {
      key: 'alignment',
      label: 'Text Alignment',
      type: 'select',
      options: [
        {label: 'Left', value: 'left'},
        {label: 'Center', value: 'center'},
        {label: 'Right', value: 'right'}
      ],
      default: 'left'
    }
  ],
  preview: '<p>Sample text content</p>'
};
```

## Page Management API

### Page Endpoints

- `GET /api/cms/v1/pages` - Danh sách trang (filtered by website_id)
- `POST /api/cms/v1/pages` - Tạo trang mới
- `PUT /api/cms/v1/pages/{id}` - Cập nhật trang
- `POST /api/cms/v1/pages/{id}/publish` - Publish trang

### Page Service Implementation

```go
type PageService struct {
    repo  PageRepository
    cache CacheService
}

func (s *PageService) GetPageBySlugForWebsite(websiteID int64, slug string) (*Page, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("page:%s", slug))
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Page), nil
    }
    
    // Fetch from database with website_id filter
    page, err := s.repo.GetPageBySlugAndWebsiteID(slug, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cachePage(websiteID, page)
    return page, nil
}

func (s *PageService) UpdatePageContent(pageID int64, content PageContent) error {
    // Validate block structure
    if err := s.validateBlocks(content.Blocks); err != nil {
        return err
    }
    
    // Update in database
    if err := s.repo.UpdatePageContent(pageID, content); err != nil {
        return err
    }
    
    // Clear cache
    s.clearPageCache(pageID)
    return nil
}
```

## Block Validation

### Content Validation

```go
type BlockValidator struct {
    allowedBlocks map[string]BlockConfig
}

func (v *BlockValidator) ValidateBlock(block Block) error {
    config, exists := v.allowedBlocks[block.Type]
    if !exists {
        return fmt.Errorf("block type %s not allowed", block.Type)
    }
    
    return v.validateBlockAttributes(block, config)
}

func (v *BlockValidator) validateBlockAttributes(block Block, config BlockConfig) error {
    for _, setting := range config.Settings {
        value, exists := block.Attributes[setting.Key]
        if !exists && setting.Default != nil {
            continue // Use default value
        }
        
        if err := v.validateAttributeType(value, setting.Type); err != nil {
            return fmt.Errorf("invalid %s: %w", setting.Key, err)
        }
    }
    
    return nil
}
```

## Template System

### Page Templates

Templates định nghĩa cấu trúc cơ bản của trang và vị trí render các blocks:

```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Page.Title}}</title>
    <meta name="description" content="{{.Page.SEO.Description}}">
</head>
<body>
    <header>
        {{template "header" .}}
    </header>
    
    <main class="page-content">
        {{range .Page.Content.Blocks}}
            {{template "block" .}}
        {{end}}
    </main>
    
    <footer>
        {{template "footer" .}}
    </footer>
</body>
</html>
```

### Block Templates

Mỗi loại block có template riêng:

```html
<!-- Text Block Template -->
{{define "text-block"}}
<div class="text-block" style="text-align: {{.Attributes.alignment}}">
    {{.Attributes.content | safe}}
</div>
{{end}}

<!-- Image Block Template -->
{{define "image-block"}}
<figure class="image-block" style="text-align: {{.Attributes.alignment}}">
    <img src="{{.Attributes.src}}" alt="{{.Attributes.alt}}" loading="lazy">
    {{if .Attributes.caption}}
    <figcaption>{{.Attributes.caption}}</figcaption>
    {{end}}
</figure>
{{end}}
```

## Real-time Collaboration

### Page Editing Session

```go
type EditingSession struct {
    PageID    int64           `json:"page_id"`
    UserID    int64           `json:"user_id"`
    WebsiteID int64           `json:"website_id"`
    Changes   []BlockChange   `json:"changes"`
    CreatedAt time.Time       `json:"created_at"`
    ExpiresAt time.Time       `json:"expires_at"`
}

type BlockChange struct {
    Type      string    `json:"type"` // add, update, delete, move
    BlockID   string    `json:"block_id"`
    Position  int       `json:"position,omitempty"`
    Data      Block     `json:"data,omitempty"`
    Timestamp time.Time `json:"timestamp"`
}
```

### Conflict Resolution

```go
func (s *PageService) ApplyChanges(sessionID string, changes []BlockChange) error {
    session, err := s.getEditingSession(sessionID)
    if err != nil {
        return err
    }
    
    // Check for conflicts with other users
    conflicts, err := s.detectConflicts(session, changes)
    if err != nil {
        return err
    }
    
    if len(conflicts) > 0 {
        return NewConflictError(conflicts)
    }
    
    // Apply changes atomically
    return s.applyChangesAtomically(session.PageID, changes)
}
```

## Performance Optimization

### Block Rendering Cache

```go
type BlockRenderer struct {
    cache   CacheService
    compile TemplateCompiler
}

func (r *BlockRenderer) RenderBlock(block Block) (string, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("block:%s:%s", block.Type, block.ID)
    if cached, found := r.cache.Get(cacheKey); found {
        return cached.(string), nil
    }
    
    // Render block
    html, err := r.compile.RenderBlock(block)
    if err != nil {
        return "", err
    }
    
    // Cache result
    r.cache.Set(cacheKey, html, 1*time.Hour)
    return html, nil
}
```

### Lazy Loading Strategy

- Load blocks incrementally
- Render critical blocks first
- Defer non-visible blocks
- Progressive enhancement

## Mobile Responsiveness

### Responsive Block Attributes

```json
{
  "id": "block-1",
  "type": "columns",
  "attributes": {
    "columns": {
      "desktop": 3,
      "tablet": 2,
      "mobile": 1
    },
    "gap": {
      "desktop": "2rem",
      "tablet": "1.5rem",
      "mobile": "1rem"
    }
  }
}
```

### Viewport-specific Rendering

- Adaptive block layouts
- Mobile-optimized components
- Touch-friendly interfaces
- Performance considerations

## Best Practices

### Block Design
- Keep blocks focused and single-purpose
- Provide clear configuration options
- Ensure responsive behavior
- Test across devices

### Performance
- Minimize DOM complexity
- Optimize image loading
- Cache rendered content
- Use efficient CSS

### User Experience
- Intuitive drag & drop interface
- Clear visual feedback
- Undo/redo functionality
- Auto-save capabilities

## Tài liệu liên quan

- [Theme Management](./theme-management.md)
- [Media Management](./media-management.md)
- [SEO Optimization](./seo-optimization.md)
- [Frontend Rendering](./frontend-rendering.md)