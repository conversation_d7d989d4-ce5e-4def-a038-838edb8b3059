# SEO Optimization

## Tổng quan

SEO Optimization system cung cấp các công cụ và tính năng để tối ưu hóa website cho search engines, bao gồm on-page SEO, technical SEO, và analytics integration. Hệ thống này tự động hóa nhiều task SEO và cung cấp recommendations để cải thiện rankings.

## SEO Management Flow

```mermaid
flowchart LR
    A[Tạo/Sửa trang] --> B[Nhập SEO metadata]
    B --> C[Tự động generate]
    C --> D[Meta title]
    C --> E[Meta description]
    C --> F[Open Graph tags]
    C --> G[Structured data]
    D --> H[Validate SEO]
    E --> H
    F --> H
    G --> H
    H --> I{SEO score OK?}
    I -->|Không| J[Hiển thị đề xuất]
    J --> B
    I -->|Có| K[Lưu trang]
```

## On-Page SEO Features

### Meta Tags Management

```go
type SEOMetadata struct {
    Title           string            `json:"title"`
    Description     string            `json:"description"`
    Keywords        []string          `json:"keywords"`
    CanonicalURL    string            `json:"canonical_url"`
    OpenGraph       OpenGraphData     `json:"open_graph"`
    TwitterCard     TwitterCardData   `json:"twitter_card"`
    StructuredData  []StructuredData  `json:"structured_data"`
}

type OpenGraphData struct {
    Title       string `json:"title"`
    Description string `json:"description"`
    Image       string `json:"image"`
    URL         string `json:"url"`
    Type        string `json:"type"`
    SiteName    string `json:"site_name"`
}

type TwitterCardData struct {
    Card        string `json:"card"`
    Title       string `json:"title"`
    Description string `json:"description"`
    Image       string `json:"image"`
    Creator     string `json:"creator"`
}
```

### SEO Content Analysis

```go
type SEOAnalyzer struct {
    analyzer ContentAnalyzer
    checker  SEOChecker
}

func (s *SEOAnalyzer) AnalyzePage(page *Page) (*SEOAnalysis, error) {
    analysis := &SEOAnalysis{
        PageID: page.ID,
        Score:  0,
        Issues: []SEOIssue{},
        Suggestions: []SEOSuggestion{},
    }
    
    // Analyze title
    titleScore := s.analyzeTitle(page.SEO.Title, page.Content)
    analysis.Score += titleScore.Score
    analysis.Issues = append(analysis.Issues, titleScore.Issues...)
    analysis.Suggestions = append(analysis.Suggestions, titleScore.Suggestions...)
    
    // Analyze description
    descScore := s.analyzeDescription(page.SEO.Description, page.Content)
    analysis.Score += descScore.Score
    analysis.Issues = append(analysis.Issues, descScore.Issues...)
    
    // Analyze content structure
    contentScore := s.analyzeContentStructure(page.Content)
    analysis.Score += contentScore.Score
    analysis.Issues = append(analysis.Issues, contentScore.Issues...)
    
    // Analyze images
    imageScore := s.analyzeImages(page.Content)
    analysis.Score += imageScore.Score
    analysis.Issues = append(analysis.Issues, imageScore.Issues...)
    
    return analysis, nil
}
```

### URL Slug Optimization

```go
type SlugGenerator struct {
    stopWords   map[string]bool
    maxLength   int
    replacement string
}

func (g *SlugGenerator) GenerateSlug(title string) string {
    // Convert to lowercase
    slug := strings.ToLower(title)
    
    // Remove diacritics (Vietnamese characters)
    slug = g.removeDiacritics(slug)
    
    // Replace spaces and special chars with hyphens
    slug = regexp.MustCompile(`[^a-z0-9]+`).ReplaceAllString(slug, "-")
    
    // Remove stop words
    words := strings.Split(slug, "-")
    filteredWords := make([]string, 0, len(words))
    for _, word := range words {
        if !g.stopWords[word] && word != "" {
            filteredWords = append(filteredWords, word)
        }
    }
    
    slug = strings.Join(filteredWords, "-")
    
    // Trim to max length
    if len(slug) > g.maxLength {
        slug = slug[:g.maxLength]
        // Ensure we don't cut in the middle of a word
        if lastHyphen := strings.LastIndex(slug, "-"); lastHyphen > 0 {
            slug = slug[:lastHyphen]
        }
    }
    
    return strings.Trim(slug, "-")
}
```

## Technical SEO

### XML Sitemap Generation

```go
type SitemapGenerator struct {
    baseURL string
    cache   CacheService
}

func (g *SitemapGenerator) GenerateWebsiteSitemap(websiteID int64) (*Sitemap, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("sitemap:website:%d", websiteID)
    if cached, found := g.cache.Get(cacheKey); found {
        return cached.(*Sitemap), nil
    }
    
    sitemap := &Sitemap{
        URLs: []SitemapURL{},
    }
    
    // Get all published pages
    pages, err := g.pageRepo.GetPublishedPagesByWebsiteID(websiteID)
    if err != nil {
        return nil, err
    }
    
    for _, page := range pages {
        url := SitemapURL{
            URL:        fmt.Sprintf("%s/%s", g.baseURL, page.Slug),
            LastMod:    page.UpdatedAt,
            ChangeFreq: g.determineChangeFreq(page),
            Priority:   g.calculatePriority(page),
        }
        sitemap.URLs = append(sitemap.URLs, url)
    }
    
    // Cache for 1 hour
    g.cache.Set(cacheKey, sitemap, 1*time.Hour)
    return sitemap, nil
}

func (g *SitemapGenerator) determineChangeFreq(page *Page) string {
    // Analyze page update patterns
    updates := g.getPageUpdateHistory(page.ID)
    if len(updates) < 2 {
        return "monthly"
    }
    
    // Calculate average time between updates
    avgInterval := g.calculateAverageInterval(updates)
    
    switch {
    case avgInterval < 24*time.Hour:
        return "daily"
    case avgInterval < 7*24*time.Hour:
        return "weekly"
    case avgInterval < 30*24*time.Hour:
        return "monthly"
    default:
        return "yearly"
    }
}
```

### Robots.txt Management

```go
type RobotsGenerator struct {
    baseURL string
}

func (g *RobotsGenerator) GenerateRobots(websiteID int64, config RobotsConfig) string {
    var robots strings.Builder
    
    // User-agent directives
    for _, rule := range config.Rules {
        robots.WriteString(fmt.Sprintf("User-agent: %s\n", rule.UserAgent))
        
        for _, disallow := range rule.Disallow {
            robots.WriteString(fmt.Sprintf("Disallow: %s\n", disallow))
        }
        
        for _, allow := range rule.Allow {
            robots.WriteString(fmt.Sprintf("Allow: %s\n", allow))
        }
        
        if rule.CrawlDelay > 0 {
            robots.WriteString(fmt.Sprintf("Crawl-delay: %d\n", rule.CrawlDelay))
        }
        
        robots.WriteString("\n")
    }
    
    // Sitemap URL
    robots.WriteString(fmt.Sprintf("Sitemap: %s/sitemap.xml\n", g.baseURL))
    
    return robots.String()
}
```

### Schema.org Structured Data

```go
type StructuredDataGenerator struct {
    websiteRepo WebsiteRepository
}

func (g *StructuredDataGenerator) GenerateWebsiteSchema(website *Website) map[string]interface{} {
    return map[string]interface{}{
        "@context": "https://schema.org",
        "@type":    "WebSite",
        "name":     website.Name,
        "url":      website.Domain,
        "potentialAction": map[string]interface{}{
            "@type":       "SearchAction",
            "target":      fmt.Sprintf("%s/search?q={search_term_string}", website.Domain),
            "query-input": "required name=search_term_string",
        },
    }
}

func (g *StructuredDataGenerator) GenerateArticleSchema(page *Page, website *Website) map[string]interface{} {
    return map[string]interface{}{
        "@context": "https://schema.org",
        "@type":    "Article",
        "headline": page.Title,
        "description": page.SEO.Description,
        "url":        fmt.Sprintf("%s/%s", website.Domain, page.Slug),
        "datePublished": page.CreatedAt.Format(time.RFC3339),
        "dateModified":  page.UpdatedAt.Format(time.RFC3339),
        "author": map[string]interface{}{
            "@type": "Person",
            "name":  page.Author.Name,
        },
        "publisher": map[string]interface{}{
            "@type": "Organization",
            "name":  website.Name,
            "url":   website.Domain,
        },
    }
}
```

## SEO Performance Monitoring

### Core Web Vitals Tracking

```go
type WebVitalsCollector struct {
    analytics AnalyticsService
}

func (c *WebVitalsCollector) CollectMetrics(pageID int64, metrics WebVitalsMetrics) error {
    data := AnalyticsData{
        PageID:    pageID,
        Timestamp: time.Now(),
        Metrics: map[string]interface{}{
            "lcp":  metrics.LCP,  // Largest Contentful Paint
            "fid":  metrics.FID,  // First Input Delay
            "cls":  metrics.CLS,  // Cumulative Layout Shift
            "fcp":  metrics.FCP,  // First Contentful Paint
            "ttfb": metrics.TTFB, // Time to First Byte
        },
    }
    
    return c.analytics.Track("web_vitals", data)
}

func (c *WebVitalsCollector) GetPagePerformanceReport(pageID int64, period time.Duration) (*PerformanceReport, error) {
    metrics, err := c.analytics.GetMetrics(pageID, period)
    if err != nil {
        return nil, err
    }
    
    return &PerformanceReport{
        PageID:   pageID,
        Period:   period,
        LCP:      c.calculatePercentiles(metrics, "lcp"),
        FID:      c.calculatePercentiles(metrics, "fid"),
        CLS:      c.calculatePercentiles(metrics, "cls"),
        Score:    c.calculateWebVitalsScore(metrics),
        Issues:   c.identifyPerformanceIssues(metrics),
    }, nil
}
```

### SEO Rankings Tracking

```go
type RankingTracker struct {
    searchAPIs map[string]SearchAPI
    cache      CacheService
}

func (r *RankingTracker) TrackKeywordRankings(websiteID int64, keywords []string) error {
    for _, keyword := range keywords {
        for engine, api := range r.searchAPIs {
            ranking, err := api.GetRanking(keyword, websiteID)
            if err != nil {
                continue // Skip this engine
            }
            
            // Store ranking data
            if err := r.storeRanking(websiteID, keyword, engine, ranking); err != nil {
                return err
            }
        }
    }
    
    return nil
}

func (r *RankingTracker) GetRankingTrends(websiteID int64, period time.Duration) (*RankingTrends, error) {
    rankings, err := r.getRankings(websiteID, period)
    if err != nil {
        return nil, err
    }
    
    trends := &RankingTrends{
        WebsiteID: websiteID,
        Period:    period,
        Keywords:  make(map[string]*KeywordTrend),
    }
    
    for keyword, data := range rankings {
        trend := r.calculateTrend(data)
        trends.Keywords[keyword] = trend
    }
    
    return trends, nil
}
```

## Analytics Integration

### Google Analytics 4 Integration

```go
type GA4Integration struct {
    measurementID string
    apiSecret     string
    client        *http.Client
}

func (g *GA4Integration) TrackPageView(page *Page, context TrackingContext) error {
    event := GA4Event{
        Name: "page_view",
        Parameters: map[string]interface{}{
            "page_title":    page.Title,
            "page_location": context.URL,
            "page_referrer": context.Referrer,
            "user_agent":    context.UserAgent,
        },
    }
    
    return g.sendEvent(event, context.ClientID)
}

func (g *GA4Integration) TrackCustomEvent(eventName string, parameters map[string]interface{}, clientID string) error {
    event := GA4Event{
        Name:       eventName,
        Parameters: parameters,
    }
    
    return g.sendEvent(event, clientID)
}
```

### Search Console Integration

```go
type SearchConsoleClient struct {
    service *searchconsole.Service
}

func (s *SearchConsoleClient) GetSearchAnalytics(siteURL string, request *SearchAnalyticsRequest) (*SearchAnalyticsResponse, error) {
    query := s.service.Searchanalytics.Query(siteURL, &searchconsole.SearchAnalyticsQueryRequest{
        StartDate:  request.StartDate.Format("2006-01-02"),
        EndDate:    request.EndDate.Format("2006-01-02"),
        Dimensions: request.Dimensions,
        RowLimit:   request.RowLimit,
    })
    
    response, err := query.Do()
    if err != nil {
        return nil, err
    }
    
    return &SearchAnalyticsResponse{
        Rows: response.Rows,
    }, nil
}
```

## SEO Service Implementation

### SEO Audit Service

```go
type SEOAuditService struct {
    analyzer    SEOAnalyzer
    lighthouse  LighthouseService
    validator   HTMLValidator
}

func (s *SEOAuditService) PerformFullAudit(websiteID int64) (*SEOAuditReport, error) {
    website, err := s.websiteRepo.GetByID(websiteID)
    if err != nil {
        return nil, err
    }
    
    pages, err := s.pageRepo.GetPublishedPagesByWebsiteID(websiteID)
    if err != nil {
        return nil, err
    }
    
    report := &SEOAuditReport{
        WebsiteID: websiteID,
        Timestamp: time.Now(),
        Pages:     make(map[int64]*PageAudit),
        Overall:   &OverallAudit{},
    }
    
    // Audit each page
    for _, page := range pages {
        audit, err := s.auditPage(page)
        if err != nil {
            continue
        }
        report.Pages[page.ID] = audit
    }
    
    // Perform site-wide checks
    siteAudit, err := s.auditSite(website)
    if err != nil {
        return nil, err
    }
    report.Overall = siteAudit
    
    // Calculate overall score
    report.Score = s.calculateOverallScore(report)
    
    return report, nil
}

func (s *SEOAuditService) auditPage(page *Page) (*PageAudit, error) {
    audit := &PageAudit{
        PageID: page.ID,
        Issues: []AuditIssue{},
        Score:  0,
    }
    
    // Check title
    if len(page.SEO.Title) == 0 {
        audit.Issues = append(audit.Issues, AuditIssue{
            Type:        "missing_title",
            Severity:    "high",
            Description: "Page is missing title tag",
        })
    } else if len(page.SEO.Title) > 60 {
        audit.Issues = append(audit.Issues, AuditIssue{
            Type:        "long_title",
            Severity:    "medium",
            Description: "Title is longer than recommended 60 characters",
        })
    }
    
    // Check meta description
    if len(page.SEO.Description) == 0 {
        audit.Issues = append(audit.Issues, AuditIssue{
            Type:        "missing_description",
            Severity:    "medium",
            Description: "Page is missing meta description",
        })
    }
    
    // Check heading structure
    headings := s.extractHeadings(page.Content)
    if err := s.validateHeadingStructure(headings); err != nil {
        audit.Issues = append(audit.Issues, AuditIssue{
            Type:        "heading_structure",
            Severity:    "low",
            Description: err.Error(),
        })
    }
    
    // Calculate score based on issues
    audit.Score = s.calculatePageScore(audit.Issues)
    
    return audit, nil
}
```

## Automated SEO Suggestions

### Content Optimization Suggestions

```go
type ContentOptimizer struct {
    nlp        NLPService
    competitor CompetitorAnalyzer
}

func (o *ContentOptimizer) GenerateSuggestions(page *Page, targetKeyword string) (*OptimizationSuggestions, error) {
    suggestions := &OptimizationSuggestions{
        PageID:   page.ID,
        Keyword:  targetKeyword,
        Suggestions: []Suggestion{},
    }
    
    // Analyze current content
    analysis := o.nlp.AnalyzeContent(page.Content.Text)
    
    // Check keyword density
    density := analysis.KeywordDensity[targetKeyword]
    if density < 0.5 {
        suggestions.Suggestions = append(suggestions.Suggestions, Suggestion{
            Type:        "keyword_density",
            Description: "Consider using the target keyword more frequently in your content",
            Impact:      "medium",
        })
    } else if density > 3.0 {
        suggestions.Suggestions = append(suggestions.Suggestions, Suggestion{
            Type:        "keyword_stuffing",
            Description: "Reduce keyword usage to avoid keyword stuffing",
            Impact:      "high",
        })
    }
    
    // Analyze competitor content
    competitors, err := o.competitor.GetTopCompetitors(targetKeyword, 3)
    if err == nil {
        topicGaps := o.identifyTopicGaps(analysis, competitors)
        for _, gap := range topicGaps {
            suggestions.Suggestions = append(suggestions.Suggestions, Suggestion{
                Type:        "topic_gap",
                Description: fmt.Sprintf("Consider covering topic: %s", gap.Topic),
                Impact:      "medium",
            })
        }
    }
    
    return suggestions, nil
}
```

## Best Practices

### SEO Guidelines
- Keep titles under 60 characters
- Write compelling meta descriptions (150-160 chars)
- Use proper heading hierarchy (H1 → H2 → H3)
- Optimize images with alt text
- Create semantic URL structures

### Technical SEO
- Implement proper schema markup
- Generate XML sitemaps automatically
- Configure robots.txt appropriately
- Monitor Core Web Vitals
- Ensure mobile-friendliness

### Content Strategy
- Focus on user intent
- Create comprehensive content
- Use semantic keywords
- Build internal linking structure
- Monitor ranking performance

## Tài liệu liên quan

- [Page Builder](./page-builder.md)
- [Frontend Rendering](./frontend-rendering.md)
- [Media Management](./media-management.md)
- [Models Schema](./models-schema.md)