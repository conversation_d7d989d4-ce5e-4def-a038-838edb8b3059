# Models và Database Schema

## Tổng quan

Website Module sử dụng một set of models đ<PERSON><PERSON><PERSON> thiết kế để hỗ trợ multi-tenancy, performance optimization, và scalability. Tất cả models đều include website_id để đảm bảo tenant isolation và data integrity.

## Core Models

### Website Model

```go
type Website struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    Name        string    `gorm:"size:255;not null" json:"name"`
    Domain      string    `gorm:"size:255;uniqueIndex" json:"domain"`
    Subdomain   string    `gorm:"size:100;uniqueIndex" json:"subdomain"`
    Description string    `gorm:"type:text" json:"description"`
    
    // Theme settings
    ActiveTheme string    `gorm:"size:100" json:"active_theme"`
    CustomCSS   string    `gorm:"type:longtext" json:"custom_css"`
    CustomJS    string    `gorm:"type:longtext" json:"custom_js"`
    
    // SEO settings
    SiteLogo    string    `gorm:"size:500" json:"site_logo"`
    Favicon     string    `gorm:"size:500" json:"favicon"`
    Timezone    string    `gorm:"size:50;default:'UTC'" json:"timezone"`
    Language    string    `gorm:"size:10;default:'en'" json:"language"`
    
    // Analytics
    GoogleAnalyticsID    string `gorm:"size:50" json:"google_analytics_id"`
    GoogleTagManagerID   string `gorm:"size:50" json:"google_tag_manager_id"`
    FacebookPixelID      string `gorm:"size:50" json:"facebook_pixel_id"`
    
    // Social media
    SocialMedia datatypes.JSON `gorm:"type:json" json:"social_media"`
    
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, inactive, suspended
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Tenant      Tenant    `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
    Themes      []Theme   `gorm:"foreignKey:WebsiteID" json:"themes,omitempty"`
    Pages       []Page    `gorm:"foreignKey:WebsiteID" json:"pages,omitempty"`
    Menus       []Menu    `gorm:"foreignKey:WebsiteID" json:"menus,omitempty"`
    Widgets     []Widget  `gorm:"foreignKey:WebsiteID" json:"widgets,omitempty"`
    Media       []Media   `gorm:"foreignKey:WebsiteID" json:"media,omitempty"`
}

// Database indexes
func (Website) TableName() string {
    return "websites"
}

func (w *Website) BeforeCreate(tx *gorm.DB) error {
    // Generate subdomain if not provided
    if w.Subdomain == "" && w.Name != "" {
        w.Subdomain = generateSubdomain(w.Name)
    }
    return nil
}

func (w *Website) BeforeUpdate(tx *gorm.DB) error {
    w.UpdatedAt = time.Now()
    return nil
}
```

### Theme Model

```go
type Theme struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index:idx_website_theme" json:"website_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    Name        string    `gorm:"size:100;not null;index:idx_website_theme" json:"name"`
    Version     string    `gorm:"size:20" json:"version"`
    Author      string    `gorm:"size:255" json:"author"`
    Description string    `gorm:"type:text" json:"description"`
    
    // Theme configuration
    Config      datatypes.JSON `gorm:"type:json" json:"config"`
    
    // Assets
    StylesheetPath  string `gorm:"size:500" json:"stylesheet_path"`
    JavaScriptPath  string `gorm:"size:500" json:"javascript_path"`
    PreviewImage    string `gorm:"size:500" json:"preview_image"`
    AssetsPath      string `gorm:"size:500" json:"assets_path"`
    
    // Template files
    Templates   datatypes.JSON `gorm:"type:json" json:"templates"`
    
    // Supported features
    SupportedBlocks datatypes.JSON `gorm:"type:json" json:"supported_blocks"`
    Features        datatypes.JSON `gorm:"type:json" json:"features"`
    
    Status      string    `gorm:"size:20;default:'inactive'" json:"status"` // active, inactive
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Website     Website   `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
}

// Ensure unique active theme per website
func (t *Theme) BeforeUpdate(tx *gorm.DB) error {
    if t.Status == "active" {
        // Deactivate other themes for this website
        return tx.Model(&Theme{}).
            Where("website_id = ? AND id != ?", t.WebsiteID, t.ID).
            Update("status", "inactive").Error
    }
    return nil
}
```

### Page Model

```go
type Page struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index:idx_website_page" json:"website_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    Title       string    `gorm:"size:255;not null" json:"title"`
    Slug        string    `gorm:"size:255;not null;index:idx_website_page" json:"slug"`
    Excerpt     string    `gorm:"type:text" json:"excerpt"`
    
    // Content structure (JSON blocks)
    Content     datatypes.JSON `gorm:"type:json" json:"content"`
    
    // Template and rendering
    Template    string    `gorm:"size:100" json:"template"`
    Layout      string    `gorm:"size:100" json:"layout"`
    
    // SEO metadata
    SEO         datatypes.JSON `gorm:"type:json" json:"seo"`
    
    // Page settings
    FeaturedImage   string `gorm:"size:500" json:"featured_image"`
    AllowComments   bool   `gorm:"default:true" json:"allow_comments"`
    IsHomepage      bool   `gorm:"default:false" json:"is_homepage"`
    RequireAuth     bool   `gorm:"default:false" json:"require_auth"`
    
    // Publishing
    Status          string     `gorm:"size:20;default:'draft'" json:"status"` // draft, published, archived
    PublishedAt     *time.Time `json:"published_at"`
    ScheduledAt     *time.Time `json:"scheduled_at"`
    
    // Versioning
    Version         int    `gorm:"default:1" json:"version"`
    ParentPageID    *uint  `gorm:"index" json:"parent_page_id"`
    
    // Analytics
    ViewCount       int64  `gorm:"default:0" json:"view_count"`
    ShareCount      int64  `gorm:"default:0" json:"share_count"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Website     Website   `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
    ParentPage  *Page     `gorm:"foreignKey:ParentPageID" json:"parent_page,omitempty"`
    ChildPages  []Page    `gorm:"foreignKey:ParentPageID" json:"child_pages,omitempty"`
}

// Composite unique index for slug within website
func (Page) TableName() string {
    return "pages"
}

func (p *Page) BeforeCreate(tx *gorm.DB) error {
    // Generate slug if not provided
    if p.Slug == "" && p.Title != "" {
        p.Slug = generateSlug(p.Title)
    }
    
    // Ensure slug is unique within website
    return p.ensureUniqueSlug(tx)
}

func (p *Page) ensureUniqueSlug(tx *gorm.DB) error {
    var count int64
    query := tx.Model(&Page{}).Where("website_id = ? AND slug = ?", p.WebsiteID, p.Slug)
    if p.ID > 0 {
        query = query.Where("id != ?", p.ID)
    }
    
    if err := query.Count(&count).Error; err != nil {
        return err
    }
    
    if count > 0 {
        // Append number to make it unique
        p.Slug = fmt.Sprintf("%s-%d", p.Slug, time.Now().Unix())
    }
    
    return nil
}
```

### Menu Model

```go
type Menu struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index:idx_website_menu" json:"website_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    Name        string    `gorm:"size:100;not null" json:"name"`
    Location    string    `gorm:"size:50;not null;index:idx_website_menu" json:"location"` // header, footer, sidebar
    
    // Menu structure (hierarchical JSON)
    Items       datatypes.JSON `gorm:"type:json" json:"items"`
    
    // Display settings
    DisplayType string    `gorm:"size:50;default:'horizontal'" json:"display_type"` // horizontal, vertical, dropdown
    CSSClass    string    `gorm:"size:255" json:"css_class"`
    
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, inactive
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Website     Website   `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
}

type MenuItem struct {
    ID       string     `json:"id"`
    Title    string     `json:"title"`
    URL      string     `json:"url"`
    Type     string     `json:"type"`     // page, url, category
    Target   string     `json:"target"`   // _self, _blank
    CSSClass string     `json:"css_class"`
    Order    int        `json:"order"`
    Children []MenuItem `json:"children"`
}
```

### Widget Model

```go
type Widget struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index:idx_website_widget" json:"website_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    Type        string    `gorm:"size:50;not null" json:"type"` // text, image, recent_posts, etc.
    Title       string    `gorm:"size:255" json:"title"`
    
    // Widget configuration
    Config      datatypes.JSON `gorm:"type:json" json:"config"`
    
    // Positioning
    Position    string    `gorm:"size:50;not null;index:idx_website_widget" json:"position"` // sidebar, footer, header
    Order       int       `gorm:"default:0" json:"order"`
    
    // Display settings
    CSSClass    string    `gorm:"size:255" json:"css_class"`
    CustomCSS   string    `gorm:"type:text" json:"custom_css"`
    
    // Visibility rules
    ShowOn      datatypes.JSON `gorm:"type:json" json:"show_on"`     // pages, categories where widget shows
    HideOn      datatypes.JSON `gorm:"type:json" json:"hide_on"`     // pages, categories where widget hides
    
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, inactive
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Website     Website   `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
}
```

### Media Model

```go
type Media struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    WebsiteID       uint      `gorm:"not null;index:idx_website_media" json:"website_id"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    FileName        string    `gorm:"size:255;not null" json:"filename"`
    OriginalName    string    `gorm:"size:255;not null" json:"original_name"`
    StoragePath     string    `gorm:"size:500;not null;uniqueIndex" json:"storage_path"`
    
    // File properties
    FileSize        int64     `gorm:"not null" json:"file_size"`
    MimeType        string    `gorm:"size:100;not null" json:"mime_type"`
    FileExtension   string    `gorm:"size:10" json:"file_extension"`
    
    // Image/Video specific
    Width           int       `json:"width"`
    Height          int       `json:"height"`
    Duration        int       `json:"duration"` // for video/audio in seconds
    
    // Metadata and processing info
    Metadata        datatypes.JSON `gorm:"type:json" json:"metadata"`
    ProcessedVersions datatypes.JSON `gorm:"type:json" json:"processed_versions"`
    
    // Content description
    AltText         string    `gorm:"size:500" json:"alt_text"`
    Caption         string    `gorm:"type:text" json:"caption"`
    Description     string    `gorm:"type:text" json:"description"`
    
    // Organization
    Folder          string    `gorm:"size:255;index" json:"folder"`
    Tags            datatypes.JSON `gorm:"type:json" json:"tags"`
    
    // URLs
    URL             string    `gorm:"size:500" json:"url"`
    ThumbnailURL    string    `gorm:"size:500" json:"thumbnail_url"`
    
    // Usage tracking
    UsageCount      int64     `gorm:"default:0" json:"usage_count"`
    LastAccessedAt  *time.Time `json:"last_accessed_at"`
    
    Status          string    `gorm:"size:20;default:'uploaded'" json:"status"` // uploaded, processing, processed, failed
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relationships
    Website         Website   `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
}

func (m *Media) BeforeCreate(tx *gorm.DB) error {
    // Extract file extension
    m.FileExtension = filepath.Ext(m.OriginalName)
    
    // Generate storage path if not provided
    if m.StoragePath == "" {
        m.StoragePath = generateStoragePath(m.WebsiteID, m.FileName)
    }
    
    return nil
}
```

## Database Schema SQL

### Create Tables

```sql
-- Websites table
CREATE TABLE websites (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    subdomain VARCHAR(100) UNIQUE,
    description TEXT,
    active_theme VARCHAR(100),
    custom_css LONGTEXT,
    custom_js LONGTEXT,
    site_logo VARCHAR(500),
    favicon VARCHAR(500),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    google_analytics_id VARCHAR(50),
    google_tag_manager_id VARCHAR(50),
    facebook_pixel_id VARCHAR(50),
    social_media JSON,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Themes table
CREATE TABLE themes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20),
    author VARCHAR(255),
    description TEXT,
    config JSON,
    stylesheet_path VARCHAR(500),
    javascript_path VARCHAR(500),
    preview_image VARCHAR(500),
    assets_path VARCHAR(500),
    templates JSON,
    supported_blocks JSON,
    features JSON,
    status VARCHAR(20) DEFAULT 'inactive',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_theme (website_id, name),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Pages table
CREATE TABLE pages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    excerpt TEXT,
    content JSON,
    template VARCHAR(100),
    layout VARCHAR(100),
    seo JSON,
    featured_image VARCHAR(500),
    allow_comments BOOLEAN DEFAULT TRUE,
    is_homepage BOOLEAN DEFAULT FALSE,
    require_auth BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    scheduled_at TIMESTAMP NULL,
    version INT DEFAULT 1,
    parent_page_id INT UNSIGNED NULL,
    view_count BIGINT DEFAULT 0,
    share_count BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE INDEX idx_website_slug (website_id, slug),
    INDEX idx_website_page (website_id, status, published_at),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_parent_page_id (parent_page_id),
    INDEX idx_is_homepage (is_homepage),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_page_id) REFERENCES pages(id) ON DELETE SET NULL
);

-- Menus table
CREATE TABLE menus (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(50) NOT NULL,
    items JSON,
    display_type VARCHAR(50) DEFAULT 'horizontal',
    css_class VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_menu (website_id, location),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Widgets table
CREATE TABLE widgets (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    config JSON,
    position VARCHAR(50) NOT NULL,
    order_index INT DEFAULT 0,
    css_class VARCHAR(255),
    custom_css TEXT,
    show_on JSON,
    hide_on JSON,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_widget (website_id, position, order_index),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Media table
CREATE TABLE media (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    storage_path VARCHAR(500) NOT NULL UNIQUE,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_extension VARCHAR(10),
    width INT,
    height INT,
    duration INT,
    metadata JSON,
    processed_versions JSON,
    alt_text VARCHAR(500),
    caption TEXT,
    description TEXT,
    folder VARCHAR(255),
    tags JSON,
    url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    usage_count BIGINT DEFAULT 0,
    last_accessed_at TIMESTAMP NULL,
    status VARCHAR(20) DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_media (website_id, created_at),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_mime_type (mime_type),
    INDEX idx_folder (folder),
    INDEX idx_status (status),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);
```

## Model Relationships

### Website Relationships

```go
// Get all themes for a website
func (w *Website) GetThemes(db *gorm.DB) ([]Theme, error) {
    var themes []Theme
    err := db.Where("website_id = ?", w.ID).Find(&themes).Error
    return themes, err
}

// Get active theme for a website
func (w *Website) GetActiveTheme(db *gorm.DB) (*Theme, error) {
    var theme Theme
    err := db.Where("website_id = ? AND status = ?", w.ID, "active").First(&theme).Error
    if err != nil {
        return nil, err
    }
    return &theme, nil
}

// Get published pages for a website
func (w *Website) GetPublishedPages(db *gorm.DB) ([]Page, error) {
    var pages []Page
    err := db.Where("website_id = ? AND status = ?", w.ID, "published").
        Order("published_at DESC").Find(&pages).Error
    return pages, err
}
```

### Page Relationships

```go
// Get parent page
func (p *Page) GetParent(db *gorm.DB) (*Page, error) {
    if p.ParentPageID == nil {
        return nil, nil
    }
    
    var parent Page
    err := db.First(&parent, *p.ParentPageID).Error
    if err != nil {
        return nil, err
    }
    return &parent, nil
}

// Get child pages
func (p *Page) GetChildren(db *gorm.DB) ([]Page, error) {
    var children []Page
    err := db.Where("parent_page_id = ?", p.ID).Order("title").Find(&children).Error
    return children, err
}
```

## Validation Rules

### Model Validation

```go
func (w *Website) Validate() error {
    if w.TenantID == 0 {
        return errors.New("tenant_id is required")
    }
    if w.Name == "" {
        return errors.New("name is required")
    }
    if len(w.Name) > 255 {
        return errors.New("name must be less than 255 characters")
    }
    return nil
}

func (p *Page) Validate() error {
    if p.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    if p.Title == "" {
        return errors.New("title is required")
    }
    if p.Slug == "" {
        return errors.New("slug is required")
    }
    if !isValidSlug(p.Slug) {
        return errors.New("slug contains invalid characters")
    }
    return nil
}

func (t *Theme) Validate() error {
    if t.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    if t.Name == "" {
        return errors.New("name is required")
    }
    return nil
}
```

## Database Migrations

### Migration Files

```go
// Migration: 001_create_websites_table.go
func up_001_create_websites_table(tx *gorm.DB) error {
    return tx.AutoMigrate(&Website{})
}

func down_001_create_websites_table(tx *gorm.DB) error {
    return tx.Migrator().DropTable(&Website{})
}

// Migration: 002_create_themes_table.go
func up_002_create_themes_table(tx *gorm.DB) error {
    return tx.AutoMigrate(&Theme{})
}

func down_002_create_themes_table(tx *gorm.DB) error {
    return tx.Migrator().DropTable(&Theme{})
}

// Migration: 003_create_pages_table.go
func up_003_create_pages_table(tx *gorm.DB) error {
    return tx.AutoMigrate(&Page{})
}

func down_003_create_pages_table(tx *gorm.DB) error {
    return tx.Migrator().DropTable(&Page{})
}
```

## Performance Optimization

### Database Indexes

```sql
-- Performance indexes for common queries
CREATE INDEX idx_pages_website_status_published ON pages(website_id, status, published_at DESC);
CREATE INDEX idx_media_website_type ON media(website_id, mime_type, created_at DESC);
CREATE INDEX idx_widgets_website_position_order ON widgets(website_id, position, order_index);
CREATE INDEX idx_themes_website_status ON themes(website_id, status);

-- Composite indexes for multi-tenant queries
CREATE INDEX idx_pages_tenant_website ON pages(tenant_id, website_id, status);
CREATE INDEX idx_media_tenant_website ON media(tenant_id, website_id, created_at DESC);
```

### Query Optimization

```go
// Optimized queries with proper indexing
func (r *websiteRepository) GetPagesByWebsiteWithPagination(websiteID int64, limit, offset int) ([]Page, error) {
    var pages []Page
    return pages, r.db.Where("website_id = ? AND status = ?", websiteID, "published").
        Order("published_at DESC").
        Limit(limit).
        Offset(offset).
        Find(&pages).Error
}

func (r *websiteRepository) GetMediaByWebsiteAndType(websiteID int64, mimeType string, limit int) ([]Media, error) {
    var media []Media
    return media, r.db.Where("website_id = ? AND mime_type LIKE ?", websiteID, mimeType+"%").
        Order("created_at DESC").
        Limit(limit).
        Find(&media).Error
}
```

## Best Practices

### Model Design
- Always include WebsiteID và TenantID
- Use appropriate data types
- Implement proper validation
- Use JSON fields for flexible configuration

### Database Performance
- Create appropriate indexes
- Use composite indexes for multi-field queries
- Implement proper pagination
- Monitor query performance

### Data Integrity
- Use foreign key constraints
- Implement cascade deletes appropriately
- Validate data at model level
- Use database transactions for complex operations

## Tài liệu liên quan

- [Multi-Tenancy](./multi-tenancy.md)
- [API Endpoints](./api-endpoints.md)
- [Theme Management](./theme-management.md)
- [Page Builder](./page-builder.md)