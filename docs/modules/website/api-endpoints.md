# API Endpoints

## Tổng quan

Website Module cung cấp comprehensive RESTful API endpoints để quản lý tất cả aspects của website bao gồm themes, pages, media, menus, và widgets. Tất cả endpoints đều được thiết kế với multi-tenancy support và security best practices.

## Base URL và Authentication

### Base URL
```
/api/cms/v1/
```

### Authentication Headers
```
Authorization: Bearer <jwt_token>
X-Website-ID: <website_id>
Content-Type: application/json
```

## Theme Management API

### List Themes
```http
GET /api/cms/v1/themes
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `status` (string): Filter by status (active, inactive)
- `search` (string): Search in theme name

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "themes": [
      {
        "id": 1,
        "website_id": 123,
        "name": "Modern Blog",
        "version": "1.2.0",
        "author": "Theme Author",
        "description": "A modern responsive blog theme",
        "config": {
          "colors": {
            "primary": "#007bff",
            "secondary": "#6c757d"
          },
          "typography": {
            "heading_font": "Roboto",
            "body_font": "Open Sans"
          }
        },
        "assets": {
          "css": "/themes/modern-blog/style.css",
          "js": "/themes/modern-blog/script.js",
          "preview": "/themes/modern-blog/preview.jpg"
        },
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-16T14:20:00Z"
      }
    ]
  },
  "meta": {
    "next_cursor": null,
    "previous_cursor": null,
    "has_more": false,
    "has_previous": false,
    "limit": 20
  }
}
```

### Get Theme Details
```http
GET /api/cms/v1/themes/{id}
```

### Upload New Theme
```http
POST /api/cms/v1/themes
Content-Type: multipart/form-data
```

**Form Data:**
- `theme_file` (file): Theme package (.zip)
- `auto_activate` (boolean): Auto activate after upload

### Update Theme Configuration
```http
PUT /api/cms/v1/themes/{id}
```

**Request Body:**
```json
{
  "config": {
    "colors": {
      "primary": "#28a745",
      "secondary": "#6c757d"
    },
    "typography": {
      "heading_font": "Montserrat",
      "body_font": "Source Sans Pro"
    }
  }
}
```

### Activate Theme
```http
POST /api/cms/v1/themes/{id}/activate
```

### Delete Theme
```http
DELETE /api/cms/v1/themes/{id}
```

## Page Management API

### List Pages
```http
GET /api/cms/v1/pages
```

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status` (string): Filter by status (draft, published, archived)
- `template` (string): Filter by template
- `search` (string): Search in title and content

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "pages": [
      {
        "id": 1,
        "website_id": 123,
        "title": "Welcome to Our Website",
        "slug": "welcome",
        "excerpt": "This is our homepage with welcome message",
        "content": {
          "blocks": [
            {
              "id": "block-1",
              "type": "heading",
              "attributes": {
                "level": 1,
                "text": "Welcome to Our Website"
              }
            }
          ]
        },
        "template": "page.html",
        "seo": {
          "title": "Welcome - Our Website",
          "description": "Welcome to our amazing website",
          "keywords": ["welcome", "homepage"],
          "canonical_url": "https://example.com/welcome"
        },
        "status": "published",
        "published_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-15T09:00:00Z",
        "updated_at": "2024-01-16T11:00:00Z"
      }
    ]
  }
}
```

### Get Page Details
```http
GET /api/cms/v1/pages/{id}
```

### Create New Page
```http
POST /api/cms/v1/pages
```

**Request Body:**
```json
{
  "title": "About Us",
  "slug": "about-us",
  "content": {
    "blocks": [
      {
        "id": "block-1",
        "type": "text",
        "attributes": {
          "content": "<p>Welcome to our company...</p>",
          "alignment": "left"
        }
      }
    ]
  },
  "template": "page.html",
  "seo": {
    "title": "About Us - Company Name",
    "description": "Learn more about our company and mission",
    "keywords": ["about", "company", "mission"]
  },
  "status": "draft"
}
```

### Update Page
```http
PUT /api/cms/v1/pages/{id}
```

### Publish Page
```http
POST /api/cms/v1/pages/{id}/publish
```

### Unpublish Page
```http
POST /api/cms/v1/pages/{id}/unpublish
```

### Delete Page
```http
DELETE /api/cms/v1/pages/{id}
```

### Duplicate Page
```http
POST /api/cms/v1/pages/{id}/duplicate
```

**Request Body:**
```json
{
  "title": "New Page Title",
  "slug": "new-page-slug"
}
```

## Media Library API

### List Media Files
```http
GET /api/cms/v1/media
```

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `type` (string): Filter by type (image, video, audio, document)
- `search` (string): Search in filename
- `folder` (string): Filter by folder

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "media": [
      {
        "id": 1,
        "website_id": 123,
        "filename": "hero-image.jpg",
        "original_name": "DSC_0001.jpg",
        "storage_path": "websites/123/media/2024/01/hero-image_abc123.jpg",
        "file_size": 2048576,
        "mime_type": "image/jpeg",
        "dimensions": {
          "width": 1920,
          "height": 1080
        },
        "metadata": {
          "thumbnails": {
            "small": {
              "url": "/media/thumb-150x150.jpg",
              "width": 150,
              "height": 150
            },
            "medium": {
              "url": "/media/thumb-300x300.jpg",
              "width": 300,
              "height": 300
            }
          },
          "exif": {
            "camera": "Canon EOS R5",
            "date_taken": "2024-01-15T10:30:00Z"
          }
        },
        "url": "https://cdn.example.com/media/hero-image.jpg",
        "alt_text": "Hero image for homepage",
        "caption": "Beautiful landscape photo",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Get Media Details
```http
GET /api/cms/v1/media/{id}
```

### Upload Media File
```http
POST /api/cms/v1/media/upload
Content-Type: multipart/form-data
```

**Form Data:**
- `file` (file): Media file to upload
- `alt_text` (string): Alt text for images
- `caption` (string): Caption for media
- `folder` (string): Target folder path

### Update Media Metadata
```http
PUT /api/cms/v1/media/{id}
```

**Request Body:**
```json
{
  "alt_text": "Updated alt text",
  "caption": "Updated caption",
  "filename": "new-filename.jpg"
}
```

### Delete Media File
```http
DELETE /api/cms/v1/media/{id}
```

### Generate Signed URL
```http
POST /api/cms/v1/media/{id}/signed-url
```

**Request Body:**
```json
{
  "expiry_minutes": 60
}
```

## Menu Management API

### List Menus
```http
GET /api/cms/v1/menus
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "menus": [
      {
        "id": 1,
        "website_id": 123,
        "name": "Main Navigation",
        "location": "header",
        "items": [
          {
            "id": "item-1",
            "title": "Home",
            "url": "/",
            "type": "page",
            "target": "_self",
            "order": 1,
            "children": []
          },
          {
            "id": "item-2",
            "title": "About",
            "url": "/about",
            "type": "page",
            "target": "_self",
            "order": 2,
            "children": [
              {
                "id": "item-2-1",
                "title": "Our Team",
                "url": "/about/team",
                "type": "page",
                "target": "_self",
                "order": 1
              }
            ]
          }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-16T11:00:00Z"
      }
    ]
  }
}
```

### Get Menu Details
```http
GET /api/cms/v1/menus/{id}
```

### Create Menu
```http
POST /api/cms/v1/menus
```

**Request Body:**
```json
{
  "name": "Footer Menu",
  "location": "footer",
  "items": [
    {
      "title": "Privacy Policy",
      "url": "/privacy",
      "type": "page",
      "target": "_self",
      "order": 1
    }
  ]
}
```

### Update Menu
```http
PUT /api/cms/v1/menus/{id}
```

### Delete Menu
```http
DELETE /api/cms/v1/menus/{id}
```

## Widget Management API

### List Widgets
```http
GET /api/cms/v1/widgets
```

**Query Parameters:**
- `position` (string): Filter by position (sidebar, footer, header)
- `type` (string): Filter by widget type

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "widgets": [
      {
        "id": 1,
        "website_id": 123,
        "type": "recent_posts",
        "title": "Recent Posts",
        "position": "sidebar",
        "config": {
          "posts_count": 5,
          "show_thumbnails": true,
          "show_dates": true
        },
        "order": 1,
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Create Widget
```http
POST /api/cms/v1/widgets
```

### Update Widget
```http
PUT /api/cms/v1/widgets/{id}
```

### Delete Widget
```http
DELETE /api/cms/v1/widgets/{id}
```

### Reorder Widgets
```http
POST /api/cms/v1/widgets/reorder
```

**Request Body:**
```json
{
  "position": "sidebar",
  "widget_orders": [
    {"id": 1, "order": 1},
    {"id": 2, "order": 2},
    {"id": 3, "order": 3}
  ]
}
```

## SEO Management API

### Get SEO Analysis
```http
GET /api/cms/v1/pages/{id}/seo-analysis
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "analysis": {
      "score": 85,
      "issues": [
        {
          "type": "meta_description_length",
          "severity": "medium",
          "message": "Meta description is too short"
        }
      ],
      "suggestions": [
        {
          "type": "keyword_density",
          "message": "Consider using target keyword more frequently"
        }
      ],
      "metrics": {
        "title_length": 45,
        "description_length": 120,
        "heading_structure": "good",
        "image_alt_tags": 8,
        "internal_links": 3
      }
    }
  }
}
```

### Generate Sitemap
```http
GET /api/cms/v1/sitemap.xml
```

### Get Robots.txt
```http
GET /api/cms/v1/robots.txt
```

### Update SEO Settings
```http
PUT /api/cms/v1/seo/settings
```

**Request Body:**
```json
{
  "meta_title_template": "{page_title} - {site_name}",
  "meta_description_template": "{page_excerpt}",
  "robots_rules": [
    {
      "user_agent": "*",
      "disallow": ["/admin", "/api"],
      "allow": ["/public"],
      "crawl_delay": 1
    }
  ],
  "structured_data": {
    "enable_website_schema": true,
    "enable_article_schema": true,
    "enable_breadcrumb_schema": true
  }
}
```

## Website Settings API

### Get Website Settings
```http
GET /api/cms/v1/settings
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Success"
  },
  "data": {
    "settings": {
      "site_name": "My Website",
      "site_description": "Welcome to my website",
      "site_logo": "/media/logo.png",
      "favicon": "/media/favicon.ico",
      "timezone": "UTC",
      "language": "en",
      "theme_settings": {
        "active_theme": "modern-blog",
        "custom_css": ".custom { color: red; }",
        "custom_js": "console.log('Hello');"
      },
      "social_media": {
        "facebook": "https://facebook.com/mypage",
        "twitter": "https://twitter.com/myhandle",
        "instagram": "https://instagram.com/myhandle"
      },
      "analytics": {
        "google_analytics_id": "GA-XXXXX-X",
        "google_tag_manager_id": "GTM-XXXXX",
        "facebook_pixel_id": "123456789"
      }
    }
  }
}
```

### Update Website Settings
```http
PUT /api/cms/v1/settings
```

## Analytics API

### Get Page Analytics
```http
GET /api/cms/v1/analytics/pages/{id}
```

**Query Parameters:**
- `start_date` (string): Start date (YYYY-MM-DD)
- `end_date` (string): End date (YYYY-MM-DD)
- `metrics` (string): Comma-separated metrics (views, visitors, bounce_rate)

### Get Website Analytics
```http
GET /api/cms/v1/analytics/website
```

### Get Real-time Analytics
```http
GET /api/cms/v1/analytics/realtime
```

## Error Responses

### Standard Error Format
```json
{
  "status": {
    "code": 400,
    "message": "Validation Error"
  },
  "errors": [
    {
      "field": "title",
      "code": "required",
      "message": "Title is required"
    },
    {
      "field": "slug",
      "code": "unique",
      "message": "Slug must be unique"
    }
  ]
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Rate Limit Rules
- **General API**: 1000 requests per hour
- **Media Upload**: 100 uploads per hour
- **Bulk Operations**: 50 requests per hour

## Webhook Events

### Available Events
- `page.created`
- `page.updated`
- `page.published`
- `page.deleted`
- `theme.activated`
- `media.uploaded`
- `menu.updated`

### Webhook Payload Example
```json
{
  "event": "page.published",
  "timestamp": "2024-01-15T10:30:00Z",
  "website_id": 123,
  "data": {
    "page": {
      "id": 1,
      "title": "New Blog Post",
      "slug": "new-blog-post",
      "status": "published"
    }
  }
}
```

## API Versioning

### Version Headers
```
Accept: application/vnd.api+json;version=1
```

### Backward Compatibility
- v1: Current stable version
- Deprecated features marked in responses
- 6-month deprecation notice for breaking changes

## Best Practices

### Request Guidelines
- Always include `X-Website-ID` header
- Use appropriate HTTP methods
- Include proper Content-Type headers
- Handle rate limiting gracefully

### Response Handling
- Check status codes before parsing data
- Handle pagination for list endpoints
- Implement retry logic for 5xx errors
- Cache responses when appropriate

### Security
- Always use HTTPS
- Validate JWT tokens
- Sanitize all inputs
- Follow OWASP guidelines

## Tài liệu liên quan

- [Models Schema](./models-schema.md)
- [Multi-Tenancy](./multi-tenancy.md)
- [Theme Management](./theme-management.md)
- [SEO Optimization](./seo-optimization.md)