# Theme Management

## Tổng quan

Theme System trong Website Module cho phép người dùng tùy chỉnh giao diện website của họ thông qua việc cài đặt, cấu hình và quản lý các theme khác nhau. Hệ thống này hỗ trợ multi-tenancy và cung cấp các công cụ linh hoạt để tùy chỉnh giao diện mà không cần kiến thức lập trình.

## Theme System Architecture

### Cấu trúc Theme

```
theme-name/
├── theme.json              # Metadata theme
├── style.css              # CSS chính
├── script.js              # JavaScript
├── templates/             # Template files
│   ├── index.html         # Trang chủ
│   ├── single.html        # Trang bài viết
│   ├── category.html      # Trang danh mục
│   └── page.html          # Trang custom
├── partials/              # Partial templates
│   ├── header.html        # Header
│   ├── footer.html        # Footer
│   └── sidebar.html       # Sidebar
└── assets/                # Static assets
    ├── images/
    ├── fonts/
    └── icons/
```

### Theme Configuration

```json
{
  "name": "Modern Blog",
  "version": "1.0.0",
  "author": "Theme Author",
  "description": "A modern blog theme",
  "settings": {
    "colors": {
      "primary": "#007bff",
      "secondary": "#6c757d",
      "background": "#ffffff"
    },
    "typography": {
      "heading_font": "Roboto",
      "body_font": "Open Sans"
    },
    "layout": {
      "sidebar_position": "right",
      "container_width": "1200px"
    }
  },
  "supported_blocks": [
    "text", "heading", "image", "video",
    "gallery", "button", "form"
  ]
}
```

## Theme Model

### Theme Data Structure

- **ID**: Định danh theme
- **WebsiteID**: ID website sở hữu (multi-tenancy)
- **TenantID**: ID tenant sở hữu
- **Name**: Tên theme
- **Version**: Phiên bản theme
- **Config**: Cấu hình theme (JSON)
- **Assets**: Đường dẫn assets (CSS, JS, images)
- **Status**: Trạng thái (active, inactive)

## Theme Installation Flow

```mermaid
sequenceDiagram
    participant User as Người dùng
    participant Admin as Website Admin
    participant ThemeStore as Theme Store
    participant API as Website API
    participant Storage as File Storage
    
    User->>Admin: Chọn theme từ store
    Admin->>ThemeStore: Download theme package
    ThemeStore->>Admin: Trả về theme files
    Admin->>API: Upload theme
    API->>Storage: Lưu theme files
    API->>API: Parse theme config
    API->>Admin: Theme cài đặt thành công
```

## Theme Management API

### Theme Endpoints

- `GET /api/cms/v1/themes` - Danh sách theme (filtered by website_id)
- `POST /api/cms/v1/themes` - Upload theme mới
- `PUT /api/cms/v1/themes/{id}` - Cập nhật theme
- `POST /api/cms/v1/themes/{id}/activate` - Kích hoạt theme

### Theme Service Implementation

```go
type ThemeService struct {
    repo  ThemeRepository
    cache CacheService
}

func (s *ThemeService) GetActiveThemeForWebsite(websiteID int64) (*Theme, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, "active_theme")
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Theme), nil
    }
    
    // Fetch from database with website_id filter
    theme, err := s.repo.GetActiveThemeByWebsiteID(websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cacheTheme(websiteID, theme)
    return theme, nil
}
```

## Theme Customization

### Color Settings
- Primary và secondary colors
- Background và text colors
- Accent colors cho highlights
- Custom color palettes

### Typography Settings
- Heading font families
- Body text font families
- Font sizes và weights
- Line heights và spacing

### Layout Options
- Sidebar position (left, right, none)
- Container width settings
- Grid system configuration
- Responsive breakpoints

## Theme Repository Pattern

```go
type ThemeRepository interface {
    GetThemesByWebsiteID(websiteID int64) ([]*Theme, error)
    GetActiveThemeByWebsiteID(websiteID int64) (*Theme, error)
    ActivateTheme(websiteID int64, themeID int64) error
    UpdateThemeConfig(themeID int64, config ThemeConfig) error
}

type themeRepository struct {
    db *gorm.DB
}

func (r *themeRepository) GetThemesByWebsiteID(websiteID int64) ([]*Theme, error) {
    var themes []*Theme
    return themes, r.db.Where("website_id = ?", websiteID).Find(&themes).Error
}

func (r *themeRepository) GetActiveThemeByWebsiteID(websiteID int64) (*Theme, error) {
    var theme Theme
    return &theme, r.db.Where("website_id = ? AND status = ?", websiteID, "active").First(&theme).Error
}
```

## Theme Asset Management

### Asset Organization
- CSS files compilation
- JavaScript bundling
- Image optimization
- Font loading strategies

### Asset Delivery
- CDN integration
- Caching headers
- Asset versioning
- Preloading critical resources

## Theme Security

### Theme Validation
- Configuration validation
- Template security checks
- Asset scanning
- XSS prevention

### Sandbox Environment
- Theme preview mode
- Safe template rendering
- Limited API access
- Resource usage limits

## Performance Optimization

### Theme Loading
- Lazy load non-critical assets
- CSS critical path optimization
- JavaScript execution optimization
- Asset compression

### Caching Strategy
- Theme configuration caching
- Template compilation caching
- Asset caching policies
- Browser cache optimization

## Best Practices

### Theme Development
- Follow naming conventions
- Implement responsive design
- Optimize for performance
- Test across browsers

### Theme Management
- Regular theme updates
- Security patch management
- Performance monitoring
- User feedback collection

## Tài liệu liên quan

- [Page Builder](./page-builder.md)
- [Frontend Rendering](./frontend-rendering.md)
- [Multi-Tenancy](./multi-tenancy.md)
- [API Endpoints](./api-endpoints.md)