# Media Management

## Tổng quan

Media Management System cung cấp khả năng quản lý tất cả các file media như hình ảnh, video, audio và documents trong website. Hệ thống này tối ưu hóa performance, storage và delivery của media files với multi-tenancy support.

## Media Upload Flow

```mermaid
flowchart TD
    A[Chọn file] --> B[Validate file]
    B --> C{File hợp lệ?}
    C -->|Không| D[Hiển thị lỗi]
    C -->|Có| E[Resize/Optimize]
    E --> F[Upload to storage]
    F --> G[Generate thumbnails]
    G --> H[Lưu metadata DB]
    H --> I[Trả về URL]
```

## Media Model

### Media Data Structure

- **ID**: Định danh media file
- **WebsiteID**: ID website (multi-tenancy)
- **TenantID**: ID tenant
- **FileName**: Tên file gốc
- **StoragePath**: Đường dẫn lưu trữ
- **FileSize**: <PERSON><PERSON><PERSON> thước file
- **MimeType**: Loại file
- **Dimensions**: <PERSON><PERSON><PERSON> th<PERSON> (cho ảnh/video)
- **Metadata**: Thông tin EXIF và metadata khác
- **Status**: Trạng thái file

### Media Metadata Structure

```json
{
  "original": {
    "width": 1920,
    "height": 1080,
    "file_size": 2048576,
    "format": "JPEG"
  },
  "thumbnails": {
    "small": {
      "width": 150,
      "height": 150,
      "url": "/media/thumb-150x150.jpg"
    },
    "medium": {
      "width": 300,
      "height": 300,
      "url": "/media/thumb-300x300.jpg"
    },
    "large": {
      "width": 800,
      "height": 600,
      "url": "/media/thumb-800x600.jpg"
    }
  },
  "exif": {
    "camera": "Canon EOS R5",
    "iso": 100,
    "focal_length": "50mm",
    "date_taken": "2024-01-15T10:30:00Z"
  }
}
```

## File Upload Processing

### Upload Validation

```go
type FileValidator struct {
    allowedTypes    map[string]bool
    maxFileSize     int64
    maxDimensions   Dimensions
}

func (v *FileValidator) ValidateUpload(file multipart.File, header *multipart.FileHeader) error {
    // Check file size
    if header.Size > v.maxFileSize {
        return fmt.Errorf("file size exceeds limit: %d bytes", header.Size)
    }
    
    // Check MIME type
    mimeType := header.Header.Get("Content-Type")
    if !v.allowedTypes[mimeType] {
        return fmt.Errorf("file type not allowed: %s", mimeType)
    }
    
    // For images, check dimensions
    if strings.HasPrefix(mimeType, "image/") {
        return v.validateImageDimensions(file)
    }
    
    return nil
}
```

### Image Processing Pipeline

```go
type ImageProcessor struct {
    resizer     ImageResizer
    optimizer   ImageOptimizer
    converter   FormatConverter
}

func (p *ImageProcessor) ProcessImage(src io.Reader, config ProcessingConfig) (*ProcessedImage, error) {
    // 1. Decode original image
    img, format, err := image.Decode(src)
    if err != nil {
        return nil, err
    }
    
    // 2. Generate thumbnails
    thumbnails := make(map[string]*Thumbnail)
    for name, size := range config.ThumbnailSizes {
        thumb, err := p.resizer.Resize(img, size.Width, size.Height)
        if err != nil {
            return nil, err
        }
        
        thumbnails[name] = &Thumbnail{
            Image:  thumb,
            Width:  size.Width,
            Height: size.Height,
        }
    }
    
    // 3. Optimize original
    optimized, err := p.optimizer.Optimize(img, config.Quality)
    if err != nil {
        return nil, err
    }
    
    // 4. Convert to modern formats if needed
    webp, err := p.converter.ToWebP(optimized, config.WebPQuality)
    if err != nil {
        return nil, err
    }
    
    return &ProcessedImage{
        Original:   optimized,
        WebP:       webp,
        Thumbnails: thumbnails,
    }, nil
}
```

## Storage Strategy

### Storage Backends

```go
type StorageBackend interface {
    Upload(path string, data io.Reader) (*UploadResult, error)
    Download(path string) (io.ReadCloser, error)
    Delete(path string) error
    GetURL(path string) string
    GetPresignedURL(path string, expiry time.Duration) (string, error)
}

// Local file system storage
type LocalStorage struct {
    basePath string
    baseURL  string
}

// AWS S3 storage
type S3Storage struct {
    bucket string
    region string
    client *s3.Client
}

// Google Cloud Storage
type GCSStorage struct {
    bucket string
    client *storage.Client
}
```

### Storage Path Strategy

```go
func (s *MediaService) generateStoragePath(websiteID int64, filename string) string {
    // Organize by website and date
    now := time.Now()
    year := now.Format("2006")
    month := now.Format("01")
    
    // Generate unique filename to avoid conflicts
    ext := filepath.Ext(filename)
    name := strings.TrimSuffix(filename, ext)
    uuid := generateUUID()
    
    return fmt.Sprintf("websites/%d/media/%s/%s/%s_%s%s", 
        websiteID, year, month, name, uuid, ext)
}
```

## Media API Endpoints

### Media Management Endpoints

- `GET /api/cms/v1/media` - Danh sách media (filtered by website_id)
- `POST /api/cms/v1/media/upload` - Upload file
- `PUT /api/cms/v1/media/{id}` - Cập nhật metadata
- `DELETE /api/cms/v1/media/{id}` - Xóa file

### Media Service Implementation

```go
type MediaService struct {
    repo     MediaRepository
    storage  StorageBackend
    processor ImageProcessor
    cache    CacheService
}

func (s *MediaService) UploadFile(websiteID int64, file multipart.File, header *multipart.FileHeader) (*Media, error) {
    // 1. Validate file
    if err := s.validator.ValidateUpload(file, header); err != nil {
        return nil, err
    }
    
    // 2. Generate storage path
    storagePath := s.generateStoragePath(websiteID, header.Filename)
    
    // 3. Process file if it's an image
    var metadata MediaMetadata
    if strings.HasPrefix(header.Header.Get("Content-Type"), "image/") {
        processed, err := s.processor.ProcessImage(file, ProcessingConfig{
            ThumbnailSizes: DefaultThumbnailSizes,
            Quality:        85,
            WebPQuality:    80,
        })
        if err != nil {
            return nil, err
        }
        
        // Upload processed images
        if err := s.uploadProcessedImage(storagePath, processed); err != nil {
            return nil, err
        }
        
        metadata = s.buildImageMetadata(processed)
    } else {
        // Upload file as-is
        if _, err := s.storage.Upload(storagePath, file); err != nil {
            return nil, err
        }
    }
    
    // 4. Save metadata to database
    media := &Media{
        WebsiteID:   websiteID,
        FileName:    header.Filename,
        StoragePath: storagePath,
        FileSize:    header.Size,
        MimeType:    header.Header.Get("Content-Type"),
        Metadata:    metadata,
        Status:      "uploaded",
    }
    
    return s.repo.Create(media)
}
```

## Media Optimization

### Image Optimization Techniques

- **Format Conversion**: JPEG, PNG → WebP, AVIF
- **Compression**: Lossy và lossless compression
- **Resizing**: Tạo multiple sizes cho responsive images
- **Progressive Loading**: Progressive JPEG
- **Metadata Stripping**: Xóa EXIF data không cần thiết

### Video Optimization

```go
type VideoProcessor struct {
    ffmpeg FFmpegWrapper
}

func (p *VideoProcessor) ProcessVideo(src string, config VideoConfig) (*ProcessedVideo, error) {
    // 1. Generate thumbnail from video
    thumbnail, err := p.ffmpeg.ExtractThumbnail(src, config.ThumbnailTime)
    if err != nil {
        return nil, err
    }
    
    // 2. Transcode to multiple formats/qualities
    var variants []VideoVariant
    for _, preset := range config.Presets {
        variant, err := p.ffmpeg.Transcode(src, preset)
        if err != nil {
            return nil, err
        }
        variants = append(variants, variant)
    }
    
    return &ProcessedVideo{
        Thumbnail: thumbnail,
        Variants:  variants,
    }, nil
}
```

## CDN Integration

### CDN Configuration

```go
type CDNConfig struct {
    BaseURL        string
    CacheHeaders   map[string]string
    Transformations CDNTransformations
}

type CDNTransformations struct {
    ImageResize    bool
    ImageOptimize  bool
    VideoStreaming bool
}

func (s *MediaService) GetCDNURL(media *Media, transform *Transform) string {
    baseURL := s.cdnConfig.BaseURL
    path := media.StoragePath
    
    if transform != nil && s.cdnConfig.Transformations.ImageResize {
        path += s.buildTransformParams(transform)
    }
    
    return fmt.Sprintf("%s/%s", baseURL, path)
}
```

### Smart Image Delivery

```go
func (s *MediaService) GetOptimalImageURL(media *Media, context RequestContext) string {
    // Choose format based on browser support
    format := "jpg"
    if context.SupportsWebP {
        format = "webp"
    }
    if context.SupportsAVIF {
        format = "avif"
    }
    
    // Choose size based on device
    size := s.chooseOptimalSize(context.DevicePixelRatio, context.ViewportWidth)
    
    transform := &Transform{
        Width:   size.Width,
        Height:  size.Height,
        Format:  format,
        Quality: s.getOptimalQuality(context.ConnectionSpeed),
    }
    
    return s.GetCDNURL(media, transform)
}
```

## Lazy Loading Implementation

### Progressive Image Loading

```javascript
class LazyImageLoader {
    constructor() {
        this.observer = new IntersectionObserver(this.handleIntersect.bind(this));
        this.init();
    }
    
    init() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.observer.observe(img));
    }
    
    handleIntersect(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadImage(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    loadImage(img) {
        // Load appropriate size based on container
        const containerWidth = img.parentElement.offsetWidth;
        const srcset = this.buildSrcSet(img.dataset, containerWidth);
        
        img.srcset = srcset;
        img.src = img.dataset.src;
        img.classList.add('loaded');
    }
}
```

## Media Security

### Access Control

```go
func (s *MediaService) CheckAccess(mediaID int64, userContext UserContext) error {
    media, err := s.repo.GetByID(mediaID)
    if err != nil {
        return err
    }
    
    // Check if user has access to this website
    if !userContext.HasWebsiteAccess(media.WebsiteID) {
        return ErrAccessDenied
    }
    
    return nil
}

func (s *MediaService) GenerateSecureURL(mediaID int64, expiry time.Duration) (string, error) {
    media, err := s.repo.GetByID(mediaID)
    if err != nil {
        return "", err
    }
    
    // Generate signed URL
    return s.storage.GetPresignedURL(media.StoragePath, expiry)
}
```

### File Scanning

```go
type VirusScanner interface {
    ScanFile(file io.Reader) (*ScanResult, error)
}

func (s *MediaService) scanUploadedFile(file io.Reader) error {
    if s.virusScanner == nil {
        return nil // Scanner not configured
    }
    
    result, err := s.virusScanner.ScanFile(file)
    if err != nil {
        return err
    }
    
    if result.IsInfected {
        return fmt.Errorf("file contains malware: %s", result.ThreatName)
    }
    
    return nil
}
```

## Performance Monitoring

### Media Usage Analytics

```go
type MediaAnalytics struct {
    views     int64
    downloads int64
    bandwidth int64
    lastAccess time.Time
}

func (s *MediaService) TrackMediaAccess(mediaID int64, accessType string, bytes int64) {
    analytics := s.getOrCreateAnalytics(mediaID)
    
    switch accessType {
    case "view":
        analytics.views++
    case "download":
        analytics.downloads++
    }
    
    analytics.bandwidth += bytes
    analytics.lastAccess = time.Now()
    
    s.updateAnalytics(mediaID, analytics)
}
```

### Storage Optimization

- **Automated cleanup**: Xóa file không sử dụng
- **Duplicate detection**: Phát hiện file trùng lặp
- **Archive old files**: Chuyển file cũ sang cold storage
- **Usage analytics**: Theo dõi file usage patterns

## Best Practices

### Upload Guidelines
- Validate file types và sizes
- Implement virus scanning
- Use secure storage paths
- Generate thumbnails automatically

### Performance
- Implement lazy loading
- Use responsive images
- Optimize image formats
- Leverage CDN caching

### Security
- Validate all uploads
- Use signed URLs for sensitive content
- Implement access controls
- Regular security scans

## Tài liệu liên quan

- [Page Builder](./page-builder.md)
- [Theme Management](./theme-management.md)
- [Frontend Rendering](./frontend-rendering.md)
- [Multi-Tenancy](./multi-tenancy.md)