# Multi-Tenancy Implementation

## Tổng quan

Website Module được thiết kế với multi-tenancy architecture cho phép mỗi tenant có website riêng biệt với complete isolation về data, themes, media, và configurations. Hệ thống đảm bảo tenant không thể truy cập hoặc can thiệp vào data của tenant khác.

## Tenant Isolation Strategy

### Website Context Identification

```go
type WebsiteContext struct {
    ID        int64  `json:"id"`
    TenantID  int64  `json:"tenant_id"`
    Domain    string `json:"domain"`
    Subdomain string `json:"subdomain"`
    Name      string `json:"name"`
    Theme     string `json:"theme"`
    Status    string `json:"status"`
}

func (w *WebsiteContext) Validate() error {
    if w.ID == 0 {
        return errors.New("website ID is required")
    }
    if w.TenantID == 0 {
        return errors.New("tenant ID is required")
    }
    return nil
}
```

### Website Context Middleware

```go
func WebsiteContextMiddleware(websiteService WebsiteService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract website ID from multiple sources
        websiteID := extractWebsiteID(c)
        if websiteID == 0 {
            c.JSON(400, gin.H{
                "status": gin.H{
                    "code":    400,
                    "message": "Website ID is required",
                },
                "errors": []gin.H{
                    {
                        "field":   "website_id",
                        "code":    "required",
                        "message": "Website ID must be provided via header or subdomain",
                    },
                },
            })
            c.Abort()
            return
        }
        
        // Validate website exists and get context
        websiteCtx, err := websiteService.GetWebsiteContext(websiteID)
        if err != nil {
            c.JSON(404, gin.H{
                "status": gin.H{
                    "code":    404,
                    "message": "Website not found",
                },
            })
            c.Abort()
            return
        }
        
        // Check if website is active
        if websiteCtx.Status != "active" {
            c.JSON(403, gin.H{
                "status": gin.H{
                    "code":    403,
                    "message": "Website is not active",
                },
            })
            c.Abort()
            return
        }
        
        // Store in context for use in handlers
        c.Set("website_id", websiteID)
        c.Set("website_context", websiteCtx)
        c.Set("tenant_id", websiteCtx.TenantID)
        
        c.Next()
    }
}

func extractWebsiteID(c *gin.Context) int64 {
    // 1. Try from X-Website-ID header (highest priority)
    if websiteID := c.GetHeader("X-Website-ID"); websiteID != "" {
        if id, err := strconv.ParseInt(websiteID, 10, 64); err == nil {
            return id
        }
    }
    
    // 2. Try from subdomain
    host := c.Request.Host
    if subdomain := extractSubdomain(host); subdomain != "" {
        return lookupWebsiteBySubdomain(subdomain)
    }
    
    // 3. Try from custom domain
    if websiteID := lookupWebsiteByDomain(host); websiteID > 0 {
        return websiteID
    }
    
    return 0
}

func extractSubdomain(host string) string {
    parts := strings.Split(host, ".")
    if len(parts) >= 3 {
        return parts[0]
    }
    return ""
}
```

## Repository Pattern với Website Isolation

### Base Repository Interface

```go
type WebsiteRepository interface {
    GetThemesByWebsiteID(websiteID int64) ([]*Theme, error)
    GetPagesByWebsiteID(websiteID int64) ([]*Page, error)
    GetMenusByWebsiteID(websiteID int64) ([]*Menu, error)
    GetWidgetsByWebsiteID(websiteID int64) ([]*Widget, error)
    GetMediaByWebsiteID(websiteID int64) ([]*Media, error)
}

type websiteRepository struct {
    db *gorm.DB
}

func NewWebsiteRepository(db *gorm.DB) WebsiteRepository {
    return &websiteRepository{db: db}
}
```

### Theme Repository với Website Filtering

```go
func (r *websiteRepository) GetThemesByWebsiteID(websiteID int64) ([]*Theme, error) {
    var themes []*Theme
    return themes, r.db.Where("website_id = ?", websiteID).Find(&themes).Error
}

func (r *websiteRepository) GetActiveThemeByWebsiteID(websiteID int64) (*Theme, error) {
    var theme Theme
    err := r.db.Where("website_id = ? AND status = ?", websiteID, "active").First(&theme).Error
    if err != nil {
        return nil, err
    }
    return &theme, nil
}

func (r *websiteRepository) CreateTheme(theme *Theme) error {
    // Ensure website_id is set
    if theme.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    // Validate website exists
    if err := r.validateWebsiteExists(theme.WebsiteID); err != nil {
        return err
    }
    
    return r.db.Create(theme).Error
}

func (r *websiteRepository) validateWebsiteExists(websiteID int64) error {
    var count int64
    err := r.db.Model(&Website{}).Where("id = ?", websiteID).Count(&count).Error
    if err != nil {
        return err
    }
    if count == 0 {
        return errors.New("website not found")
    }
    return nil
}
```

### Page Repository với Content Isolation

```go
func (r *websiteRepository) GetPagesByWebsiteID(websiteID int64) ([]*Page, error) {
    var pages []*Page
    return pages, r.db.Where("website_id = ?", websiteID).Find(&pages).Error
}

func (r *websiteRepository) GetPageBySlugAndWebsiteID(slug string, websiteID int64) (*Page, error) {
    var page Page
    err := r.db.Where("slug = ? AND website_id = ?", slug, websiteID).First(&page).Error
    if err != nil {
        return nil, err
    }
    return &page, nil
}

func (r *websiteRepository) CreatePage(page *Page) error {
    // Ensure website_id is set
    if page.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    // Check for slug uniqueness within website
    if err := r.validateSlugUnique(page.Slug, page.WebsiteID, 0); err != nil {
        return err
    }
    
    return r.db.Create(page).Error
}

func (r *websiteRepository) validateSlugUnique(slug string, websiteID int64, excludePageID int64) error {
    var count int64
    query := r.db.Model(&Page{}).Where("slug = ? AND website_id = ?", slug, websiteID)
    if excludePageID > 0 {
        query = query.Where("id != ?", excludePageID)
    }
    
    err := query.Count(&count).Error
    if err != nil {
        return err
    }
    if count > 0 {
        return errors.New("slug must be unique within website")
    }
    return nil
}
```

### Media Repository với Storage Isolation

```go
func (r *websiteRepository) GetMediaByWebsiteID(websiteID int64) ([]*Media, error) {
    var media []*Media
    return media, r.db.Where("website_id = ?", websiteID).Find(&media).Error
}

func (r *websiteRepository) CreateMedia(media *Media) error {
    // Ensure website_id is set
    if media.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    // Generate isolated storage path
    media.StoragePath = r.generateIsolatedStoragePath(media.WebsiteID, media.FileName)
    
    return r.db.Create(media).Error
}

func (r *websiteRepository) generateIsolatedStoragePath(websiteID int64, filename string) string {
    now := time.Now()
    year := now.Format("2006")
    month := now.Format("01")
    
    // Create isolated path with website ID
    return fmt.Sprintf("websites/%d/media/%s/%s/%s", websiteID, year, month, filename)
}
```

## Service Layer với Website Context

### Website Service Implementation

```go
type WebsiteService struct {
    repo  WebsiteRepository
    cache CacheService
}

func (s *WebsiteService) GetWebsiteContext(websiteID int64) (*WebsiteContext, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, "context")
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*WebsiteContext), nil
    }
    
    // Fetch from database
    website, err := s.repo.GetWebsiteByID(websiteID)
    if err != nil {
        return nil, err
    }
    
    context := &WebsiteContext{
        ID:        website.ID,
        TenantID:  website.TenantID,
        Domain:    website.Domain,
        Subdomain: website.Subdomain,
        Name:      website.Name,
        Theme:     website.ActiveTheme,
        Status:    website.Status,
    }
    
    // Cache the context
    s.cache.Set(cacheKey, context, 30*time.Minute)
    return context, nil
}

func (s *WebsiteService) getCacheKey(websiteID int64, suffix string) string {
    return fmt.Sprintf("website:%d:%s", websiteID, suffix)
}
```

### Theme Service với Website Scoping

```go
func (s *WebsiteService) GetActiveThemeForWebsite(websiteID int64) (*Theme, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, "active_theme")
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Theme), nil
    }
    
    // Fetch from database with website_id filter
    theme, err := s.repo.GetActiveThemeByWebsiteID(websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cacheTheme(websiteID, theme)
    return theme, nil
}

func (s *WebsiteService) ActivateTheme(websiteID int64, themeID int64) error {
    // Validate theme belongs to website
    theme, err := s.repo.GetThemeByID(themeID)
    if err != nil {
        return err
    }
    
    if theme.WebsiteID != websiteID {
        return errors.New("theme does not belong to this website")
    }
    
    // Deactivate current theme
    if err := s.repo.DeactivateAllThemes(websiteID); err != nil {
        return err
    }
    
    // Activate new theme
    if err := s.repo.ActivateTheme(themeID); err != nil {
        return err
    }
    
    // Clear cache
    s.clearWebsiteCache(websiteID)
    return nil
}
```

### Page Service với Content Isolation

```go
func (s *WebsiteService) GetPageBySlugForWebsite(websiteID int64, slug string) (*Page, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("page:%s", slug))
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Page), nil
    }
    
    // Fetch from database with website_id filter
    page, err := s.repo.GetPageBySlugAndWebsiteID(slug, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cachePage(websiteID, page)
    return page, nil
}

func (s *WebsiteService) CreatePage(websiteID int64, pageData CreatePageRequest) (*Page, error) {
    page := &Page{
        WebsiteID: websiteID,
        Title:     pageData.Title,
        Slug:      pageData.Slug,
        Content:   pageData.Content,
        Template:  pageData.Template,
        SEO:       pageData.SEO,
        Status:    pageData.Status,
    }
    
    // Generate slug if not provided
    if page.Slug == "" {
        page.Slug = s.generateSlug(page.Title, websiteID)
    }
    
    // Create in database
    if err := s.repo.CreatePage(page); err != nil {
        return nil, err
    }
    
    // Clear website cache
    s.clearWebsiteCache(websiteID)
    return page, nil
}
```

## Cache Keys với Website Isolation

### Website-Scoped Cache Keys

```go
func (s *WebsiteService) getCacheKey(websiteID int64, suffix string) string {
    return fmt.Sprintf("website:%d:%s", websiteID, suffix)
}

// Cache theme data
func (s *WebsiteService) cacheTheme(websiteID int64, theme *Theme) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("theme:%s", theme.Name))
    s.cache.Set(key, theme, 1*time.Hour)
}

// Cache page data
func (s *WebsiteService) cachePage(websiteID int64, page *Page) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("page:%s", page.Slug))
    s.cache.Set(key, page, 30*time.Minute)
}

// Cache menu data
func (s *WebsiteService) cacheMenu(websiteID int64, menu *Menu) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("menu:%s", menu.Location))
    s.cache.Set(key, menu, 1*time.Hour)
}

// Cache media data
func (s *WebsiteService) cacheMedia(websiteID int64, media *Media) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("media:%d", media.ID))
    s.cache.Set(key, media, 2*time.Hour)
}
```

### Cache Invalidation Patterns

```go
func (s *WebsiteService) clearWebsiteCache(websiteID int64) error {
    patterns := []string{
        fmt.Sprintf("website:%d:*", websiteID),
    }
    
    for _, pattern := range patterns {
        if err := s.cache.DeleteByPattern(pattern); err != nil {
            return err
        }
    }
    
    return nil
}

func (s *WebsiteService) clearPageCache(websiteID int64, pageID int64) error {
    patterns := []string{
        fmt.Sprintf("website:%d:page:*", websiteID),
        fmt.Sprintf("website:%d:pages", websiteID),
    }
    
    for _, pattern := range patterns {
        if err := s.cache.DeleteByPattern(pattern); err != nil {
            return err
        }
    }
    
    return nil
}
```

## Request/Response Context

### Response với Website Context

```go
type WebsiteResponse struct {
    Status  Status      `json:"status"`
    Data    interface{} `json:"data"`
    Meta    *Meta       `json:"meta,omitempty"`
    Website *WebsiteContext `json:"website,omitempty"`
}

type Status struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

type Meta struct {
    Pagination *Pagination `json:"pagination,omitempty"`
}

func (s *WebsiteService) BuildResponse(data interface{}, websiteID int64) *WebsiteResponse {
    response := &WebsiteResponse{
        Status: Status{
            Code:    200,
            Message: "Success",
        },
        Data: data,
    }
    
    // Add website context if available
    if websiteID > 0 {
        if websiteCtx, err := s.GetWebsiteContext(websiteID); err == nil {
            response.Website = websiteCtx
        }
    }
    
    return response
}
```

### Handler với Website Context

```go
func (h *WebsiteHandler) GetPages(c *gin.Context) {
    // Get website ID from context (set by middleware)
    websiteID := c.GetInt64("website_id")
    
    // Parse query parameters
    params := ParsePageListParams(c)
    
    // Get pages for this website only
    pages, err := h.pageService.GetPagesByWebsiteID(websiteID, params)
    if err != nil {
        c.JSON(500, gin.H{
            "status": gin.H{
                "code":    500,
                "message": "Internal server error",
            },
        })
        return
    }
    
    // Build response with website context
    response := h.websiteService.BuildResponse(gin.H{
        "pages": pages,
    }, websiteID)
    
    c.JSON(200, response)
}

func (h *WebsiteHandler) CreatePage(c *gin.Context) {
    websiteID := c.GetInt64("website_id")
    
    var request CreatePageRequest
    if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(400, gin.H{
            "status": gin.H{
                "code":    400,
                "message": "Invalid request data",
            },
            "errors": []gin.H{
                {
                    "field":   "request",
                    "code":    "invalid",
                    "message": err.Error(),
                },
            },
        })
        return
    }
    
    // Create page for this website
    page, err := h.pageService.CreatePage(websiteID, request)
    if err != nil {
        c.JSON(422, gin.H{
            "status": gin.H{
                "code":    422,
                "message": "Validation error",
            },
            "errors": []gin.H{
                {
                    "field":   "page",
                    "code":    "creation_failed",
                    "message": err.Error(),
                },
            },
        })
        return
    }
    
    response := h.websiteService.BuildResponse(gin.H{
        "page": page,
    }, websiteID)
    
    c.JSON(201, response)
}
```

## Security và Access Control

### Website Ownership Validation

```go
func (s *WebsiteService) ValidateWebsiteAccess(websiteID int64, userID int64) error {
    // Get website context
    websiteCtx, err := s.GetWebsiteContext(websiteID)
    if err != nil {
        return err
    }
    
    // Check if user has access to this tenant
    hasAccess, err := s.tenantService.UserHasAccessToTenant(userID, websiteCtx.TenantID)
    if err != nil {
        return err
    }
    
    if !hasAccess {
        return errors.New("user does not have access to this website")
    }
    
    return nil
}

func (s *WebsiteService) ValidateResourceOwnership(resourceType string, resourceID int64, websiteID int64) error {
    switch resourceType {
    case "page":
        page, err := s.repo.GetPageByID(resourceID)
        if err != nil {
            return err
        }
        if page.WebsiteID != websiteID {
            return errors.New("page does not belong to this website")
        }
    case "theme":
        theme, err := s.repo.GetThemeByID(resourceID)
        if err != nil {
            return err
        }
        if theme.WebsiteID != websiteID {
            return errors.New("theme does not belong to this website")
        }
    case "media":
        media, err := s.repo.GetMediaByID(resourceID)
        if err != nil {
            return err
        }
        if media.WebsiteID != websiteID {
            return errors.New("media does not belong to this website")
        }
    default:
        return errors.New("unknown resource type")
    }
    
    return nil
}
```

### Resource Access Middleware

```go
func ResourceAccessMiddleware(resourceType string) gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetInt64("website_id")
        resourceIDStr := c.Param("id")
        
        resourceID, err := strconv.ParseInt(resourceIDStr, 10, 64)
        if err != nil {
            c.JSON(400, gin.H{
                "status": gin.H{
                    "code":    400,
                    "message": "Invalid resource ID",
                },
            })
            c.Abort()
            return
        }
        
        // Validate resource belongs to website
        websiteService := c.MustGet("website_service").(WebsiteService)
        if err := websiteService.ValidateResourceOwnership(resourceType, resourceID, websiteID); err != nil {
            c.JSON(404, gin.H{
                "status": gin.H{
                    "code":    404,
                    "message": "Resource not found",
                },
            })
            c.Abort()
            return
        }
        
        c.Set("resource_id", resourceID)
        c.Next()
    }
}
```

## Best Practices

### Database Design
- Always include `website_id` in all entities
- Use compound indexes (website_id, other_fields)
- Implement foreign key constraints
- Use consistent naming conventions

### API Design
- Require website context in all endpoints
- Validate resource ownership
- Use consistent error responses
- Implement proper pagination

### Caching Strategy
- Include website_id in all cache keys
- Implement cache invalidation patterns
- Use appropriate TTL values
- Monitor cache hit rates

### Security
- Validate tenant access at middleware level
- Check resource ownership before operations
- Implement audit logging
- Use secure session management

## Monitoring và Debugging

### Tenant Isolation Monitoring

```go
type TenantIsolationMonitor struct {
    logger Logger
    metrics MetricsCollector
}

func (m *TenantIsolationMonitor) LogCrossWebsiteAccess(userID int64, requestedWebsiteID int64, actualWebsiteID int64) {
    m.logger.Warn("Cross-website access attempt", 
        "user_id", userID,
        "requested_website_id", requestedWebsiteID,
        "actual_website_id", actualWebsiteID,
    )
    
    m.metrics.Increment("security.cross_website_access_attempts")
}

func (m *TenantIsolationMonitor) LogResourceAccessViolation(resourceType string, resourceID int64, websiteID int64) {
    m.logger.Error("Resource access violation",
        "resource_type", resourceType,
        "resource_id", resourceID,
        "website_id", websiteID,
    )
    
    m.metrics.Increment("security.resource_access_violations")
}
```

## Tài liệu liên quan

- [Models Schema](./models-schema.md)
- [API Endpoints](./api-endpoints.md)
- [Theme Management](./theme-management.md)
- [Tenant Module](../tenant.md)