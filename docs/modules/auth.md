# Auth Module

## Tổng quan

Auth Module cung cấp hệ thống xác thực và phân quyền toàn diện cho Blog API v3, bao gồm đăng ký, đ<PERSON><PERSON> nhập, quản lý JWT tokens, reset password, role-based access control, và các tính năng bảo mật nâng cao.

## Mục tiêu

- **User Authentication**: <PERSON><PERSON><PERSON> thực người dùng an toàn với JWT
- **Authorization**: Phân quyền dựa trên roles và permissions
- **Account Security**: Quản lý bảo mật tài khoản và authentication
- **Password Security**: B<PERSON>o mật mật khẩu với bcrypt và validation
- **Session Management**: Quản lý sessions và refresh tokens
- **Security Features**: Rate limiting, account lockout, audit logging
- **Multi-tenancy**: Hỗ trợ tenant isolation với website_id

## ⚠️ Phân định trách nhiệm Module

### Auth Module (Tài l<PERSON> này)
**Tập trung vào Authentication & Authorization:**
- ✅ User registration và login
- ✅ JWT token management và validation
- ✅ Password management (change password, reset password)
- ✅ Multi-factor authentication (2FA)
- ✅ Session management và refresh tokens
- ✅ Account security (lockout, rate limiting)
- ✅ Authorization và permission checking
- ✅ Audit logging cho security events

### User Module
**Tập trung vào User Profile & Preferences:**
- ✅ User profile management (bio, avatar, social links)
- ✅ User preferences (UI, content, notification settings)
- ✅ Activity tracking và analytics
- ✅ Social features (following, bookmarks)
- ✅ Privacy settings và GDPR compliance
- ✅ User-generated content management

**📋 Migration Plan:**
- Profile management endpoints sẽ được di chuyển từ Auth Module sang User Module
- Password change vẫn thuộc Auth Module vì liên quan đến security
- User preferences và settings thuộc User Module

## Kiến trúc hệ thống

### Auth Module Architecture

```mermaid
flowchart TD
    A[Auth Module] --> B[Authentication Engine]
    A --> C[Authorization Engine]
    A --> D[Token Manager]
    A --> E[Password Manager]
    A --> F[Session Manager]
    A --> G[Security Monitor]
    
    B --> B1[Login/Register]
    B --> B2[JWT Validation]
    B --> B3[Multi-factor Auth]
    B --> B4[Social Login]
    
    C --> C1[Role-based Access]
    C --> C2[Permission Checks]
    C --> C3[Resource Authorization]
    C --> C4[Dynamic Permissions]
    
    D --> D1[JWT Generation]
    D --> D2[Token Refresh]
    D --> D3[Token Blacklist]
    D --> D4[Token Validation]
    
    E --> E1[Password Hashing]
    E --> E2[Password Validation]
    E --> E3[Password Reset]
    E --> E4[Password History]
    
    F --> F1[Session Creation]
    F --> F2[Session Tracking]
    F --> F3[Device Management]
    F --> F4[Concurrent Sessions]
    
    G --> G1[Failed Attempts]
    G --> G2[Suspicious Activity]
    G --> G3[Audit Logging]
    G --> G4[Rate Limiting]
```

## Tài liệu chi tiết

Tài liệu Auth Module được tách thành các phần chuyên biệt để dễ quản lý và tham khảo:

### 📋 Core Documentation
- **[Authentication Flows](./auth/authentication-flows.md)** - Các luồng xác thực chi tiết với module integration
- **[Authorization & Permissions](./auth/authorization.md)** - Hệ thống phân quyền và kiểm soát truy cập
- **[Security Features](./auth/security-features.md)** - Các tính năng bảo mật: rate limiting, MFA, account lockout
- **[API Endpoints](./auth/api-endpoints.md)** - Tất cả endpoints của Auth Module với examples
- **[Models & Database Schema](./auth/models-schema.md)** - Cấu trúc database và models

### 🔧 Implementation & Architecture
- **[Implementation Details](./auth/implementation.md)** - Chi tiết implementation: services, repositories, cache
- **[Tenant Isolation](./auth/tenant-isolation.md)** - Multi-tenancy implementation và security
- **[Testing Guidelines](./auth/testing.md)** - Unit tests, integration tests, và performance testing

### 📈 Original Content Reference
> **Note**: Nội dung gốc từ file auth.md đã được tách nhỏ vào các file chuyên biệt ở trên. Việc này giúp:
> - Dễ dàng tìm kiếm và cập nhật thông tin cụ thể
> - Tránh trùng lặp thông tin giữa các tài liệu
> - Tăng khả năng bảo trì và cập nhật
> - Cung cấp single source of truth cho từng chuyên đề

## Tài liệu liên quan

### Core Modules
- **[RBAC Module](./rbac.md)** - Role-based access control và permissions
- **[User Module](./user.md)** - User profile management và preferences
- **[Tenant Module](./tenant.md)** - Multi-tenant architecture và isolation

### Architecture & Design
- **[Multi-tenant User Management](../architecture/multi-tenant-user-management.md)** - Chi tiết về multi-tenancy
- **[Inter-module Communication](../architecture/inter-module-communication.md)** - Event-driven communication patterns
- **[Database Design](../database/database-design.md)** - Database schema và naming conventions

### API & Development
- **[Response Standard](../api/response-standard.md)** - API response format standards
- **[CMS API](../api/cms-api.md)** - CMS API endpoints documentation
- **[Testing Guidelines](../development/testing.md)** - Testing strategies và best practices

### Security & Compliance
- **[Security Best Practices](../best-practices/security.md)** - Security implementation guidelines
- **[GDPR Compliance](../compliance/gdpr.md)** - Data protection và privacy compliance

## Configuration & Best Practices

Để hiểu chi tiết về configuration và best practices, vui lòng tham khảo:
- **[Implementation Details](./auth/implementation.md#configuration-management)** - Chi tiết configuration
- **[Security Features](./auth/security-features.md#configuration)** - Security configuration
- **[Tenant Isolation](./auth/tenant-isolation.md#best-practices-for-tenant-isolation)** - Multi-tenancy best practices

## Tài liệu liên quan

### Core Modules
- **[RBAC Module](./rbac.md)** - Role-based access control và permissions
- **[User Module](./user.md)** - User profile management và preferences
- **[Tenant Module](./tenant.md)** - Multi-tenant architecture và isolation

### Architecture & Design
- **[Multi-tenant User Management](../architecture/multi-tenant-user-management.md)** - Chi tiết về multi-tenancy
- **[Inter-module Communication](../architecture/inter-module-communication.md)** - Event-driven communication patterns
- **[Database Design](../database/database-design.md)** - Database schema và naming conventions

### API & Development
- **[Response Standard](../api/response-standard.md)** - API response format standards
- **[CMS API](../api/cms-api.md)** - CMS API endpoints documentation
- **[Testing Guidelines](../development/testing.md)** - Testing strategies và best practices

### Security & Compliance
- **[Security Best Practices](../best-practices/security.md)** - Security implementation guidelines
- **[GDPR Compliance](../compliance/gdpr.md)** - Data protection và privacy compliance