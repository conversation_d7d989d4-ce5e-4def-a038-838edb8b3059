# Blog Module - Tài liệu Tiếng Việt

## Tổng quan

Blog Module là core module chính của Blog API v3, cung cấp hệ thống quản lý blog posts, categories, và tags hoàn chỉnh. Module được thiết kế để hỗ trợ multi-tenancy, multi-language, và các tính năng blog hiện đại như comment system, social sharing, và SEO optimization.

## Mục tiêu

- **Content Management**: Quản lý blog posts với editor hiệ<PERSON> đại
- **Categorization**: Hệ thống phân loại linh hoạt với categories và tags
- **Multi-language**: Hỗ trợ đa ngôn ngữ cho content
- **SEO Optimization**: Tối ưu hóa SEO tự động
- **Social Features**: Comment system và social sharing
- **Workflow Management**: Draft, review, publish workflow

## Kiến trúc hệ thống

### Blog Architecture Overview

```mermaid
flowchart TD
    A[Blog Module] --> B[Post Management]
    A --> C[Category System]
    A --> D[Tag System]
    A --> E[Content Editor]
    A --> F[Media Integration]
    A --> G[SEO Engine]
    
    B --> B1[Post CRUD]
    B --> B2[Status Management]
    B --> B3[Version Control]
    B --> B4[Scheduling]
    
    C --> C1[Category CRUD]
    C --> C2[Hierarchy Support]
    C --> C3[Color Coding]
    C --> C4[Category Templates]
    
    D --> D1[Tag CRUD]
    D --> D2[Tag Suggestions]
    D --> D3[Tag Analytics]
    D --> D4[Related Tags]
    
    E --> E1[Rich Text Editor]
    E --> E2[Markdown Support]
    E --> E3[Code Highlighting]
    E --> E4[Media Embedding]
    
    F --> F1[Image Upload]
    F --> F2[Gallery Management]
    F --> F3[Featured Images]
    F --> F4[Image Optimization]
    
    G --> G1[Meta Generation]
    G --> G2[Schema Markup]
    G --> G3[Sitemap Updates]
    G --> G4[URL Optimization]
```

### Components

#### Post Management
- **CRUD Operations**: Create, Read, Update, Delete posts
- **Status Workflow**: Draft → Review → Published → Archived
- **Version Control**: Track content changes và revisions
- **Scheduling**: Publish posts at specified times

#### Category System
- **Hierarchical Structure**: Parent-child category relationships
- **Multi-language Support**: Localized category names và descriptions
- **Color Coding**: Visual category identification
- **Template Assignment**: Category-specific templates

#### Tag System
- **Flexible Tagging**: Free-form tag creation
- **Tag Suggestions**: AI-powered tag recommendations
- **Tag Analytics**: Usage statistics và trending tags
- **Related Tags**: Tag relationship mapping

## Model Structures

### Blog Post Model

```mermaid
erDiagram
    POST {
        uint id PK
        string slug UK
        uint author_id FK
        uint category_id FK
        string status
        string type
        bool is_featured
        bool allow_comments
        string password
        datetime scheduled_at
        datetime published_at
        datetime created_at
        datetime updated_at
        string status
    }
    
    POST_TRANSLATION {
        uint id PK
        uint post_id FK
        string language
        string title
        text content
        text excerpt
        string meta_title
        string meta_description
        datetime created_at
        datetime updated_at
    }
    
    POST_TAG {
        uint post_id FK
        uint tag_id FK
    }
    
    POST_AUTHOR {
        uint post_id FK
        uint user_id FK
        string role
        datetime created_at
    }
    
    CATEGORY {
        uint id PK
        string slug UK
        uint lft
        uint rgt
        uint level
        string color
        bool is_active
        datetime created_at
        datetime updated_at
    }
    
    CATEGORY_TRANSLATION {
        uint id PK
        uint category_id FK
        string language
        string name
        text description
        datetime created_at
        datetime updated_at
    }
    
    TAG {
        uint id PK
        string slug UK
        string color
        int usage_count
        bool is_active
        datetime created_at
        datetime updated_at
    }
    
    TAG_TRANSLATION {
        uint id PK
        uint tag_id FK
        string language
        string name
        text description
        datetime created_at
        datetime updated_at
    }
    
    POST_SCHEDULE {
        uint id PK
        uint post_id FK
        string schedule_type
        datetime scheduled_at
        string timezone
        json recurring_config
        string status
        datetime created_at
        datetime updated_at
    }
    
    POST_RELATED {
        uint post_id FK
        uint related_post_id FK
        string relation_type
        float relevance_score
        datetime created_at
    }
    
    POST ||--o{ POST_TRANSLATION : "has"
    POST ||--o{ POST_TAG : "has"
    POST ||--o{ POST_AUTHOR : "has"
    POST ||--o{ POST_SCHEDULE : "has"
    POST ||--o{ POST_RELATED : "has"
    POST }o--|| CATEGORY : "belongs to"
    CATEGORY ||--o{ CATEGORY_TRANSLATION : "has"
    TAG ||--o{ TAG_TRANSLATION : "has"
    TAG ||--o{ POST_TAG : "belongs to"
```

## Blog Post Management

### 1. Post Creation Flow

```mermaid
sequenceDiagram
    participant User as Content Creator
    participant UI as CMS Interface
    participant API as Blog API
    participant Editor as Content Editor
    participant AI as AI Assistant
    participant Media as Media Service
    participant SEO as SEO Service
    participant DB as Database
    
    User->>UI: Create new post
    UI->>API: POST /cms/v1/posts
    API->>DB: Create draft post
    DB->>API: Return post ID
    
    API->>Editor: Initialize editor
    Editor->>UI: Ready for content
    
    User->>UI: Enter title & content
    UI->>AI: Request SEO suggestions
    AI->>UI: Return meta suggestions
    
    User->>UI: Upload featured image
    UI->>Media: Upload image
    Media->>UI: Return image URL
    
    User->>UI: Add categories & tags
    UI->>API: Update post metadata
    
    User->>UI: Save draft
    UI->>API: PUT /cms/v1/posts/{id}
    API->>SEO: Generate meta data
    API->>DB: Save post data
    
    User->>UI: Publish post
    UI->>API: POST /cms/v1/posts/{id}/publish
    API->>SEO: Update sitemap
    API->>DB: Update post status
    API->>UI: Publish success
```

### 2. Post Status Workflow

```mermaid
stateDiagram-v2
    [*] --> Draft : Create post
    Draft --> Review : Submit for review
    Draft --> Published : Auto-publish (if authorized)
    Draft --> Scheduled : Schedule for later
    Review --> Draft : Request changes
    Review --> Published : Approve
    Review --> Rejected : Reject
    Published --> Draft : Unpublish
    Published --> Archived : Archive
    Scheduled --> Published : Auto-publish (on schedule)
    Scheduled --> Draft : Cancel schedule
    Archived --> Published : Restore
    Rejected --> Draft : Edit & resubmit
```

### 3. Content Editor Features

```mermaid
flowchart LR
    A[Content Editor] --> B[Rich Text Editor]
    A --> C[Markdown Editor]
    A --> D[Code Editor]
    A --> E[Visual Builder]
    
    B --> B1[WYSIWYG Editing]
    B --> B2[Text Formatting]
    B --> B3[Media Embedding]
    B --> B4[Link Management]
    
    C --> C1[Live Preview]
    C --> C2[Syntax Highlighting]
    C --> C3[Table Support]
    C --> C4[Math Equations]
    
    D --> D1[Code Blocks]
    D --> D2[Syntax Highlighting]
    D --> D3[Language Detection]
    D --> D4[Copy Button]
    
    E --> E1[Block Editor]
    E --> E2[Component Library]
    E --> E3[Custom Blocks]
    E --> E4[Layout Builder]
```

## Category Management

### 1. Category Hierarchy (Nested Set Model)

```mermaid
flowchart TD
    A["Technology<br/>lft: 1, rgt: 20, level: 0"] --> B["Programming<br/>lft: 2, rgt: 11, level: 1"]
    A --> C["Design<br/>lft: 12, rgt: 17, level: 1"]
    A --> D["Hardware<br/>lft: 18, rgt: 19, level: 1"]
    
    B --> B1["Web Development<br/>lft: 3, rgt: 8, level: 2"]
    B --> B2["Mobile Development<br/>lft: 9, rgt: 10, level: 2"]
    
    B1 --> B1a["Frontend<br/>lft: 4, rgt: 5, level: 3"]
    B1 --> B1b["Backend<br/>lft: 6, rgt: 7, level: 3"]
    
    C --> C1["UI/UX Design<br/>lft: 13, rgt: 14, level: 2"]
    C --> C2["Graphic Design<br/>lft: 15, rgt: 16, level: 2"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style B1 fill:#fff3e0
    style B2 fill:#fff3e0
    style B1a fill:#f1f8e9
    style B1b fill:#f1f8e9
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
```

### 2. Category Operations Flow

```mermaid
sequenceDiagram
    participant Admin as Admin User
    participant CMS as CMS Interface
    participant API as Blog API
    participant Cache as Cache Service
    participant DB as Database
    participant Search as Search Engine
    
    Admin->>CMS: Create category
    CMS->>API: POST /cms/v1/categories
    API->>DB: Insert category
    DB->>API: Return category data
    
    API->>Cache: Invalidate category cache
    API->>Search: Index category
    API->>CMS: Return success
    
    Admin->>CMS: Assign posts to category
    CMS->>API: PUT /cms/v1/posts/{id}/category
    API->>DB: Update post category
    API->>Cache: Update post cache
    
    Admin->>CMS: Reorder categories
    CMS->>API: PUT /cms/v1/categories/reorder
    API->>DB: Update sort orders
    API->>Cache: Refresh category tree
```

### 3. Category Features

- **Hierarchical Structure**: Multi-level category nesting
- **Color Coding**: Visual category identification
- **Post Count**: Automatic post counting per category
- **SEO Optimization**: Category-specific meta tags
- **Template Assignment**: Custom templates cho categories
- **Access Control**: Role-based category permissions

## Tag Management

### 1. Tag System Architecture

```mermaid
flowchart TD
    A[Tag System] --> B[Tag Creation]
    A --> C[Tag Discovery]
    A --> D[Tag Analytics]
    A --> E[Tag Relationships]
    
    B --> B1[Manual Creation]
    B --> B2[Auto-generation]
    B --> B3[Import/Export]
    B --> B4[Bulk Operations]
    
    C --> C1[Tag Suggestions]
    C --> C2[Popular Tags]
    C --> C3[Trending Tags]
    C --> C4[Related Tags]
    
    D --> D1[Usage Statistics]
    D --> D2[Performance Metrics]
    D --> D3[Growth Tracking]
    D --> D4[Tag Rankings]
    
    E --> E1[Semantic Relationships]
    E --> E2[Co-occurrence Analysis]
    E --> E3[Tag Clustering]
    E --> E4[Synonym Detection]
```

### 2. AI-Powered Tag Suggestions

```mermaid
sequenceDiagram
    participant User as Content Creator
    participant UI as CMS Interface
    participant AI as AI Service
    participant NLP as NLP Engine
    participant DB as Tag Database
    participant Analytics as Tag Analytics
    
    User->>UI: Enter post content
    UI->>AI: Analyze content for tags
    AI->>NLP: Extract keywords & entities
    NLP->>AI: Return extracted terms
    
    AI->>DB: Query existing tags
    DB->>AI: Return matching tags
    
    AI->>Analytics: Get popular tags
    Analytics->>AI: Return trending tags
    
    AI->>UI: Return tag suggestions
    UI->>User: Display suggested tags
    
    User->>UI: Select/modify tags
    UI->>DB: Save tag associations
    DB->>Analytics: Update tag usage
```

### 3. Tag Analytics Flow

```mermaid
flowchart LR
    A[Tag Usage] --> B[Data Collection]
    B --> C[Analytics Processing]
    C --> D[Insights Generation]
    D --> E[Recommendations]
    
    B --> B1[Usage Count]
    B --> B2[Click Tracking]
    B --> B3[Search Queries]
    B --> B4[User Behavior]
    
    C --> C1[Trend Analysis]
    C --> C2[Correlation Analysis]
    C --> C3[Performance Metrics]
    C --> C4[Prediction Models]
    
    D --> D1[Popular Tags]
    D --> D2[Trending Topics]
    D --> D3[Underused Tags]
    D --> D4[Tag Performance]
    
    E --> E1[Content Suggestions]
    E --> E2[Tag Optimization]
    E --> E3[Content Strategy]
    E --> E4[SEO Improvements]
```

## Content Workflow

### 1. Editorial Workflow

```mermaid
flowchart TD
    A[Content Planning] --> B[Content Creation]
    B --> C[Content Review]
    C --> D[Content Approval]
    D --> E[Content Publishing]
    E --> F[Content Promotion]
    
    A --> A1[Content Calendar]
    A --> A2[Topic Research]
    A --> A3[Keyword Planning]
    A --> A4[Resource Allocation]
    
    B --> B1[Draft Writing]
    B --> B2[Media Creation]
    B --> B3[SEO Optimization]
    B --> B4[Internal Linking]
    
    C --> C1[Editorial Review]
    C --> C2[Fact Checking]
    C --> C3[Grammar Check]
    C --> C4[Style Guide Check]
    
    D --> D1[Final Approval]
    D --> D2[Legal Review]
    D --> D3[Compliance Check]
    D --> D4[Brand Alignment]
    
    E --> E1[Publication]
    E --> E2[Distribution]
    E --> E3[Social Sharing]
    E --> E4[Newsletter Inclusion]
    
    F --> F1[Social Media]
    F --> F2[Email Marketing]
    F --> F3[SEO Monitoring]
    F --> F4[Performance Tracking]
```

### 2. Content Scheduling

```mermaid
sequenceDiagram
    participant Editor as Content Editor
    participant CMS as CMS System
    participant Scheduler as Task Scheduler
    participant Queue as Message Queue
    participant Publisher as Publishing Service
    participant CDN as Content Delivery
    participant Social as Social Media
    
    Editor->>CMS: Schedule post publication
    CMS->>Scheduler: Create scheduled task
    Scheduler->>Queue: Queue publication job
    
    Note over Scheduler: Wait for scheduled time
    
    Scheduler->>Queue: Trigger publication
    Queue->>Publisher: Execute publication
    Publisher->>CMS: Update post status
    Publisher->>CDN: Deploy content
    Publisher->>Social: Share on social media
    
    CDN->>Publisher: Confirm deployment
    Social->>Publisher: Confirm sharing
    Publisher->>CMS: Complete publication
```

### 3. Advanced Scheduling Features

#### Recurring Schedules
```mermaid
flowchart TD
    A[Create Post] --> B[Set Schedule Type]
    B --> C{Schedule Type}
    
    C -->|One-time| D[Set Date & Time]
    C -->|Recurring| E[Configure Recurrence]
    
    E --> E1[Daily]
    E --> E2[Weekly]
    E --> E3[Monthly]
    E --> E4[Custom Cron]
    
    E1 --> F[Set Time & Days]
    E2 --> G[Set Weekdays & Time]
    E3 --> H[Set Day of Month & Time]
    E4 --> I[Set Cron Expression]
    
    D --> J[Save Schedule]
    F --> J
    G --> J
    H --> J
    I --> J
    
    J --> K[Queue Scheduler Job]
    K --> L[Monitor Execution]
```

#### Schedule Types
- **One-time**: Single publication at specific date/time
- **Daily**: Publish every day at specified time
- **Weekly**: Publish on specific weekdays
- **Monthly**: Publish on specific day of month
- **Custom Cron**: Advanced cron expression scheduling

#### Timezone Handling
```yaml
schedule_config:
  timezone_support: true
  default_timezone: "UTC"
  user_timezone_detection: true
  
  supported_timezones:
    - "UTC"
    - "America/New_York"
    - "Europe/London"
    - "Asia/Tokyo"
    - "Asia/Ho_Chi_Minh"
    
  recurring_options:
    daily:
      time: "09:00"
      skip_weekends: false
    weekly:
      days: ["monday", "wednesday", "friday"]
      time: "10:00"
    monthly:
      day: 15
      time: "14:00"
    custom:
      cron_expression: "0 9 * * 1-5" # Weekdays at 9 AM
```

## API Endpoints

### Blog Posts

#### List Posts
```http
GET /api/cms/v1/posts?cursor=abc123&limit=20&category=technology&tag=programming&status=published&featured=true
```

#### Get Single Post
```http
GET /api/cms/v1/posts/{slug}
```

#### Create Post (CMS)
```http
POST /cms/v1/posts
Content-Type: application/json

{
  "translations": [
    {
      "language": "en",
      "title": "Getting Started with Go Programming",
      "content": "# Introduction\n\nGo is a powerful programming language...",
      "excerpt": "Learn the basics of Go programming language",
      "meta_title": "Go Programming Guide - Learn Go Language",
      "meta_description": "Complete guide to getting started with Go programming language. Learn syntax, best practices, and build your first Go application."
    },
    {
      "language": "vi", 
      "title": "Bắt đầu với lập trình Go",
      "content": "# Giới thiệu\n\nGo là một ngôn ngữ lập trình mạnh mẽ...",
      "excerpt": "Học các kiến thức cơ bản về ngôn ngữ lập trình Go"
    }
  ],
  "category_id": 5,
  "tag_ids": [1, 3, 7],
  "featured_image": "https://cdn.yourblog.com/go-programming.jpg",
  "is_featured": true,
  "allow_comments": true,
  "status": "draft",
  "scheduled_at": "2024-07-15T10:00:00Z"
}
```

#### Update Post (CMS)
```http
PUT /cms/v1/posts/{id}
Content-Type: application/json

{
  "translations": [
    {
      "language": "en",
      "title": "Updated Go Programming Guide",
      "content": "Updated content here..."
    }
  ],
  "status": "published"
}
```

#### Publish Post (CMS)
```http
POST /cms/v1/posts/{id}/publish
```

#### Schedule Post (CMS)
```http
POST /cms/v1/posts/{id}/schedule
Content-Type: application/json

{
  "scheduled_at": "2024-07-20T14:00:00Z"
}
```

### Categories

#### List Categories
```http
GET /api/cms/v1/categories?cursor=abc123&limit=20&parent_id=5&active=true
```

#### Get Category with Posts
```http
GET /api/cms/v1/categories/{slug}/posts?cursor=abc123&limit=12
```

#### Create Category (CMS)
```http
POST /cms/v1/categories
Content-Type: application/json

{
  "slug": "web-development",
  "parent_id": 2,
  "color": "#3498db",
  "sort_order": 1,
  "translations": [
    {
      "language": "en",
      "name": "Web Development",
      "description": "Articles about web development technologies, frameworks, and best practices."
    },
    {
      "language": "vi",
      "name": "Phát triển Web",
      "description": "Các bài viết về công nghệ phát triển web, framework và các phương pháp hay nhất."
    }
  ]
}
```

#### Update Category (CMS)
```http
PUT /cms/v1/categories/{id}
Content-Type: application/json

{
  "color": "#2ecc71",
  "sort_order": 2,
  "is_active": true
}
```

#### Reorder Categories (CMS)
```http
PUT /cms/v1/categories/reorder
Content-Type: application/json

{
  "categories": [
    {"id": 1, "sort_order": 1},
    {"id": 3, "sort_order": 2},
    {"id": 2, "sort_order": 3}
  ]
}
```

### Tags

#### List Tags
```http
GET /api/cms/v1/tags?cursor=abc123&limit=50&popular=true&min_usage=5
```

#### Get Tag with Posts
```http
GET /api/cms/v1/tags/{slug}/posts?cursor=abc123&limit=12
```

#### Create Tag (CMS)
```http
POST /cms/v1/tags
Content-Type: application/json

{
  "slug": "machine-learning",
  "color": "#e74c3c",
  "translations": [
    {
      "language": "en",
      "name": "Machine Learning",
      "description": "Artificial intelligence and machine learning topics"
    },
    {
      "language": "vi", 
      "name": "Học máy",
      "description": "Các chủ đề về trí tuệ nhân tạo và học máy"
    }
  ]
}
```

#### Get Tag Suggestions (CMS)
```http
POST /cms/v1/tags/suggest
Content-Type: application/json

{
  "content": "This article covers machine learning algorithms including neural networks, deep learning, and natural language processing for building AI applications.",
  "existing_tags": [1, 5, 8],
  "limit": 10
}
```

#### Tag Analytics (CMS)
```http
GET /cms/v1/tags/analytics?period=30d&top=20
```

## Related Posts System

### 1. Related Posts Architecture

```mermaid
flowchart TD
    A[Post Content] --> B[Content Analysis]
    B --> C[Relationship Detection]
    C --> D[Relevance Scoring]
    D --> E[Related Posts Database]
    
    B --> B1[Keyword Extraction]
    B --> B2[Category Analysis]
    B --> B3[Tag Analysis]
    B --> B4[Content Similarity]
    
    C --> C1[Manual Relations]
    C --> C2[Auto Relations]
    C --> C3[AI Recommendations]
    C --> C4[User Behavior]
    
    D --> D1[Content Similarity Score]
    D --> D2[Category Relevance]
    D --> D3[Tag Overlap]
    D --> D4[User Engagement]
    
    E --> F[Related Posts API]
    F --> G[Frontend Display]
```

### 2. Relation Types

#### Content-Based Relations
- **Similar Content**: Based on content similarity analysis
- **Same Category**: Posts from the same category
- **Shared Tags**: Posts sharing common tags
- **Sequential**: Part of a series or sequence

#### Editorial Relations
- **Manual**: Manually curated by editors
- **Featured**: Editor-selected featured relations
- **Series**: Posts in a specific series
- **Follow-up**: Continuation or update posts

#### Behavioral Relations
- **Frequently Viewed Together**: Based on user reading patterns
- **User Recommended**: User-generated recommendations
- **Social Signals**: Based on social media sharing patterns

### 3. AI-Powered Recommendations

```mermaid
sequenceDiagram
    participant Post as New Post
    participant AI as AI Engine
    participant NLP as NLP Service
    participant ML as ML Model
    participant DB as Database
    participant Cache as Cache Layer
    
    Post->>AI: Analyze for related posts
    AI->>NLP: Extract content features
    NLP->>AI: Return feature vectors
    
    AI->>ML: Find similar posts
    ML->>DB: Query existing posts
    DB->>ML: Return candidate posts
    
    ML->>AI: Calculate relevance scores
    AI->>DB: Store related post relationships
    DB->>Cache: Update cache
    AI->>Post: Return related posts
```

#### AI Features
- **Semantic Analysis**: Understanding content meaning beyond keywords
- **Topic Modeling**: Automatic topic detection and grouping
- **User Preference Learning**: Personalized recommendations
- **Trending Analysis**: Popular content recommendations

### 4. Related Posts API

#### Get Related Posts
```http
GET /api/cms/v1/posts/{post_id}/related?limit=5&type=auto&min_score=0.7
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Related posts retrieved successfully",
    "success": true,
    "path": "/api/cms/v1/posts/123/related",
    "timestamp": "2024-07-15T10:30:00Z"
  },
  "data": [
    {
      "id": 456,
      "title": "Advanced Go Concurrency Patterns",
      "slug": "advanced-go-concurrency-patterns",
      "excerpt": "Deep dive into advanced concurrency patterns in Go programming language.",
      "featured_image": "https://cdn.yourblog.com/go-concurrency.jpg",
      "published_at": "2024-07-10T14:00:00Z",
      "relation_type": "similar_content",
      "relevance_score": 0.89,
      "category": {
        "id": 5,
        "name": "Programming",
        "slug": "programming"
      }
    }
  ],
  "meta": {
    "total_relations": 12,
    "displayed": 5,
    "has_more": true
  }
}
```

#### Create Manual Relation (CMS)
```http
POST /cms/v1/posts/{post_id}/related
Content-Type: application/json

{
  "related_post_id": 789,
  "relation_type": "manual",
  "relevance_score": 0.95
}
```

#### Update Relation Score (CMS)
```http
PUT /cms/v1/posts/{post_id}/related/{related_post_id}
Content-Type: application/json

{
  "relevance_score": 0.85,
  "relation_type": "featured"
}
```

#### Bulk Update Relations (CMS)
```http
PUT /cms/v1/posts/{post_id}/related
Content-Type: application/json

{
  "relations": [
    {
      "related_post_id": 456,
      "relation_type": "series",
      "relevance_score": 0.95
    },
    {
      "related_post_id": 789,
      "relation_type": "similar_content", 
      "relevance_score": 0.78
    }
  ]
}
```

## Content Features

### 1. Rich Text Editor

#### Editor Capabilities
- **WYSIWYG Editing**: Visual content editing
- **Markdown Support**: Raw markdown editing mode
- **Code Highlighting**: Syntax highlighting cho code blocks
- **Media Embedding**: Drag-and-drop media insertion
- **Link Management**: Internal và external link handling
- **Table Support**: Rich table editing
- **Custom Blocks**: Extensible block types

#### Editor Configuration
```yaml
editor:
  type: "rich_text" # rich_text, markdown, block_editor
  features:
    - bold
    - italic
    - underline
    - heading
    - link
    - image
    - video
    - code_block
    - table
    - quote
    - list
    - embed
  
  upload:
    enabled: true
    max_size: "10MB"
    allowed_types: ["jpg", "png", "gif", "webp", "svg"]
    
  auto_save:
    enabled: true
    interval: 30 # seconds
```

### 2. SEO Integration

#### Automatic SEO Features
- **Meta Generation**: AI-powered title và description generation
- **Schema Markup**: Automatic JSON-LD structured data
- **Image Optimization**: Alt text generation và compression
- **Internal Linking**: Automatic related post suggestions
- **Sitemap Updates**: Real-time sitemap updates

#### SEO Workflow
```mermaid
flowchart LR
    A[Content Creation] --> B[SEO Analysis]
    B --> C[Optimization Suggestions]
    C --> D[Auto-optimization]
    D --> E[SEO Validation]
    E --> F[Publishing]
    
    B --> B1[Keyword Analysis]
    B --> B2[Content Structure]
    B --> B3[Readability Check]
    B --> B4[Meta Review]
    
    C --> C1[Title Optimization]
    C --> C2[Description Enhancement]
    C --> C3[Header Structure]
    C --> C4[Internal Links]
    
    D --> D1[Meta Generation]
    D --> D2[Schema Markup]
    D --> D3[Image Alt Text]
    D --> D4[URL Optimization]
```

### 3. Version Control

#### Content Versioning
- **Automatic Saves**: Auto-save drafts every 30 seconds
- **Version History**: Track all content changes
- **Restore Points**: Restore to previous versions
- **Compare Versions**: Side-by-side version comparison
- **Collaborative Editing**: Multiple editor support

#### Version Management Flow
```mermaid
sequenceDiagram
    participant Editor as Content Editor
    participant System as Version System
    participant DB as Database
    participant Cache as Cache Layer
    
    Editor->>System: Auto-save content
    System->>DB: Create version snapshot
    DB->>System: Confirm save
    
    Editor->>System: Request version history
    System->>DB: Query versions
    DB->>System: Return version list
    System->>Editor: Display history
    
    Editor->>System: Restore to version X
    System->>DB: Load version X content
    DB->>System: Return content
    System->>Cache: Update cache
    System->>Editor: Content restored
```

## Multi-Tenancy Architecture

### Website-based Isolation

```mermaid
flowchart TD
    A[Request] --> B[Website Detection]
    B --> C[Set Website Context]
    C --> D[Database Query with website_id]
    D --> E[Return Website-specific Data]
    
    F[Content Creation] --> G[Auto-add website_id]
    G --> H[Store in Database]
    H --> I[Update Website Cache]
```

### Repository Pattern with Tenant Isolation

```go
type BlogRepository struct {
    db *gorm.DB
    websiteID uint
}

func (r *BlogRepository) GetPosts(filters PostFilters) ([]Post, error) {
    query := r.db.Where("website_id = ?", r.websiteID)
    
    // Apply additional filters
    if filters.Status != "" {
        query = query.Where("status = ?", filters.Status)
    }
    
    var posts []Post
    err := query.Find(&posts).Error
    return posts, err
}

func (r *BlogRepository) CreatePost(post *Post) error {
    post.WebsiteID = r.websiteID
    return r.db.Create(post).Error
}
```

### Cache Strategy with Website Isolation

```yaml
cache_keys:
  posts: "website:{website_id}:posts:page:{page}:limit:{limit}"
  post_detail: "website:{website_id}:post:{post_id}"
  categories: "website:{website_id}:categories:tree"
  tags: "website:{website_id}:tags:popular"
  post_search: "website:{website_id}:search:{query}:{filters}"
```

## Performance Optimization

### 1. Caching Strategy

```mermaid
flowchart TD
    A[Request] --> B{Cache Check}
    B -->|Hit| C[Return Cached Content]
    B -->|Miss| D[Database Query with website_id]
    D --> E[Process Content]
    E --> F[Store in Cache with website prefix]
    F --> G[Return Content]
    
    H[Content Update] --> I[Invalidate Website Cache]
    I --> J[Update Database]
    J --> K[Refresh Website Cache]
```

#### Cache Layers
- **Application Cache**: In-memory caching với Redis, website-prefixed keys
- **Database Query Cache**: Query result caching với website context
- **CDN Cache**: Static content delivery per website
- **Browser Cache**: Client-side caching headers

### 2. Database Optimization

#### Indexing Strategy
```sql
-- Primary indexes
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_published_at ON posts(published_at);
CREATE INDEX idx_posts_category ON posts(category_id);
CREATE INDEX idx_posts_author ON posts(author_id);

-- Composite indexes
CREATE INDEX idx_posts_status_published ON posts(status, published_at);
CREATE INDEX idx_posts_featured_status ON posts(is_featured, status);

-- Translation indexes  
CREATE INDEX idx_post_translations_lang ON post_translations(language);
CREATE INDEX idx_post_translations_post_lang ON post_translations(post_id, language);

-- Tag indexes
CREATE INDEX idx_post_tags_post ON post_tags(post_id);
CREATE INDEX idx_post_tags_tag ON post_tags(tag_id);
```

### 3. Content Delivery

#### CDN Integration
- **Static Assets**: Images, CSS, JavaScript
- **Content Caching**: Cached HTML pages
- **Global Distribution**: Multiple edge locations
- **Image Optimization**: Automatic image processing

## Security Considerations

### 1. Content Security

#### Input Validation
- **HTML Sanitization**: Clean user-generated content
- **XSS Prevention**: Cross-site scripting protection
- **SQL Injection**: Parameterized queries
- **File Upload Security**: Secure file handling
- **Website Isolation**: Ensure all queries include website_id

#### Access Control with Website Isolation
```yaml
permissions:
  posts:
    create: ["editor", "admin"]
    read: ["public"]
    update: ["author", "editor", "admin"]
    delete: ["admin"]
    publish: ["editor", "admin"]
    # All operations automatically scoped to user's website
    
  categories:
    create: ["admin"]
    read: ["public"]
    update: ["admin"]
    delete: ["admin"]
    # Categories isolated per website
    
  tags:
    create: ["editor", "admin"]
    read: ["public"] 
    update: ["editor", "admin"]
    delete: ["admin"]
    # Tags isolated per website
```

#### Website-level Security
```go
type WebsiteSecurityMiddleware struct {
    websiteService WebsiteService
}

func (m *WebsiteSecurityMiddleware) ValidateWebsiteAccess(c *gin.Context) {
    userID := c.GetUint("user_id")
    websiteID := c.GetUint("website_id")
    
    // Validate user has access to website
    if !m.websiteService.HasAccess(userID, websiteID) {
        c.JSON(403, gin.H{"error": "Access denied to website"})
        c.Abort()
        return
    }
    
    c.Next()
}
```

### 2. Data Protection

#### Content Backup
- **Automatic Backups**: Daily content backups
- **Version History**: Complete edit history
- **Export Capabilities**: Content export functionality
- **Disaster Recovery**: Backup restoration procedures

## Configuration

### Blog Settings
```yaml
blog:
  enabled: true
  posts_per_page: 12
  auto_save_interval: 30 # seconds
  
  features:
    comments: true
    social_sharing: true
    related_posts: true
    reading_time: true
    author_bio: true
    
  editor:
    type: "rich_text" # rich_text, markdown, block_editor
    auto_save: true
    spell_check: true
    word_count: true
    
  seo:
    auto_meta: true
    schema_markup: true
    sitemap_update: true
    
  workflow:
    require_review: false
    auto_publish: true
    scheduled_posts: true
```

### Content Rules
```yaml
content_rules:
  post:
    title:
      min_length: 10
      max_length: 100
      required: true
      
    content:
      min_words: 100
      max_words: 10000
      required: true
      
    excerpt:
      max_length: 300
      auto_generate: true
      
  category:
    max_depth: 3
    min_posts: 1
    
  tag:
    max_per_post: 10
    min_length: 2
    max_length: 50
```

## Best Practices

### Content Creation
- **Consistent Voice**: Maintain consistent brand voice
- **SEO Optimization**: Follow SEO best practices
- **User Experience**: Focus on reader experience
- **Mobile Friendly**: Ensure mobile responsiveness

### Content Management
- **Regular Audits**: Review và update old content
- **Performance Monitoring**: Track content performance
- **Backup Strategy**: Regular content backups
- **Security Updates**: Keep system updated

### Editorial Workflow
- **Content Calendar**: Plan content in advance
- **Review Process**: Implement editorial review
- **Style Guide**: Maintain content style guide
- **Quality Control**: Ensure content quality

## Multi-Tenancy Best Practices

### Data Isolation
- **Always include website_id** in all database queries
- **Validate website access** before any operation
- **Use website-scoped repositories** for data access
- **Implement website-aware caching** keys

### Performance Considerations
- **Index all tables** with website_id as first column
- **Use website-specific cache keys** to avoid collisions
- **Implement proper cache invalidation** per website
- **Consider database partitioning** for large websites

### Security Guidelines
- **Never expose cross-website data** in APIs
- **Validate website ownership** in all operations
- **Use website-scoped search indices** to prevent data leakage
- **Implement proper audit logging** with website context

### Example Implementation
```go
type BlogService struct {
    repo BlogRepository
    cache CacheService
    websiteID uint
}

func (s *BlogService) GetPost(id uint) (*Post, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("website:%d:post:%d", s.websiteID, id)
    if cached, err := s.cache.Get(cacheKey); err == nil {
        return cached.(*Post), nil
    }
    
    // Query with website isolation
    post, err := s.repo.GetByID(id, s.websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache with website prefix
    s.cache.Set(cacheKey, post, 3600)
    return post, nil
}
```

## Tài liệu liên quan

### Core Modules
- **[Auth Module](./auth.md)** - User authentication và authorization
- **[Media Module](./media.md)** - File upload và media management
- **[SEO Module](./seo.md)** - SEO optimization và meta tags
- **[Notification Module](./notification.md)** - Email notifications và alerts

### Workflow & Submission
- **[Blog Submission Flow](./blog-submission-flow.md)** - Chi tiết về draft → review → publish workflow
- **[RBAC Module](./rbac.md)** - Role-based permissions cho content management

### Architecture & Integration
- **[Inter-module Communication](../architecture/inter-module-communication.md)** - Event-driven patterns
- **[Database Design](../database/database-design.md)** - Blog tables và relationships
- **[Tenant Module](./tenant.md)** - Multi-tenant content isolation

### API & Features
- **[Frontend API](../api/frontend-api.md)** - Public blog API endpoints
- **[CMS API](../api/cms-api.md)** - Admin content management API
- **[I18n Feature](../features/i18n.md)** - Multi-language content support
- **[Response Standard](../api/response-standard.md)** - API response formats