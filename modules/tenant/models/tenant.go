package models

import (
	"time"

	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// TenantStatus định nghĩa trạng thái của tenant
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"
	TenantStatusSuspended TenantStatus = "suspended"
	TenantStatusCanceled  TenantStatus = "canceled"
	TenantStatusTrial     TenantStatus = "trial"
	TenantStatusDeleted   TenantStatus = "deleted"
)

// SubscriptionStatus định nghĩa trạng thái subscription
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusInactive  SubscriptionStatus = "inactive"
	SubscriptionStatusCanceled  SubscriptionStatus = "canceled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusTrialing  SubscriptionStatus = "trialing"
)

// PlanType định nghĩa loại plan
type PlanType string

const (
	PlanTypeFree       PlanType = "free"
	PlanTypeBasic      PlanType = "basic"
	PlanTypeProfessional PlanType = "professional"
	PlanTypeEnterprise PlanType = "enterprise"
)

// Tenant đại diện cho một tổ chức/công ty
type Tenant struct {
	ID          string       `json:"id" gorm:"primaryKey"`
	Name        string       `json:"name" validate:"required,min=2,max=100"`
	Slug        string       `json:"slug" validate:"required,slug" gorm:"uniqueIndex"`
	Description string       `json:"description" validate:"max=500"`
	Website     string       `json:"website" validate:"omitempty,url"`
	Industry    string       `json:"industry" validate:"max=100"`
	Size        string       `json:"size" validate:"oneof=1-10 11-50 51-200 201-1000 1000+"`
	Country     string       `json:"country" validate:"required,len=2"`
	Timezone    string       `json:"timezone" validate:"required"`
	Language    string       `json:"language" validate:"required,len=2"`
	Logo        string       `json:"logo" validate:"omitempty,url"`
	Status      TenantStatus `json:"status" validate:"required"`
	IsActive    bool         `json:"is_active"`
	
	// Owner information
	OwnerID     string `json:"owner_id" validate:"required"`
	OwnerEmail  string `json:"owner_email" validate:"required,email"`
	
	// Settings
	Settings    TenantSettings `json:"settings" gorm:"embedded"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	
	// Trial information
	TrialEndsAt *time.Time `json:"trial_ends_at,omitempty"`
	
	// Relationships
	Subscription *Subscription `json:"subscription,omitempty" gorm:"foreignKey:TenantID"`
	Members      []TenantMember `json:"members,omitempty" gorm:"foreignKey:TenantID"`
}

// TenantSettings chứa các cài đặt của tenant
type TenantSettings struct {
	AllowInvitations     bool   `json:"allow_invitations"`
	RequireEmailVerification bool `json:"require_email_verification"`
	DefaultUserRole      string `json:"default_user_role" validate:"oneof=member admin"`
	MaxMembers           int    `json:"max_members" validate:"min=1,max=10000"`
	AllowPublicSignup    bool   `json:"allow_public_signup"`
	CustomDomain         string `json:"custom_domain" validate:"omitempty,fqdn"`
	BrandingEnabled      bool   `json:"branding_enabled"`
	SSOEnabled           bool   `json:"sso_enabled"`
	TwoFactorRequired    bool   `json:"two_factor_required"`
	SessionTimeout       int    `json:"session_timeout" validate:"min=5,max=1440"` // minutes
}

// TenantMember đại diện cho thành viên của tenant
type TenantMember struct {
	ID        string                `json:"id" gorm:"primaryKey"`
	TenantID  string                `json:"tenant_id" validate:"required"`
	UserID    string                `json:"user_id" validate:"required"`
	Role      authModels.Role       `json:"role" validate:"required"`
	Status    TenantMemberStatus    `json:"status" validate:"required"`
	InvitedBy string                `json:"invited_by,omitempty"`
	InvitedAt *time.Time            `json:"invited_at,omitempty"`
	JoinedAt  *time.Time            `json:"joined_at,omitempty"`
	CreatedAt time.Time             `json:"created_at"`
	UpdatedAt time.Time             `json:"updated_at"`
	
	// Embedded user info for convenience
	UserEmail     string `json:"user_email,omitempty"`
	UserFirstName string `json:"user_first_name,omitempty"`
	UserLastName  string `json:"user_last_name,omitempty"`
}

// TenantMemberStatus định nghĩa trạng thái thành viên
type TenantMemberStatus string

const (
	TenantMemberStatusPending  TenantMemberStatus = "pending"
	TenantMemberStatusActive   TenantMemberStatus = "active"
	TenantMemberStatusInactive TenantMemberStatus = "inactive"
	TenantMemberStatusRemoved  TenantMemberStatus = "removed"
)

// Subscription đại diện cho subscription của tenant
type Subscription struct {
	ID              string             `json:"id" gorm:"primaryKey"`
	TenantID        string             `json:"tenant_id" validate:"required"`
	PlanID          string             `json:"plan_id" validate:"required"`
	Status          SubscriptionStatus `json:"status" validate:"required"`
	CurrentPeriodStart time.Time       `json:"current_period_start"`
	CurrentPeriodEnd   time.Time       `json:"current_period_end"`
	TrialStart      *time.Time         `json:"trial_start,omitempty"`
	TrialEnd        *time.Time         `json:"trial_end,omitempty"`
	CanceledAt      *time.Time         `json:"canceled_at,omitempty"`
	EndedAt         *time.Time         `json:"ended_at,omitempty"`
	
	// Payment information
	StripeSubscriptionID string  `json:"stripe_subscription_id,omitempty"`
	Amount               float64 `json:"amount"`
	Currency             string  `json:"currency" validate:"required,len=3"`
	
	// Usage tracking
	Usage         SubscriptionUsage `json:"usage" gorm:"embedded"`
	
	// Timestamps
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	
	// Relationships
	Plan          *Plan      `json:"plan,omitempty" gorm:"foreignKey:PlanID"`
	Tenant        *Tenant    `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// SubscriptionUsage theo dõi usage của subscription
type SubscriptionUsage struct {
	Users        int `json:"users"`
	Storage      int64 `json:"storage"` // bytes
	APIRequests  int64 `json:"api_requests"`
	Posts        int `json:"posts"`
	Comments     int `json:"comments"`
	LastUpdated  time.Time `json:"last_updated"`
}

// Plan định nghĩa các gói dịch vụ
type Plan struct {
	ID          string   `json:"id" gorm:"primaryKey"`
	Name        string   `json:"name" validate:"required,min=2,max=100"`
	Description string   `json:"description" validate:"max=500"`
	Type        PlanType `json:"type" validate:"required"`
	Price       float64  `json:"price" validate:"min=0"`
	Currency    string   `json:"currency" validate:"required,len=3"`
	Interval    string   `json:"interval" validate:"oneof=month year"` // monthly, yearly
	IsActive    bool     `json:"is_active"`
	IsPublic    bool     `json:"is_public"`
	
	// Limits
	Limits      PlanLimits `json:"limits" gorm:"embedded"`
	
	// Features
	Features    []string `json:"features" gorm:"type:json"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	
	// Stripe information
	StripePriceID string `json:"stripe_price_id,omitempty"`
}

// PlanLimits định nghĩa giới hạn của plan
type PlanLimits struct {
	MaxUsers      int   `json:"max_users" validate:"min=1"`
	MaxStorage    int64 `json:"max_storage"` // bytes
	MaxAPIRequests int64 `json:"max_api_requests"` // per month
	MaxPosts      int   `json:"max_posts"`
	MaxComments   int   `json:"max_comments"`
	HasCustomDomain bool `json:"has_custom_domain"`
	HasSSO        bool  `json:"has_sso"`
	HasBranding   bool  `json:"has_branding"`
	HasAnalytics  bool  `json:"has_analytics"`
	HasPriority   bool  `json:"has_priority_support"`
}

// TenantInvitation đại diện cho lời mời tham gia tenant
type TenantInvitation struct {
	ID           string             `json:"id" gorm:"primaryKey"`
	TenantID     string             `json:"tenant_id" validate:"required"`
	Email        string             `json:"email" validate:"required,email"`
	Role         authModels.Role    `json:"role" validate:"required"`
	InvitedBy    string             `json:"invited_by" validate:"required"`
	Token        string             `json:"token" validate:"required" gorm:"uniqueIndex"`
	Status       InvitationStatus   `json:"status" validate:"required"`
	ExpiresAt    time.Time          `json:"expires_at"`
	AcceptedAt   *time.Time         `json:"accepted_at,omitempty"`
	DeclinedAt   *time.Time         `json:"declined_at,omitempty"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
	
	// Relationships
	Tenant       *Tenant            `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// InvitationStatus định nghĩa trạng thái invitation
type InvitationStatus string

const (
	InvitationStatusPending  InvitationStatus = "pending"
	InvitationStatusAccepted InvitationStatus = "accepted"
	InvitationStatusDeclined InvitationStatus = "declined"
	InvitationStatusExpired  InvitationStatus = "expired"
	InvitationStatusCanceled InvitationStatus = "canceled"
)

// TenantFilter để filter tenants
type TenantFilter struct {
	Cursor     string       `json:"cursor,omitempty"`       // Cursor for pagination
	Limit      int          `json:"limit,omitempty"`        // Number of items per page
	Status     TenantStatus `json:"status,omitempty"`
	PlanType   PlanType     `json:"plan_type,omitempty"`
	Country    string       `json:"country,omitempty"`
	Industry   string       `json:"industry,omitempty"`
	Size       string       `json:"size,omitempty"`
	Query      string       `json:"query,omitempty"`
	SortBy     string       `json:"sort_by,omitempty"`
	SortOrder  string       `json:"sort_order,omitempty"`
	CreatedFrom *time.Time  `json:"created_from,omitempty"`
	CreatedTo   *time.Time  `json:"created_to,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page       int          `json:"page,omitempty"`
	PageSize   int          `json:"page_size,omitempty"`
}

// TenantUpdate để cập nhật tenant
type TenantUpdate struct {
	Name        *string         `json:"name,omitempty"`
	Description *string         `json:"description,omitempty"`
	Website     *string         `json:"website,omitempty"`
	Industry    *string         `json:"industry,omitempty"`
	Size        *string         `json:"size,omitempty"`
	Country     *string         `json:"country,omitempty"`
	Timezone    *string         `json:"timezone,omitempty"`
	Language    *string         `json:"language,omitempty"`
	Logo        *string         `json:"logo,omitempty"`
	Settings    *TenantSettings `json:"settings,omitempty"`
}

// TenantStatistics để thống kê tenant
type TenantStatistics struct {
	TenantID         string    `json:"tenant_id"`
	TotalMembers     int       `json:"total_members"`
	ActiveMembers    int       `json:"active_members"`
	TotalPosts       int64     `json:"total_posts"`
	TotalComments    int64     `json:"total_comments"`
	StorageUsed      int64     `json:"storage_used"`
	APIRequestsMonth int64     `json:"api_requests_month"`
	LastActiveAt     time.Time `json:"last_active_at"`
	CreatedAt        time.Time `json:"created_at"`
}

// TenantActivity để log hoạt động tenant
type TenantActivity struct {
	ID         string                 `json:"id" gorm:"primaryKey"`
	TenantID   string                 `json:"tenant_id" validate:"required"`
	UserID     string                 `json:"user_id,omitempty"`
	Action     string                 `json:"action" validate:"required"`
	Resource   string                 `json:"resource" validate:"required"`
	ResourceID string                 `json:"resource_id,omitempty"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Metadata   string                 `json:"metadata"` // JSON string
	CreatedAt  time.Time              `json:"created_at"`
}