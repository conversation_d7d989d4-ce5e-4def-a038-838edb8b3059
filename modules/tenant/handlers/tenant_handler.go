package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
	"github.com/blog-api-v3/blog-api-v3/modules/tenant/services"
	"github.com/blog-api-v3/blog-api-v3/pkg/http/middleware"
	httpPkg "github.com/blog-api-v3/blog-api-v3/pkg/http"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
	"github.com/gorilla/mux"
)

// TenantHandler handles tenant-related endpoints
type TenantHandler struct {
	tenantService services.TenantService
	planService   services.PlanService
	validator     *validator.RequestValidator
	logger        utils.Logger
}

// NewTenantHandler tạo tenant handler mới
func NewTenantHandler(
	tenantService services.TenantService,
	planService services.PlanService,
	v validator.Validator,
	logger utils.Logger,
) *TenantHandler {
	return &TenantHandler{
		tenantService: tenantService,
		planService:   planService,
		validator:     validator.NewRequestValidator(v),
		logger:        logger,
	}
}

// RegisterRoutes đăng ký routes
func (h *TenantHandler) RegisterRoutes(router *mux.Router, authService interface{}) {
	// Public routes
	router.HandleFunc("/tenants/{slug}", h.GetTenantBySlug).Methods(http.MethodGet)
	router.HandleFunc("/plans", h.GetPublicPlans).Methods(http.MethodGet)
	
	// Protected routes
	protected := router.PathPrefix("/tenants").Subrouter()
	protected.Use(middleware.AuthMiddleware(authService.(middleware.AuthService)))
	
	protected.HandleFunc("", h.CreateTenant).Methods(http.MethodPost)
	protected.HandleFunc("", h.ListTenants).Methods(http.MethodGet)
	protected.HandleFunc("/{id}", h.GetTenant).Methods(http.MethodGet)
	protected.HandleFunc("/{id}", h.UpdateTenant).Methods(http.MethodPut, http.MethodPatch)
	protected.HandleFunc("/{id}", h.DeleteTenant).Methods(http.MethodDelete)
	protected.HandleFunc("/{id}/statistics", h.GetTenantStatistics).Methods(http.MethodGet)
	protected.HandleFunc("/{id}/activities", h.GetTenantActivities).Methods(http.MethodGet)
	protected.HandleFunc("/{id}/members", h.ListMembers).Methods(http.MethodGet)
	protected.HandleFunc("/my", h.GetMyTenants).Methods(http.MethodGet)
	protected.HandleFunc("/search", h.SearchTenants).Methods(http.MethodGet)
	
	// Tenant management routes
	protected.HandleFunc("/{id}/suspend", h.SuspendTenant).Methods(http.MethodPost)
	protected.HandleFunc("/{id}/unsuspend", h.UnsuspendTenant).Methods(http.MethodPost)
	protected.HandleFunc("/{id}/restore", h.RestoreTenant).Methods(http.MethodPost)
	protected.HandleFunc("/{id}/trial/extend", h.ExtendTrial).Methods(http.MethodPost)
	
	// Admin routes
	admin := router.PathPrefix("/admin/tenants").Subrouter()
	admin.Use(middleware.AuthMiddleware(authService.(middleware.AuthService)))
	admin.Use(middleware.RequireRoleMiddleware(authModels.RoleAdmin, authModels.RoleSuperAdmin))
	
	admin.HandleFunc("", h.AdminListTenants).Methods(http.MethodGet)
	admin.HandleFunc("/{id}", h.AdminGetTenant).Methods(http.MethodGet)
	admin.HandleFunc("/{id}", h.AdminUpdateTenant).Methods(http.MethodPut)
	admin.HandleFunc("/{id}", h.AdminDeleteTenant).Methods(http.MethodDelete)
	admin.HandleFunc("/{id}/hard-delete", h.AdminHardDeleteTenant).Methods(http.MethodDelete)
	admin.HandleFunc("/expiring", h.GetExpiringTenants).Methods(http.MethodGet)
}

// CreateTenant tạo tenant mới
func (h *TenantHandler) CreateTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var tenant models.Tenant
	if err := h.validator.ValidateJSON(r, &tenant); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Set owner info
	tenant.OwnerID = claims.UserID
	if tenant.OwnerEmail == "" {
		tenant.OwnerEmail = claims.Email
	}
	
	if err := h.tenantService.CreateTenant(r.Context(), &tenant); err != nil {
		h.logger.WithError(err).Error("Failed to create tenant")
		resp.Error(http.StatusInternalServerError, "Failed to create tenant")
		return
	}
	
	resp.Success(tenant)
}

// GetTenant lấy tenant theo ID
func (h *TenantHandler) GetTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	tenant, err := h.tenantService.GetTenant(r.Context(), tenantID)
	if err != nil {
		resp.Error(http.StatusNotFound, "Tenant not found")
		return
	}
	
	resp.Success(tenant)
}

// GetTenantBySlug lấy tenant theo slug (public)
func (h *TenantHandler) GetTenantBySlug(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	slug := vars["slug"]
	
	tenant, err := h.tenantService.GetTenantBySlug(r.Context(), slug)
	if err != nil {
		resp.Error(http.StatusNotFound, "Tenant not found")
		return
	}
	
	// Return public info only
	publicTenant := h.toPublicTenant(tenant)
	resp.Success(publicTenant)
}

// UpdateTenant cập nhật tenant
func (h *TenantHandler) UpdateTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	var update models.TenantUpdate
	if err := h.validator.ValidateJSON(r, &update); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	if err := h.tenantService.UpdateTenant(r.Context(), tenantID, &update); err != nil {
		h.logger.WithError(err).Error("Failed to update tenant")
		resp.Error(http.StatusInternalServerError, "Failed to update tenant")
		return
	}
	
	// Get updated tenant
	tenant, _ := h.tenantService.GetTenant(r.Context(), tenantID)
	resp.Success(tenant)
}

// DeleteTenant xóa tenant
func (h *TenantHandler) DeleteTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	if err := h.tenantService.DeleteTenant(r.Context(), tenantID); err != nil {
		h.logger.WithError(err).Error("Failed to delete tenant")
		resp.Error(http.StatusInternalServerError, "Failed to delete tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant deleted successfully"})
}

// ListTenants liệt kê tenants
func (h *TenantHandler) ListTenants(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Parse filter từ query params
	filter := &models.TenantFilter{
		Page:     1,
		PageSize: 20,
	}
	
	// Parse pagination
	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			filter.PageSize = ps
		}
	}
	
	// Parse filters
	if status := r.URL.Query().Get("status"); status != "" {
		filter.Status = models.TenantStatus(status)
	}
	
	if planType := r.URL.Query().Get("plan_type"); planType != "" {
		filter.PlanType = models.PlanType(planType)
	}
	
	filter.Country = r.URL.Query().Get("country")
	filter.Industry = r.URL.Query().Get("industry")
	filter.Size = r.URL.Query().Get("size")
	filter.Query = r.URL.Query().Get("q")
	filter.SortBy = r.URL.Query().Get("sort_by")
	filter.SortOrder = r.URL.Query().Get("sort_order")
	
	tenants, total, err := h.tenantService.ListTenants(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list tenants")
		resp.Error(http.StatusInternalServerError, "Failed to list tenants")
		return
	}
	
	resp.Paginated(tenants, filter.Page, filter.PageSize, int(total))
}

// SearchTenants tìm kiếm tenants
func (h *TenantHandler) SearchTenants(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	query := r.URL.Query().Get("q")
	if query == "" {
		resp.Error(http.StatusBadRequest, "Search query is required")
		return
	}
	
	limitStr := r.URL.Query().Get("limit")
	limit := 10
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	tenants, err := h.tenantService.SearchTenants(r.Context(), query, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search tenants")
		resp.Error(http.StatusInternalServerError, "Search failed")
		return
	}
	
	resp.Success(map[string]interface{}{
		"tenants": tenants,
		"count":   len(tenants),
		"query":   query,
	})
}

// GetMyTenants lấy tenants của user hiện tại
func (h *TenantHandler) GetMyTenants(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	tenants, err := h.tenantService.GetTenantsByOwner(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user tenants")
		resp.Error(http.StatusInternalServerError, "Failed to get tenants")
		return
	}
	
	resp.Success(map[string]interface{}{
		"tenants": tenants,
		"count":   len(tenants),
	})
}

// GetTenantStatistics lấy thống kê tenant
func (h *TenantHandler) GetTenantStatistics(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	stats, err := h.tenantService.GetTenantStatistics(r.Context(), tenantID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant statistics")
		resp.Error(http.StatusInternalServerError, "Failed to get statistics")
		return
	}
	
	resp.Success(stats)
}

// GetTenantActivities lấy activities của tenant
func (h *TenantHandler) GetTenantActivities(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	limitStr := r.URL.Query().Get("limit")
	limit := 20
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	activities, err := h.tenantService.GetTenantActivities(r.Context(), tenantID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant activities")
		resp.Error(http.StatusInternalServerError, "Failed to get activities")
		return
	}
	
	resp.Success(map[string]interface{}{
		"activities": activities,
		"count":      len(activities),
	})
}

// SuspendTenant tạm khóa tenant
func (h *TenantHandler) SuspendTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	var request struct {
		Reason string `json:"reason" validate:"required"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	if err := h.tenantService.SuspendTenant(r.Context(), tenantID, request.Reason); err != nil {
		h.logger.WithError(err).Error("Failed to suspend tenant")
		resp.Error(http.StatusInternalServerError, "Failed to suspend tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant suspended successfully"})
}

// UnsuspendTenant mở khóa tenant
func (h *TenantHandler) UnsuspendTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	if err := h.tenantService.UnsuspendTenant(r.Context(), tenantID); err != nil {
		h.logger.WithError(err).Error("Failed to unsuspend tenant")
		resp.Error(http.StatusInternalServerError, "Failed to unsuspend tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant unsuspended successfully"})
}

// RestoreTenant khôi phục tenant
func (h *TenantHandler) RestoreTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	if err := h.tenantService.RestoreTenant(r.Context(), tenantID); err != nil {
		h.logger.WithError(err).Error("Failed to restore tenant")
		resp.Error(http.StatusInternalServerError, "Failed to restore tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant restored successfully"})
}

// ExtendTrial gia hạn trial
func (h *TenantHandler) ExtendTrial(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	var request struct {
		Days int `json:"days" validate:"required,min=1,max=365"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	if err := h.tenantService.ExtendTrial(r.Context(), tenantID, request.Days); err != nil {
		h.logger.WithError(err).Error("Failed to extend trial")
		resp.Error(http.StatusInternalServerError, "Failed to extend trial")
		return
	}
	
	resp.Success(map[string]string{"message": "Trial extended successfully"})
}

// GetPublicPlans lấy public plans
func (h *TenantHandler) GetPublicPlans(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	plans, err := h.planService.GetPublicPlans(r.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get public plans")
		resp.Error(http.StatusInternalServerError, "Failed to get plans")
		return
	}
	
	resp.Success(map[string]interface{}{
		"plans": plans,
		"count": len(plans),
	})
}

// ListMembers liệt kê members (placeholder)
func (h *TenantHandler) ListMembers(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Mock members response
	resp.Success(map[string]interface{}{
		"members": []interface{}{},
		"count":   0,
		"message": "Member management will be implemented in future versions",
	})
}

// Admin endpoints

// AdminListTenants liệt kê tất cả tenants (admin)
func (h *TenantHandler) AdminListTenants(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	filter := &models.TenantFilter{
		Page:     1,
		PageSize: 50,
	}
	
	// Include all statuses for admin
	if status := r.URL.Query().Get("status"); status != "" {
		filter.Status = models.TenantStatus(status)
	}
	
	tenants, total, err := h.tenantService.ListTenants(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list tenants")
		resp.Error(http.StatusInternalServerError, "Failed to list tenants")
		return
	}
	
	resp.Paginated(tenants, filter.Page, filter.PageSize, int(total))
}

// AdminGetTenant lấy full tenant details (admin)
func (h *TenantHandler) AdminGetTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	tenant, err := h.tenantService.GetTenant(r.Context(), tenantID)
	if err != nil {
		resp.Error(http.StatusNotFound, "Tenant not found")
		return
	}
	
	// Get statistics and activities
	stats, _ := h.tenantService.GetTenantStatistics(r.Context(), tenantID)
	activities, _ := h.tenantService.GetTenantActivities(r.Context(), tenantID, 10)
	
	resp.Success(map[string]interface{}{
		"tenant":     tenant,
		"statistics": stats,
		"activities": activities,
	})
}

// AdminUpdateTenant cập nhật tenant (admin)
func (h *TenantHandler) AdminUpdateTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	var update models.TenantUpdate
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		resp.Error(http.StatusBadRequest, "Invalid request body")
		return
	}
	
	if err := h.tenantService.UpdateTenant(r.Context(), tenantID, &update); err != nil {
		h.logger.WithError(err).Error("Failed to update tenant")
		resp.Error(http.StatusInternalServerError, "Failed to update tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant updated successfully"})
}

// AdminDeleteTenant xóa tenant (admin)
func (h *TenantHandler) AdminDeleteTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	if err := h.tenantService.DeleteTenant(r.Context(), tenantID); err != nil {
		h.logger.WithError(err).Error("Failed to delete tenant")
		resp.Error(http.StatusInternalServerError, "Failed to delete tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant deleted successfully"})
}

// AdminHardDeleteTenant xóa vĩnh viễn tenant (admin)
func (h *TenantHandler) AdminHardDeleteTenant(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	tenantID := vars["id"]
	
	if err := h.tenantService.PermanentlyDeleteTenant(r.Context(), tenantID); err != nil {
		h.logger.WithError(err).Error("Failed to permanently delete tenant")
		resp.Error(http.StatusInternalServerError, "Failed to permanently delete tenant")
		return
	}
	
	resp.Success(map[string]string{"message": "Tenant permanently deleted successfully"})
}

// GetExpiringTenants lấy tenants sắp hết hạn (admin)
func (h *TenantHandler) GetExpiringTenants(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	daysStr := r.URL.Query().Get("days")
	days := 7 // Default 7 days
	if daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 {
			days = d
		}
	}
	
	tenants, err := h.tenantService.GetExpiringTenants(r.Context(), days)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get expiring tenants")
		resp.Error(http.StatusInternalServerError, "Failed to get expiring tenants")
		return
	}
	
	resp.Success(map[string]interface{}{
		"tenants": tenants,
		"count":   len(tenants),
		"days":    days,
	})
}

// Helper methods

// toPublicTenant converts tenant to public info
func (h *TenantHandler) toPublicTenant(tenant *models.Tenant) map[string]interface{} {
	return map[string]interface{}{
		"id":          tenant.ID,
		"name":        tenant.Name,
		"slug":        tenant.Slug,
		"description": tenant.Description,
		"website":     tenant.Website,
		"industry":    tenant.Industry,
		"size":        tenant.Size,
		"country":     tenant.Country,
		"logo":        tenant.Logo,
		"created_at":  tenant.CreatedAt,
	}
}