package repositories

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockPlanRepository implements PlanRepository với in-memory storage
type MockPlanRepository struct {
	plans map[string]*models.Plan
	mu    sync.RWMutex
}

// NewMockPlanRepository tạo mock plan repository mới
func NewMockPlanRepository() *MockPlanRepository {
	repo := &MockPlanRepository{
		plans: make(map[string]*models.Plan),
	}
	
	// Seed với mock data
	repo.seedMockData()
	
	return repo
}

// seedMockData thêm dữ liệu plans mẫu
func (r *MockPlanRepository) seedMockData() {
	now := time.Now()
	
	// Free Plan
	freePlan := &models.Plan{
		ID:          utils.GenerateULID(),
		Name:        "Free",
		Description: "Free plan for individuals and small teams",
		Type:        models.PlanTypeFree,
		Price:       0,
		Currency:    "USD",
		Interval:    "month",
		IsActive:    true,
		IsPublic:    true,
		Limits: models.PlanLimits{
			MaxUsers:        5,
			MaxStorage:      1024 * 1024 * 100, // 100MB
			MaxAPIRequests:  1000,
			MaxPosts:        10,
			MaxComments:     50,
			HasCustomDomain: false,
			HasSSO:          false,
			HasBranding:     false,
			HasAnalytics:    false,
			HasPriority:     false,
		},
		Features: []string{
			"Up to 5 users",
			"100MB storage",
			"1K API requests/month",
			"Basic support",
		},
		CreatedAt: now,
		UpdatedAt: now,
	}
	
	// Basic Plan
	basicPlan := &models.Plan{
		ID:          utils.GenerateULID(),
		Name:        "Basic",
		Description: "Basic plan for growing teams",
		Type:        models.PlanTypeBasic,
		Price:       9.99,
		Currency:    "USD",
		Interval:    "month",
		IsActive:    true,
		IsPublic:    true,
		Limits: models.PlanLimits{
			MaxUsers:        25,
			MaxStorage:      1024 * 1024 * 500, // 500MB
			MaxAPIRequests:  10000,
			MaxPosts:        100,
			MaxComments:     500,
			HasCustomDomain: false,
			HasSSO:          false,
			HasBranding:     true,
			HasAnalytics:    true,
			HasPriority:     false,
		},
		Features: []string{
			"Up to 25 users",
			"500MB storage",
			"10K API requests/month",
			"Custom branding",
			"Basic analytics",
			"Email support",
		},
		CreatedAt: now,
		UpdatedAt: now,
	}
	
	// Professional Plan
	proPlan := &models.Plan{
		ID:          utils.GenerateULID(),
		Name:        "Professional",
		Description: "Professional plan for established teams",
		Type:        models.PlanTypeProfessional,
		Price:       29.99,
		Currency:    "USD",
		Interval:    "month",
		IsActive:    true,
		IsPublic:    true,
		Limits: models.PlanLimits{
			MaxUsers:        100,
			MaxStorage:      1024 * 1024 * 2048, // 2GB
			MaxAPIRequests:  50000,
			MaxPosts:        1000,
			MaxComments:     5000,
			HasCustomDomain: true,
			HasSSO:          false,
			HasBranding:     true,
			HasAnalytics:    true,
			HasPriority:     true,
		},
		Features: []string{
			"Up to 100 users",
			"2GB storage",
			"50K API requests/month",
			"Custom domain",
			"Advanced analytics",
			"Priority support",
			"API access",
		},
		CreatedAt: now,
		UpdatedAt: now,
	}
	
	// Enterprise Plan
	enterprisePlan := &models.Plan{
		ID:          utils.GenerateULID(),
		Name:        "Enterprise",
		Description: "Enterprise plan for large organizations",
		Type:        models.PlanTypeEnterprise,
		Price:       99.99,
		Currency:    "USD",
		Interval:    "month",
		IsActive:    true,
		IsPublic:    true,
		Limits: models.PlanLimits{
			MaxUsers:        1000,
			MaxStorage:      1024 * 1024 * 10240, // 10GB
			MaxAPIRequests:  200000,
			MaxPosts:        10000,
			MaxComments:     50000,
			HasCustomDomain: true,
			HasSSO:          true,
			HasBranding:     true,
			HasAnalytics:    true,
			HasPriority:     true,
		},
		Features: []string{
			"Up to 1000 users",
			"10GB storage",
			"200K API requests/month",
			"Custom domain",
			"SSO integration",
			"Advanced analytics",
			"24/7 priority support",
			"Dedicated success manager",
			"SLA guarantee",
		},
		CreatedAt: now,
		UpdatedAt: now,
	}
	
	// Store plans
	r.plans[freePlan.ID] = freePlan
	r.plans[basicPlan.ID] = basicPlan
	r.plans[proPlan.ID] = proPlan
	r.plans[enterprisePlan.ID] = enterprisePlan
}

// Create tạo plan mới
func (r *MockPlanRepository) Create(ctx context.Context, plan *models.Plan) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.plans[plan.ID]; exists {
		return errors.New("plan already exists")
	}
	
	if plan.ID == "" {
		plan.ID = utils.GenerateULID()
	}
	
	now := time.Now()
	plan.CreatedAt = now
	plan.UpdatedAt = now
	
	r.plans[plan.ID] = plan
	return nil
}

// GetByID lấy plan theo ID
func (r *MockPlanRepository) GetByID(ctx context.Context, planID string) (*models.Plan, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	plan, exists := r.plans[planID]
	if !exists {
		return nil, errors.New("plan not found")
	}
	
	return plan, nil
}

// GetByType lấy plan theo type
func (r *MockPlanRepository) GetByType(ctx context.Context, planType models.PlanType) (*models.Plan, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, plan := range r.plans {
		if plan.Type == planType && plan.IsActive && plan.DeletedAt == nil {
			return plan, nil
		}
	}
	
	return nil, errors.New("plan not found")
}

// List liệt kê plans
func (r *MockPlanRepository) List(ctx context.Context, activeOnly bool) ([]*models.Plan, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var plans []*models.Plan
	for _, plan := range r.plans {
		if plan.DeletedAt != nil {
			continue
		}
		
		if activeOnly && !plan.IsActive {
			continue
		}
		
		plans = append(plans, plan)
	}
	
	return plans, nil
}

// Update cập nhật plan
func (r *MockPlanRepository) Update(ctx context.Context, planID string, updates map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	plan, exists := r.plans[planID]
	if !exists {
		return errors.New("plan not found")
	}
	
	// Apply updates
	for key, value := range updates {
		switch key {
		case "name":
			plan.Name = value.(string)
		case "description":
			plan.Description = value.(string)
		case "price":
			plan.Price = value.(float64)
		case "currency":
			plan.Currency = value.(string)
		case "interval":
			plan.Interval = value.(string)
		case "is_active":
			plan.IsActive = value.(bool)
		case "is_public":
			plan.IsPublic = value.(bool)
		case "limits":
			plan.Limits = value.(models.PlanLimits)
		case "features":
			plan.Features = value.([]string)
		}
	}
	
	plan.UpdatedAt = time.Now()
	return nil
}

// Delete xóa plan
func (r *MockPlanRepository) Delete(ctx context.Context, planID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	plan, exists := r.plans[planID]
	if !exists {
		return errors.New("plan not found")
	}
	
	now := time.Now()
	plan.DeletedAt = &now
	plan.IsActive = false
	plan.IsPublic = false
	
	return nil
}

// GetPublicPlans lấy plans công khai
func (r *MockPlanRepository) GetPublicPlans(ctx context.Context) ([]*models.Plan, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var publicPlans []*models.Plan
	for _, plan := range r.plans {
		if plan.IsPublic && plan.IsActive && plan.DeletedAt == nil {
			publicPlans = append(publicPlans, plan)
		}
	}
	
	return publicPlans, nil
}