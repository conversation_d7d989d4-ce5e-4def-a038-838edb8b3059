package repositories

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockTenantRepository implements TenantRepository với in-memory storage
type MockTenantRepository struct {
	tenants       map[string]*models.Tenant
	tenantsBySlug map[string]*models.Tenant
	activities    map[string][]*models.TenantActivity
	mu            sync.RWMutex
}

// NewMockTenantRepository tạo mock tenant repository mới
func NewMockTenantRepository() *MockTenantRepository {
	repo := &MockTenantRepository{
		tenants:       make(map[string]*models.Tenant),
		tenantsBySlug: make(map[string]*models.Tenant),
		activities:    make(map[string][]*models.TenantActivity),
	}

	// Seed với mock data
	repo.seedMockData()

	return repo
}

// seedMockData thêm dữ liệu mẫu
func (r *MockTenantRepository) seedMockData() {
	now := time.Now()
	trialEnd := now.AddDate(0, 0, 14) // 14 days trial

	// Demo tenant
	demo := &models.Tenant{
		ID:          utils.GenerateULID(),
		Name:        "Demo Company",
		Slug:        "demo-company",
		Description: "This is a demo tenant for testing purposes",
		Website:     "https://demo.example.com",
		Industry:    "Technology",
		Size:        "11-50",
		Country:     "VN",
		Timezone:    "Asia/Ho_Chi_Minh",
		Language:    "vi",
		Status:      models.TenantStatusTrial,
		IsActive:    true,
		OwnerID:     "owner-1",
		OwnerEmail:  "<EMAIL>",
		Settings: models.TenantSettings{
			AllowInvitations:         true,
			RequireEmailVerification: true,
			DefaultUserRole:          "member",
			MaxMembers:               50,
			AllowPublicSignup:        false,
			BrandingEnabled:          false,
			SSOEnabled:               false,
			TwoFactorRequired:        false,
			SessionTimeout:           60,
		},
		CreatedAt:   now,
		UpdatedAt:   now,
		TrialEndsAt: &trialEnd,
	}

	// Enterprise tenant
	enterprise := &models.Tenant{
		ID:          utils.GenerateULID(),
		Name:        "Enterprise Corp",
		Slug:        "enterprise-corp",
		Description: "Large enterprise organization",
		Website:     "https://enterprise.example.com",
		Industry:    "Finance",
		Size:        "1000+",
		Country:     "US",
		Timezone:    "America/New_York",
		Language:    "en",
		Status:      models.TenantStatusActive,
		IsActive:    true,
		OwnerID:     "owner-2",
		OwnerEmail:  "<EMAIL>",
		Settings: models.TenantSettings{
			AllowInvitations:         true,
			RequireEmailVerification: true,
			DefaultUserRole:          "member",
			MaxMembers:               1000,
			AllowPublicSignup:        false,
			CustomDomain:             "app.enterprise.com",
			BrandingEnabled:          true,
			SSOEnabled:               true,
			TwoFactorRequired:        true,
			SessionTimeout:           30,
		},
		CreatedAt: now.AddDate(0, -2, 0), // 2 months ago
		UpdatedAt: now,
	}

	// Store tenants
	r.tenants[demo.ID] = demo
	r.tenantsBySlug[demo.Slug] = demo

	r.tenants[enterprise.ID] = enterprise
	r.tenantsBySlug[enterprise.Slug] = enterprise
}

// Create tạo tenant mới
func (r *MockTenantRepository) Create(ctx context.Context, tenant *models.Tenant) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check duplicates
	if _, exists := r.tenants[tenant.ID]; exists {
		return errors.New("tenant already exists")
	}
	if _, exists := r.tenantsBySlug[tenant.Slug]; exists {
		return errors.New("slug already exists")
	}

	// Set defaults
	if tenant.ID == "" {
		tenant.ID = utils.GenerateULID()
	}
	if tenant.Status == "" {
		tenant.Status = models.TenantStatusTrial
	}
	if tenant.Language == "" {
		tenant.Language = "en"
	}
	if tenant.Timezone == "" {
		tenant.Timezone = "UTC"
	}

	now := time.Now()
	tenant.CreatedAt = now
	tenant.UpdatedAt = now

	// Set trial end date for trial tenants
	if tenant.Status == models.TenantStatusTrial && tenant.TrialEndsAt == nil {
		trialEnd := now.AddDate(0, 0, 14) // 14 days trial
		tenant.TrialEndsAt = &trialEnd
	}

	// Store tenant
	r.tenants[tenant.ID] = tenant
	r.tenantsBySlug[tenant.Slug] = tenant

	return nil
}

// GetByID lấy tenant theo ID
func (r *MockTenantRepository) GetByID(ctx context.Context, tenantID string) (*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tenant, exists := r.tenants[tenantID]
	if !exists {
		return nil, errors.New("tenant not found")
	}

	return tenant, nil
}

// GetBySlug lấy tenant theo slug
func (r *MockTenantRepository) GetBySlug(ctx context.Context, slug string) (*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tenant, exists := r.tenantsBySlug[slug]
	if !exists {
		return nil, errors.New("tenant not found")
	}

	return tenant, nil
}

// GetByOwnerID lấy tenant theo owner ID
func (r *MockTenantRepository) GetByOwnerID(ctx context.Context, ownerID string) ([]*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var tenants []*models.Tenant
	for _, tenant := range r.tenants {
		if tenant.OwnerID == ownerID && tenant.DeletedAt == nil {
			tenants = append(tenants, tenant)
		}
	}

	return tenants, nil
}

// Update cập nhật tenant
func (r *MockTenantRepository) Update(ctx context.Context, tenantID string, updates map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	tenant, exists := r.tenants[tenantID]
	if !exists {
		return errors.New("tenant not found")
	}

	// Apply updates
	for key, value := range updates {
		switch key {
		case "name":
			tenant.Name = value.(string)
		case "description":
			tenant.Description = value.(string)
		case "website":
			tenant.Website = value.(string)
		case "industry":
			tenant.Industry = value.(string)
		case "size":
			tenant.Size = value.(string)
		case "country":
			tenant.Country = value.(string)
		case "timezone":
			tenant.Timezone = value.(string)
		case "language":
			tenant.Language = value.(string)
		case "logo":
			tenant.Logo = value.(string)
		case "status":
			tenant.Status = value.(models.TenantStatus)
		case "is_active":
			tenant.IsActive = value.(bool)
		case "settings":
			tenant.Settings = value.(models.TenantSettings)
		case "slug":
			// Update slug mapping
			oldSlug := tenant.Slug
			newSlug := value.(string)
			delete(r.tenantsBySlug, oldSlug)
			tenant.Slug = newSlug
			r.tenantsBySlug[newSlug] = tenant
		}
	}

	tenant.UpdatedAt = time.Now()
	return nil
}

// Delete xóa mềm tenant
func (r *MockTenantRepository) Delete(ctx context.Context, tenantID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	tenant, exists := r.tenants[tenantID]
	if !exists {
		return errors.New("tenant not found")
	}

	now := time.Now()
	tenant.DeletedAt = &now
	tenant.Status = models.TenantStatusCanceled
	tenant.IsActive = false

	return nil
}

// HardDelete xóa vĩnh viễn tenant
func (r *MockTenantRepository) HardDelete(ctx context.Context, tenantID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	tenant, exists := r.tenants[tenantID]
	if !exists {
		return errors.New("tenant not found")
	}

	delete(r.tenants, tenantID)
	delete(r.tenantsBySlug, tenant.Slug)
	delete(r.activities, tenantID)

	return nil
}

// List liệt kê tenants với filter
func (r *MockTenantRepository) List(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Apply filters
	var filtered []*models.Tenant
	for _, tenant := range r.tenants {
		if r.matchesFilter(tenant, filter) {
			filtered = append(filtered, tenant)
		}
	}

	total := int64(len(filtered))

	// Apply pagination
	page := filter.Page
	if page < 1 {
		page = 1
	}
	pageSize := filter.PageSize
	if pageSize < 1 {
		pageSize = 20
	}

	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(filtered) {
		return []*models.Tenant{}, total, nil
	}

	if end > len(filtered) {
		end = len(filtered)
	}

	return filtered[start:end], total, nil
}

// ListWithCursor liệt kê tenants với cursor-based pagination
func (r *MockTenantRepository) ListWithCursor(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, *pagination.CursorResponse, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Apply filters
	var filtered []*models.Tenant
	for _, tenant := range r.tenants {
		if r.matchesFilter(tenant, filter) {
			filtered = append(filtered, tenant)
		}
	}

	// For mock purposes, just return a simple cursor response
	limit := filter.Limit
	if limit <= 0 {
		limit = 20
	}

	// Simple cursor logic for mock
	var results []*models.Tenant
	hasMore := false

	if len(filtered) > limit {
		results = filtered[:limit]
		hasMore = true
	} else {
		results = filtered
	}

	cursorResponse := &pagination.CursorResponse{
		HasMore: hasMore,
		Limit:   limit,
	}

	if hasMore && len(results) > 0 {
		// Generate a simple cursor for the last item
		lastTenant := results[len(results)-1]
		cursor := pagination.NewCursorFromEntity(1, lastTenant.CreatedAt, nil)
		if encodedCursor, err := cursor.String(); err == nil {
			cursorResponse.NextCursor = encodedCursor
		}
	}

	return results, cursorResponse, nil
}

// Search tìm kiếm tenants
func (r *MockTenantRepository) Search(ctx context.Context, query string, limit int) ([]*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	query = strings.ToLower(query)
	var results []*models.Tenant

	for _, tenant := range r.tenants {
		if tenant.DeletedAt != nil {
			continue
		}

		if strings.Contains(strings.ToLower(tenant.Name), query) ||
			strings.Contains(strings.ToLower(tenant.Slug), query) ||
			strings.Contains(strings.ToLower(tenant.Description), query) ||
			strings.Contains(strings.ToLower(tenant.Industry), query) {
			results = append(results, tenant)

			if limit > 0 && len(results) >= limit {
				break
			}
		}
	}

	return results, nil
}

// Count đếm số lượng tenants
func (r *MockTenantRepository) Count(ctx context.Context, filter *models.TenantFilter) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	count := int64(0)
	for _, tenant := range r.tenants {
		if r.matchesFilter(tenant, filter) {
			count++
		}
	}

	return count, nil
}

// Exists kiểm tra tenant tồn tại
func (r *MockTenantRepository) Exists(ctx context.Context, tenantID string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.tenants[tenantID]
	return exists, nil
}

// ExistsBySlug kiểm tra slug tồn tại
func (r *MockTenantRepository) ExistsBySlug(ctx context.Context, slug string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.tenantsBySlug[slug]
	return exists, nil
}

// UpdateStatus cập nhật status
func (r *MockTenantRepository) UpdateStatus(ctx context.Context, tenantID string, status models.TenantStatus) error {
	return r.Update(ctx, tenantID, map[string]interface{}{
		"status": status,
	})
}

// GetStatistics lấy thống kê tenant
func (r *MockTenantRepository) GetStatistics(ctx context.Context, tenantID string) (*models.TenantStatistics, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tenant, exists := r.tenants[tenantID]
	if !exists {
		return nil, errors.New("tenant not found")
	}

	// Mock statistics
	stats := &models.TenantStatistics{
		TenantID:         tenantID,
		TotalMembers:     5,
		ActiveMembers:    4,
		TotalPosts:       150,
		TotalComments:    450,
		StorageUsed:      1024 * 1024 * 50, // 50MB
		APIRequestsMonth: 5000,
		LastActiveAt:     time.Now(),
		CreatedAt:        tenant.CreatedAt,
	}

	return stats, nil
}

// GetActivities lấy activities của tenant
func (r *MockTenantRepository) GetActivities(ctx context.Context, tenantID string, limit int) ([]*models.TenantActivity, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	activities, exists := r.activities[tenantID]
	if !exists {
		return []*models.TenantActivity{}, nil
	}

	if limit > 0 && len(activities) > limit {
		return activities[:limit], nil
	}

	return activities, nil
}

// LogActivity ghi log activity
func (r *MockTenantRepository) LogActivity(ctx context.Context, activity *models.TenantActivity) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if activity.ID == "" {
		activity.ID = utils.GenerateULID()
	}
	activity.CreatedAt = time.Now()

	r.activities[activity.TenantID] = append([]*models.TenantActivity{activity}, r.activities[activity.TenantID]...)

	// Keep only last 100 activities per tenant
	if len(r.activities[activity.TenantID]) > 100 {
		r.activities[activity.TenantID] = r.activities[activity.TenantID][:100]
	}

	return nil
}

// BatchGet lấy nhiều tenants theo IDs
func (r *MockTenantRepository) BatchGet(ctx context.Context, tenantIDs []string) ([]*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tenants := make([]*models.Tenant, 0, len(tenantIDs))
	for _, id := range tenantIDs {
		if tenant, exists := r.tenants[id]; exists {
			tenants = append(tenants, tenant)
		}
	}

	return tenants, nil
}

// GetExpiring lấy tenants sắp hết hạn trial
func (r *MockTenantRepository) GetExpiring(ctx context.Context, days int) ([]*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	cutoff := time.Now().AddDate(0, 0, days)
	var expiring []*models.Tenant

	for _, tenant := range r.tenants {
		if tenant.Status == models.TenantStatusTrial &&
			tenant.TrialEndsAt != nil &&
			tenant.TrialEndsAt.Before(cutoff) &&
			tenant.DeletedAt == nil {
			expiring = append(expiring, tenant)
		}
	}

	return expiring, nil
}

// matchesFilter kiểm tra tenant có match filter không
func (r *MockTenantRepository) matchesFilter(tenant *models.Tenant, filter *models.TenantFilter) bool {
	// Skip deleted tenants
	if tenant.DeletedAt != nil {
		return false
	}

	// Status filter
	if filter.Status != "" && tenant.Status != filter.Status {
		return false
	}

	// Country filter
	if filter.Country != "" && tenant.Country != filter.Country {
		return false
	}

	// Industry filter
	if filter.Industry != "" && tenant.Industry != filter.Industry {
		return false
	}

	// Size filter
	if filter.Size != "" && tenant.Size != filter.Size {
		return false
	}

	// Date range filters
	if filter.CreatedFrom != nil && tenant.CreatedAt.Before(*filter.CreatedFrom) {
		return false
	}
	if filter.CreatedTo != nil && tenant.CreatedAt.After(*filter.CreatedTo) {
		return false
	}

	// Search query
	if filter.Query != "" {
		query := strings.ToLower(filter.Query)
		if !strings.Contains(strings.ToLower(tenant.Name), query) &&
			!strings.Contains(strings.ToLower(tenant.Slug), query) &&
			!strings.Contains(strings.ToLower(tenant.Description), query) {
			return false
		}
	}

	return true
}
