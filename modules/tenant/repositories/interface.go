package repositories

import (
	"context"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// TenantRepository định nghĩa interface cho tenant data access
type TenantRepository interface {
	// Create tạo tenant mới
	Create(ctx context.Context, tenant *models.Tenant) error
	
	// GetByID lấy tenant theo ID
	GetByID(ctx context.Context, tenantID string) (*models.Tenant, error)
	
	// GetBySlug lấy tenant theo slug
	GetBySlug(ctx context.Context, slug string) (*models.Tenant, error)
	
	// GetByOwnerID lấy tenant theo owner ID
	GetByOwnerID(ctx context.Context, ownerID string) ([]*models.Tenant, error)
	
	// Update cập nhật tenant
	Update(ctx context.Context, tenantID string, updates map[string]interface{}) error
	
	// Delete xóa mềm tenant
	Delete(ctx context.Context, tenantID string) error
	
	// HardDelete xóa vĩnh viễn tenant
	HardDelete(ctx context.Context, tenantID string) error
	
	// List liệt kê tenants với filter
	List(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, int64, error)
	
	// ListWithCursor liệt kê tenants với cursor-based pagination
	ListWithCursor(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, *pagination.CursorResponse, error)
	
	// Search tìm kiếm tenants
	Search(ctx context.Context, query string, limit int) ([]*models.Tenant, error)
	
	// Count đếm số lượng tenants
	Count(ctx context.Context, filter *models.TenantFilter) (int64, error)
	
	// Exists kiểm tra tenant tồn tại
	Exists(ctx context.Context, tenantID string) (bool, error)
	
	// ExistsBySlug kiểm tra slug tồn tại
	ExistsBySlug(ctx context.Context, slug string) (bool, error)
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, tenantID string, status models.TenantStatus) error
	
	// GetStatistics lấy thống kê tenant
	GetStatistics(ctx context.Context, tenantID string) (*models.TenantStatistics, error)
	
	// GetActivities lấy activities của tenant
	GetActivities(ctx context.Context, tenantID string, limit int) ([]*models.TenantActivity, error)
	
	// LogActivity ghi log activity
	LogActivity(ctx context.Context, activity *models.TenantActivity) error
	
	// BatchGet lấy nhiều tenants theo IDs
	BatchGet(ctx context.Context, tenantIDs []string) ([]*models.Tenant, error)
	
	// GetExpiring lấy tenants sắp hết hạn trial
	GetExpiring(ctx context.Context, days int) ([]*models.Tenant, error)
}

// TenantMemberRepository định nghĩa interface cho tenant member data access
type TenantMemberRepository interface {
	// AddMember thêm member vào tenant
	AddMember(ctx context.Context, member *models.TenantMember) error
	
	// GetMember lấy member theo ID
	GetMember(ctx context.Context, memberID string) (*models.TenantMember, error)
	
	// GetMemberByUserID lấy member theo user ID và tenant ID
	GetMemberByUserID(ctx context.Context, tenantID, userID string) (*models.TenantMember, error)
	
	// ListMembers liệt kê members của tenant
	ListMembers(ctx context.Context, tenantID string, page, pageSize int) ([]*models.TenantMember, int64, error)
	
	// UpdateMember cập nhật member
	UpdateMember(ctx context.Context, memberID string, updates map[string]interface{}) error
	
	// RemoveMember xóa member khỏi tenant
	RemoveMember(ctx context.Context, memberID string) error
	
	// UpdateMemberRole cập nhật role của member
	UpdateMemberRole(ctx context.Context, memberID string, role string) error
	
	// GetMembersByUserID lấy tất cả tenants mà user là member
	GetMembersByUserID(ctx context.Context, userID string) ([]*models.TenantMember, error)
	
	// CountMembers đếm số members của tenant
	CountMembers(ctx context.Context, tenantID string) (int64, error)
	
	// IsMember kiểm tra user có phải member không
	IsMember(ctx context.Context, tenantID, userID string) (bool, error)
}

// SubscriptionRepository định nghĩa interface cho subscription data access
type SubscriptionRepository interface {
	// Create tạo subscription mới
	Create(ctx context.Context, subscription *models.Subscription) error
	
	// GetByID lấy subscription theo ID
	GetByID(ctx context.Context, subscriptionID string) (*models.Subscription, error)
	
	// GetByTenantID lấy subscription theo tenant ID
	GetByTenantID(ctx context.Context, tenantID string) (*models.Subscription, error)
	
	// Update cập nhật subscription
	Update(ctx context.Context, subscriptionID string, updates map[string]interface{}) error
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, subscriptionID string, status models.SubscriptionStatus) error
	
	// UpdateUsage cập nhật usage
	UpdateUsage(ctx context.Context, subscriptionID string, usage models.SubscriptionUsage) error
	
	// Cancel hủy subscription
	Cancel(ctx context.Context, subscriptionID string) error
	
	// List liệt kê subscriptions
	List(ctx context.Context, page, pageSize int) ([]*models.Subscription, int64, error)
	
	// GetExpiring lấy subscriptions sắp hết hạn
	GetExpiring(ctx context.Context, days int) ([]*models.Subscription, error)
	
	// GetByStatus lấy subscriptions theo status
	GetByStatus(ctx context.Context, status models.SubscriptionStatus) ([]*models.Subscription, error)
}

// PlanRepository định nghĩa interface cho plan data access
type PlanRepository interface {
	// Create tạo plan mới
	Create(ctx context.Context, plan *models.Plan) error
	
	// GetByID lấy plan theo ID
	GetByID(ctx context.Context, planID string) (*models.Plan, error)
	
	// GetByType lấy plan theo type
	GetByType(ctx context.Context, planType models.PlanType) (*models.Plan, error)
	
	// List liệt kê plans
	List(ctx context.Context, activeOnly bool) ([]*models.Plan, error)
	
	// Update cập nhật plan
	Update(ctx context.Context, planID string, updates map[string]interface{}) error
	
	// Delete xóa plan
	Delete(ctx context.Context, planID string) error
	
	// GetPublicPlans lấy plans công khai
	GetPublicPlans(ctx context.Context) ([]*models.Plan, error)
}

// TenantInvitationRepository định nghĩa interface cho invitation data access
type TenantInvitationRepository interface {
	// Create tạo invitation mới
	Create(ctx context.Context, invitation *models.TenantInvitation) error
	
	// GetByID lấy invitation theo ID
	GetByID(ctx context.Context, invitationID string) (*models.TenantInvitation, error)
	
	// GetByToken lấy invitation theo token
	GetByToken(ctx context.Context, token string) (*models.TenantInvitation, error)
	
	// ListByTenant liệt kê invitations của tenant
	ListByTenant(ctx context.Context, tenantID string, page, pageSize int) ([]*models.TenantInvitation, int64, error)
	
	// ListByEmail liệt kê invitations theo email
	ListByEmail(ctx context.Context, email string) ([]*models.TenantInvitation, error)
	
	// Update cập nhật invitation
	Update(ctx context.Context, invitationID string, updates map[string]interface{}) error
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, invitationID string, status models.InvitationStatus) error
	
	// Delete xóa invitation
	Delete(ctx context.Context, invitationID string) error
	
	// ExpireOld đánh dấu expired cho invitations cũ
	ExpireOld(ctx context.Context) error
	
	// GetPendingByEmail lấy pending invitations theo email
	GetPendingByEmail(ctx context.Context, email string) ([]*models.TenantInvitation, error)
}