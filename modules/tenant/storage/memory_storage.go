package storage

import (
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
)

// MemoryTenantRepository implements in-memory storage for tenants
type MemoryTenantRepository struct {
	tenants map[string]*models.Tenant
	mu      sync.RWMutex
}

// NewMemoryTenantRepository creates a new in-memory tenant repository
func NewMemoryTenantRepository() *MemoryTenantRepository {
	repo := &MemoryTenantRepository{
		tenants: make(map[string]*models.Tenant),
	}
	
	// Seed with some mock tenants
	repo.seedTenants()
	return repo
}

func (r *MemoryTenantRepository) seedTenants() {
	now := time.Now()
	
	tenants := []*models.Tenant{
		{
			ID:          "tenant-1",
			Name:        "Demo Tenant",
			Slug:        "demo",
			Description: "Demo tenant for testing",
			Status:      models.TenantStatusActive,
			Settings: models.TenantSettings{
				AllowInvitations:     true,
				RequireEmailVerification: true,
				DefaultUserRole:      "member",
				MaxMembers:           100,
				AllowPublicSignup:    false,
				BrandingEnabled:      true,
				SSOEnabled:          false,
				TwoFactorRequired:   false,
				SessionTimeout:      60,
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
		{
			ID:          "tenant-2",
			Name:        "Test Company",
			Slug:        "test-company",
			Description: "Test company tenant",
			Status:      models.TenantStatusActive,
			Settings: models.TenantSettings{
				AllowInvitations:     false,
				RequireEmailVerification: true,
				DefaultUserRole:      "member",
				MaxMembers:           50,
				AllowPublicSignup:    false,
				BrandingEnabled:      false,
				SSOEnabled:          false,
				TwoFactorRequired:   true,
				SessionTimeout:      30,
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}
	
	for _, tenant := range tenants {
		r.tenants[tenant.ID] = tenant
	}
}

func (r *MemoryTenantRepository) Create(tenant *models.Tenant) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	tenant.CreatedAt = time.Now()
	tenant.UpdatedAt = time.Now()
	r.tenants[tenant.ID] = tenant
	return nil
}

func (r *MemoryTenantRepository) GetByID(id string) (*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if tenant, exists := r.tenants[id]; exists {
		return tenant, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryTenantRepository) GetBySlug(slug string) (*models.Tenant, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, tenant := range r.tenants {
		if tenant.Slug == slug {
			return tenant, nil
		}
	}
	return nil, ErrNotFound
}

func (r *MemoryTenantRepository) Update(tenant *models.Tenant) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.tenants[tenant.ID]; !exists {
		return ErrNotFound
	}
	
	tenant.UpdatedAt = time.Now()
	r.tenants[tenant.ID] = tenant
	return nil
}

func (r *MemoryTenantRepository) Delete(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.tenants[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.tenants, id)
	return nil
}

func (r *MemoryTenantRepository) List(filter *models.TenantFilter) ([]*models.Tenant, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	tenants := make([]*models.Tenant, 0)
	
	for _, tenant := range r.tenants {
		if filter.Status != "" && tenant.Status != filter.Status {
			continue
		}
		if filter.Query != "" && !contains(tenant.Name, filter.Query) && !contains(tenant.Slug, filter.Query) {
			continue
		}
		tenants = append(tenants, tenant)
	}
	
	return tenants, int64(len(tenants)), nil
}

// contains checks if a string contains a substring (case-insensitive)
func contains(str, substr string) bool {
	// Simple case-insensitive contains check
	return len(str) >= len(substr) && 
		   str == substr || 
		   (len(str) > len(substr) && (str[:len(substr)] == substr || str[len(str)-len(substr):] == substr))
}

// Error definitions
var (
	ErrNotFound = &StorageError{Code: "NOT_FOUND", Message: "Record not found"}
)

// StorageError represents a storage error
type StorageError struct {
	Code    string
	Message string
}

func (e *StorageError) Error() string {
	return e.Message
}