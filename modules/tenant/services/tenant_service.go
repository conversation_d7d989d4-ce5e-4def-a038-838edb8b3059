package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
	"github.com/blog-api-v3/blog-api-v3/modules/tenant/repositories"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockTenantService implements TenantService interface
type MockTenantService struct {
	tenantRepo repositories.TenantRepository
	planRepo   repositories.PlanRepository
	logger     utils.Logger
}

// NewMockTenantService tạo mock tenant service mới
func NewMockTenantService(
	tenantRepo repositories.TenantRepository,
	planRepo repositories.PlanRepository,
	logger utils.Logger,
) TenantService {
	return &MockTenantService{
		tenantRepo: tenantRepo,
		planRepo:   planRepo,
		logger:     logger,
	}
}

// CreateTenant tạo tenant mới
func (s *MockTenantService) CreateTenant(ctx context.Context, tenant *models.Tenant) error {
	// Validate tenant data
	if tenant.Name == "" || tenant.OwnerEmail == "" {
		return errors.New("name and owner email are required")
	}
	
	// Generate slug if not provided
	if tenant.Slug == "" {
		slug, err := s.GenerateSlug(ctx, tenant.Name)
		if err != nil {
			return fmt.Errorf("failed to generate slug: %w", err)
		}
		tenant.Slug = slug
	}
	
	// Validate slug
	if err := s.ValidateSlug(ctx, tenant.Slug, ""); err != nil {
		return err
	}
	
	// Set defaults
	if tenant.ID == "" {
		tenant.ID = utils.GenerateULID()
	}
	if tenant.Status == "" {
		tenant.Status = models.TenantStatusTrial
	}
	if tenant.Language == "" {
		tenant.Language = "en"
	}
	if tenant.Timezone == "" {
		tenant.Timezone = "UTC"
	}
	
	// Set default settings
	if tenant.Settings.DefaultUserRole == "" {
		tenant.Settings.DefaultUserRole = "member"
	}
	if tenant.Settings.MaxMembers == 0 {
		tenant.Settings.MaxMembers = 5 // Default free plan limit
	}
	if tenant.Settings.SessionTimeout == 0 {
		tenant.Settings.SessionTimeout = 60 // 1 hour
	}
	
	// Create tenant
	if err := s.tenantRepo.Create(ctx, tenant); err != nil {
		s.logger.WithError(err).Error("Failed to create tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenant.ID, tenant.OwnerID, "tenant_created", "tenant", tenant.ID, nil)
	
	return nil
}

// GetTenant lấy tenant theo ID
func (s *MockTenantService) GetTenant(ctx context.Context, tenantID string) (*models.Tenant, error) {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).WithField("tenant_id", tenantID).Error("Failed to get tenant")
		return nil, err
	}
	return tenant, nil
}

// GetTenantBySlug lấy tenant theo slug
func (s *MockTenantService) GetTenantBySlug(ctx context.Context, slug string) (*models.Tenant, error) {
	return s.tenantRepo.GetBySlug(ctx, slug)
}

// GetTenantsByOwner lấy tenants theo owner
func (s *MockTenantService) GetTenantsByOwner(ctx context.Context, ownerID string) ([]*models.Tenant, error) {
	return s.tenantRepo.GetByOwnerID(ctx, ownerID)
}

// UpdateTenant cập nhật tenant
func (s *MockTenantService) UpdateTenant(ctx context.Context, tenantID string, update *models.TenantUpdate) error {
	// Get existing tenant
	_, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}
	
	// Build updates map
	updates := make(map[string]interface{})
	
	if update.Name != nil {
		updates["name"] = *update.Name
	}
	if update.Description != nil {
		updates["description"] = *update.Description
	}
	if update.Website != nil {
		updates["website"] = *update.Website
	}
	if update.Industry != nil {
		updates["industry"] = *update.Industry
	}
	if update.Size != nil {
		updates["size"] = *update.Size
	}
	if update.Country != nil {
		updates["country"] = *update.Country
	}
	if update.Timezone != nil {
		updates["timezone"] = *update.Timezone
	}
	if update.Language != nil {
		updates["language"] = *update.Language
	}
	if update.Logo != nil {
		updates["logo"] = *update.Logo
	}
	if update.Settings != nil {
		updates["settings"] = *update.Settings
	}
	
	// Update tenant
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to update tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "tenant_updated", "tenant", tenantID, updates)
	
	return nil
}

// DeleteTenant xóa tenant
func (s *MockTenantService) DeleteTenant(ctx context.Context, tenantID string) error {
	if err := s.tenantRepo.Delete(ctx, tenantID); err != nil {
		s.logger.WithError(err).Error("Failed to delete tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "tenant_deleted", "tenant", tenantID, nil)
	
	return nil
}

// RestoreTenant khôi phục tenant
func (s *MockTenantService) RestoreTenant(ctx context.Context, tenantID string) error {
	updates := map[string]interface{}{
		"status":     models.TenantStatusActive,
		"is_active":  true,
	}
	
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to restore tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "tenant_restored", "tenant", tenantID, nil)
	
	return nil
}

// PermanentlyDeleteTenant xóa vĩnh viễn tenant
func (s *MockTenantService) PermanentlyDeleteTenant(ctx context.Context, tenantID string) error {
	if err := s.tenantRepo.HardDelete(ctx, tenantID); err != nil {
		s.logger.WithError(err).Error("Failed to permanently delete tenant")
		return err
	}
	
	return nil
}

// ListTenants liệt kê tenants với filter
func (s *MockTenantService) ListTenants(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, int64, error) {
	// Set defaults
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.PageSize < 1 {
		filter.PageSize = 20
	}
	if filter.PageSize > 100 {
		filter.PageSize = 100
	}
	if filter.SortBy == "" {
		filter.SortBy = "created_at"
	}
	if filter.SortOrder == "" {
		filter.SortOrder = "desc"
	}
	
	return s.tenantRepo.List(ctx, filter)
}

// SearchTenants tìm kiếm tenants
func (s *MockTenantService) SearchTenants(ctx context.Context, query string, limit int) ([]*models.Tenant, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	
	return s.tenantRepo.Search(ctx, query, limit)
}

// UpdateTenantStatus cập nhật status
func (s *MockTenantService) UpdateTenantStatus(ctx context.Context, tenantID string, status models.TenantStatus) error {
	if err := s.tenantRepo.UpdateStatus(ctx, tenantID, status); err != nil {
		s.logger.WithError(err).Error("Failed to update tenant status")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "status_changed", "tenant", tenantID, map[string]interface{}{
		"new_status": status,
	})
	
	return nil
}

// SuspendTenant tạm khóa tenant
func (s *MockTenantService) SuspendTenant(ctx context.Context, tenantID string, reason string) error {
	updates := map[string]interface{}{
		"status":    models.TenantStatusSuspended,
		"is_active": false,
	}
	
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to suspend tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "tenant_suspended", "tenant", tenantID, map[string]interface{}{
		"reason": reason,
	})
	
	return nil
}

// UnsuspendTenant mở khóa tenant
func (s *MockTenantService) UnsuspendTenant(ctx context.Context, tenantID string) error {
	updates := map[string]interface{}{
		"status":    models.TenantStatusActive,
		"is_active": true,
	}
	
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to unsuspend tenant")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "tenant_unsuspended", "tenant", tenantID, nil)
	
	return nil
}

// GetTenantStatistics lấy thống kê tenant
func (s *MockTenantService) GetTenantStatistics(ctx context.Context, tenantID string) (*models.TenantStatistics, error) {
	return s.tenantRepo.GetStatistics(ctx, tenantID)
}

// GetTenantActivities lấy activities
func (s *MockTenantService) GetTenantActivities(ctx context.Context, tenantID string, limit int) ([]*models.TenantActivity, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	
	return s.tenantRepo.GetActivities(ctx, tenantID, limit)
}

// ValidateSlug kiểm tra slug hợp lệ và available
func (s *MockTenantService) ValidateSlug(ctx context.Context, slug string, excludeTenantID string) error {
	// Check slug format
	if !isValidSlug(slug) {
		return errors.New("invalid slug format. Use only lowercase letters, numbers, and hyphens")
	}
	
	// Check length
	if len(slug) < 3 || len(slug) > 63 {
		return errors.New("slug must be between 3 and 63 characters")
	}
	
	// Check reserved slugs
	reservedSlugs := []string{
		"api", "www", "app", "admin", "dashboard", "blog", "help", "support",
		"docs", "status", "mail", "cdn", "assets", "static", "public",
	}
	for _, reserved := range reservedSlugs {
		if slug == reserved {
			return errors.New("slug is reserved")
		}
	}
	
	// Check availability
	exists, err := s.tenantRepo.ExistsBySlug(ctx, slug)
	if err != nil {
		return err
	}
	
	if exists {
		// If excluding a tenant, check if it's the same tenant
		if excludeTenantID != "" {
			existing, err := s.tenantRepo.GetBySlug(ctx, slug)
			if err == nil && existing.ID == excludeTenantID {
				return nil // Same tenant, allow
			}
		}
		return errors.New("slug already exists")
	}
	
	return nil
}

// GenerateSlug tạo slug từ name
func (s *MockTenantService) GenerateSlug(ctx context.Context, name string) (string, error) {
	// Convert to lowercase and replace spaces with hyphens
	slug := strings.ToLower(name)
	slug = strings.ReplaceAll(slug, " ", "-")
	
	// Remove special characters except hyphens
	reg := regexp.MustCompile(`[^a-z0-9\-]`)
	slug = reg.ReplaceAllString(slug, "")
	
	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile(`-+`)
	slug = reg.ReplaceAllString(slug, "-")
	
	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")
	
	// Check if slug is available
	originalSlug := slug
	counter := 1
	
	for {
		err := s.ValidateSlug(ctx, slug, "")
		if err == nil {
			break
		}
		
		// If slug exists, try with counter
		if strings.Contains(err.Error(), "already exists") {
			slug = fmt.Sprintf("%s-%d", originalSlug, counter)
			counter++
			
			if counter > 100 {
				return "", errors.New("unable to generate unique slug")
			}
		} else {
			return "", err
		}
	}
	
	return slug, nil
}

// CheckTrialExpiration kiểm tra trial hết hạn
func (s *MockTenantService) CheckTrialExpiration(ctx context.Context, tenantID string) (bool, error) {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return false, err
	}
	
	if tenant.Status != models.TenantStatusTrial || tenant.TrialEndsAt == nil {
		return false, nil
	}
	
	return time.Now().After(*tenant.TrialEndsAt), nil
}

// ExtendTrial gia hạn trial
func (s *MockTenantService) ExtendTrial(ctx context.Context, tenantID string, days int) error {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}
	
	if tenant.Status != models.TenantStatusTrial {
		return errors.New("tenant is not in trial")
	}
	
	// Extend trial
	newTrialEnd := time.Now().AddDate(0, 0, days)
	if tenant.TrialEndsAt != nil && tenant.TrialEndsAt.After(time.Now()) {
		newTrialEnd = tenant.TrialEndsAt.AddDate(0, 0, days)
	}
	
	updates := map[string]interface{}{
		"trial_ends_at": newTrialEnd,
	}
	
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "trial_extended", "tenant", tenantID, map[string]interface{}{
		"days": days,
		"new_trial_end": newTrialEnd,
	})
	
	return nil
}

// UpgradePlan nâng cấp plan
func (s *MockTenantService) UpgradePlan(ctx context.Context, tenantID string, planID string) error {
	// Get tenant
	_, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}
	
	// Get new plan
	newPlan, err := s.planRepo.GetByID(ctx, planID)
	if err != nil {
		return err
	}
	
	// Update tenant status
	updates := map[string]interface{}{
		"status": models.TenantStatusActive,
	}
	
	if err := s.tenantRepo.Update(ctx, tenantID, updates); err != nil {
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, tenantID, "", "plan_upgraded", "tenant", tenantID, map[string]interface{}{
		"new_plan": newPlan.Name,
		"plan_type": newPlan.Type,
	})
	
	return nil
}

// DowngradePlan hạ cấp plan
func (s *MockTenantService) DowngradePlan(ctx context.Context, tenantID string, planID string) error {
	// Similar implementation to upgrade
	return s.UpgradePlan(ctx, tenantID, planID)
}

// GetExpiringTenants lấy tenants sắp hết hạn
func (s *MockTenantService) GetExpiringTenants(ctx context.Context, days int) ([]*models.Tenant, error) {
	return s.tenantRepo.GetExpiring(ctx, days)
}

// LogActivity ghi log activity
func (s *MockTenantService) LogActivity(ctx context.Context, tenantID, userID, action, resource, resourceID string, metadata map[string]interface{}) error {
	// Get client info from context if available
	ipAddress := ""
	userAgent := ""
	
	// Convert metadata to JSON
	metadataJSON := "{}"
	if metadata != nil {
		data, _ := json.Marshal(metadata)
		metadataJSON = string(data)
	}
	
	activity := &models.TenantActivity{
		TenantID:   tenantID,
		UserID:     userID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		Metadata:   metadataJSON,
	}
	
	return s.tenantRepo.LogActivity(ctx, activity)
}

// isValidSlug kiểm tra format của slug
func isValidSlug(slug string) bool {
	// Slug should only contain lowercase letters, numbers, and hyphens
	// Should not start or end with hyphen
	// Should not contain consecutive hyphens
	pattern := `^[a-z0-9]+(?:-[a-z0-9]+)*$`
	matched, _ := regexp.MatchString(pattern, slug)
	return matched
}