package services

import (
	"context"

	"github.com/blog-api-v3/blog-api-v3/modules/tenant/models"
)

// TenantService định nghĩa interface cho tenant business logic
type TenantService interface {
	// CreateTenant tạo tenant mới
	CreateTenant(ctx context.Context, tenant *models.Tenant) error
	
	// GetTenant lấy tenant theo ID
	GetTenant(ctx context.Context, tenantID string) (*models.Tenant, error)
	
	// GetTenantBySlug lấy tenant theo slug
	GetTenantBySlug(ctx context.Context, slug string) (*models.Tenant, error)
	
	// GetTenantsByOwner lấy tenants theo owner
	GetTenantsByOwner(ctx context.Context, ownerID string) ([]*models.Tenant, error)
	
	// UpdateTenant cập nhật tenant
	UpdateTenant(ctx context.Context, tenantID string, update *models.TenantUpdate) error
	
	// DeleteTenant xóa tenant
	DeleteTenant(ctx context.Context, tenantID string) error
	
	// RestoreTenant khôi phục tenant
	RestoreTenant(ctx context.Context, tenantID string) error
	
	// PermanentlyDeleteTenant xóa vĩnh viễn tenant
	PermanentlyDeleteTenant(ctx context.Context, tenantID string) error
	
	// ListTenants liệt kê tenants với filter
	ListTenants(ctx context.Context, filter *models.TenantFilter) ([]*models.Tenant, int64, error)
	
	// SearchTenants tìm kiếm tenants
	SearchTenants(ctx context.Context, query string, limit int) ([]*models.Tenant, error)
	
	// UpdateTenantStatus cập nhật status
	UpdateTenantStatus(ctx context.Context, tenantID string, status models.TenantStatus) error
	
	// SuspendTenant tạm khóa tenant
	SuspendTenant(ctx context.Context, tenantID string, reason string) error
	
	// UnsuspendTenant mở khóa tenant
	UnsuspendTenant(ctx context.Context, tenantID string) error
	
	// GetTenantStatistics lấy thống kê tenant
	GetTenantStatistics(ctx context.Context, tenantID string) (*models.TenantStatistics, error)
	
	// GetTenantActivities lấy activities
	GetTenantActivities(ctx context.Context, tenantID string, limit int) ([]*models.TenantActivity, error)
	
	// ValidateSlug kiểm tra slug hợp lệ và available
	ValidateSlug(ctx context.Context, slug string, excludeTenantID string) error
	
	// GenerateSlug tạo slug từ name
	GenerateSlug(ctx context.Context, name string) (string, error)
	
	// CheckTrialExpiration kiểm tra trial hết hạn
	CheckTrialExpiration(ctx context.Context, tenantID string) (bool, error)
	
	// ExtendTrial gia hạn trial
	ExtendTrial(ctx context.Context, tenantID string, days int) error
	
	// UpgradePlan nâng cấp plan
	UpgradePlan(ctx context.Context, tenantID string, planID string) error
	
	// DowngradePlan hạ cấp plan
	DowngradePlan(ctx context.Context, tenantID string, planID string) error
	
	// GetExpiringTenants lấy tenants sắp hết hạn
	GetExpiringTenants(ctx context.Context, days int) ([]*models.Tenant, error)
	
	// LogActivity ghi log activity
	LogActivity(ctx context.Context, tenantID, userID, action, resource, resourceID string, metadata map[string]interface{}) error
}

// TenantMemberService định nghĩa interface cho member management
type TenantMemberService interface {
	// AddMember thêm member vào tenant
	AddMember(ctx context.Context, tenantID, userID string, role string, invitedBy string) error
	
	// RemoveMember xóa member khỏi tenant
	RemoveMember(ctx context.Context, tenantID, userID string) error
	
	// UpdateMemberRole cập nhật role của member
	UpdateMemberRole(ctx context.Context, tenantID, userID string, role string) error
	
	// GetMember lấy thông tin member
	GetMember(ctx context.Context, tenantID, userID string) (*models.TenantMember, error)
	
	// ListMembers liệt kê members của tenant
	ListMembers(ctx context.Context, tenantID string, page, pageSize int) ([]*models.TenantMember, int64, error)
	
	// GetMemberTenants lấy tenants mà user là member
	GetMemberTenants(ctx context.Context, userID string) ([]*models.TenantMember, error)
	
	// IsMember kiểm tra user có phải member không
	IsMember(ctx context.Context, tenantID, userID string) (bool, error)
	
	// IsOwner kiểm tra user có phải owner không
	IsOwner(ctx context.Context, tenantID, userID string) (bool, error)
	
	// TransferOwnership chuyển quyền sở hữu
	TransferOwnership(ctx context.Context, tenantID, newOwnerID string) error
	
	// GetMemberCount đếm số members
	GetMemberCount(ctx context.Context, tenantID string) (int64, error)
}

// TenantInvitationService định nghĩa interface cho invitation management
type TenantInvitationService interface {
	// CreateInvitation tạo invitation mới
	CreateInvitation(ctx context.Context, tenantID, email string, role string, invitedBy string) (*models.TenantInvitation, error)
	
	// GetInvitation lấy invitation theo ID
	GetInvitation(ctx context.Context, invitationID string) (*models.TenantInvitation, error)
	
	// GetInvitationByToken lấy invitation theo token
	GetInvitationByToken(ctx context.Context, token string) (*models.TenantInvitation, error)
	
	// AcceptInvitation chấp nhận invitation
	AcceptInvitation(ctx context.Context, token string, userID string) error
	
	// DeclineInvitation từ chối invitation
	DeclineInvitation(ctx context.Context, token string) error
	
	// CancelInvitation hủy invitation
	CancelInvitation(ctx context.Context, invitationID string) error
	
	// ListInvitations liệt kê invitations của tenant
	ListInvitations(ctx context.Context, tenantID string, page, pageSize int) ([]*models.TenantInvitation, int64, error)
	
	// ListUserInvitations liệt kê invitations của user
	ListUserInvitations(ctx context.Context, email string) ([]*models.TenantInvitation, error)
	
	// ResendInvitation gửi lại invitation
	ResendInvitation(ctx context.Context, invitationID string) error
	
	// ExpireOldInvitations đánh dấu expired cho invitations cũ
	ExpireOldInvitations(ctx context.Context) error
	
	// GenerateInvitationToken tạo token cho invitation
	GenerateInvitationToken() string
	
	// ValidateInvitationToken kiểm tra token hợp lệ
	ValidateInvitationToken(ctx context.Context, token string) (*models.TenantInvitation, error)
}

// PlanService định nghĩa interface cho plan management
type PlanService interface {
	// CreatePlan tạo plan mới
	CreatePlan(ctx context.Context, plan *models.Plan) error
	
	// GetPlan lấy plan theo ID
	GetPlan(ctx context.Context, planID string) (*models.Plan, error)
	
	// GetPlanByType lấy plan theo type
	GetPlanByType(ctx context.Context, planType models.PlanType) (*models.Plan, error)
	
	// ListPlans liệt kê plans
	ListPlans(ctx context.Context, activeOnly bool) ([]*models.Plan, error)
	
	// GetPublicPlans lấy plans công khai
	GetPublicPlans(ctx context.Context) ([]*models.Plan, error)
	
	// UpdatePlan cập nhật plan
	UpdatePlan(ctx context.Context, planID string, updates map[string]interface{}) error
	
	// DeletePlan xóa plan
	DeletePlan(ctx context.Context, planID string) error
	
	// ValidatePlanUpgrade kiểm tra có thể upgrade không
	ValidatePlanUpgrade(ctx context.Context, currentPlanID, newPlanID string) error
	
	// CalculateUpgradeCost tính toán chi phí upgrade
	CalculateUpgradeCost(ctx context.Context, currentPlanID, newPlanID string) (float64, error)
	
	// GetDefaultPlan lấy plan mặc định
	GetDefaultPlan(ctx context.Context) (*models.Plan, error)
}

// SubscriptionService định nghĩa interface cho subscription management
type SubscriptionService interface {
	// CreateSubscription tạo subscription mới
	CreateSubscription(ctx context.Context, subscription *models.Subscription) error
	
	// GetSubscription lấy subscription theo ID
	GetSubscription(ctx context.Context, subscriptionID string) (*models.Subscription, error)
	
	// GetSubscriptionByTenant lấy subscription theo tenant
	GetSubscriptionByTenant(ctx context.Context, tenantID string) (*models.Subscription, error)
	
	// UpdateSubscription cập nhật subscription
	UpdateSubscription(ctx context.Context, subscriptionID string, updates map[string]interface{}) error
	
	// CancelSubscription hủy subscription
	CancelSubscription(ctx context.Context, subscriptionID string) error
	
	// RenewSubscription gia hạn subscription
	RenewSubscription(ctx context.Context, subscriptionID string) error
	
	// UpdateUsage cập nhật usage
	UpdateUsage(ctx context.Context, subscriptionID string, usage models.SubscriptionUsage) error
	
	// CheckLimits kiểm tra limits
	CheckLimits(ctx context.Context, tenantID string, resource string, current int64) error
	
	// GetUsagePercentage lấy phần trăm sử dụng
	GetUsagePercentage(ctx context.Context, subscriptionID string) (map[string]float64, error)
	
	// GetExpiringSubscriptions lấy subscriptions sắp hết hạn
	GetExpiringSubscriptions(ctx context.Context, days int) ([]*models.Subscription, error)
	
	// ProcessPayment xử lý thanh toán
	ProcessPayment(ctx context.Context, subscriptionID string, amount float64) error
}