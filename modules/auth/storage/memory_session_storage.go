package storage

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// MemorySessionStorage implements SessionStorage interface using in-memory storage
type MemorySessionStorage struct {
	sessions          map[string]*models.Session
	tokenIndex        map[string]string    // token -> sessionID
	refreshTokenIndex map[string]string    // refreshToken -> sessionID
	userIndex         map[string][]string  // userID -> []sessionID
	mu                sync.RWMutex
}

// NewMemorySessionStorage creates a new memory session storage
func NewMemorySessionStorage() *MemorySessionStorage {
	return &MemorySessionStorage{
		sessions:          make(map[string]*models.Session),
		tokenIndex:        make(map[string]string),
		refreshTokenIndex: make(map[string]string),
		userIndex:         make(map[string][]string),
	}
}

// Create creates a new session
func (s *MemorySessionStorage) Create(ctx context.Context, session *models.Session) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Check if session already exists
	if _, exists := s.sessions[session.ID]; exists {
		return errors.New("session already exists")
	}
	
	// Check token uniqueness
	if _, exists := s.tokenIndex[session.Token]; exists {
		return errors.New("token already exists")
	}
	
	if session.RefreshToken != "" {
		if _, exists := s.refreshTokenIndex[session.RefreshToken]; exists {
			return errors.New("refresh token already exists")
		}
	}
	
	// Store session
	s.sessions[session.ID] = session
	s.tokenIndex[session.Token] = session.ID
	
	if session.RefreshToken != "" {
		s.refreshTokenIndex[session.RefreshToken] = session.ID
	}
	
	// Update user index
	s.userIndex[session.UserID] = append(s.userIndex[session.UserID], session.ID)
	
	return nil
}

// Get retrieves a session by ID
func (s *MemorySessionStorage) Get(ctx context.Context, sessionID string) (*models.Session, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return nil, errors.New("session not found")
	}
	
	// Return a copy to prevent external modifications
	sessionCopy := *session
	return &sessionCopy, nil
}

// GetByToken retrieves a session by token
func (s *MemorySessionStorage) GetByToken(ctx context.Context, token string) (*models.Session, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	sessionID, exists := s.tokenIndex[token]
	if !exists {
		return nil, errors.New("session not found")
	}
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return nil, errors.New("session not found")
	}
	
	sessionCopy := *session
	return &sessionCopy, nil
}

// GetByRefreshToken retrieves a session by refresh token
func (s *MemorySessionStorage) GetByRefreshToken(ctx context.Context, refreshToken string) (*models.Session, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	sessionID, exists := s.refreshTokenIndex[refreshToken]
	if !exists {
		return nil, errors.New("session not found")
	}
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return nil, errors.New("session not found")
	}
	
	sessionCopy := *session
	return &sessionCopy, nil
}

// Update updates a session
func (s *MemorySessionStorage) Update(ctx context.Context, sessionID string, updates map[string]interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return errors.New("session not found")
	}
	
	// Apply updates
	for key, value := range updates {
		switch key {
		case "token":
			newToken := value.(string)
			// Remove old token index
			delete(s.tokenIndex, session.Token)
			// Update token
			session.Token = newToken
			// Add new token index
			s.tokenIndex[newToken] = sessionID
			
		case "refresh_token":
			newRefreshToken := value.(string)
			// Remove old refresh token index
			if session.RefreshToken != "" {
				delete(s.refreshTokenIndex, session.RefreshToken)
			}
			// Update refresh token
			session.RefreshToken = newRefreshToken
			// Add new refresh token index
			if newRefreshToken != "" {
				s.refreshTokenIndex[newRefreshToken] = sessionID
			}
			
		case "expires_at":
			session.ExpiresAt = value.(time.Time)
			
		case "last_active_at":
			session.LastActiveAt = value.(time.Time)
			
		case "ip_address":
			session.IPAddress = value.(string)
			
		case "user_agent":
			session.UserAgent = value.(string)
		}
	}
	
	return nil
}

// Delete deletes a session
func (s *MemorySessionStorage) Delete(ctx context.Context, sessionID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return errors.New("session not found")
	}
	
	// Remove from indexes
	delete(s.tokenIndex, session.Token)
	if session.RefreshToken != "" {
		delete(s.refreshTokenIndex, session.RefreshToken)
	}
	
	// Remove from user index
	userSessions := s.userIndex[session.UserID]
	for i, sid := range userSessions {
		if sid == sessionID {
			s.userIndex[session.UserID] = append(userSessions[:i], userSessions[i+1:]...)
			break
		}
	}
	
	// Remove session
	delete(s.sessions, sessionID)
	
	return nil
}

// GetUserSessions retrieves all sessions for a user
func (s *MemorySessionStorage) GetUserSessions(ctx context.Context, userID string) ([]*models.Session, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	sessionIDs, exists := s.userIndex[userID]
	if !exists {
		return []*models.Session{}, nil
	}
	
	sessions := make([]*models.Session, 0, len(sessionIDs))
	for _, sessionID := range sessionIDs {
		if session, exists := s.sessions[sessionID]; exists {
			sessionCopy := *session
			sessions = append(sessions, &sessionCopy)
		}
	}
	
	return sessions, nil
}

// DeleteUserSessions deletes all sessions for a user
func (s *MemorySessionStorage) DeleteUserSessions(ctx context.Context, userID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	sessionIDs, exists := s.userIndex[userID]
	if !exists {
		return nil
	}
	
	// Delete each session
	for _, sessionID := range sessionIDs {
		if session, exists := s.sessions[sessionID]; exists {
			delete(s.tokenIndex, session.Token)
			if session.RefreshToken != "" {
				delete(s.refreshTokenIndex, session.RefreshToken)
			}
			delete(s.sessions, sessionID)
		}
	}
	
	// Remove user index
	delete(s.userIndex, userID)
	
	return nil
}

// DeleteExpiredSessions deletes all expired sessions
func (s *MemorySessionStorage) DeleteExpiredSessions(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	now := time.Now()
	var toDelete []string
	
	// Find expired sessions
	for sessionID, session := range s.sessions {
		if now.After(session.ExpiresAt) {
			toDelete = append(toDelete, sessionID)
		}
	}
	
	// Delete expired sessions
	for _, sessionID := range toDelete {
		session := s.sessions[sessionID]
		
		// Remove from indexes
		delete(s.tokenIndex, session.Token)
		if session.RefreshToken != "" {
			delete(s.refreshTokenIndex, session.RefreshToken)
		}
		
		// Remove from user index
		userSessions := s.userIndex[session.UserID]
		for i, sid := range userSessions {
			if sid == sessionID {
				s.userIndex[session.UserID] = append(userSessions[:i], userSessions[i+1:]...)
				break
			}
		}
		
		// Remove session
		delete(s.sessions, sessionID)
	}
	
	return nil
}

// DeleteInactiveSessions deletes sessions that haven't been active for a specified duration
func (s *MemorySessionStorage) DeleteInactiveSessions(ctx context.Context, inactiveDuration time.Duration) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	cutoff := time.Now().Add(-inactiveDuration)
	var toDelete []string
	
	// Find inactive sessions
	for sessionID, session := range s.sessions {
		if session.LastActiveAt.Before(cutoff) {
			toDelete = append(toDelete, sessionID)
		}
	}
	
	// Delete inactive sessions
	for _, sessionID := range toDelete {
		session := s.sessions[sessionID]
		
		// Remove from indexes
		delete(s.tokenIndex, session.Token)
		if session.RefreshToken != "" {
			delete(s.refreshTokenIndex, session.RefreshToken)
		}
		
		// Remove from user index
		userSessions := s.userIndex[session.UserID]
		for i, sid := range userSessions {
			if sid == sessionID {
				s.userIndex[session.UserID] = append(userSessions[:i], userSessions[i+1:]...)
				break
			}
		}
		
		// Remove session
		delete(s.sessions, sessionID)
	}
	
	return nil
}