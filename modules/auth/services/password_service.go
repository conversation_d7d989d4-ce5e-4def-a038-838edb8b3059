package services

import (
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

// BcryptPasswordService implements PasswordService interface using bcrypt
type BcryptPasswordService struct {
	cost int
}

// NewBcryptPasswordService creates a new bcrypt password service
func NewBcryptPasswordService(cost int) PasswordService {
	if cost < bcrypt.MinCost || cost > bcrypt.MaxCost {
		cost = bcrypt.DefaultCost
	}
	return &BcryptPasswordService{
		cost: cost,
	}
}

// HashPassword hashes a password using bcrypt
func (s *BcryptPasswordService) HashPassword(password string) (string, error) {
	if err := s.ValidatePasswordStrength(password); err != nil {
		return "", err
	}
	
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), s.cost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	
	return string(bytes), nil
}

// VerifyPassword verifies a password against a hash
func (s *BcryptPasswordService) VerifyPassword(password, hash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return errors.New("invalid password")
		}
		return fmt.Errorf("failed to verify password: %w", err)
	}
	return nil
}

// ValidatePasswordStrength validates password strength
func (s *BcryptPasswordService) ValidatePasswordStrength(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}
	
	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)
	
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}
	
	// Count how many character types are present
	count := 0
	if hasUpper {
		count++
	}
	if hasLower {
		count++
	}
	if hasNumber {
		count++
	}
	if hasSpecial {
		count++
	}
	
	// Require at least 3 out of 4 character types
	if count < 3 {
		return errors.New("password must contain at least 3 of the following: uppercase letters, lowercase letters, numbers, special characters")
	}
	
	// Check for common patterns
	if isCommonPassword(password) {
		return errors.New("password is too common")
	}
	
	return nil
}

// GenerateRandomPassword generates a random password
func (s *BcryptPasswordService) GenerateRandomPassword(length int) (string, error) {
	if length < 8 {
		length = 12
	}
	
	const (
		upperChars   = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		lowerChars   = "abcdefghijklmnopqrstuvwxyz"
		numberChars  = "0123456789"
		specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
	)
	
	allChars := upperChars + lowerChars + numberChars + specialChars
	
	// Ensure at least one character from each type
	password := make([]byte, length)
	
	// Add one character from each type
	password[0] = upperChars[randInt(len(upperChars))]
	password[1] = lowerChars[randInt(len(lowerChars))]
	password[2] = numberChars[randInt(len(numberChars))]
	password[3] = specialChars[randInt(len(specialChars))]
	
	// Fill the rest randomly
	for i := 4; i < length; i++ {
		password[i] = allChars[randInt(len(allChars))]
	}
	
	// Shuffle the password
	for i := len(password) - 1; i > 0; i-- {
		j := randInt(i + 1)
		password[i], password[j] = password[j], password[i]
	}
	
	return string(password), nil
}

// randInt generates a random integer between 0 and max-1
func randInt(max int) int {
	n, err := rand.Int(rand.Reader, big.NewInt(int64(max)))
	if err != nil {
		panic(err)
	}
	return int(n.Int64())
}

// isCommonPassword checks if a password is in the list of common passwords
func isCommonPassword(password string) bool {
	// In production, this would check against a larger list
	commonPasswords := []string{
		"password", "12345678", "123456789", "qwerty", "abc123",
		"password123", "admin", "letmein", "welcome", "monkey",
		"dragon", "baseball", "football", "iloveyou", "trustno1",
	}
	
	lowerPassword := string(password)
	for _, common := range commonPasswords {
		if lowerPassword == common {
			return true
		}
	}
	
	return false
}