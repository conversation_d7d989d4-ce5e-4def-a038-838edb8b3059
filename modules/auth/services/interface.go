package services

import (
	"context"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// AuthService defines the authentication service interface
type AuthService interface {
	// User authentication
	Login(ctx context.Context, credentials *models.UserCredentials, ipAddress, userAgent string) (*models.TokenPair, error)
	Logout(ctx context.Context, userID, sessionID string, allDevices bool) error
	Register(ctx context.Context, registration *models.UserRegistration) (*models.User, error)
	RefreshToken(ctx context.Context, refreshToken string) (*models.TokenPair, error)
	
	// Token validation
	ValidateToken(ctx context.Context, token string) (*models.TokenClaims, error)
	ValidateTokenMiddleware(token string) (*models.TokenClaims, error) // For middleware compatibility
	RevokeToken(ctx context.Context, token string) error
	
	// User lookup for middleware
	GetUserByID(userID string) (*models.User, error)
	
	// User verification
	VerifyEmail(ctx context.Context, userID, token string) error
	ResendVerificationEmail(ctx context.Context, email string) error
	
	// Password management
	ChangePassword(ctx context.Context, userID, oldPassword, newPassword string) error
	ResetPasswordRequest(ctx context.Context, email string) error
	ResetPassword(ctx context.Context, token, newPassword string) error
	
	// Session management
	GetUserSessions(ctx context.Context, userID string) ([]*models.Session, error)
	RevokeSession(ctx context.Context, sessionID string) error
	CleanupExpiredSessions(ctx context.Context) error
}

// UserService defines the user management service interface
type UserService interface {
	// User CRUD
	CreateUser(ctx context.Context, user *models.User) error
	GetUserByID(ctx context.Context, userID string) (*models.User, error)
	GetUserByEmail(ctx context.Context, email string) (*models.User, error)
	GetUserByUsername(ctx context.Context, username string) (*models.User, error)
	UpdateUser(ctx context.Context, userID string, updates map[string]interface{}) error
	DeleteUser(ctx context.Context, userID string) error
	
	// User listing and search
	ListUsers(ctx context.Context, offset, limit int) ([]*models.User, int64, error)
	SearchUsers(ctx context.Context, query string, offset, limit int) ([]*models.User, int64, error)
	
	// Role management
	AssignRole(ctx context.Context, userID, roleID string) error
	RemoveRole(ctx context.Context, userID, roleID string) error
	GetUserRoles(ctx context.Context, userID string) ([]*models.Role, error)
	GetUserPermissions(ctx context.Context, userID string) ([]string, error)
}

// RoleService defines the role management service interface
type RoleService interface {
	// Role CRUD
	CreateRole(ctx context.Context, role *models.Role) error
	GetRoleByID(ctx context.Context, roleID string) (*models.Role, error)
	GetRoleByName(ctx context.Context, name string) (*models.Role, error)
	UpdateRole(ctx context.Context, roleID string, updates map[string]interface{}) error
	DeleteRole(ctx context.Context, roleID string) error
	
	// Role listing
	ListRoles(ctx context.Context) ([]*models.Role, error)
	
	// Permission management
	AssignPermission(ctx context.Context, roleID, permissionID string) error
	RemovePermission(ctx context.Context, roleID, permissionID string) error
	GetRolePermissions(ctx context.Context, roleID string) ([]*models.Permission, error)
}

// PermissionService defines the permission management service interface
type PermissionService interface {
	// Permission CRUD
	CreatePermission(ctx context.Context, permission *models.Permission) error
	GetPermissionByID(ctx context.Context, permissionID string) (*models.Permission, error)
	GetPermissionByName(ctx context.Context, name string) (*models.Permission, error)
	UpdatePermission(ctx context.Context, permissionID string, updates map[string]interface{}) error
	DeletePermission(ctx context.Context, permissionID string) error
	
	// Permission listing
	ListPermissions(ctx context.Context) ([]*models.Permission, error)
	ListPermissionsByResource(ctx context.Context, resource string) ([]*models.Permission, error)
}

// TokenService defines the token management service interface
type TokenService interface {
	// Token generation
	GenerateAccessToken(claims *models.TokenClaims) (string, error)
	GenerateRefreshToken() (string, error)
	GenerateTokenPair(user *models.User, sessionID string) (*models.TokenPair, error)
	
	// Token validation
	ValidateAccessToken(token string) (*models.TokenClaims, error)
	ValidateRefreshToken(token string) (string, error) // Returns session ID
	
	// Token revocation
	RevokeToken(token string) error
	IsTokenRevoked(token string) (bool, error)
	
	// Token cleanup
	CleanupRevokedTokens() error
}

// PasswordService defines the password service interface
type PasswordService interface {
	// Password hashing
	HashPassword(password string) (string, error)
	VerifyPassword(password, hash string) error
	
	// Password validation
	ValidatePasswordStrength(password string) error
	
	// Password generation
	GenerateRandomPassword(length int) (string, error)
}

// SessionStorage defines the session storage interface
type SessionStorage interface {
	// Session CRUD
	Create(ctx context.Context, session *models.Session) error
	Get(ctx context.Context, sessionID string) (*models.Session, error)
	GetByToken(ctx context.Context, token string) (*models.Session, error)
	GetByRefreshToken(ctx context.Context, refreshToken string) (*models.Session, error)
	Update(ctx context.Context, sessionID string, updates map[string]interface{}) error
	Delete(ctx context.Context, sessionID string) error
	
	// User sessions
	GetUserSessions(ctx context.Context, userID string) ([]*models.Session, error)
	DeleteUserSessions(ctx context.Context, userID string) error
	
	// Cleanup
	DeleteExpiredSessions(ctx context.Context) error
	DeleteInactiveSessions(ctx context.Context, inactiveDuration time.Duration) error
}

// EmailService defines the email service interface for auth-related emails
type EmailService interface {
	SendVerificationEmail(ctx context.Context, user *models.User, token string) error
	SendPasswordResetEmail(ctx context.Context, user *models.User, token string) error
	SendPasswordChangedEmail(ctx context.Context, user *models.User) error
	SendLoginNotification(ctx context.Context, user *models.User, ipAddress, userAgent string) error
}