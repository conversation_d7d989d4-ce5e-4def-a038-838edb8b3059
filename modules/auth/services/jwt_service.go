package services

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// JWTService implements TokenService using JWT
type JWTService struct {
	secretKey         []byte
	accessTokenTTL    time.Duration
	refreshTokenTTL   time.Duration
	issuer            string
	revokedTokens     map[string]time.Time // In production, use Redis or database
}

// NewJWTService creates a new JWT service
func NewJWTService(secretKey string, accessTTL, refreshTTL time.Duration, issuer string) *JWTService {
	return &JWTService{
		secretKey:       []byte(secretKey),
		accessTokenTTL:  accessTTL,
		refreshTokenTTL: refreshTTL,
		issuer:          issuer,
		revokedTokens:   make(map[string]time.Time),
	}
}

// GenerateAccessToken generates a new access token
func (s *JWTService) GenerateAccessToken(claims *models.TokenClaims) (string, error) {
	now := time.Now()
	claims.IssuedAt = now.Unix()
	claims.NotBefore = now.Unix()
	claims.ExpiresAt = now.Add(s.accessTokenTTL).Unix()
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":     claims.UserID,
		"username":    claims.Username,
		"email":       claims.Email,
		"roles":       claims.Roles,
		"permissions": claims.Permissions,
		"session_id":  claims.SessionID,
		"exp":         claims.ExpiresAt,
		"iat":         claims.IssuedAt,
		"nbf":         claims.NotBefore,
		"iss":         s.issuer,
	})
	
	tokenString, err := token.SignedString(s.secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}
	
	return tokenString, nil
}

// GenerateRefreshToken generates a new refresh token
func (s *JWTService) GenerateRefreshToken() (string, error) {
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// GenerateTokenPair generates both access and refresh tokens
func (s *JWTService) GenerateTokenPair(user *models.User, sessionID string) (*models.TokenPair, error) {
	// Extract roles and permissions
	var roles []string
	var permissions []string
	
	for _, role := range user.Roles {
		roles = append(roles, role.Name)
		for _, perm := range role.Permissions {
			permissions = append(permissions, perm.Name)
		}
	}
	
	// Create claims
	claims := &models.TokenClaims{
		UserID:      user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Roles:       roles,
		Permissions: permissions,
		SessionID:   sessionID,
	}
	
	// Generate tokens
	accessToken, err := s.GenerateAccessToken(claims)
	if err != nil {
		return nil, err
	}
	
	refreshToken, err := s.GenerateRefreshToken()
	if err != nil {
		return nil, err
	}
	
	return &models.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int(s.accessTokenTTL.Seconds()),
		ExpiresAt:    time.Now().Add(s.accessTokenTTL),
	}, nil
}

// ValidateAccessToken validates an access token and returns claims
func (s *JWTService) ValidateAccessToken(tokenString string) (*models.TokenClaims, error) {
	// Check if token is revoked
	if revoked, err := s.IsTokenRevoked(tokenString); err != nil {
		return nil, err
	} else if revoked {
		return nil, errors.New("token has been revoked")
	}
	
	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}
	
	if !token.Valid {
		return nil, errors.New("invalid token")
	}
	
	// Extract claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}
	
	// Verify issuer
	if issuer, ok := claims["iss"].(string); !ok || issuer != s.issuer {
		return nil, errors.New("invalid token issuer")
	}
	
	// Build token claims
	tokenClaims := &models.TokenClaims{
		UserID:    getStringClaim(claims, "user_id"),
		Username:  getStringClaim(claims, "username"),
		Email:     getStringClaim(claims, "email"),
		SessionID: getStringClaim(claims, "session_id"),
		ExpiresAt: int64(getFloatClaim(claims, "exp")),
		IssuedAt:  int64(getFloatClaim(claims, "iat")),
		NotBefore: int64(getFloatClaim(claims, "nbf")),
	}
	
	// Extract roles
	if roles, ok := claims["roles"].([]interface{}); ok {
		for _, role := range roles {
			if r, ok := role.(string); ok {
				tokenClaims.Roles = append(tokenClaims.Roles, r)
			}
		}
	}
	
	// Extract permissions
	if permissions, ok := claims["permissions"].([]interface{}); ok {
		for _, perm := range permissions {
			if p, ok := perm.(string); ok {
				tokenClaims.Permissions = append(tokenClaims.Permissions, p)
			}
		}
	}
	
	return tokenClaims, nil
}

// ValidateRefreshToken validates a refresh token
func (s *JWTService) ValidateRefreshToken(token string) (string, error) {
	// In a real implementation, this would check against a database
	// For now, just validate the format
	if len(token) < 32 {
		return "", errors.New("invalid refresh token")
	}
	
	// Decode to verify it's valid base64
	if _, err := base64.URLEncoding.DecodeString(token); err != nil {
		return "", errors.New("invalid refresh token format")
	}
	
	// In production, look up the session ID associated with this refresh token
	return "", errors.New("refresh token validation not implemented")
}

// RevokeToken revokes a token
func (s *JWTService) RevokeToken(token string) error {
	s.revokedTokens[token] = time.Now().Add(s.accessTokenTTL)
	return nil
}

// IsTokenRevoked checks if a token has been revoked
func (s *JWTService) IsTokenRevoked(token string) (bool, error) {
	expiresAt, exists := s.revokedTokens[token]
	if !exists {
		return false, nil
	}
	
	// Check if revocation has expired
	if time.Now().After(expiresAt) {
		delete(s.revokedTokens, token)
		return false, nil
	}
	
	return true, nil
}

// CleanupRevokedTokens removes expired revoked tokens
func (s *JWTService) CleanupRevokedTokens() error {
	now := time.Now()
	for token, expiresAt := range s.revokedTokens {
		if now.After(expiresAt) {
			delete(s.revokedTokens, token)
		}
	}
	return nil
}

// Helper functions
func getStringClaim(claims jwt.MapClaims, key string) string {
	if val, ok := claims[key].(string); ok {
		return val
	}
	return ""
}

func getFloatClaim(claims jwt.MapClaims, key string) float64 {
	if val, ok := claims[key].(float64); ok {
		return val
	}
	return 0
}