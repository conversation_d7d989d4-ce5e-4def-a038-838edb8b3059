package services

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// MockUserService implements UserService interface with mock data
type MockUserService struct {
	users           map[string]*models.User
	usersByEmail    map[string]*models.User
	usersByUsername map[string]*models.User
	mu              sync.RWMutex
}

// NewMockUserService creates a new mock user service
func NewMockUserService() *MockUserService {
	return &MockUserService{
		users:           make(map[string]*models.User),
		usersByEmail:    make(map[string]*models.User),
		usersByUsername: make(map[string]*models.User),
	}
}

// <PERSON><PERSON><PERSON><PERSON> creates a new user
func (s *MockUserService) CreateUser(ctx context.Context, user *models.User) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if _, exists := s.users[user.ID]; exists {
		return errors.New("user already exists")
	}
	
	s.users[user.ID] = user
	s.usersByEmail[user.Email] = user
	s.usersByUsername[user.Username] = user
	
	return nil
}

// GetUserByID retrieves a user by ID
func (s *MockUserService) GetUserByID(ctx context.Context, userID string) (*models.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user, exists := s.users[userID]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	return user, nil
}

// GetUserByEmail retrieves a user by email
func (s *MockUserService) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user, exists := s.usersByEmail[email]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	return user, nil
}

// GetUserByUsername retrieves a user by username
func (s *MockUserService) GetUserByUsername(ctx context.Context, username string) (*models.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user, exists := s.usersByUsername[username]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	return user, nil
}

// UpdateUser updates a user
func (s *MockUserService) UpdateUser(ctx context.Context, userID string, updates map[string]interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Apply updates (simplified)
	for key, value := range updates {
		switch key {
		case "email":
			delete(s.usersByEmail, user.Email)
			user.Email = value.(string)
			s.usersByEmail[user.Email] = user
		case "username":
			delete(s.usersByUsername, user.Username)
			user.Username = value.(string)
			s.usersByUsername[user.Username] = user
		case "first_name":
			user.FirstName = value.(string)
		case "last_name":
			user.LastName = value.(string)
		case "is_active":
			user.IsActive = value.(bool)
		case "is_verified":
			user.IsVerified = value.(bool)
		}
	}
	
	user.UpdatedAt = time.Now()
	return nil
}

// DeleteUser deletes a user
func (s *MockUserService) DeleteUser(ctx context.Context, userID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	delete(s.users, userID)
	delete(s.usersByEmail, user.Email)
	delete(s.usersByUsername, user.Username)
	
	return nil
}

// ListUsers lists users with pagination
func (s *MockUserService) ListUsers(ctx context.Context, offset, limit int) ([]*models.User, int64, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	users := make([]*models.User, 0, len(s.users))
	for _, user := range s.users {
		users = append(users, user)
	}
	
	total := int64(len(users))
	
	// Apply pagination
	if offset >= len(users) {
		return []*models.User{}, total, nil
	}
	
	end := offset + limit
	if end > len(users) {
		end = len(users)
	}
	
	return users[offset:end], total, nil
}

// SearchUsers searches users by query
func (s *MockUserService) SearchUsers(ctx context.Context, query string, offset, limit int) ([]*models.User, int64, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	results := make([]*models.User, 0)
	
	for _, user := range s.users {
		if containsIgnoreCase(user.Email, query) ||
			containsIgnoreCase(user.Username, query) ||
			containsIgnoreCase(user.FirstName, query) ||
			containsIgnoreCase(user.LastName, query) {
			results = append(results, user)
		}
	}
	
	total := int64(len(results))
	
	// Apply pagination
	if offset >= len(results) {
		return []*models.User{}, total, nil
	}
	
	end := offset + limit
	if end > len(results) {
		end = len(results)
	}
	
	return results[offset:end], total, nil
}

// AssignRole assigns a role to a user
func (s *MockUserService) AssignRole(ctx context.Context, userID, roleID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Check if role already assigned
	for _, role := range user.Roles {
		if role.ID == roleID {
			return nil // Already assigned
		}
	}
	
	// Add role (simplified - in real implementation, fetch role from RoleService)
	user.Roles = append(user.Roles, models.Role{ID: roleID})
	user.UpdatedAt = time.Now()
	
	return nil
}

// RemoveRole removes a role from a user
func (s *MockUserService) RemoveRole(ctx context.Context, userID, roleID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Remove role
	roles := make([]models.Role, 0)
	for _, role := range user.Roles {
		if role.ID != roleID {
			roles = append(roles, role)
		}
	}
	
	user.Roles = roles
	user.UpdatedAt = time.Now()
	
	return nil
}

// GetUserRoles returns user's roles
func (s *MockUserService) GetUserRoles(ctx context.Context, userID string) ([]*models.Role, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user, exists := s.users[userID]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	roles := make([]*models.Role, len(user.Roles))
	for i := range user.Roles {
		roles[i] = &user.Roles[i]
	}
	
	return roles, nil
}

// GetUserPermissions returns user's permissions
func (s *MockUserService) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user, exists := s.users[userID]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	return user.GetPermissions(), nil
}

// SetUsers sets the users map (for seeding data)
func (s *MockUserService) SetUsers(users map[string]*models.User) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.users = users
	s.usersByEmail = make(map[string]*models.User)
	s.usersByUsername = make(map[string]*models.User)
	
	for _, user := range users {
		s.usersByEmail[user.Email] = user
		s.usersByUsername[user.Username] = user
	}
}

// containsIgnoreCase checks if str contains substr (case-insensitive)
func containsIgnoreCase(str, substr string) bool {
	return len(substr) > 0 && len(str) >= len(substr) &&
		strings.Contains(strings.ToLower(str), strings.ToLower(substr))
}