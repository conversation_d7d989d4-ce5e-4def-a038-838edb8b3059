package services

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/auth/storage"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockAuthService implements AuthService interface with mock data
type MockAuthService struct {
	users           map[string]*models.User
	usersByEmail    map[string]*models.User
	usersByUsername map[string]*models.User
	tokenService    TokenService
	passwordService PasswordService
	sessionStorage  SessionStorage
	emailTokens     map[string]string // token -> userID
	resetTokens     map[string]string // token -> userID
	mu              sync.RWMutex
}

// NewMockAuthService creates a new mock auth service
func NewMockAuthService(tokenService TokenService, passwordService PasswordService) *MockAuthService {
	service := &MockAuthService{
		users:           make(map[string]*models.User),
		usersByEmail:    make(map[string]*models.User),
		usersByUsername: make(map[string]*models.User),
		tokenService:    tokenService,
		passwordService: passwordService,
		sessionStorage:  storage.NewMemorySessionStorage(),
		emailTokens:     make(map[string]string),
		resetTokens:     make(map[string]string),
	}
	
	// Add some mock users
	service.seedMockUsers()
	
	return service
}

// seedMockUsers adds initial mock users
func (s *MockAuthService) seedMockUsers() {
	// Create super admin
	adminPassword, _ := s.passwordService.HashPassword("Admin@123")
	admin := &models.User{
		ID:         utils.GenerateULID(),
		Email:      "<EMAIL>",
		Username:   "admin",
		Password:   adminPassword,
		FirstName:  "Admin",
		LastName:   "User",
		IsActive:   true,
		IsVerified: true,
		VerifiedAt: &[]time.Time{time.Now()}[0],
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Roles: []models.Role{
			{ID: "1", Name: models.RoleSuperAdmin, DisplayName: "Super Admin"},
		},
	}
	
	// Create regular user
	userPassword, _ := s.passwordService.HashPassword("User@123")
	user := &models.User{
		ID:         utils.GenerateULID(),
		Email:      "<EMAIL>",
		Username:   "testuser",
		Password:   userPassword,
		FirstName:  "Test",
		LastName:   "User",
		IsActive:   true,
		IsVerified: true,
		VerifiedAt: &[]time.Time{time.Now()}[0],
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Roles: []models.Role{
			{ID: "3", Name: models.RoleUser, DisplayName: "User"},
		},
	}
	
	// Store users
	s.users[admin.ID] = admin
	s.usersByEmail[admin.Email] = admin
	s.usersByUsername[admin.Username] = admin
	
	s.users[user.ID] = user
	s.usersByEmail[user.Email] = user
	s.usersByUsername[user.Username] = user
}

// Login authenticates a user and returns tokens
func (s *MockAuthService) Login(ctx context.Context, credentials *models.UserCredentials, ipAddress, userAgent string) (*models.TokenPair, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	// Find user by email or username
	var user *models.User
	if credentials.Email != "" {
		user = s.usersByEmail[credentials.Email]
	} else if credentials.Username != "" {
		user = s.usersByUsername[credentials.Username]
	}
	
	if user == nil {
		return nil, errors.New("invalid credentials")
	}
	
	// Check password
	if err := s.passwordService.VerifyPassword(credentials.Password, user.Password); err != nil {
		return nil, errors.New("invalid credentials")
	}
	
	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("account is disabled")
	}
	
	// Create session
	session := &models.Session{
		ID:           utils.GenerateULID(),
		UserID:       user.ID,
		Token:        utils.GenerateUUID(),
		RefreshToken: utils.GenerateUUID(),
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		ExpiresAt:    time.Now().Add(24 * time.Hour),
		LastActiveAt: time.Now(),
		CreatedAt:    time.Now(),
	}
	
	if err := s.sessionStorage.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}
	
	// Generate tokens
	tokenPair, err := s.tokenService.GenerateTokenPair(user, session.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}
	
	// Update session with actual tokens
	s.sessionStorage.Update(ctx, session.ID, map[string]interface{}{
		"token":         tokenPair.AccessToken,
		"refresh_token": tokenPair.RefreshToken,
	})
	
	// Update last login
	user.LastLoginAt = &[]time.Time{time.Now()}[0]
	
	return tokenPair, nil
}

// Logout logs out a user
func (s *MockAuthService) Logout(ctx context.Context, userID, sessionID string, allDevices bool) error {
	if allDevices {
		return s.sessionStorage.DeleteUserSessions(ctx, userID)
	}
	return s.sessionStorage.Delete(ctx, sessionID)
}

// Register creates a new user account
func (s *MockAuthService) Register(ctx context.Context, registration *models.UserRegistration) (*models.User, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Check if user exists
	if _, exists := s.usersByEmail[registration.Email]; exists {
		return nil, errors.New("email already exists")
	}
	if _, exists := s.usersByUsername[registration.Username]; exists {
		return nil, errors.New("username already exists")
	}
	
	// Hash password
	hashedPassword, err := s.passwordService.HashPassword(registration.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	
	// Create user
	user := &models.User{
		ID:         utils.GenerateULID(),
		Email:      registration.Email,
		Username:   registration.Username,
		Password:   hashedPassword,
		FirstName:  registration.FirstName,
		LastName:   registration.LastName,
		IsActive:   true,
		IsVerified: false,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Roles: []models.Role{
			{ID: "3", Name: models.RoleUser, DisplayName: "User"},
		},
	}
	
	// Store user
	s.users[user.ID] = user
	s.usersByEmail[user.Email] = user
	s.usersByUsername[user.Username] = user
	
	// Generate verification token
	verificationToken := utils.GenerateUUID()
	s.emailTokens[verificationToken] = user.ID
	
	// In a real implementation, send verification email here
	
	return user, nil
}

// RefreshToken refreshes an access token
func (s *MockAuthService) RefreshToken(ctx context.Context, refreshToken string) (*models.TokenPair, error) {
	// Get session by refresh token
	session, err := s.sessionStorage.GetByRefreshToken(ctx, refreshToken)
	if err != nil {
		return nil, errors.New("invalid refresh token")
	}
	
	// Check if session is expired
	if session.IsExpired() {
		return nil, errors.New("session expired")
	}
	
	// Get user
	s.mu.RLock()
	user, exists := s.users[session.UserID]
	s.mu.RUnlock()
	
	if !exists {
		return nil, errors.New("user not found")
	}
	
	// Generate new tokens
	tokenPair, err := s.tokenService.GenerateTokenPair(user, session.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}
	
	// Update session
	s.sessionStorage.Update(ctx, session.ID, map[string]interface{}{
		"token":          tokenPair.AccessToken,
		"refresh_token":  tokenPair.RefreshToken,
		"last_active_at": time.Now(),
	})
	
	return tokenPair, nil
}

// ValidateToken validates an access token
func (s *MockAuthService) ValidateToken(ctx context.Context, token string) (*models.TokenClaims, error) {
	// Validate token format and signature
	claims, err := s.tokenService.ValidateAccessToken(token)
	if err != nil {
		return nil, err
	}
	
	// Check if session exists and is valid
	session, err := s.sessionStorage.Get(ctx, claims.SessionID)
	if err != nil {
		return nil, errors.New("session not found")
	}
	
	if session.IsExpired() {
		return nil, errors.New("session expired")
	}
	
	// Update session activity
	s.sessionStorage.Update(ctx, session.ID, map[string]interface{}{
		"last_active_at": time.Now(),
	})
	
	return claims, nil
}

// RevokeToken revokes an access token
func (s *MockAuthService) RevokeToken(ctx context.Context, token string) error {
	return s.tokenService.RevokeToken(token)
}

// VerifyEmail verifies a user's email address
func (s *MockAuthService) VerifyEmail(ctx context.Context, userID, token string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Check token
	storedUserID, exists := s.emailTokens[token]
	if !exists || storedUserID != userID {
		return errors.New("invalid or expired token")
	}
	
	// Get user
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Verify email
	user.IsVerified = true
	user.VerifiedAt = &[]time.Time{time.Now()}[0]
	
	// Remove token
	delete(s.emailTokens, token)
	
	return nil
}

// ResendVerificationEmail resends the verification email
func (s *MockAuthService) ResendVerificationEmail(ctx context.Context, email string) error {
	s.mu.RLock()
	user, exists := s.usersByEmail[email]
	s.mu.RUnlock()
	
	if !exists {
		// Don't reveal if email exists
		return nil
	}
	
	if user.IsVerified {
		return errors.New("email already verified")
	}
	
	// Generate new verification token
	verificationToken := utils.GenerateUUID()
	s.mu.Lock()
	s.emailTokens[verificationToken] = user.ID
	s.mu.Unlock()
	
	// In a real implementation, send verification email here
	
	return nil
}

// ChangePassword changes a user's password
func (s *MockAuthService) ChangePassword(ctx context.Context, userID, oldPassword, newPassword string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Get user
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Verify old password
	if err := s.passwordService.VerifyPassword(oldPassword, user.Password); err != nil {
		return errors.New("invalid old password")
	}
	
	// Hash new password
	hashedPassword, err := s.passwordService.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}
	
	// Update password
	user.Password = hashedPassword
	user.UpdatedAt = time.Now()
	
	// In a real implementation, send password changed notification email
	
	return nil
}

// ResetPasswordRequest initiates a password reset
func (s *MockAuthService) ResetPasswordRequest(ctx context.Context, email string) error {
	s.mu.RLock()
	user, exists := s.usersByEmail[email]
	s.mu.RUnlock()
	
	if !exists {
		// Don't reveal if email exists
		return nil
	}
	
	// Generate reset token
	resetToken := utils.GenerateUUID()
	s.mu.Lock()
	s.resetTokens[resetToken] = user.ID
	s.mu.Unlock()
	
	// In a real implementation, send password reset email here
	
	return nil
}

// ResetPassword resets a user's password
func (s *MockAuthService) ResetPassword(ctx context.Context, token, newPassword string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Check token
	userID, exists := s.resetTokens[token]
	if !exists {
		return errors.New("invalid or expired token")
	}
	
	// Get user
	user, exists := s.users[userID]
	if !exists {
		return errors.New("user not found")
	}
	
	// Hash new password
	hashedPassword, err := s.passwordService.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}
	
	// Update password
	user.Password = hashedPassword
	user.UpdatedAt = time.Now()
	
	// Remove token
	delete(s.resetTokens, token)
	
	// In a real implementation, send password changed notification email
	
	return nil
}

// GetUserSessions returns all active sessions for a user
func (s *MockAuthService) GetUserSessions(ctx context.Context, userID string) ([]*models.Session, error) {
	return s.sessionStorage.GetUserSessions(ctx, userID)
}

// RevokeSession revokes a specific session
func (s *MockAuthService) RevokeSession(ctx context.Context, sessionID string) error {
	return s.sessionStorage.Delete(ctx, sessionID)
}

// CleanupExpiredSessions removes all expired sessions
func (s *MockAuthService) CleanupExpiredSessions(ctx context.Context) error {
	return s.sessionStorage.DeleteExpiredSessions(ctx)
}

// ValidateTokenMiddleware validates token for middleware (without context)
func (s *MockAuthService) ValidateTokenMiddleware(token string) (*models.TokenClaims, error) {
	return s.ValidateToken(context.Background(), token)
}

// GetUserByID gets user by ID for middleware
func (s *MockAuthService) GetUserByID(userID string) (*models.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	user := s.users[userID]
	if user == nil {
		return nil, ErrUserNotFound
	}
	return user, nil
}

// Error definitions
var (
	ErrUserNotFound = errors.New("user not found")
)