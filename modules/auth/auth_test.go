package auth_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/handlers"
	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/auth/services"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
	"github.com/gorilla/mux"
)

func setupTest(t *testing.T) (*mux.Router, services.AuthService) {
	// Create services
	jwtService := services.NewJWTService(
		"test-secret-key",
		15*time.Minute,
		7*24*time.Hour,
		"test-issuer",
	)
	
	passwordService := services.NewBcryptPasswordService(10)
	authService := services.NewMockAuthService(jwtService, passwordService)
	userService := services.NewMockUserService()
	
	// Initialize validator
	validator.Initialize()
	v := validator.GetDefaultValidator()
	
	// Create logger
	logger := utils.NewLogger()
	
	// Create handler
	authHandler := handlers.NewAuthHandler(authService, userService, v, logger)
	
	// Create router
	router := mux.NewRouter()
	authHandler.RegisterRoutes(router)
	
	return router, authService
}

func TestLogin(t *testing.T) {
	router, _ := setupTest(t)
	
	tests := []struct {
		name       string
		body       interface{}
		wantStatus int
	}{
		{
			name: "Valid login with email",
			body: map[string]string{
				"email":    "<EMAIL>",
				"password": "Admin@123",
			},
			wantStatus: http.StatusOK,
		},
		{
			name: "Valid login with username",
			body: map[string]string{
				"username": "admin",
				"password": "Admin@123",
			},
			wantStatus: http.StatusOK,
		},
		{
			name: "Invalid password",
			body: map[string]string{
				"email":    "<EMAIL>",
				"password": "WrongPassword",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Non-existent user",
			body: map[string]string{
				"email":    "<EMAIL>",
				"password": "Password@123",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Missing credentials",
			body: map[string]string{
				"password": "Password@123",
			},
			wantStatus: http.StatusBadRequest,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, _ := json.Marshal(tt.body)
			req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)
			
			if rr.Code != tt.wantStatus {
				t.Errorf("Login() status = %v, want %v", rr.Code, tt.wantStatus)
			}
			
			if rr.Code == http.StatusOK {
				var response models.TokenPair
				if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				
				if response.AccessToken == "" {
					t.Error("Expected access token in response")
				}
				if response.RefreshToken == "" {
					t.Error("Expected refresh token in response")
				}
			}
		})
	}
}

func TestRegister(t *testing.T) {
	router, _ := setupTest(t)
	
	tests := []struct {
		name       string
		body       interface{}
		wantStatus int
	}{
		{
			name: "Valid registration",
			body: map[string]string{
				"email":      "<EMAIL>",
				"username":   "newuser",
				"password":   "NewUser@123",
				"first_name": "New",
				"last_name":  "User",
			},
			wantStatus: http.StatusCreated,
		},
		{
			name: "Duplicate email",
			body: map[string]string{
				"email":    "<EMAIL>",
				"username": "newadmin",
				"password": "NewAdmin@123",
			},
			wantStatus: http.StatusConflict,
		},
		{
			name: "Weak password",
			body: map[string]string{
				"email":    "<EMAIL>",
				"username": "weakuser",
				"password": "weak",
			},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "Invalid email",
			body: map[string]string{
				"email":    "invalid-email",
				"username": "testuser2",
				"password": "Test@123",
			},
			wantStatus: http.StatusBadRequest,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, _ := json.Marshal(tt.body)
			req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)
			
			if rr.Code != tt.wantStatus {
				t.Errorf("Register() status = %v, want %v", rr.Code, tt.wantStatus)
			}
			
			if rr.Code == http.StatusCreated {
				var response models.UserProfile
				if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				
				if response.ID == "" {
					t.Error("Expected user ID in response")
				}
			}
		})
	}
}

func TestRefreshToken(t *testing.T) {
	router, _ := setupTest(t)
	
	// First login to get tokens
	loginBody, _ := json.Marshal(map[string]string{
		"email":    "<EMAIL>",
		"password": "Admin@123",
	})
	
	loginReq := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewReader(loginBody))
	loginReq.Header.Set("Content-Type", "application/json")
	
	loginRR := httptest.NewRecorder()
	router.ServeHTTP(loginRR, loginReq)
	
	var loginResp models.TokenPair
	json.Unmarshal(loginRR.Body.Bytes(), &loginResp)
	
	tests := []struct {
		name       string
		body       interface{}
		wantStatus int
	}{
		{
			name: "Valid refresh token",
			body: map[string]string{
				"refresh_token": loginResp.RefreshToken,
			},
			wantStatus: http.StatusOK,
		},
		{
			name: "Invalid refresh token",
			body: map[string]string{
				"refresh_token": "invalid-token",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Missing refresh token",
			body: map[string]string{},
			wantStatus: http.StatusBadRequest,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, _ := json.Marshal(tt.body)
			req := httptest.NewRequest(http.MethodPost, "/auth/refresh", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)
			
			if rr.Code != tt.wantStatus {
				t.Errorf("RefreshToken() status = %v, want %v", rr.Code, tt.wantStatus)
			}
		})
	}
}

func TestProtectedEndpoints(t *testing.T) {
	router, _ := setupTest(t)
	
	// First login to get tokens
	loginBody, _ := json.Marshal(map[string]string{
		"email":    "<EMAIL>",
		"password": "Admin@123",
	})
	
	loginReq := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewReader(loginBody))
	loginReq.Header.Set("Content-Type", "application/json")
	
	loginRR := httptest.NewRecorder()
	router.ServeHTTP(loginRR, loginReq)
	
	var loginResp models.TokenPair
	json.Unmarshal(loginRR.Body.Bytes(), &loginResp)
	
	tests := []struct {
		name       string
		endpoint   string
		method     string
		token      string
		wantStatus int
	}{
		{
			name:       "Get profile with valid token",
			endpoint:   "/auth/profile",
			method:     http.MethodGet,
			token:      loginResp.AccessToken,
			wantStatus: http.StatusOK,
		},
		{
			name:       "Get profile without token",
			endpoint:   "/auth/profile",
			method:     http.MethodGet,
			token:      "",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:       "Get sessions with valid token",
			endpoint:   "/auth/sessions",
			method:     http.MethodGet,
			token:      loginResp.AccessToken,
			wantStatus: http.StatusOK,
		},
		{
			name:       "Logout with valid token",
			endpoint:   "/auth/logout",
			method:     http.MethodPost,
			token:      loginResp.AccessToken,
			wantStatus: http.StatusOK,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.endpoint, nil)
			if tt.token != "" {
				req.Header.Set("Authorization", "Bearer "+tt.token)
			}
			req.Header.Set("Content-Type", "application/json")
			
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)
			
			if rr.Code != tt.wantStatus {
				t.Errorf("%s status = %v, want %v", tt.endpoint, rr.Code, tt.wantStatus)
			}
		})
	}
}

func TestTokenValidation(t *testing.T) {
	_, authService := setupTest(t)
	ctx := context.Background()
	
	// Login to get valid token
	tokenPair, err := authService.Login(ctx, &models.UserCredentials{
		Email:    "<EMAIL>",
		Password: "Admin@123",
	}, "127.0.0.1", "Test/1.0")
	
	if err != nil {
		t.Fatalf("Failed to login: %v", err)
	}
	
	// Test valid token
	claims, err := authService.ValidateToken(ctx, tokenPair.AccessToken)
	if err != nil {
		t.Errorf("ValidateToken() with valid token failed: %v", err)
	}
	
	if claims.UserID == "" {
		t.Error("Expected UserID in claims")
	}
	
	// Test invalid token
	_, err = authService.ValidateToken(ctx, "invalid-token")
	if err == nil {
		t.Error("ValidateToken() with invalid token should fail")
	}
	
	// Test revoked token
	authService.RevokeToken(ctx, tokenPair.AccessToken)
	_, err = authService.ValidateToken(ctx, tokenPair.AccessToken)
	if err == nil {
		t.Error("ValidateToken() with revoked token should fail")
	}
}

func TestPasswordOperations(t *testing.T) {
	_, authService := setupTest(t)
	ctx := context.Background()
	
	// Test change password
	err := authService.ChangePassword(ctx, "fake-user-id", "oldpass", "newpass")
	if err == nil {
		t.Error("ChangePassword() with invalid user should fail")
	}
	
	// Test password reset flow
	err = authService.ResetPasswordRequest(ctx, "<EMAIL>")
	if err != nil {
		t.Errorf("ResetPasswordRequest() failed: %v", err)
	}
	
	err = authService.ResetPasswordRequest(ctx, "<EMAIL>")
	if err != nil {
		t.Errorf("ResetPasswordRequest() should not reveal if email exists: %v", err)
	}
}