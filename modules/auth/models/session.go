package models

import (
	"time"
)

// Session represents a user session
type Session struct {
	ID           string    `json:"id" gorm:"primaryKey;size:26"`
	UserID       string    `json:"user_id" gorm:"size:26;not null;index"`
	Token        string    `json:"-" gorm:"uniqueIndex;size:500;not null"`
	RefreshToken string    `json:"-" gorm:"uniqueIndex;size:500"`
	IPAddress    string    `json:"ip_address" gorm:"size:45"`
	UserAgent    string    `json:"user_agent" gorm:"size:500"`
	ExpiresAt    time.Time `json:"expires_at" gorm:"not null;index"`
	LastActiveAt time.Time `json:"last_active_at"`
	CreatedAt    time.Time `json:"created_at"`
	
	// Relationships
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName returns the table name for Session model
func (Session) TableName() string {
	return "sessions"
}

// IsExpired checks if the session has expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsActive checks if the session is still active (not expired and recently used)
func (s *Session) IsActive() bool {
	if s.IsExpired() {
		return false
	}
	
	// Consider session inactive if not used for 30 days
	inactiveThreshold := 30 * 24 * time.Hour
	return time.Since(s.LastActiveAt) < inactiveThreshold
}

// UpdateActivity updates the last active timestamp
func (s *Session) UpdateActivity() {
	s.LastActiveAt = time.Now()
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	TokenType    string    `json:"token_type"`
	ExpiresIn    int       `json:"expires_in"` // seconds
	ExpiresAt    time.Time `json:"expires_at"`
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID      string   `json:"user_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	SessionID   string   `json:"session_id"`
	ExpiresAt   int64    `json:"exp"`
	IssuedAt    int64    `json:"iat"`
	NotBefore   int64    `json:"nbf"`
}

// RefreshTokenRequest represents a token refresh request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// LogoutRequest represents a logout request
type LogoutRequest struct {
	AllDevices bool `json:"all_devices"` // Logout from all devices
}