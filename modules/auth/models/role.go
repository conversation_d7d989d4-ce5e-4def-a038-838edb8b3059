package models

import (
	"time"
)

// Role represents a user role
type Role struct {
	ID          string    `json:"id" gorm:"primaryKey;size:26"`
	Name        string    `json:"name" gorm:"uniqueIndex;size:50;not null"`
	DisplayName string    `json:"display_name" gorm:"size:100"`
	Description string    `json:"description" gorm:"size:500"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"` // System roles cannot be deleted
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// Relationships
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
	Users       []User       `json:"-" gorm:"many2many:user_roles;"`
}

// TableName returns the table name for Role model
func (Role) TableName() string {
	return "roles"
}

// HasPermission checks if role has a specific permission
func (r *Role) HasPermission(permissionName string) bool {
	for _, perm := range r.Permissions {
		if perm.Name == permissionName {
			return true
		}
	}
	return false
}

// Permission represents a permission
type Permission struct {
	ID          string    `json:"id" gorm:"primaryKey;size:26"`
	Name        string    `json:"name" gorm:"uniqueIndex;size:100;not null"`
	DisplayName string    `json:"display_name" gorm:"size:100"`
	Description string    `json:"description" gorm:"size:500"`
	Resource    string    `json:"resource" gorm:"size:50;index"` // e.g., "user", "post", "admin"
	Action      string    `json:"action" gorm:"size:50;index"`   // e.g., "create", "read", "update", "delete"
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// Relationships
	Roles []Role `json:"-" gorm:"many2many:role_permissions;"`
}

// TableName returns the table name for Permission model
func (Permission) TableName() string {
	return "permissions"
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID    string    `gorm:"primaryKey;size:26"`
	RoleID    string    `gorm:"primaryKey;size:26"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName returns the table name for UserRole model
func (UserRole) TableName() string {
	return "user_roles"
}

// RolePermission represents the many-to-many relationship between roles and permissions
type RolePermission struct {
	RoleID       string    `gorm:"primaryKey;size:26"`
	PermissionID string    `gorm:"primaryKey;size:26"`
	CreatedAt    time.Time `json:"created_at"`
}

// TableName returns the table name for RolePermission model
func (RolePermission) TableName() string {
	return "role_permissions"
}

// Predefined role names
const (
	RoleSuperAdmin = "super_admin"
	RoleAdmin      = "admin"
	RoleUser       = "user"
	RoleGuest      = "guest"
)

// Predefined permissions
const (
	// User permissions
	PermUserCreate = "user:create"
	PermUserRead   = "user:read"
	PermUserUpdate = "user:update"
	PermUserDelete = "user:delete"
	
	// Admin permissions
	PermAdminAccess    = "admin:access"
	PermAdminUsers     = "admin:users"
	PermAdminRoles     = "admin:roles"
	PermAdminSettings  = "admin:settings"
	
	// System permissions
	PermSystemAll = "system:all"
)

// DefaultRoles returns the default system roles
func DefaultRoles() []Role {
	return []Role{
		{
			Name:        RoleSuperAdmin,
			DisplayName: "Super Administrator",
			Description: "Full system access",
			IsSystem:    true,
		},
		{
			Name:        RoleAdmin,
			DisplayName: "Administrator",
			Description: "Administrative access",
			IsSystem:    true,
		},
		{
			Name:        RoleUser,
			DisplayName: "User",
			Description: "Regular user access",
			IsSystem:    true,
		},
		{
			Name:        RoleGuest,
			DisplayName: "Guest",
			Description: "Guest access",
			IsSystem:    true,
		},
	}
}

// DefaultPermissions returns the default system permissions
func DefaultPermissions() []Permission {
	return []Permission{
		// User permissions
		{Name: PermUserCreate, DisplayName: "Create User", Resource: "user", Action: "create"},
		{Name: PermUserRead, DisplayName: "Read User", Resource: "user", Action: "read"},
		{Name: PermUserUpdate, DisplayName: "Update User", Resource: "user", Action: "update"},
		{Name: PermUserDelete, DisplayName: "Delete User", Resource: "user", Action: "delete"},
		
		// Admin permissions
		{Name: PermAdminAccess, DisplayName: "Admin Access", Resource: "admin", Action: "access"},
		{Name: PermAdminUsers, DisplayName: "Manage Users", Resource: "admin", Action: "users"},
		{Name: PermAdminRoles, DisplayName: "Manage Roles", Resource: "admin", Action: "roles"},
		{Name: PermAdminSettings, DisplayName: "Manage Settings", Resource: "admin", Action: "settings"},
		
		// System permissions
		{Name: PermSystemAll, DisplayName: "All Permissions", Resource: "system", Action: "all"},
	}
}