package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID        string    `json:"id" gorm:"primaryKey;size:26"`
	Email     string    `json:"email" gorm:"uniqueIndex;size:255;not null"`
	Username  string    `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Password  string    `json:"-" gorm:"size:255;not null"` // Hashed password
	FirstName string    `json:"first_name" gorm:"size:100"`
	LastName  string    `json:"last_name" gorm:"size:100"`
	Avatar    string    `json:"avatar" gorm:"size:500"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	IsVerified bool     `json:"is_verified" gorm:"default:false"`
	VerifiedAt *time.Time `json:"verified_at"`
	LastLoginAt *time.Time `json:"last_login_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// Relationships
	Roles []Role `json:"roles,omitempty" gorm:"many2many:user_roles;"`
	Sessions []Session `json:"-" gorm:"foreignKey:UserID"`
}

// TableName returns the table name for User model
func (User) TableName() string {
	return "users"
}

// FullName returns the user's full name
func (u *User) FullName() string {
	if u.FirstName == "" && u.LastName == "" {
		return u.Username
	}
	return u.FirstName + " " + u.LastName
}

// HasRole checks if user has a specific role
func (u *User) HasRole(roleName string) bool {
	for _, role := range u.Roles {
		if role.Name == roleName {
			return true
		}
	}
	return false
}

// HasPermission checks if user has a specific permission
func (u *User) HasPermission(permissionName string) bool {
	for _, role := range u.Roles {
		for _, perm := range role.Permissions {
			if perm.Name == permissionName {
				return true
			}
		}
	}
	return false
}

// GetPermissions returns all permissions for the user
func (u *User) GetPermissions() []string {
	permMap := make(map[string]bool)
	var permissions []string
	
	for _, role := range u.Roles {
		for _, perm := range role.Permissions {
			if !permMap[perm.Name] {
				permMap[perm.Name] = true
				permissions = append(permissions, perm.Name)
			}
		}
	}
	
	return permissions
}

// UserCredentials represents login credentials
type UserCredentials struct {
	Email    string `json:"email" validate:"required_without=Username,omitempty,email"`
	Username string `json:"username" validate:"required_without=Email,omitempty,username"`
	Password string `json:"password" validate:"required,min=8"`
}

// UserRegistration represents registration data
type UserRegistration struct {
	Email     string `json:"email" validate:"required,email"`
	Username  string `json:"username" validate:"required,username,min=3,max=50"`
	Password  string `json:"password" validate:"required,strongpassword"`
	FirstName string `json:"first_name" validate:"omitempty,min=1,max=100"`
	LastName  string `json:"last_name" validate:"omitempty,min=1,max=100"`
}

// UserProfile represents public user profile
type UserProfile struct {
	ID         string    `json:"id"`
	Email      string    `json:"email"`
	Username   string    `json:"username"`
	FirstName  string    `json:"first_name"`
	LastName   string    `json:"last_name"`
	Avatar     string    `json:"avatar"`
	IsVerified bool      `json:"is_verified"`
	CreatedAt  time.Time `json:"created_at"`
}

// ToProfile converts User to UserProfile
func (u *User) ToProfile() *UserProfile {
	return &UserProfile{
		ID:         u.ID,
		Email:      u.Email,
		Username:   u.Username,
		FirstName:  u.FirstName,
		LastName:   u.LastName,
		Avatar:     u.Avatar,
		IsVerified: u.IsVerified,
		CreatedAt:  u.CreatedAt,
	}
}