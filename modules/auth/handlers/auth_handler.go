package handlers

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/auth/services"
	"github.com/blog-api-v3/blog-api-v3/pkg/http/middleware"
	httpPkg "github.com/blog-api-v3/blog-api-v3/pkg/http"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
	"github.com/gorilla/mux"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	authService  services.AuthService
	userService  services.UserService
	validator    *validator.RequestValidator
	logger       utils.Logger
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService services.AuthService, userService services.UserService, v validator.Validator, logger utils.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		userService: userService,
		validator:   validator.NewRequestValidator(v),
		logger:      logger,
	}
}

// RegisterRoutes registers auth routes
func (h *AuthHandler) RegisterRoutes(router *mux.Router) {
	// Public routes
	router.HandleFunc("/auth/login", h.Login).Methods(http.MethodPost)
	router.HandleFunc("/auth/register", h.Register).Methods(http.MethodPost)
	router.HandleFunc("/auth/refresh", h.RefreshToken).Methods(http.MethodPost)
	router.HandleFunc("/auth/verify-email", h.VerifyEmail).Methods(http.MethodGet)
	router.HandleFunc("/auth/resend-verification", h.ResendVerification).Methods(http.MethodPost)
	router.HandleFunc("/auth/forgot-password", h.ForgotPassword).Methods(http.MethodPost)
	router.HandleFunc("/auth/reset-password", h.ResetPassword).Methods(http.MethodPost)
	
	// Protected routes (require authentication)
	protected := router.PathPrefix("/auth").Subrouter()
	protected.Use(middleware.AuthMiddleware(h.authService))
	
	protected.HandleFunc("/logout", h.Logout).Methods(http.MethodPost)
	protected.HandleFunc("/change-password", h.ChangePassword).Methods(http.MethodPost)
	protected.HandleFunc("/profile", h.GetProfile).Methods(http.MethodGet)
	protected.HandleFunc("/sessions", h.GetSessions).Methods(http.MethodGet)
	protected.HandleFunc("/sessions/{id}", h.RevokeSession).Methods(http.MethodDelete)
}

// Login handles user login
func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	// Safety checks
	if h == nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	if h.authService == nil {
		http.Error(w, "Auth service not available", http.StatusInternalServerError)
		return
	}
	if h.validator == nil {
		http.Error(w, "Validator not available", http.StatusInternalServerError)
		return
	}
	if h.logger == nil {
		http.Error(w, "Logger not available", http.StatusInternalServerError)
		return
	}

	resp := httpPkg.NewResponse(w)
	
	var credentials models.UserCredentials
	if err := h.validator.ValidateJSON(r, &credentials); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Get IP and user agent
	ipAddress := getClientIP(r)
	userAgent := r.Header.Get("User-Agent")
	
	// Attempt login
	tokenPair, err := h.authService.Login(r.Context(), &credentials, ipAddress, userAgent)
	if err != nil {
		h.logger.WithError(err).WithField("email", credentials.Email).Error("Login failed")
		resp.Error(http.StatusUnauthorized, "Invalid credentials")
		return
	}
	
	// Return token pair
	resp.Success(tokenPair)
}

// Register handles user registration
func (h *AuthHandler) Register(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	var registration models.UserRegistration
	if err := h.validator.ValidateJSON(r, &registration); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Register user
	user, err := h.authService.Register(r.Context(), &registration)
	if err != nil {
		h.logger.WithError(err).Error("Registration failed")
		if strings.Contains(err.Error(), "already exists") {
			resp.Error(http.StatusConflict, "User already exists")
			return
		}
		resp.Error(http.StatusInternalServerError, "Registration failed")
		return
	}
	
	// Return user profile
	resp.JSON(http.StatusCreated, user.ToProfile())
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	var request models.RefreshTokenRequest
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Refresh token
	tokenPair, err := h.authService.RefreshToken(r.Context(), request.RefreshToken)
	if err != nil {
		h.logger.WithError(err).Error("Token refresh failed")
		resp.Error(http.StatusUnauthorized, "Invalid refresh token")
		return
	}
	
	resp.Success(tokenPair)
}

// Logout handles user logout
func (h *AuthHandler) Logout(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get user from context (set by auth middleware)
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var request models.LogoutRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		request = models.LogoutRequest{} // Use default values
	}
	
	// Logout
	if err := h.authService.Logout(r.Context(), claims.UserID, claims.SessionID, request.AllDevices); err != nil {
		h.logger.WithError(err).Error("Logout failed")
		resp.Error(http.StatusInternalServerError, "Logout failed")
		return
	}
	
	resp.Success(map[string]string{"message": "Logged out successfully"})
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get user from context
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var request struct {
		OldPassword string `json:"old_password" validate:"required"`
		NewPassword string `json:"new_password" validate:"required,strongpassword"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Change password
	if err := h.authService.ChangePassword(r.Context(), claims.UserID, request.OldPassword, request.NewPassword); err != nil {
		h.logger.WithError(err).Error("Password change failed")
		if strings.Contains(err.Error(), "invalid") {
			resp.Error(http.StatusBadRequest, "Invalid old password")
			return
		}
		resp.Error(http.StatusInternalServerError, "Password change failed")
		return
	}
	
	resp.Success(map[string]string{"message": "Password changed successfully"})
}

// GetProfile returns the authenticated user's profile
func (h *AuthHandler) GetProfile(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get user from context
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	// Get user
	user, err := h.userService.GetUserByID(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user profile")
		resp.Error(http.StatusNotFound, "User not found")
		return
	}
	
	resp.Success(user.ToProfile())
}

// GetSessions returns the user's active sessions
func (h *AuthHandler) GetSessions(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get user from context
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	// Get sessions
	sessions, err := h.authService.GetUserSessions(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user sessions")
		resp.Error(http.StatusInternalServerError, "Failed to get sessions")
		return
	}
	
	// Filter out sensitive data
	sessionData := make([]map[string]interface{}, len(sessions))
	for i, session := range sessions {
		sessionData[i] = map[string]interface{}{
			"id":             session.ID,
			"ip_address":     session.IPAddress,
			"user_agent":     session.UserAgent,
			"last_active_at": session.LastActiveAt,
			"created_at":     session.CreatedAt,
			"expires_at":     session.ExpiresAt,
			"is_current":     session.ID == claims.SessionID,
		}
	}
	
	resp.Success(map[string]interface{}{
		"sessions": sessionData,
		"total":    len(sessions),
	})
}

// RevokeSession revokes a specific session
func (h *AuthHandler) RevokeSession(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get user from context
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	// Get session ID
	vars := mux.Vars(r)
	sessionID := vars["id"]
	
	// Revoke session
	if err := h.authService.RevokeSession(r.Context(), sessionID); err != nil {
		h.logger.WithError(err).Error("Failed to revoke session")
		resp.Error(http.StatusInternalServerError, "Failed to revoke session")
		return
	}
	
	resp.Success(map[string]string{"message": "Session revoked successfully"})
}

// VerifyEmail handles email verification
func (h *AuthHandler) VerifyEmail(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Get parameters
	userID := r.URL.Query().Get("user")
	token := r.URL.Query().Get("token")
	
	if userID == "" || token == "" {
		resp.Error(http.StatusBadRequest, "Missing parameters")
		return
	}
	
	// Verify email
	if err := h.authService.VerifyEmail(r.Context(), userID, token); err != nil {
		h.logger.WithError(err).Error("Email verification failed")
		resp.Error(http.StatusBadRequest, "Invalid or expired token")
		return
	}
	
	resp.Success(map[string]string{"message": "Email verified successfully"})
}

// ResendVerification resends verification email
func (h *AuthHandler) ResendVerification(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	var request struct {
		Email string `json:"email" validate:"required,email"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Resend verification email
	if err := h.authService.ResendVerificationEmail(r.Context(), request.Email); err != nil {
		h.logger.WithError(err).Error("Failed to resend verification email")
		// Don't reveal if email exists
		resp.Success(map[string]string{"message": "Verification email sent if account exists"})
		return
	}
	
	resp.Success(map[string]string{"message": "Verification email sent"})
}

// ForgotPassword handles password reset request
func (h *AuthHandler) ForgotPassword(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	var request struct {
		Email string `json:"email" validate:"required,email"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Request password reset
	if err := h.authService.ResetPasswordRequest(r.Context(), request.Email); err != nil {
		h.logger.WithError(err).Error("Password reset request failed")
		// Don't reveal if email exists
		resp.Success(map[string]string{"message": "Password reset email sent if account exists"})
		return
	}
	
	resp.Success(map[string]string{"message": "Password reset email sent"})
}

// ResetPassword handles password reset
func (h *AuthHandler) ResetPassword(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	var request struct {
		Token       string `json:"token" validate:"required"`
		NewPassword string `json:"new_password" validate:"required,strongpassword"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	// Reset password
	if err := h.authService.ResetPassword(r.Context(), request.Token, request.NewPassword); err != nil {
		h.logger.WithError(err).Error("Password reset failed")
		resp.Error(http.StatusBadRequest, "Invalid or expired token")
		return
	}
	
	resp.Success(map[string]string{"message": "Password reset successfully"})
}

// getClientIP extracts the client IP address from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}
	
	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if idx := strings.LastIndex(ip, ":"); idx != -1 {
		ip = ip[:idx]
	}
	
	return ip
}