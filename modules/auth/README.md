# Auth Module

Module xác thực cho Blog API v3, cung cấp đầy đủ chức năng authentication, authorization, và session management.

## Features

- **User Authentication**: Login, logout, register với JWT
- **Token Management**: Access token, refresh token với TTL tùy chỉnh
- **Password Security**: Bcrypt hashing, password strength validation
- **Session Management**: Multi-device sessions, session revocation
- **Role-Based Access Control (RBAC)**: Roles và permissions linh hoạt
- **Email Verification**: <PERSON><PERSON><PERSON> thực email với token
- **Password Reset**: Reset password qua email
- **Mock Implementation**: Sẵn sàng để test và demo

## Architecture

### Models
- `User`: User entity với profile information
- `Session`: User sessions với device tracking
- `Role`: Roles cho RBAC
- `Permission`: Granular permissions
- `TokenClaims`: JWT token payload

### Services
- `AuthService`: Core authentication logic
- `UserService`: User management
- `RoleService`: Role management
- `PermissionService`: Permission management
- `TokenService`: JWT token operations
- `PasswordService`: Password hashing và validation
- `SessionStorage`: Session persistence
- `EmailService`: Auth-related emails

### Storage
- `MemorySessionStorage`: In-memory session storage
- Database implementations có thể thêm sau

## Usage

### Setup

```go
import (
    "github.com/blog-api-v3/blog-api-v3/modules/auth/services"
    "github.com/blog-api-v3/blog-api-v3/modules/auth/handlers"
)

// Create services
jwtService := services.NewJWTService(
    secretKey,
    15*time.Minute,  // Access token TTL
    7*24*time.Hour,  // Refresh token TTL
    "blog-api-v3",
)

passwordService := services.NewBcryptPasswordService(bcrypt.DefaultCost)

authService := services.NewMockAuthService(jwtService, passwordService)

// Create handler
authHandler := handlers.NewAuthHandler(authService, userService, validator, logger)

// Register routes
authHandler.RegisterRoutes(router)
```

### API Endpoints

#### Public Endpoints

**POST /auth/login**
```json
{
  "email": "<EMAIL>",
  "password": "SecureP@ss123"
}
```

Response:
```json
{
  "access_token": "eyJ...",
  "refresh_token": "uuid-string",
  "token_type": "Bearer",
  "expires_in": 900,
  "expires_at": "2024-01-01T12:00:00Z"
}
```

**POST /auth/register**
```json
{
  "email": "<EMAIL>",
  "username": "johndoe",
  "password": "SecureP@ss123",
  "first_name": "John",
  "last_name": "Doe"
}
```

**POST /auth/refresh**
```json
{
  "refresh_token": "uuid-string"
}
```

**GET /auth/verify-email?user=USER_ID&token=TOKEN**

**POST /auth/resend-verification**
```json
{
  "email": "<EMAIL>"
}
```

**POST /auth/forgot-password**
```json
{
  "email": "<EMAIL>"
}
```

**POST /auth/reset-password**
```json
{
  "token": "reset-token",
  "new_password": "NewSecureP@ss123"
}
```

#### Protected Endpoints (Require Authentication)

**POST /auth/logout**
```json
{
  "all_devices": false  // Optional, logout from all devices
}
```

**POST /auth/change-password**
```json
{
  "old_password": "OldP@ss123",
  "new_password": "NewP@ss123"
}
```

**GET /auth/profile**

Response:
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "username": "johndoe",
  "first_name": "John",
  "last_name": "Doe",
  "avatar": "https://...",
  "is_verified": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

**GET /auth/sessions**

Response:
```json
{
  "sessions": [
    {
      "id": "session-id",
      "ip_address": "***********",
      "user_agent": "Mozilla/5.0...",
      "last_active_at": "2024-01-01T12:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "expires_at": "2024-01-02T00:00:00Z",
      "is_current": true
    }
  ],
  "total": 1
}
```

**DELETE /auth/sessions/{id}**

### Middleware Usage

```go
// Require authentication
router.Handle("/protected", 
    middleware.AuthMiddleware(authService)(handler))

// Optional authentication
router.Handle("/public", 
    middleware.OptionalAuthMiddleware(authService)(handler))

// Require specific permission
router.Handle("/admin", 
    middleware.AuthMiddleware(authService)(
        middleware.RequirePermissionMiddleware("admin:access")(handler)))

// Require specific role
router.Handle("/admin", 
    middleware.AuthMiddleware(authService)(
        middleware.RequireRoleMiddleware("admin")(handler)))

// Get user info in handler
func handler(w http.ResponseWriter, r *http.Request) {
    claims := middleware.GetTokenClaims(r)
    if claims != nil {
        userID := claims.UserID
        roles := claims.Roles
        permissions := claims.Permissions
    }
}
```

### Default Users (Mock)

1. **Admin User**
   - Email: <EMAIL>
   - Username: admin
   - Password: Admin@123
   - Role: Super Admin

2. **Regular User**
   - Email: <EMAIL>
   - Username: testuser
   - Password: User@123
   - Role: User

### Security Features

1. **Password Requirements**:
   - Minimum 8 characters
   - At least 3 of: uppercase, lowercase, numbers, special characters
   - Not in common password list

2. **Token Security**:
   - Short-lived access tokens (15 minutes default)
   - Long-lived refresh tokens (7 days default)
   - Token revocation support
   - Session tracking

3. **Session Management**:
   - Device tracking (IP, User Agent)
   - Multi-device support
   - Session revocation
   - Activity tracking

### RBAC System

**Default Roles**:
- `super_admin`: Full system access
- `admin`: Administrative access
- `user`: Regular user access
- `guest`: Limited access

**Permission Format**: `resource:action`
- Example: `user:create`, `admin:access`, `post:delete`

### Extending the Module

1. **Custom User Fields**: Add fields to User model
2. **Custom Permissions**: Add to DefaultPermissions()
3. **Custom Validation**: Implement in password service
4. **Database Storage**: Replace memory storage with database
5. **Email Integration**: Implement EmailService interface

### Testing

```go
// Create mock service for testing
authService := services.NewMockAuthService(tokenService, passwordService)

// Login
tokenPair, err := authService.Login(ctx, &models.UserCredentials{
    Email:    "<EMAIL>",
    Password: "Admin@123",
}, "127.0.0.1", "Test/1.0")

// Validate token
claims, err := authService.ValidateToken(ctx, tokenPair.AccessToken)
```

## Integration with Other Modules

Auth module provides:
- User authentication for all modules
- Authorization middleware
- User context for requests
- Session management

Other modules can:
- Use middleware to protect endpoints
- Access user info from context
- Check permissions programmatically
- Integrate with user profiles