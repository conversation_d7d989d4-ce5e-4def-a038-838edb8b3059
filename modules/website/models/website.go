package models

import (
	"time"
)

// PostStatus định nghĩa trạng thái bài viết
type PostStatus string

const (
	PostStatusDraft     PostStatus = "draft"
	PostStatusPublished PostStatus = "published"
	PostStatusArchived  PostStatus = "archived"
	PostStatusScheduled PostStatus = "scheduled"
	PostStatusDeleted   PostStatus = "deleted"
)

// PageStatus định nghĩa trạng thái trang
type PageStatus string

const (
	PageStatusDraft     PageStatus = "draft"
	PageStatusPublished PageStatus = "published"
	PageStatusArchived  PageStatus = "archived"
	PageStatusDeleted   PageStatus = "deleted"
)

// CommentStatus định nghĩa trạng thái comment
type CommentStatus string

const (
	CommentStatusPending  CommentStatus = "pending"
	CommentStatusApproved CommentStatus = "approved"
	CommentStatusRejected CommentStatus = "rejected"
	CommentStatusSpam     CommentStatus = "spam"
	CommentStatusDeleted  CommentStatus = "deleted"
)

// Post đại diện cho một bài viết blog
type Post struct {
	ID          string     `json:"id" gorm:"primaryKey"`
	TenantID    string     `json:"tenant_id" validate:"required" gorm:"index"`
	AuthorID    string     `json:"author_id" validate:"required" gorm:"index"`
	Title       string     `json:"title" validate:"required,min=3,max=200"`
	Slug        string     `json:"slug" validate:"required,slug" gorm:"uniqueIndex:idx_tenant_slug"`
	Excerpt     string     `json:"excerpt" validate:"max=500"`
	Content     string     `json:"content" validate:"required,min=10"`
	Status      PostStatus `json:"status" validate:"required"`
	IsPublic    bool       `json:"is_public"`
	IsFeatured  bool       `json:"is_featured"`
	
	// SEO fields
	MetaTitle       string `json:"meta_title" validate:"max=70"`
	MetaDescription string `json:"meta_description" validate:"max=160"`
	MetaKeywords    string `json:"meta_keywords" validate:"max=255"`
	
	// Publication info
	PublishedAt  *time.Time `json:"published_at,omitempty"`
	ScheduledFor *time.Time `json:"scheduled_for,omitempty"`
	
	// Images
	FeaturedImage    string   `json:"featured_image" validate:"omitempty,url"`
	FeaturedImageAlt string   `json:"featured_image_alt" validate:"max=255"`
	Gallery          []string `json:"gallery" gorm:"type:json"`
	
	// Engagement
	ViewCount     int64 `json:"view_count"`
	LikeCount     int64 `json:"like_count"`
	CommentCount  int64 `json:"comment_count"`
	ShareCount    int64 `json:"share_count"`
	
	// Reading time in minutes
	ReadingTime   int `json:"reading_time"`
	
	// Timestamps
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	
	// Relationships
	Categories    []Category `json:"categories,omitempty" gorm:"many2many:post_categories;"`
	Tags          []Tag      `json:"tags,omitempty" gorm:"many2many:post_tags;"`
	Comments      []Comment  `json:"comments,omitempty" gorm:"foreignKey:PostID"`
	
	// Author info (embedded for convenience)
	AuthorName  string `json:"author_name,omitempty"`
	AuthorEmail string `json:"author_email,omitempty"`
	AuthorAvatar string `json:"author_avatar,omitempty"`
}

// Category đại diện cho danh mục bài viết
type Category struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	TenantID    string    `json:"tenant_id" validate:"required" gorm:"index"`
	Name        string    `json:"name" validate:"required,min=2,max=100"`
	Slug        string    `json:"slug" validate:"required,slug" gorm:"uniqueIndex:idx_tenant_category_slug"`
	Description string    `json:"description" validate:"max=500"`
	Color       string    `json:"color" validate:"omitempty,hexcolor"`
	Icon        string    `json:"icon" validate:"max=50"`
	Image       string    `json:"image" validate:"omitempty,url"`
	IsPublic    bool      `json:"is_public"`
	PostCount   int64     `json:"post_count"`
	SortOrder   int       `json:"sort_order"`
	
	// SEO fields
	MetaTitle       string `json:"meta_title" validate:"max=70"`
	MetaDescription string `json:"meta_description" validate:"max=160"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relationships
	Posts       []Post     `json:"posts,omitempty" gorm:"many2many:post_categories;"`
}

// Tag đại diện cho thẻ bài viết
type Tag struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	TenantID    string    `json:"tenant_id" validate:"required" gorm:"index"`
	Name        string    `json:"name" validate:"required,min=2,max=50"`
	Slug        string    `json:"slug" validate:"required,slug" gorm:"uniqueIndex:idx_tenant_tag_slug"`
	Description string    `json:"description" validate:"max=200"`
	Color       string    `json:"color" validate:"omitempty,hexcolor"`
	PostCount   int64     `json:"post_count"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relationships
	Posts       []Post     `json:"posts,omitempty" gorm:"many2many:post_tags;"`
}

// Page đại diện cho trang tĩnh
type Page struct {
	ID          string     `json:"id" gorm:"primaryKey"`
	TenantID    string     `json:"tenant_id" validate:"required" gorm:"index"`
	AuthorID    string     `json:"author_id" validate:"required"`
	Title       string     `json:"title" validate:"required,min=3,max=200"`
	Slug        string     `json:"slug" validate:"required,slug" gorm:"uniqueIndex:idx_tenant_page_slug"`
	Content     string     `json:"content" validate:"required,min=10"`
	Status      PageStatus `json:"status" validate:"required"`
	IsPublic    bool       `json:"is_public"`
	Template    string     `json:"template" validate:"max=100"`
	
	// SEO fields
	MetaTitle       string `json:"meta_title" validate:"max=70"`
	MetaDescription string `json:"meta_description" validate:"max=160"`
	MetaKeywords    string `json:"meta_keywords" validate:"max=255"`
	
	// Publication info
	PublishedAt *time.Time `json:"published_at,omitempty"`
	
	// Images
	FeaturedImage    string `json:"featured_image" validate:"omitempty,url"`
	FeaturedImageAlt string `json:"featured_image_alt" validate:"max=255"`
	
	// Navigation
	ParentID    *string `json:"parent_id,omitempty"`
	SortOrder   int     `json:"sort_order"`
	ShowInMenu  bool    `json:"show_in_menu"`
	MenuTitle   string  `json:"menu_title" validate:"max=100"`
	
	// Engagement
	ViewCount   int64 `json:"view_count"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relationships
	Children    []Page     `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Parent      *Page      `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	
	// Author info
	AuthorName  string `json:"author_name,omitempty"`
	AuthorEmail string `json:"author_email,omitempty"`
}

// Comment đại diện cho bình luận
type Comment struct {
	ID        string        `json:"id" gorm:"primaryKey"`
	TenantID  string        `json:"tenant_id" validate:"required" gorm:"index"`
	PostID    string        `json:"post_id" validate:"required" gorm:"index"`
	ParentID  *string       `json:"parent_id,omitempty"`
	AuthorID  *string       `json:"author_id,omitempty"` // Null nếu guest comment
	
	// Guest info (nếu không có AuthorID)
	GuestName  string `json:"guest_name" validate:"max=100"`
	GuestEmail string `json:"guest_email" validate:"omitempty,email"`
	GuestURL   string `json:"guest_url" validate:"omitempty,url"`
	
	Content    string        `json:"content" validate:"required,min=3,max=2000"`
	Status     CommentStatus `json:"status" validate:"required"`
	IsApproved bool          `json:"is_approved"`
	
	// Moderation
	IPAddress  string `json:"ip_address"`
	UserAgent  string `json:"user_agent"`
	
	// Engagement
	LikeCount   int64 `json:"like_count"`
	ReplyCount  int64 `json:"reply_count"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	ApprovedAt  *time.Time `json:"approved_at,omitempty"`
	
	// Relationships
	Post        *Post      `json:"post,omitempty" gorm:"foreignKey:PostID"`
	Replies     []Comment  `json:"replies,omitempty" gorm:"foreignKey:ParentID"`
	Parent      *Comment   `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	
	// Author info (nếu có AuthorID)
	AuthorName   string `json:"author_name,omitempty"`
	AuthorAvatar string `json:"author_avatar,omitempty"`
}

// Media đại diện cho file media
type Media struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	TenantID    string    `json:"tenant_id" validate:"required" gorm:"index"`
	UploaderID  string    `json:"uploader_id" validate:"required"`
	FileName    string    `json:"file_name" validate:"required"`
	OriginalName string   `json:"original_name" validate:"required"`
	MimeType    string    `json:"mime_type" validate:"required"`
	Size        int64     `json:"size" validate:"min=1"`
	URL         string    `json:"url" validate:"required,url"`
	ThumbnailURL *string  `json:"thumbnail_url,omitempty"`
	Alt         string    `json:"alt" validate:"max=255"`
	Caption     string    `json:"caption" validate:"max=500"`
	Width       *int      `json:"width,omitempty"`
	Height      *int      `json:"height,omitempty"`
	
	// Timestamps
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
}

// Filter models

// PostFilter để filter posts
type PostFilter struct {
	Cursor      string     `json:"cursor,omitempty"`      // Cursor for pagination
	Limit       int        `json:"limit,omitempty"`       // Number of items per page
	TenantID    string     `json:"tenant_id,omitempty"`
	AuthorID    string     `json:"author_id,omitempty"`
	Status      PostStatus `json:"status,omitempty"`
	CategoryID  string     `json:"category_id,omitempty"`
	TagID       string     `json:"tag_id,omitempty"`
	Featured    *bool      `json:"featured,omitempty"`
	Public      *bool      `json:"public,omitempty"`
	Query       string     `json:"query,omitempty"`
	SortBy      string     `json:"sort_by,omitempty"`
	SortOrder   string     `json:"sort_order,omitempty"`
	PublishedFrom *time.Time `json:"published_from,omitempty"`
	PublishedTo   *time.Time `json:"published_to,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page        int        `json:"page,omitempty"`
	PageSize    int        `json:"page_size,omitempty"`
}

// PageFilter để filter pages
type PageFilter struct {
	Cursor    string     `json:"cursor,omitempty"`     // Cursor for pagination
	Limit     int        `json:"limit,omitempty"`      // Number of items per page
	TenantID  string     `json:"tenant_id,omitempty"`
	Status    PageStatus `json:"status,omitempty"`
	Public    *bool      `json:"public,omitempty"`
	InMenu    *bool      `json:"in_menu,omitempty"`
	Query     string     `json:"query,omitempty"`
	SortBy    string     `json:"sort_by,omitempty"`
	SortOrder string     `json:"sort_order,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page      int        `json:"page,omitempty"`
	PageSize  int        `json:"page_size,omitempty"`
}

// CommentFilter để filter comments
type CommentFilter struct {
	Cursor    string        `json:"cursor,omitempty"`    // Cursor for pagination
	Limit     int           `json:"limit,omitempty"`     // Number of items per page
	TenantID  string        `json:"tenant_id,omitempty"`
	PostID    string        `json:"post_id,omitempty"`
	Status    CommentStatus `json:"status,omitempty"`
	Approved  *bool         `json:"approved,omitempty"`
	Query     string        `json:"query,omitempty"`
	SortBy    string        `json:"sort_by,omitempty"`
	SortOrder string        `json:"sort_order,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page      int           `json:"page,omitempty"`
	PageSize  int           `json:"page_size,omitempty"`
}

// CategoryFilter để filter categories
type CategoryFilter struct {
	Cursor    string `json:"cursor,omitempty"`     // Cursor for pagination
	Limit     int    `json:"limit,omitempty"`      // Number of items per page
	TenantID  string `json:"tenant_id,omitempty"`
	Public    *bool  `json:"public,omitempty"`
	Query     string `json:"query,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
}

// TagFilter để filter tags
type TagFilter struct {
	Cursor    string `json:"cursor,omitempty"`     // Cursor for pagination
	Limit     int    `json:"limit,omitempty"`      // Number of items per page
	TenantID  string `json:"tenant_id,omitempty"`
	Query     string `json:"query,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
}

// MediaFilter để filter media
type MediaFilter struct {
	Cursor    string `json:"cursor,omitempty"`     // Cursor for pagination
	Limit     int    `json:"limit,omitempty"`      // Number of items per page
	TenantID  string `json:"tenant_id,omitempty"`
	Type      string `json:"type,omitempty"`
	Query     string `json:"query,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
}

// Update models

// PostUpdate để cập nhật post
type PostUpdate struct {
	Title           *string     `json:"title,omitempty"`
	Slug            *string     `json:"slug,omitempty"`
	Excerpt         *string     `json:"excerpt,omitempty"`
	Content         *string     `json:"content,omitempty"`
	Status          *PostStatus `json:"status,omitempty"`
	IsPublic        *bool       `json:"is_public,omitempty"`
	IsFeatured      *bool       `json:"is_featured,omitempty"`
	MetaTitle       *string     `json:"meta_title,omitempty"`
	MetaDescription *string     `json:"meta_description,omitempty"`
	MetaKeywords    *string     `json:"meta_keywords,omitempty"`
	FeaturedImage   *string     `json:"featured_image,omitempty"`
	FeaturedImageAlt *string    `json:"featured_image_alt,omitempty"`
	Gallery         []string    `json:"gallery,omitempty"`
	CategoryIDs     []string    `json:"category_ids,omitempty"`
	TagIDs          []string    `json:"tag_ids,omitempty"`
	ScheduledFor    *time.Time  `json:"scheduled_for,omitempty"`
}

// PageUpdate để cập nhật page
type PageUpdate struct {
	Title           *string     `json:"title,omitempty"`
	Slug            *string     `json:"slug,omitempty"`
	Content         *string     `json:"content,omitempty"`
	Status          *PageStatus `json:"status,omitempty"`
	IsPublic        *bool       `json:"is_public,omitempty"`
	Template        *string     `json:"template,omitempty"`
	MetaTitle       *string     `json:"meta_title,omitempty"`
	MetaDescription *string     `json:"meta_description,omitempty"`
	MetaKeywords    *string     `json:"meta_keywords,omitempty"`
	FeaturedImage   *string     `json:"featured_image,omitempty"`
	FeaturedImageAlt *string    `json:"featured_image_alt,omitempty"`
	ParentID        *string     `json:"parent_id,omitempty"`
	SortOrder       *int        `json:"sort_order,omitempty"`
	ShowInMenu      *bool       `json:"show_in_menu,omitempty"`
	MenuTitle       *string     `json:"menu_title,omitempty"`
}

// CategoryUpdate để cập nhật category
type CategoryUpdate struct {
	Name            *string `json:"name,omitempty"`
	Slug            *string `json:"slug,omitempty"`
	Description     *string `json:"description,omitempty"`
	Color           *string `json:"color,omitempty"`
	Icon            *string `json:"icon,omitempty"`
	Image           *string `json:"image,omitempty"`
	IsPublic        *bool   `json:"is_public,omitempty"`
	SortOrder       *int    `json:"sort_order,omitempty"`
	MetaTitle       *string `json:"meta_title,omitempty"`
	MetaDescription *string `json:"meta_description,omitempty"`
}

// TagUpdate để cập nhật tag
type TagUpdate struct {
	Name        *string `json:"name,omitempty"`
	Slug        *string `json:"slug,omitempty"`
	Description *string `json:"description,omitempty"`
	Color       *string `json:"color,omitempty"`
}

// Statistics models

// PostStatistics thống kê bài viết
type PostStatistics struct {
	PostID       string    `json:"post_id"`
	ViewCount    int64     `json:"view_count"`
	LikeCount    int64     `json:"like_count"`
	CommentCount int64     `json:"comment_count"`
	ShareCount   int64     `json:"share_count"`
	ReadingTime  int       `json:"reading_time"`
	LastViewedAt time.Time `json:"last_viewed_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// WebsiteStatistics thống kê tổng quan website
type WebsiteStatistics struct {
	TenantID         string    `json:"tenant_id"`
	TotalPosts       int64     `json:"total_posts"`
	PublishedPosts   int64     `json:"published_posts"`
	DraftPosts       int64     `json:"draft_posts"`
	TotalPages       int64     `json:"total_pages"`
	TotalCategories  int64     `json:"total_categories"`
	TotalTags        int64     `json:"total_tags"`
	TotalComments    int64     `json:"total_comments"`
	ApprovedComments int64     `json:"approved_comments"`
	PendingComments  int64     `json:"pending_comments"`
	TotalViews       int64     `json:"total_views"`
	TotalLikes       int64     `json:"total_likes"`
	TotalShares      int64     `json:"total_shares"`
	LastUpdated      time.Time `json:"last_updated"`
}