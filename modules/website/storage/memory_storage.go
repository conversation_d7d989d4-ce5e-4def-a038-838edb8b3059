package storage

import (
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/website/models"
)

// MemoryWebsiteRepository implements in-memory storage for website/blog content
type MemoryWebsiteRepository struct {
	posts      map[string]*models.Post
	categories map[string]*models.Category
	tags       map[string]*models.Tag
	pages      map[string]*models.Page
	comments   map[string]*models.Comment
	media      map[string]*models.Media
	mu         sync.RWMutex
}

// NewMemoryWebsiteRepository creates a new in-memory website repository
func NewMemoryWebsiteRepository() *MemoryWebsiteRepository {
	repo := &MemoryWebsiteRepository{
		posts:      make(map[string]*models.Post),
		categories: make(map[string]*models.Category),
		tags:       make(map[string]*models.Tag),
		pages:      make(map[string]*models.Page),
		comments:   make(map[string]*models.Comment),
		media:      make(map[string]*models.Media),
	}
	
	// Seed with some mock data
	repo.seedData()
	return repo
}

func (r *MemoryWebsiteRepository) seedData() {
	now := time.Now()
	
	// Seed categories
	categories := []*models.Category{
		{
			ID:          "cat-1",
			TenantID:    "tenant-1",
			Name:        "Technology",
			Slug:        "technology",
			Description: "Technology related posts",
			IsPublic:    true,
			PostCount:   2,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		{
			ID:          "cat-2",
			TenantID:    "tenant-1",
			Name:        "Business",
			Slug:        "business",
			Description: "Business related posts",
			IsPublic:    true,
			PostCount:   1,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
	}
	
	for _, cat := range categories {
		r.categories[cat.ID] = cat
	}
	
	// Seed tags
	tags := []*models.Tag{
		{
			ID:        "tag-1",
			TenantID:  "tenant-1",
			Name:      "golang",
			Slug:      "golang",
			PostCount: 2,
			CreatedAt: now,
			UpdatedAt: now,
		},
		{
			ID:        "tag-2",
			TenantID:  "tenant-1",
			Name:      "api",
			Slug:      "api",
			PostCount: 1,
			CreatedAt: now,
			UpdatedAt: now,
		},
	}
	
	for _, tag := range tags {
		r.tags[tag.ID] = tag
	}
	
	// Seed posts
	posts := []*models.Post{
		{
			ID:          "post-1",
			TenantID:    "tenant-1",
			AuthorID:    "user-1",
			Title:       "Getting Started with Go",
			Slug:        "getting-started-with-go",
			Content:     "This is a comprehensive guide to getting started with Go programming language...",
			Excerpt:     "Learn the basics of Go programming",
			Status:      models.PostStatusPublished,
			FeaturedImage: "/images/go-featured.jpg",
			ViewCount:   150,
			LikeCount:   25,
			CommentCount: 5,
			PublishedAt: &now,
			CreatedAt:   now,
			UpdatedAt:   now,
			MetaTitle:       "Getting Started with Go - Complete Guide",
			MetaDescription: "Learn Go programming from scratch with this comprehensive guide",
			MetaKeywords:    "go,golang,programming,tutorial",
		},
		{
			ID:          "post-2",
			TenantID:    "tenant-1",
			AuthorID:    "user-1",
			Title:       "Building REST APIs in Go",
			Slug:        "building-rest-apis-in-go",
			Content:     "Learn how to build robust REST APIs using Go and popular frameworks...",
			Excerpt:     "Build robust REST APIs with Go",
			Status:      models.PostStatusPublished,
			ViewCount:   89,
			LikeCount:   12,
			CommentCount: 3,
			PublishedAt: &now,
			CreatedAt:   now,
			UpdatedAt:   now,
			MetaTitle:       "Building REST APIs in Go",
			MetaDescription: "Learn to build REST APIs with Go programming language",
			MetaKeywords:    "go,api,rest,web development",
		},
	}
	
	for _, post := range posts {
		r.posts[post.ID] = post
	}
	
	// Seed pages
	pages := []*models.Page{
		{
			ID:        "page-1",
			TenantID:  "tenant-1",
			AuthorID:  "user-1",
			Title:     "About Us",
			Slug:      "about",
			Content:   "Welcome to our blog! We share insights about technology and business...",
			Status:    models.PageStatusPublished,
			Template:  "default",
			PublishedAt: &now,
			CreatedAt: now,
			UpdatedAt: now,
			MetaTitle:       "About Us - Our Blog",
			MetaDescription: "Learn more about our blog and our mission",
			MetaKeywords:    "about,blog,company",
		},
	}
	
	for _, page := range pages {
		r.pages[page.ID] = page
	}
}

// Post methods
func (r *MemoryWebsiteRepository) CreatePost(post *models.Post) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post.CreatedAt = time.Now()
	post.UpdatedAt = time.Now()
	r.posts[post.ID] = post
	return nil
}

func (r *MemoryWebsiteRepository) GetPostByID(id string) (*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if post, exists := r.posts[id]; exists {
		return post, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) GetPostBySlug(slug string) (*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, post := range r.posts {
		if post.Slug == slug {
			return post, nil
		}
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) UpdatePost(post *models.Post) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.posts[post.ID]; !exists {
		return ErrNotFound
	}
	
	post.UpdatedAt = time.Now()
	r.posts[post.ID] = post
	return nil
}

func (r *MemoryWebsiteRepository) DeletePost(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.posts[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.posts, id)
	return nil
}

func (r *MemoryWebsiteRepository) ListPosts(filter *models.PostFilter) ([]*models.Post, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	posts := make([]*models.Post, 0)
	
	for _, post := range r.posts {
		if filter.TenantID != "" && post.TenantID != filter.TenantID {
			continue
		}
		if filter.AuthorID != "" && post.AuthorID != filter.AuthorID {
			continue
		}
		if filter.Status != "" && post.Status != filter.Status {
			continue
		}
		// Note: Category filtering would need to check post-category relationships
		// For now, skip category filtering in this mock implementation
		posts = append(posts, post)
	}
	
	return posts, int64(len(posts)), nil
}

// Category methods
func (r *MemoryWebsiteRepository) CreateCategory(category *models.Category) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	category.CreatedAt = time.Now()
	category.UpdatedAt = time.Now()
	r.categories[category.ID] = category
	return nil
}

func (r *MemoryWebsiteRepository) GetCategoryByID(id string) (*models.Category, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if category, exists := r.categories[id]; exists {
		return category, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) ListCategories(filter *models.CategoryFilter) ([]*models.Category, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	categories := make([]*models.Category, 0)
	
	for _, category := range r.categories {
		if filter.TenantID != "" && category.TenantID != filter.TenantID {
			continue
		}
		if filter.Public != nil && category.IsPublic != *filter.Public {
			continue
		}
		categories = append(categories, category)
	}
	
	return categories, int64(len(categories)), nil
}

// Tag methods
func (r *MemoryWebsiteRepository) CreateTag(tag *models.Tag) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	tag.CreatedAt = time.Now()
	tag.UpdatedAt = time.Now()
	r.tags[tag.ID] = tag
	return nil
}

func (r *MemoryWebsiteRepository) GetTagByID(id string) (*models.Tag, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if tag, exists := r.tags[id]; exists {
		return tag, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) ListTags(filter *models.TagFilter) ([]*models.Tag, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	tags := make([]*models.Tag, 0)
	
	for _, tag := range r.tags {
		if filter.TenantID != "" && tag.TenantID != filter.TenantID {
			continue
		}
		tags = append(tags, tag)
	}
	
	return tags, int64(len(tags)), nil
}

// Page methods
func (r *MemoryWebsiteRepository) CreatePage(page *models.Page) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	page.CreatedAt = time.Now()
	page.UpdatedAt = time.Now()
	r.pages[page.ID] = page
	return nil
}

func (r *MemoryWebsiteRepository) GetPageByID(id string) (*models.Page, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if page, exists := r.pages[id]; exists {
		return page, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) GetPageBySlug(slug string) (*models.Page, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, page := range r.pages {
		if page.Slug == slug {
			return page, nil
		}
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) ListPages(filter *models.PageFilter) ([]*models.Page, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	pages := make([]*models.Page, 0)
	
	for _, page := range r.pages {
		if filter.TenantID != "" && page.TenantID != filter.TenantID {
			continue
		}
		if filter.Status != "" && page.Status != filter.Status {
			continue
		}
		pages = append(pages, page)
	}
	
	return pages, int64(len(pages)), nil
}

// Comment methods
func (r *MemoryWebsiteRepository) CreateComment(comment *models.Comment) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	comment.CreatedAt = time.Now()
	comment.UpdatedAt = time.Now()
	r.comments[comment.ID] = comment
	return nil
}

func (r *MemoryWebsiteRepository) GetCommentByID(id string) (*models.Comment, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if comment, exists := r.comments[id]; exists {
		return comment, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) ListComments(filter *models.CommentFilter) ([]*models.Comment, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	comments := make([]*models.Comment, 0)
	
	for _, comment := range r.comments {
		if filter.PostID != "" && comment.PostID != filter.PostID {
			continue
		}
		if filter.Status != "" && comment.Status != filter.Status {
			continue
		}
		comments = append(comments, comment)
	}
	
	return comments, int64(len(comments)), nil
}

// Media methods
func (r *MemoryWebsiteRepository) CreateMedia(media *models.Media) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	media.CreatedAt = time.Now()
	media.UpdatedAt = time.Now()
	r.media[media.ID] = media
	return nil
}

func (r *MemoryWebsiteRepository) GetMediaByID(id string) (*models.Media, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if media, exists := r.media[id]; exists {
		return media, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryWebsiteRepository) ListMedia(filter *models.MediaFilter) ([]*models.Media, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	mediaList := make([]*models.Media, 0)
	
	for _, media := range r.media {
		if filter.TenantID != "" && media.TenantID != filter.TenantID {
			continue
		}
		// Note: Media type filtering would need MediaType field
		// For now, skip type filtering in this mock implementation
		mediaList = append(mediaList, media)
	}
	
	return mediaList, int64(len(mediaList)), nil
}

// Error definitions
var (
	ErrNotFound = &StorageError{Code: "NOT_FOUND", Message: "Record not found"}
)

// StorageError represents a storage error
type StorageError struct {
	Code    string
	Message string
}

func (e *StorageError) Error() string {
	return e.Message
}