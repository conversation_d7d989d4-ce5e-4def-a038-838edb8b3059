package repositories

import (
	"context"

	"github.com/blog-api-v3/blog-api-v3/modules/website/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// PostRepository định ngh<PERSON>a interface cho post data access
type PostRepository interface {
	// Create tạo post mới
	Create(ctx context.Context, post *models.Post) error
	
	// GetByID lấy post theo ID
	GetByID(ctx context.Context, postID string) (*models.Post, error)
	
	// GetBySlug lấy post theo slug
	GetBySlug(ctx context.Context, tenantID, slug string) (*models.Post, error)
	
	// Update cập nhật post
	Update(ctx context.Context, postID string, updates map[string]interface{}) error
	
	// Delete xóa mềm post
	Delete(ctx context.Context, postID string) error
	
	// HardDelete xóa vĩnh viễn post
	HardDelete(ctx context.Context, postID string) error
	
	// List liệt kê posts với filter
	List(ctx context.Context, filter *models.PostFilter) ([]*models.Post, int64, error)
	
	// ListWithCursor liệt kê posts với cursor-based pagination
	ListWithCursor(ctx context.Context, filter *models.PostFilter) ([]*models.Post, *pagination.CursorResponse, error)
	
	// Search tìm kiếm posts
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Post, error)
	
	// Count đếm số posts
	Count(ctx context.Context, filter *models.PostFilter) (int64, error)
	
	// Exists kiểm tra post tồn tại
	Exists(ctx context.Context, postID string) (bool, error)
	
	// ExistsBySlug kiểm tra slug tồn tại
	ExistsBySlug(ctx context.Context, tenantID, slug string) (bool, error)
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, postID string, status models.PostStatus) error
	
	// UpdateViewCount tăng view count
	UpdateViewCount(ctx context.Context, postID string) error
	
	// UpdateLikeCount cập nhật like count
	UpdateLikeCount(ctx context.Context, postID string, increment bool) error
	
	// GetPublished lấy posts đã publish
	GetPublished(ctx context.Context, tenantID string, limit int) ([]*models.Post, error)
	
	// GetFeatured lấy posts featured
	GetFeatured(ctx context.Context, tenantID string, limit int) ([]*models.Post, error)
	
	// GetByCategory lấy posts theo category (deprecated)
	GetByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// GetByCategoryWithCursor lấy posts theo category với cursor pagination
	GetByCategoryWithCursor(ctx context.Context, categoryID, cursor string, limit int) ([]*models.Post, *pagination.CursorResponse, error)
	
	// GetByTag lấy posts theo tag (deprecated)
	GetByTag(ctx context.Context, tagID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// GetByTagWithCursor lấy posts theo tag với cursor pagination
	GetByTagWithCursor(ctx context.Context, tagID, cursor string, limit int) ([]*models.Post, *pagination.CursorResponse, error)
	
	// GetByAuthor lấy posts theo author (deprecated)
	GetByAuthor(ctx context.Context, authorID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// GetByAuthorWithCursor lấy posts theo author với cursor pagination
	GetByAuthorWithCursor(ctx context.Context, authorID, cursor string, limit int) ([]*models.Post, *pagination.CursorResponse, error)
	
	// GetRelated lấy posts liên quan
	GetRelated(ctx context.Context, postID string, limit int) ([]*models.Post, error)
	
	// GetPopular lấy posts phổ biến
	GetPopular(ctx context.Context, tenantID string, days, limit int) ([]*models.Post, error)
	
	// GetScheduled lấy posts đã schedule
	GetScheduled(ctx context.Context, tenantID string) ([]*models.Post, error)
	
	// PublishScheduled publish posts đã schedule
	PublishScheduled(ctx context.Context) error
	
	// AttachCategories gắn categories vào post
	AttachCategories(ctx context.Context, postID string, categoryIDs []string) error
	
	// AttachTags gắn tags vào post
	AttachTags(ctx context.Context, postID string, tagIDs []string) error
	
	// DetachCategories gỡ categories khỏi post
	DetachCategories(ctx context.Context, postID string, categoryIDs []string) error
	
	// DetachTags gỡ tags khỏi post
	DetachTags(ctx context.Context, postID string, tagIDs []string) error
	
	// GetStatistics lấy thống kê post
	GetStatistics(ctx context.Context, postID string) (*models.PostStatistics, error)
}

// PageRepository định nghĩa interface cho page data access
type PageRepository interface {
	// Create tạo page mới
	Create(ctx context.Context, page *models.Page) error
	
	// GetByID lấy page theo ID
	GetByID(ctx context.Context, pageID string) (*models.Page, error)
	
	// GetBySlug lấy page theo slug
	GetBySlug(ctx context.Context, tenantID, slug string) (*models.Page, error)
	
	// Update cập nhật page
	Update(ctx context.Context, pageID string, updates map[string]interface{}) error
	
	// Delete xóa mềm page
	Delete(ctx context.Context, pageID string) error
	
	// HardDelete xóa vĩnh viễn page
	HardDelete(ctx context.Context, pageID string) error
	
	// List liệt kê pages với filter
	List(ctx context.Context, filter *models.PageFilter) ([]*models.Page, int64, error)
	
	// Search tìm kiếm pages
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Page, error)
	
	// Count đếm số pages
	Count(ctx context.Context, filter *models.PageFilter) (int64, error)
	
	// Exists kiểm tra page tồn tại
	Exists(ctx context.Context, pageID string) (bool, error)
	
	// ExistsBySlug kiểm tra slug tồn tại
	ExistsBySlug(ctx context.Context, tenantID, slug string) (bool, error)
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, pageID string, status models.PageStatus) error
	
	// UpdateViewCount tăng view count
	UpdateViewCount(ctx context.Context, pageID string) error
	
	// GetMenuPages lấy pages hiển thị trong menu
	GetMenuPages(ctx context.Context, tenantID string) ([]*models.Page, error)
	
	// GetChildren lấy pages con
	GetChildren(ctx context.Context, parentID string) ([]*models.Page, error)
	
	// GetHierarchy lấy cây phân cấp pages
	GetHierarchy(ctx context.Context, tenantID string) ([]*models.Page, error)
	
	// UpdateSortOrder cập nhật thứ tự sắp xếp
	UpdateSortOrder(ctx context.Context, pageID string, sortOrder int) error
}

// CategoryRepository định nghĩa interface cho category data access
type CategoryRepository interface {
	// Create tạo category mới
	Create(ctx context.Context, category *models.Category) error
	
	// GetByID lấy category theo ID
	GetByID(ctx context.Context, categoryID string) (*models.Category, error)
	
	// GetBySlug lấy category theo slug
	GetBySlug(ctx context.Context, tenantID, slug string) (*models.Category, error)
	
	// Update cập nhật category
	Update(ctx context.Context, categoryID string, updates map[string]interface{}) error
	
	// Delete xóa mềm category
	Delete(ctx context.Context, categoryID string) error
	
	// HardDelete xóa vĩnh viễn category
	HardDelete(ctx context.Context, categoryID string) error
	
	// List liệt kê categories
	List(ctx context.Context, tenantID string, publicOnly bool) ([]*models.Category, error)
	
	// Search tìm kiếm categories
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Category, error)
	
	// Count đếm số categories
	Count(ctx context.Context, tenantID string) (int64, error)
	
	// Exists kiểm tra category tồn tại
	Exists(ctx context.Context, categoryID string) (bool, error)
	
	// ExistsBySlug kiểm tra slug tồn tại
	ExistsBySlug(ctx context.Context, tenantID, slug string) (bool, error)
	
	// UpdatePostCount cập nhật số lượng posts
	UpdatePostCount(ctx context.Context, categoryID string) error
	
	// GetPopular lấy categories phổ biến
	GetPopular(ctx context.Context, tenantID string, limit int) ([]*models.Category, error)
	
	// UpdateSortOrder cập nhật thứ tự sắp xếp
	UpdateSortOrder(ctx context.Context, categoryID string, sortOrder int) error
}

// TagRepository định nghĩa interface cho tag data access
type TagRepository interface {
	// Create tạo tag mới
	Create(ctx context.Context, tag *models.Tag) error
	
	// GetByID lấy tag theo ID
	GetByID(ctx context.Context, tagID string) (*models.Tag, error)
	
	// GetBySlug lấy tag theo slug
	GetBySlug(ctx context.Context, tenantID, slug string) (*models.Tag, error)
	
	// GetByName lấy tag theo name
	GetByName(ctx context.Context, tenantID, name string) (*models.Tag, error)
	
	// Update cập nhật tag
	Update(ctx context.Context, tagID string, updates map[string]interface{}) error
	
	// Delete xóa mềm tag
	Delete(ctx context.Context, tagID string) error
	
	// HardDelete xóa vĩnh viễn tag
	HardDelete(ctx context.Context, tagID string) error
	
	// List liệt kê tags
	List(ctx context.Context, tenantID string) ([]*models.Tag, error)
	
	// Search tìm kiếm tags
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Tag, error)
	
	// Count đếm số tags
	Count(ctx context.Context, tenantID string) (int64, error)
	
	// Exists kiểm tra tag tồn tại
	Exists(ctx context.Context, tagID string) (bool, error)
	
	// ExistsBySlug kiểm tra slug tồn tại
	ExistsBySlug(ctx context.Context, tenantID, slug string) (bool, error)
	
	// ExistsByName kiểm tra name tồn tại
	ExistsByName(ctx context.Context, tenantID, name string) (bool, error)
	
	// UpdatePostCount cập nhật số lượng posts
	UpdatePostCount(ctx context.Context, tagID string) error
	
	// GetPopular lấy tags phổ biến
	GetPopular(ctx context.Context, tenantID string, limit int) ([]*models.Tag, error)
	
	// GetOrCreate lấy hoặc tạo tag mới
	GetOrCreate(ctx context.Context, tenantID, name string) (*models.Tag, error)
	
	// BatchGetOrCreate lấy hoặc tạo nhiều tags
	BatchGetOrCreate(ctx context.Context, tenantID string, names []string) ([]*models.Tag, error)
}

// CommentRepository định nghĩa interface cho comment data access
type CommentRepository interface {
	// Create tạo comment mới
	Create(ctx context.Context, comment *models.Comment) error
	
	// GetByID lấy comment theo ID
	GetByID(ctx context.Context, commentID string) (*models.Comment, error)
	
	// Update cập nhật comment
	Update(ctx context.Context, commentID string, updates map[string]interface{}) error
	
	// Delete xóa mềm comment
	Delete(ctx context.Context, commentID string) error
	
	// HardDelete xóa vĩnh viễn comment
	HardDelete(ctx context.Context, commentID string) error
	
	// List liệt kê comments với filter
	List(ctx context.Context, filter *models.CommentFilter) ([]*models.Comment, int64, error)
	
	// Search tìm kiếm comments
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Comment, error)
	
	// Count đếm số comments
	Count(ctx context.Context, filter *models.CommentFilter) (int64, error)
	
	// Exists kiểm tra comment tồn tại
	Exists(ctx context.Context, commentID string) (bool, error)
	
	// UpdateStatus cập nhật status
	UpdateStatus(ctx context.Context, commentID string, status models.CommentStatus) error
	
	// Approve phê duyệt comment
	Approve(ctx context.Context, commentID string) error
	
	// Reject từ chối comment
	Reject(ctx context.Context, commentID string) error
	
	// MarkAsSpam đánh dấu spam
	MarkAsSpam(ctx context.Context, commentID string) error
	
	// GetByPost lấy comments theo post
	GetByPost(ctx context.Context, postID string, approved bool, page, pageSize int) ([]*models.Comment, int64, error)
	
	// GetReplies lấy replies của comment
	GetReplies(ctx context.Context, commentID string) ([]*models.Comment, error)
	
	// GetPending lấy comments chờ duyệt
	GetPending(ctx context.Context, tenantID string, limit int) ([]*models.Comment, error)
	
	// UpdateLikeCount cập nhật like count
	UpdateLikeCount(ctx context.Context, commentID string, increment bool) error
	
	// UpdateReplyCount cập nhật reply count
	UpdateReplyCount(ctx context.Context, commentID string) error
	
	// BatchApprove phê duyệt nhiều comments
	BatchApprove(ctx context.Context, commentIDs []string) error
	
	// BatchReject từ chối nhiều comments
	BatchReject(ctx context.Context, commentIDs []string) error
	
	// BatchDelete xóa nhiều comments
	BatchDelete(ctx context.Context, commentIDs []string) error
}

// MediaRepository định nghĩa interface cho media data access
type MediaRepository interface {
	// Create tạo media mới
	Create(ctx context.Context, media *models.Media) error
	
	// GetByID lấy media theo ID
	GetByID(ctx context.Context, mediaID string) (*models.Media, error)
	
	// Update cập nhật media
	Update(ctx context.Context, mediaID string, updates map[string]interface{}) error
	
	// Delete xóa mềm media
	Delete(ctx context.Context, mediaID string) error
	
	// HardDelete xóa vĩnh viễn media
	HardDelete(ctx context.Context, mediaID string) error
	
	// List liệt kê media files
	List(ctx context.Context, tenantID string, page, pageSize int) ([]*models.Media, int64, error)
	
	// Search tìm kiếm media
	Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Media, error)
	
	// Count đếm số media files
	Count(ctx context.Context, tenantID string) (int64, error)
	
	// Exists kiểm tra media tồn tại
	Exists(ctx context.Context, mediaID string) (bool, error)
	
	// GetByType lấy media theo mime type
	GetByType(ctx context.Context, tenantID, mimeType string, page, pageSize int) ([]*models.Media, int64, error)
	
	// GetByUploader lấy media theo uploader
	GetByUploader(ctx context.Context, uploaderID string, page, pageSize int) ([]*models.Media, int64, error)
	
	// GetTotalSize tính tổng dung lượng
	GetTotalSize(ctx context.Context, tenantID string) (int64, error)
	
	// CleanupOrphaned xóa files không được sử dụng
	CleanupOrphaned(ctx context.Context, days int) error
}

// WebsiteRepository định nghĩa interface cho website statistics
type WebsiteRepository interface {
	// GetStatistics lấy thống kê tổng quan
	GetStatistics(ctx context.Context, tenantID string) (*models.WebsiteStatistics, error)
	
	// UpdateStatistics cập nhật thống kê
	UpdateStatistics(ctx context.Context, tenantID string) error
	
	// GetPostStatistics lấy thống kê bài viết
	GetPostStatistics(ctx context.Context, postID string) (*models.PostStatistics, error)
	
	// UpdatePostStatistics cập nhật thống kê bài viết
	UpdatePostStatistics(ctx context.Context, postID string, stats *models.PostStatistics) error
}