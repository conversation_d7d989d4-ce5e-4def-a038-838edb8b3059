package repositories

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/website/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockPostRepository implements PostRepository với in-memory storage
type MockPostRepository struct {
	posts      map[string]*models.Post
	postsBySlug map[string]*models.Post // key: tenantID:slug
	mu         sync.RWMutex
}

// NewMockPostRepository tạo mock post repository mới
func NewMockPostRepository() *MockPostRepository {
	repo := &MockPostRepository{
		posts:       make(map[string]*models.Post),
		postsBySlug: make(map[string]*models.Post),
	}
	
	// Seed với mock data
	repo.seedMockData()
	
	return repo
}

// seedMockData thêm dữ liệu posts mẫu
func (r *MockPostRepository) seedMockData() {
	now := time.Now()
	
	// Sample posts
	posts := []*models.Post{
		{
			ID:          utils.GenerateULID(),
			TenantID:    "tenant-1",
			AuthorID:    "author-1",
			Title:       "Getting Started with Go Programming",
			Slug:        "getting-started-with-go-programming",
			Excerpt:     "Learn the basics of Go programming language in this comprehensive guide.",
			Content:     "Go, also known as Golang, is a statically typed, compiled programming language designed at Google...",
			Status:      models.PostStatusPublished,
			IsPublic:    true,
			IsFeatured:  true,
			MetaTitle:   "Getting Started with Go Programming - Complete Guide",
			MetaDescription: "Learn Go programming from scratch with this complete beginner's guide. Covers syntax, concepts, and best practices.",
			MetaKeywords: "go, golang, programming, tutorial, beginner",
			PublishedAt: &now,
			FeaturedImage: "https://example.com/images/go-programming.jpg",
			ViewCount:   150,
			LikeCount:   25,
			CommentCount: 8,
			ShareCount:  12,
			ReadingTime: 8,
			CreatedAt:   now.AddDate(0, 0, -10),
			UpdatedAt:   now.AddDate(0, 0, -5),
			AuthorName:  "John Doe",
			AuthorEmail: "<EMAIL>",
		},
		{
			ID:          utils.GenerateULID(),
			TenantID:    "tenant-1",
			AuthorID:    "author-2",
			Title:       "Advanced REST API Design Patterns",
			Slug:        "advanced-rest-api-design-patterns",
			Excerpt:     "Explore advanced patterns and best practices for designing scalable REST APIs.",
			Content:     "When building REST APIs at scale, it's important to follow established patterns and practices...",
			Status:      models.PostStatusPublished,
			IsPublic:    true,
			IsFeatured:  false,
			MetaTitle:   "Advanced REST API Design Patterns and Best Practices",
			MetaDescription: "Learn advanced REST API design patterns for building scalable and maintainable web services.",
			MetaKeywords: "rest api, design patterns, web services, architecture",
			PublishedAt: &now,
			FeaturedImage: "https://example.com/images/rest-api.jpg",
			ViewCount:   89,
			LikeCount:   15,
			CommentCount: 5,
			ShareCount:  8,
			ReadingTime: 12,
			CreatedAt:   now.AddDate(0, 0, -7),
			UpdatedAt:   now.AddDate(0, 0, -2),
			AuthorName:  "Jane Smith",
			AuthorEmail: "<EMAIL>",
		},
		{
			ID:          utils.GenerateULID(),
			TenantID:    "tenant-1",
			AuthorID:    "author-1",
			Title:       "Building Microservices with Docker",
			Slug:        "building-microservices-with-docker",
			Excerpt:     "Learn how to containerize and deploy microservices using Docker and Docker Compose.",
			Content:     "Microservices architecture has become increasingly popular for building scalable applications...",
			Status:      models.PostStatusDraft,
			IsPublic:    false,
			IsFeatured:  false,
			MetaTitle:   "Building Microservices with Docker - Complete Tutorial",
			MetaDescription: "Step-by-step guide to building and deploying microservices using Docker containers.",
			MetaKeywords: "microservices, docker, containers, deployment, devops",
			FeaturedImage: "https://example.com/images/docker-microservices.jpg",
			ViewCount:   0,
			LikeCount:   0,
			CommentCount: 0,
			ShareCount:  0,
			ReadingTime: 15,
			CreatedAt:   now.AddDate(0, 0, -3),
			UpdatedAt:   now.AddDate(0, 0, -1),
			AuthorName:  "John Doe",
			AuthorEmail: "<EMAIL>",
		},
		{
			ID:          utils.GenerateULID(),
			TenantID:    "tenant-2",
			AuthorID:    "author-3",
			Title:       "Machine Learning Fundamentals",
			Slug:        "machine-learning-fundamentals",
			Excerpt:     "Introduction to machine learning concepts and algorithms for beginners.",
			Content:     "Machine learning is a subset of artificial intelligence that enables computers to learn...",
			Status:      models.PostStatusPublished,
			IsPublic:    true,
			IsFeatured:  true,
			MetaTitle:   "Machine Learning Fundamentals for Beginners",
			MetaDescription: "Learn the basics of machine learning with practical examples and clear explanations.",
			MetaKeywords: "machine learning, ai, algorithms, data science",
			PublishedAt: &now,
			FeaturedImage: "https://example.com/images/ml-fundamentals.jpg",
			ViewCount:   234,
			LikeCount:   45,
			CommentCount: 18,
			ShareCount:  28,
			ReadingTime: 10,
			CreatedAt:   now.AddDate(0, 0, -15),
			UpdatedAt:   now.AddDate(0, 0, -8),
			AuthorName:  "Alice Johnson",
			AuthorEmail: "<EMAIL>",
		},
	}
	
	// Store posts
	for _, post := range posts {
		r.posts[post.ID] = post
		slugKey := post.TenantID + ":" + post.Slug
		r.postsBySlug[slugKey] = post
	}
}

// Create tạo post mới
func (r *MockPostRepository) Create(ctx context.Context, post *models.Post) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Check duplicates
	if _, exists := r.posts[post.ID]; exists {
		return errors.New("post already exists")
	}
	
	slugKey := post.TenantID + ":" + post.Slug
	if _, exists := r.postsBySlug[slugKey]; exists {
		return errors.New("slug already exists in this tenant")
	}
	
	// Set defaults
	if post.ID == "" {
		post.ID = utils.GenerateULID()
	}
	if post.Status == "" {
		post.Status = models.PostStatusDraft
	}
	
	now := time.Now()
	post.CreatedAt = now
	post.UpdatedAt = now
	
	// Calculate reading time (rough estimate: 200 words per minute)
	wordCount := len(strings.Fields(post.Content))
	post.ReadingTime = max(1, wordCount/200)
	
	// Store post
	r.posts[post.ID] = post
	r.postsBySlug[slugKey] = post
	
	return nil
}

// GetByID lấy post theo ID
func (r *MockPostRepository) GetByID(ctx context.Context, postID string) (*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return nil, errors.New("post not found")
	}
	
	return post, nil
}

// GetBySlug lấy post theo slug
func (r *MockPostRepository) GetBySlug(ctx context.Context, tenantID, slug string) (*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	slugKey := tenantID + ":" + slug
	post, exists := r.postsBySlug[slugKey]
	if !exists {
		return nil, errors.New("post not found")
	}
	
	return post, nil
}

// Update cập nhật post
func (r *MockPostRepository) Update(ctx context.Context, postID string, updates map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return errors.New("post not found")
	}
	
	// Apply updates
	for key, value := range updates {
		switch key {
		case "title":
			post.Title = value.(string)
		case "slug":
			// Update slug mapping
			oldSlugKey := post.TenantID + ":" + post.Slug
			newSlug := value.(string)
			newSlugKey := post.TenantID + ":" + newSlug
			delete(r.postsBySlug, oldSlugKey)
			post.Slug = newSlug
			r.postsBySlug[newSlugKey] = post
		case "excerpt":
			post.Excerpt = value.(string)
		case "content":
			post.Content = value.(string)
			// Recalculate reading time
			wordCount := len(strings.Fields(post.Content))
			post.ReadingTime = max(1, wordCount/200)
		case "status":
			post.Status = value.(models.PostStatus)
		case "is_public":
			post.IsPublic = value.(bool)
		case "is_featured":
			post.IsFeatured = value.(bool)
		case "meta_title":
			post.MetaTitle = value.(string)
		case "meta_description":
			post.MetaDescription = value.(string)
		case "meta_keywords":
			post.MetaKeywords = value.(string)
		case "featured_image":
			post.FeaturedImage = value.(string)
		case "featured_image_alt":
			post.FeaturedImageAlt = value.(string)
		case "gallery":
			post.Gallery = value.([]string)
		case "published_at":
			if v, ok := value.(*time.Time); ok {
				post.PublishedAt = v
			}
		case "scheduled_for":
			if v, ok := value.(*time.Time); ok {
				post.ScheduledFor = v
			}
		}
	}
	
	post.UpdatedAt = time.Now()
	return nil
}

// Delete xóa mềm post
func (r *MockPostRepository) Delete(ctx context.Context, postID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return errors.New("post not found")
	}
	
	now := time.Now()
	post.DeletedAt = &now
	
	return nil
}

// HardDelete xóa vĩnh viễn post
func (r *MockPostRepository) HardDelete(ctx context.Context, postID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return errors.New("post not found")
	}
	
	slugKey := post.TenantID + ":" + post.Slug
	delete(r.posts, postID)
	delete(r.postsBySlug, slugKey)
	
	return nil
}

// List liệt kê posts với filter
func (r *MockPostRepository) List(ctx context.Context, filter *models.PostFilter) ([]*models.Post, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// Apply filters
	var filtered []*models.Post
	for _, post := range r.posts {
		if r.matchesFilter(post, filter) {
			filtered = append(filtered, post)
		}
	}
	
	total := int64(len(filtered))
	
	// Apply pagination
	page := filter.Page
	if page < 1 {
		page = 1
	}
	pageSize := filter.PageSize
	if pageSize < 1 {
		pageSize = 20
	}
	
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= len(filtered) {
		return []*models.Post{}, total, nil
	}
	
	if end > len(filtered) {
		end = len(filtered)
	}
	
	return filtered[start:end], total, nil
}

// Search tìm kiếm posts
func (r *MockPostRepository) Search(ctx context.Context, tenantID, query string, limit int) ([]*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	query = strings.ToLower(query)
	var results []*models.Post
	
	for _, post := range r.posts {
		if post.TenantID != tenantID || post.DeletedAt != nil {
			continue
		}
		
		if strings.Contains(strings.ToLower(post.Title), query) ||
			strings.Contains(strings.ToLower(post.Content), query) ||
			strings.Contains(strings.ToLower(post.Excerpt), query) {
			results = append(results, post)
			
			if limit > 0 && len(results) >= limit {
				break
			}
		}
	}
	
	return results, nil
}

// Count đếm số posts
func (r *MockPostRepository) Count(ctx context.Context, filter *models.PostFilter) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := int64(0)
	for _, post := range r.posts {
		if r.matchesFilter(post, filter) {
			count++
		}
	}
	
	return count, nil
}

// Exists kiểm tra post tồn tại
func (r *MockPostRepository) Exists(ctx context.Context, postID string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	_, exists := r.posts[postID]
	return exists, nil
}

// ExistsBySlug kiểm tra slug tồn tại
func (r *MockPostRepository) ExistsBySlug(ctx context.Context, tenantID, slug string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	slugKey := tenantID + ":" + slug
	_, exists := r.postsBySlug[slugKey]
	return exists, nil
}

// UpdateStatus cập nhật status
func (r *MockPostRepository) UpdateStatus(ctx context.Context, postID string, status models.PostStatus) error {
	return r.Update(ctx, postID, map[string]interface{}{
		"status": status,
	})
}

// UpdateViewCount tăng view count
func (r *MockPostRepository) UpdateViewCount(ctx context.Context, postID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return errors.New("post not found")
	}
	
	post.ViewCount++
	return nil
}

// UpdateLikeCount cập nhật like count
func (r *MockPostRepository) UpdateLikeCount(ctx context.Context, postID string, increment bool) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return errors.New("post not found")
	}
	
	if increment {
		post.LikeCount++
	} else {
		if post.LikeCount > 0 {
			post.LikeCount--
		}
	}
	
	return nil
}

// GetPublished lấy posts đã publish
func (r *MockPostRepository) GetPublished(ctx context.Context, tenantID string, limit int) ([]*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var published []*models.Post
	for _, post := range r.posts {
		if post.TenantID == tenantID &&
			post.Status == models.PostStatusPublished &&
			post.IsPublic &&
			post.DeletedAt == nil {
			published = append(published, post)
			
			if limit > 0 && len(published) >= limit {
				break
			}
		}
	}
	
	return published, nil
}

// GetFeatured lấy posts featured
func (r *MockPostRepository) GetFeatured(ctx context.Context, tenantID string, limit int) ([]*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var featured []*models.Post
	for _, post := range r.posts {
		if post.TenantID == tenantID &&
			post.Status == models.PostStatusPublished &&
			post.IsPublic &&
			post.IsFeatured &&
			post.DeletedAt == nil {
			featured = append(featured, post)
			
			if limit > 0 && len(featured) >= limit {
				break
			}
		}
	}
	
	return featured, nil
}

// GetByCategory lấy posts theo category
func (r *MockPostRepository) GetByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*models.Post, int64, error) {
	// Mock implementation - in real implementation would join with post_categories table
	return []*models.Post{}, 0, nil
}

// GetByTag lấy posts theo tag
func (r *MockPostRepository) GetByTag(ctx context.Context, tagID string, page, pageSize int) ([]*models.Post, int64, error) {
	// Mock implementation - in real implementation would join with post_tags table
	return []*models.Post{}, 0, nil
}

// GetByAuthor lấy posts theo author
func (r *MockPostRepository) GetByAuthor(ctx context.Context, authorID string, page, pageSize int) ([]*models.Post, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var authorPosts []*models.Post
	for _, post := range r.posts {
		if post.AuthorID == authorID && post.DeletedAt == nil {
			authorPosts = append(authorPosts, post)
		}
	}
	
	total := int64(len(authorPosts))
	
	// Apply pagination
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= len(authorPosts) {
		return []*models.Post{}, total, nil
	}
	
	if end > len(authorPosts) {
		end = len(authorPosts)
	}
	
	return authorPosts[start:end], total, nil
}

// GetRelated lấy posts liên quan
func (r *MockPostRepository) GetRelated(ctx context.Context, postID string, limit int) ([]*models.Post, error) {
	// Mock implementation - return posts from same tenant
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	currentPost, exists := r.posts[postID]
	if !exists {
		return []*models.Post{}, nil
	}
	
	var related []*models.Post
	for _, post := range r.posts {
		if post.ID != postID &&
			post.TenantID == currentPost.TenantID &&
			post.Status == models.PostStatusPublished &&
			post.IsPublic &&
			post.DeletedAt == nil {
			related = append(related, post)
			
			if limit > 0 && len(related) >= limit {
				break
			}
		}
	}
	
	return related, nil
}

// GetPopular lấy posts phổ biến
func (r *MockPostRepository) GetPopular(ctx context.Context, tenantID string, days, limit int) ([]*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	cutoff := time.Now().AddDate(0, 0, -days)
	var popular []*models.Post
	
	for _, post := range r.posts {
		if post.TenantID == tenantID &&
			post.Status == models.PostStatusPublished &&
			post.IsPublic &&
			post.CreatedAt.After(cutoff) &&
			post.DeletedAt == nil {
			popular = append(popular, post)
		}
	}
	
	// Sort by view count (simplified)
	// In real implementation would sort properly
	if limit > 0 && len(popular) > limit {
		popular = popular[:limit]
	}
	
	return popular, nil
}

// GetScheduled lấy posts đã schedule
func (r *MockPostRepository) GetScheduled(ctx context.Context, tenantID string) ([]*models.Post, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var scheduled []*models.Post
	for _, post := range r.posts {
		if post.TenantID == tenantID &&
			post.Status == models.PostStatusScheduled &&
			post.ScheduledFor != nil &&
			post.DeletedAt == nil {
			scheduled = append(scheduled, post)
		}
	}
	
	return scheduled, nil
}

// PublishScheduled publish posts đã schedule
func (r *MockPostRepository) PublishScheduled(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	now := time.Now()
	for _, post := range r.posts {
		if post.Status == models.PostStatusScheduled &&
			post.ScheduledFor != nil &&
			post.ScheduledFor.Before(now) {
			post.Status = models.PostStatusPublished
			post.PublishedAt = &now
			post.ScheduledFor = nil
		}
	}
	
	return nil
}

// AttachCategories gắn categories vào post
func (r *MockPostRepository) AttachCategories(ctx context.Context, postID string, categoryIDs []string) error {
	// Mock implementation
	return nil
}

// AttachTags gắn tags vào post
func (r *MockPostRepository) AttachTags(ctx context.Context, postID string, tagIDs []string) error {
	// Mock implementation
	return nil
}

// DetachCategories gỡ categories khỏi post
func (r *MockPostRepository) DetachCategories(ctx context.Context, postID string, categoryIDs []string) error {
	// Mock implementation
	return nil
}

// DetachTags gỡ tags khỏi post
func (r *MockPostRepository) DetachTags(ctx context.Context, postID string, tagIDs []string) error {
	// Mock implementation
	return nil
}

// GetStatistics lấy thống kê post
func (r *MockPostRepository) GetStatistics(ctx context.Context, postID string) (*models.PostStatistics, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	post, exists := r.posts[postID]
	if !exists {
		return nil, errors.New("post not found")
	}
	
	stats := &models.PostStatistics{
		PostID:       postID,
		ViewCount:    post.ViewCount,
		LikeCount:    post.LikeCount,
		CommentCount: post.CommentCount,
		ShareCount:   post.ShareCount,
		ReadingTime:  post.ReadingTime,
		LastViewedAt: time.Now(),
		CreatedAt:    post.CreatedAt,
	}
	
	return stats, nil
}

// matchesFilter kiểm tra post có match filter không
func (r *MockPostRepository) matchesFilter(post *models.Post, filter *models.PostFilter) bool {
	// Skip deleted posts
	if post.DeletedAt != nil {
		return false
	}
	
	// Tenant filter
	if filter.TenantID != "" && post.TenantID != filter.TenantID {
		return false
	}
	
	// Author filter
	if filter.AuthorID != "" && post.AuthorID != filter.AuthorID {
		return false
	}
	
	// Status filter
	if filter.Status != "" && post.Status != filter.Status {
		return false
	}
	
	// Featured filter
	if filter.Featured != nil && post.IsFeatured != *filter.Featured {
		return false
	}
	
	// Public filter
	if filter.Public != nil && post.IsPublic != *filter.Public {
		return false
	}
	
	// Date range filters
	if filter.PublishedFrom != nil && post.PublishedAt != nil && post.PublishedAt.Before(*filter.PublishedFrom) {
		return false
	}
	if filter.PublishedTo != nil && post.PublishedAt != nil && post.PublishedAt.After(*filter.PublishedTo) {
		return false
	}
	
	// Search query
	if filter.Query != "" {
		query := strings.ToLower(filter.Query)
		if !strings.Contains(strings.ToLower(post.Title), query) &&
			!strings.Contains(strings.ToLower(post.Content), query) &&
			!strings.Contains(strings.ToLower(post.Excerpt), query) {
			return false
		}
	}
	
	return true
}

// max helper function
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}