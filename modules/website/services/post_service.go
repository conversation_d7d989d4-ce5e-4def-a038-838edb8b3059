package services

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/website/models"
	"github.com/blog-api-v3/blog-api-v3/modules/website/repositories"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockPostService implements PostService interface
type MockPostService struct {
	postRepo     repositories.PostRepository
	categoryRepo repositories.CategoryRepository
	tagRepo      repositories.TagRepository
	logger       utils.Logger
}

// NewMockPostService tạo mock post service mới
func NewMockPostService(
	postRepo repositories.PostRepository,
	categoryRepo repositories.CategoryRepository,
	tagRepo repositories.TagRepository,
	logger utils.Logger,
) PostService {
	return &MockPostService{
		postRepo:     postRepo,
		categoryRepo: categoryRepo,
		tagRepo:      tagRepo,
		logger:       logger,
	}
}

// CreatePost tạo post mới
func (s *MockPostService) CreatePost(ctx context.Context, post *models.Post) error {
	// Validate post data
	if post.Title == "" || post.Content == "" {
		return errors.New("title and content are required")
	}
	
	// Generate slug if not provided
	if post.Slug == "" {
		slug, err := s.GenerateSlug(ctx, post.TenantID, post.Title)
		if err != nil {
			return fmt.Errorf("failed to generate slug: %w", err)
		}
		post.Slug = slug
	}
	
	// Validate slug
	if err := s.ValidateSlug(ctx, post.TenantID, post.Slug, ""); err != nil {
		return err
	}
	
	// Set defaults
	if post.ID == "" {
		post.ID = utils.GenerateULID()
	}
	if post.Status == "" {
		post.Status = models.PostStatusDraft
	}
	
	// Calculate reading time
	post.ReadingTime = s.CalculateReadingTime(post.Content)
	
	// Create post
	if err := s.postRepo.Create(ctx, post); err != nil {
		s.logger.WithError(err).Error("Failed to create post")
		return err
	}
	
	return nil
}

// GetPost lấy post theo ID
func (s *MockPostService) GetPost(ctx context.Context, postID string) (*models.Post, error) {
	post, err := s.postRepo.GetByID(ctx, postID)
	if err != nil {
		s.logger.WithError(err).WithField("post_id", postID).Error("Failed to get post")
		return nil, err
	}
	return post, nil
}

// GetPostBySlug lấy post theo slug
func (s *MockPostService) GetPostBySlug(ctx context.Context, tenantID, slug string) (*models.Post, error) {
	post, err := s.postRepo.GetBySlug(ctx, tenantID, slug)
	if err != nil {
		return nil, err
	}
	
	// Increment view count for published posts
	if post.Status == models.PostStatusPublished && post.IsPublic {
		s.IncrementViewCount(ctx, post.ID)
	}
	
	return post, nil
}

// UpdatePost cập nhật post
func (s *MockPostService) UpdatePost(ctx context.Context, postID string, update *models.PostUpdate) error {
	// Get existing post
	post, err := s.postRepo.GetByID(ctx, postID)
	if err != nil {
		return err
	}
	
	// Build updates map
	updates := make(map[string]interface{})
	
	if update.Title != nil {
		updates["title"] = *update.Title
	}
	if update.Slug != nil {
		// Validate new slug
		if err := s.ValidateSlug(ctx, post.TenantID, *update.Slug, postID); err != nil {
			return err
		}
		updates["slug"] = *update.Slug
	}
	if update.Excerpt != nil {
		updates["excerpt"] = *update.Excerpt
	}
	if update.Content != nil {
		updates["content"] = *update.Content
		// Recalculate reading time
		readingTime := s.CalculateReadingTime(*update.Content)
		updates["reading_time"] = readingTime
	}
	if update.Status != nil {
		updates["status"] = *update.Status
		
		// Set published_at if publishing
		if *update.Status == models.PostStatusPublished && post.Status != models.PostStatusPublished {
			now := time.Now()
			updates["published_at"] = &now
		}
	}
	if update.IsPublic != nil {
		updates["is_public"] = *update.IsPublic
	}
	if update.IsFeatured != nil {
		updates["is_featured"] = *update.IsFeatured
	}
	if update.MetaTitle != nil {
		updates["meta_title"] = *update.MetaTitle
	}
	if update.MetaDescription != nil {
		updates["meta_description"] = *update.MetaDescription
	}
	if update.MetaKeywords != nil {
		updates["meta_keywords"] = *update.MetaKeywords
	}
	if update.FeaturedImage != nil {
		updates["featured_image"] = *update.FeaturedImage
	}
	if update.FeaturedImageAlt != nil {
		updates["featured_image_alt"] = *update.FeaturedImageAlt
	}
	if update.Gallery != nil {
		updates["gallery"] = update.Gallery
	}
	if update.ScheduledFor != nil {
		updates["scheduled_for"] = update.ScheduledFor
		if update.ScheduledFor.After(time.Now()) {
			updates["status"] = models.PostStatusScheduled
		}
	}
	
	// Update post
	if err := s.postRepo.Update(ctx, postID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to update post")
		return err
	}
	
	// Handle categories and tags
	if update.CategoryIDs != nil {
		s.postRepo.DetachCategories(ctx, postID, []string{}) // Remove all first
		if len(update.CategoryIDs) > 0 {
			s.postRepo.AttachCategories(ctx, postID, update.CategoryIDs)
		}
	}
	
	if update.TagIDs != nil {
		s.postRepo.DetachTags(ctx, postID, []string{}) // Remove all first
		if len(update.TagIDs) > 0 {
			s.postRepo.AttachTags(ctx, postID, update.TagIDs)
		}
	}
	
	return nil
}

// DeletePost xóa post
func (s *MockPostService) DeletePost(ctx context.Context, postID string) error {
	if err := s.postRepo.Delete(ctx, postID); err != nil {
		s.logger.WithError(err).Error("Failed to delete post")
		return err
	}
	
	return nil
}

// RestorePost khôi phục post
func (s *MockPostService) RestorePost(ctx context.Context, postID string) error {
	updates := map[string]interface{}{
		"status": models.PostStatusPublished,
	}
	
	if err := s.postRepo.Update(ctx, postID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to restore post")
		return err
	}
	
	return nil
}

// PermanentlyDeletePost xóa vĩnh viễn post
func (s *MockPostService) PermanentlyDeletePost(ctx context.Context, postID string) error {
	if err := s.postRepo.HardDelete(ctx, postID); err != nil {
		s.logger.WithError(err).Error("Failed to permanently delete post")
		return err
	}
	
	return nil
}

// ListPosts liệt kê posts với filter
func (s *MockPostService) ListPosts(ctx context.Context, filter *models.PostFilter) ([]*models.Post, int64, error) {
	// Set defaults
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.PageSize < 1 {
		filter.PageSize = 20
	}
	if filter.PageSize > 100 {
		filter.PageSize = 100
	}
	if filter.SortBy == "" {
		filter.SortBy = "created_at"
	}
	if filter.SortOrder == "" {
		filter.SortOrder = "desc"
	}
	
	return s.postRepo.List(ctx, filter)
}

// SearchPosts tìm kiếm posts
func (s *MockPostService) SearchPosts(ctx context.Context, tenantID, query string, limit int) ([]*models.Post, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	
	return s.postRepo.Search(ctx, tenantID, query, limit)
}

// PublishPost publish post
func (s *MockPostService) PublishPost(ctx context.Context, postID string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":       models.PostStatusPublished,
		"published_at": &now,
	}
	
	if err := s.postRepo.Update(ctx, postID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to publish post")
		return err
	}
	
	return nil
}

// UnpublishPost unpublish post
func (s *MockPostService) UnpublishPost(ctx context.Context, postID string) error {
	updates := map[string]interface{}{
		"status": models.PostStatusDraft,
	}
	
	if err := s.postRepo.Update(ctx, postID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to unpublish post")
		return err
	}
	
	return nil
}

// SchedulePost schedule post
func (s *MockPostService) SchedulePost(ctx context.Context, postID string, scheduledFor time.Time) error {
	if scheduledFor.Before(time.Now()) {
		return errors.New("scheduled time must be in the future")
	}
	
	updates := map[string]interface{}{
		"status":        models.PostStatusScheduled,
		"scheduled_for": &scheduledFor,
	}
	
	if err := s.postRepo.Update(ctx, postID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to schedule post")
		return err
	}
	
	return nil
}

// FeaturePost đặt post làm featured
func (s *MockPostService) FeaturePost(ctx context.Context, postID string) error {
	return s.postRepo.Update(ctx, postID, map[string]interface{}{
		"is_featured": true,
	})
}

// UnfeaturePost bỏ featured
func (s *MockPostService) UnfeaturePost(ctx context.Context, postID string) error {
	return s.postRepo.Update(ctx, postID, map[string]interface{}{
		"is_featured": false,
	})
}

// GetPublishedPosts lấy posts đã publish
func (s *MockPostService) GetPublishedPosts(ctx context.Context, tenantID string, limit int) ([]*models.Post, error) {
	return s.postRepo.GetPublished(ctx, tenantID, limit)
}

// GetFeaturedPosts lấy posts featured
func (s *MockPostService) GetFeaturedPosts(ctx context.Context, tenantID string, limit int) ([]*models.Post, error) {
	return s.postRepo.GetFeatured(ctx, tenantID, limit)
}

// GetRelatedPosts lấy posts liên quan
func (s *MockPostService) GetRelatedPosts(ctx context.Context, postID string, limit int) ([]*models.Post, error) {
	return s.postRepo.GetRelated(ctx, postID, limit)
}

// GetPopularPosts lấy posts phổ biến
func (s *MockPostService) GetPopularPosts(ctx context.Context, tenantID string, days, limit int) ([]*models.Post, error) {
	return s.postRepo.GetPopular(ctx, tenantID, days, limit)
}

// GetPostsByAuthor lấy posts theo author
func (s *MockPostService) GetPostsByAuthor(ctx context.Context, authorID string, page, pageSize int) ([]*models.Post, int64, error) {
	return s.postRepo.GetByAuthor(ctx, authorID, page, pageSize)
}

// GetPostsByCategory lấy posts theo category
func (s *MockPostService) GetPostsByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*models.Post, int64, error) {
	return s.postRepo.GetByCategory(ctx, categoryID, page, pageSize)
}

// GetPostsByTag lấy posts theo tag
func (s *MockPostService) GetPostsByTag(ctx context.Context, tagID string, page, pageSize int) ([]*models.Post, int64, error) {
	return s.postRepo.GetByTag(ctx, tagID, page, pageSize)
}

// IncrementViewCount tăng view count
func (s *MockPostService) IncrementViewCount(ctx context.Context, postID string) error {
	return s.postRepo.UpdateViewCount(ctx, postID)
}

// LikePost like post
func (s *MockPostService) LikePost(ctx context.Context, postID string) error {
	return s.postRepo.UpdateLikeCount(ctx, postID, true)
}

// UnlikePost unlike post
func (s *MockPostService) UnlikePost(ctx context.Context, postID string) error {
	return s.postRepo.UpdateLikeCount(ctx, postID, false)
}

// SharePost tăng share count
func (s *MockPostService) SharePost(ctx context.Context, postID string) error {
	post, err := s.postRepo.GetByID(ctx, postID)
	if err != nil {
		return err
	}
	
	return s.postRepo.Update(ctx, postID, map[string]interface{}{
		"share_count": post.ShareCount + 1,
	})
}

// ValidateSlug kiểm tra slug hợp lệ
func (s *MockPostService) ValidateSlug(ctx context.Context, tenantID, slug string, excludePostID string) error {
	// Check slug format
	if !isValidSlug(slug) {
		return errors.New("invalid slug format. Use only lowercase letters, numbers, and hyphens")
	}
	
	// Check length
	if len(slug) < 3 || len(slug) > 100 {
		return errors.New("slug must be between 3 and 100 characters")
	}
	
	// Check availability
	exists, err := s.postRepo.ExistsBySlug(ctx, tenantID, slug)
	if err != nil {
		return err
	}
	
	if exists {
		// If excluding a post, check if it's the same post
		if excludePostID != "" {
			existing, err := s.postRepo.GetBySlug(ctx, tenantID, slug)
			if err == nil && existing.ID == excludePostID {
				return nil // Same post, allow
			}
		}
		return errors.New("slug already exists")
	}
	
	return nil
}

// GenerateSlug tạo slug từ title
func (s *MockPostService) GenerateSlug(ctx context.Context, tenantID, title string) (string, error) {
	// Convert to lowercase and replace spaces with hyphens
	slug := strings.ToLower(title)
	slug = strings.ReplaceAll(slug, " ", "-")
	
	// Remove special characters except hyphens
	reg := regexp.MustCompile(`[^a-z0-9\-]`)
	slug = reg.ReplaceAllString(slug, "")
	
	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile(`-+`)
	slug = reg.ReplaceAllString(slug, "-")
	
	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")
	
	// Check if slug is available
	originalSlug := slug
	counter := 1
	
	for {
		err := s.ValidateSlug(ctx, tenantID, slug, "")
		if err == nil {
			break
		}
		
		// If slug exists, try with counter
		if strings.Contains(err.Error(), "already exists") {
			slug = fmt.Sprintf("%s-%d", originalSlug, counter)
			counter++
			
			if counter > 100 {
				return "", errors.New("unable to generate unique slug")
			}
		} else {
			return "", err
		}
	}
	
	return slug, nil
}

// ProcessScheduledPosts xử lý posts đã schedule
func (s *MockPostService) ProcessScheduledPosts(ctx context.Context) error {
	return s.postRepo.PublishScheduled(ctx)
}

// GetPostStatistics lấy thống kê post
func (s *MockPostService) GetPostStatistics(ctx context.Context, postID string) (*models.PostStatistics, error) {
	return s.postRepo.GetStatistics(ctx, postID)
}

// CalculateReadingTime tính thời gian đọc
func (s *MockPostService) CalculateReadingTime(content string) int {
	// Average reading speed: 200 words per minute
	wordCount := len(strings.Fields(content))
	readingTime := wordCount / 200
	
	if readingTime < 1 {
		readingTime = 1
	}
	
	return readingTime
}

// isValidSlug kiểm tra format của slug
func isValidSlug(slug string) bool {
	// Slug should only contain lowercase letters, numbers, and hyphens
	// Should not start or end with hyphen
	// Should not contain consecutive hyphens
	pattern := `^[a-z0-9]+(?:-[a-z0-9]+)*$`
	matched, _ := regexp.MatchString(pattern, slug)
	return matched
}