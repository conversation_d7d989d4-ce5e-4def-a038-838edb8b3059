package services

import (
	"context"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/website/models"
)

// PostService định nghĩa interface cho post business logic
type PostService interface {
	// CreatePost tạo post mới
	CreatePost(ctx context.Context, post *models.Post) error
	
	// GetPost lấy post theo ID
	GetPost(ctx context.Context, postID string) (*models.Post, error)
	
	// GetPostBySlug lấy post theo slug
	GetPostBySlug(ctx context.Context, tenantID, slug string) (*models.Post, error)
	
	// UpdatePost cập nhật post
	UpdatePost(ctx context.Context, postID string, update *models.PostUpdate) error
	
	// DeletePost xóa post
	DeletePost(ctx context.Context, postID string) error
	
	// RestorePost khôi phục post
	RestorePost(ctx context.Context, postID string) error
	
	// PermanentlyDeletePost xóa vĩ<PERSON> viễ<PERSON> post
	PermanentlyDeletePost(ctx context.Context, postID string) error
	
	// ListPosts liệt kê posts với filter
	ListPosts(ctx context.Context, filter *models.PostFilter) ([]*models.Post, int64, error)
	
	// SearchPosts tìm kiếm posts
	SearchPosts(ctx context.Context, tenantID, query string, limit int) ([]*models.Post, error)
	
	// PublishPost publish post
	PublishPost(ctx context.Context, postID string) error
	
	// UnpublishPost unpublish post
	UnpublishPost(ctx context.Context, postID string) error
	
	// SchedulePost schedule post
	SchedulePost(ctx context.Context, postID string, scheduledFor time.Time) error
	
	// FeaturePost đặt post làm featured
	FeaturePost(ctx context.Context, postID string) error
	
	// UnfeaturePost bỏ featured
	UnfeaturePost(ctx context.Context, postID string) error
	
	// GetPublishedPosts lấy posts đã publish
	GetPublishedPosts(ctx context.Context, tenantID string, limit int) ([]*models.Post, error)
	
	// GetFeaturedPosts lấy posts featured
	GetFeaturedPosts(ctx context.Context, tenantID string, limit int) ([]*models.Post, error)
	
	// GetRelatedPosts lấy posts liên quan
	GetRelatedPosts(ctx context.Context, postID string, limit int) ([]*models.Post, error)
	
	// GetPopularPosts lấy posts phổ biến
	GetPopularPosts(ctx context.Context, tenantID string, days, limit int) ([]*models.Post, error)
	
	// GetPostsByAuthor lấy posts theo author
	GetPostsByAuthor(ctx context.Context, authorID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// GetPostsByCategory lấy posts theo category
	GetPostsByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// GetPostsByTag lấy posts theo tag
	GetPostsByTag(ctx context.Context, tagID string, page, pageSize int) ([]*models.Post, int64, error)
	
	// IncrementViewCount tăng view count
	IncrementViewCount(ctx context.Context, postID string) error
	
	// LikePost like post
	LikePost(ctx context.Context, postID string) error
	
	// UnlikePost unlike post
	UnlikePost(ctx context.Context, postID string) error
	
	// SharePost tăng share count
	SharePost(ctx context.Context, postID string) error
	
	// ValidateSlug kiểm tra slug hợp lệ
	ValidateSlug(ctx context.Context, tenantID, slug string, excludePostID string) error
	
	// GenerateSlug tạo slug từ title
	GenerateSlug(ctx context.Context, tenantID, title string) (string, error)
	
	// ProcessScheduledPosts xử lý posts đã schedule
	ProcessScheduledPosts(ctx context.Context) error
	
	// GetPostStatistics lấy thống kê post
	GetPostStatistics(ctx context.Context, postID string) (*models.PostStatistics, error)
	
	// CalculateReadingTime tính thời gian đọc
	CalculateReadingTime(content string) int
}

// PageService định nghĩa interface cho page business logic
type PageService interface {
	// CreatePage tạo page mới
	CreatePage(ctx context.Context, page *models.Page) error
	
	// GetPage lấy page theo ID
	GetPage(ctx context.Context, pageID string) (*models.Page, error)
	
	// GetPageBySlug lấy page theo slug
	GetPageBySlug(ctx context.Context, tenantID, slug string) (*models.Page, error)
	
	// UpdatePage cập nhật page
	UpdatePage(ctx context.Context, pageID string, update *models.PageUpdate) error
	
	// DeletePage xóa page
	DeletePage(ctx context.Context, pageID string) error
	
	// RestorePage khôi phục page
	RestorePage(ctx context.Context, pageID string) error
	
	// PermanentlyDeletePage xóa vĩnh viễn page
	PermanentlyDeletePage(ctx context.Context, pageID string) error
	
	// ListPages liệt kê pages với filter
	ListPages(ctx context.Context, filter *models.PageFilter) ([]*models.Page, int64, error)
	
	// SearchPages tìm kiếm pages
	SearchPages(ctx context.Context, tenantID, query string, limit int) ([]*models.Page, error)
	
	// PublishPage publish page
	PublishPage(ctx context.Context, pageID string) error
	
	// UnpublishPage unpublish page
	UnpublishPage(ctx context.Context, pageID string) error
	
	// GetMenuPages lấy pages hiển thị trong menu
	GetMenuPages(ctx context.Context, tenantID string) ([]*models.Page, error)
	
	// GetPageHierarchy lấy cây phân cấp pages
	GetPageHierarchy(ctx context.Context, tenantID string) ([]*models.Page, error)
	
	// ReorderPages sắp xếp lại thứ tự pages
	ReorderPages(ctx context.Context, pageOrders []struct{ ID string; SortOrder int }) error
	
	// IncrementViewCount tăng view count
	IncrementViewCount(ctx context.Context, pageID string) error
	
	// ValidateSlug kiểm tra slug hợp lệ
	ValidateSlug(ctx context.Context, tenantID, slug string, excludePageID string) error
	
	// GenerateSlug tạo slug từ title
	GenerateSlug(ctx context.Context, tenantID, title string) (string, error)
}

// CategoryService định nghĩa interface cho category business logic
type CategoryService interface {
	// CreateCategory tạo category mới
	CreateCategory(ctx context.Context, category *models.Category) error
	
	// GetCategory lấy category theo ID
	GetCategory(ctx context.Context, categoryID string) (*models.Category, error)
	
	// GetCategoryBySlug lấy category theo slug
	GetCategoryBySlug(ctx context.Context, tenantID, slug string) (*models.Category, error)
	
	// UpdateCategory cập nhật category
	UpdateCategory(ctx context.Context, categoryID string, update *models.CategoryUpdate) error
	
	// DeleteCategory xóa category
	DeleteCategory(ctx context.Context, categoryID string) error
	
	// RestoreCategory khôi phục category
	RestoreCategory(ctx context.Context, categoryID string) error
	
	// PermanentlyDeleteCategory xóa vĩnh viễn category
	PermanentlyDeleteCategory(ctx context.Context, categoryID string) error
	
	// ListCategories liệt kê categories
	ListCategories(ctx context.Context, tenantID string, publicOnly bool) ([]*models.Category, error)
	
	// SearchCategories tìm kiếm categories
	SearchCategories(ctx context.Context, tenantID, query string, limit int) ([]*models.Category, error)
	
	// GetPopularCategories lấy categories phổ biến
	GetPopularCategories(ctx context.Context, tenantID string, limit int) ([]*models.Category, error)
	
	// ReorderCategories sắp xếp lại thứ tự categories
	ReorderCategories(ctx context.Context, categoryOrders []struct{ ID string; SortOrder int }) error
	
	// UpdatePostCount cập nhật số lượng posts
	UpdatePostCount(ctx context.Context, categoryID string) error
	
	// ValidateSlug kiểm tra slug hợp lệ
	ValidateSlug(ctx context.Context, tenantID, slug string, excludeCategoryID string) error
	
	// GenerateSlug tạo slug từ name
	GenerateSlug(ctx context.Context, tenantID, name string) (string, error)
}

// TagService định nghĩa interface cho tag business logic
type TagService interface {
	// CreateTag tạo tag mới
	CreateTag(ctx context.Context, tag *models.Tag) error
	
	// GetTag lấy tag theo ID
	GetTag(ctx context.Context, tagID string) (*models.Tag, error)
	
	// GetTagBySlug lấy tag theo slug
	GetTagBySlug(ctx context.Context, tenantID, slug string) (*models.Tag, error)
	
	// GetTagByName lấy tag theo name
	GetTagByName(ctx context.Context, tenantID, name string) (*models.Tag, error)
	
	// UpdateTag cập nhật tag
	UpdateTag(ctx context.Context, tagID string, update *models.TagUpdate) error
	
	// DeleteTag xóa tag
	DeleteTag(ctx context.Context, tagID string) error
	
	// RestoreTag khôi phục tag
	RestoreTag(ctx context.Context, tagID string) error
	
	// PermanentlyDeleteTag xóa vĩnh viễn tag
	PermanentlyDeleteTag(ctx context.Context, tagID string) error
	
	// ListTags liệt kê tags
	ListTags(ctx context.Context, tenantID string) ([]*models.Tag, error)
	
	// SearchTags tìm kiếm tags
	SearchTags(ctx context.Context, tenantID, query string, limit int) ([]*models.Tag, error)
	
	// GetPopularTags lấy tags phổ biến
	GetPopularTags(ctx context.Context, tenantID string, limit int) ([]*models.Tag, error)
	
	// GetOrCreateTag lấy hoặc tạo tag mới
	GetOrCreateTag(ctx context.Context, tenantID, name string) (*models.Tag, error)
	
	// GetOrCreateTags lấy hoặc tạo nhiều tags
	GetOrCreateTags(ctx context.Context, tenantID string, names []string) ([]*models.Tag, error)
	
	// UpdatePostCount cập nhật số lượng posts
	UpdatePostCount(ctx context.Context, tagID string) error
	
	// ValidateSlug kiểm tra slug hợp lệ
	ValidateSlug(ctx context.Context, tenantID, slug string, excludeTagID string) error
	
	// ValidateName kiểm tra name hợp lệ
	ValidateName(ctx context.Context, tenantID, name string, excludeTagID string) error
	
	// GenerateSlug tạo slug từ name
	GenerateSlug(ctx context.Context, tenantID, name string) (string, error)
}

// CommentService định nghĩa interface cho comment business logic
type CommentService interface {
	// CreateComment tạo comment mới
	CreateComment(ctx context.Context, comment *models.Comment) error
	
	// GetComment lấy comment theo ID
	GetComment(ctx context.Context, commentID string) (*models.Comment, error)
	
	// UpdateComment cập nhật comment
	UpdateComment(ctx context.Context, commentID string, content string) error
	
	// DeleteComment xóa comment
	DeleteComment(ctx context.Context, commentID string) error
	
	// RestoreComment khôi phục comment
	RestoreComment(ctx context.Context, commentID string) error
	
	// PermanentlyDeleteComment xóa vĩnh viễn comment
	PermanentlyDeleteComment(ctx context.Context, commentID string) error
	
	// ListComments liệt kê comments với filter
	ListComments(ctx context.Context, filter *models.CommentFilter) ([]*models.Comment, int64, error)
	
	// SearchComments tìm kiếm comments
	SearchComments(ctx context.Context, tenantID, query string, limit int) ([]*models.Comment, error)
	
	// GetCommentsByPost lấy comments theo post
	GetCommentsByPost(ctx context.Context, postID string, approved bool, page, pageSize int) ([]*models.Comment, int64, error)
	
	// GetCommentReplies lấy replies của comment
	GetCommentReplies(ctx context.Context, commentID string) ([]*models.Comment, error)
	
	// ApproveComment phê duyệt comment
	ApproveComment(ctx context.Context, commentID string) error
	
	// RejectComment từ chối comment
	RejectComment(ctx context.Context, commentID string) error
	
	// MarkAsSpam đánh dấu spam
	MarkAsSpam(ctx context.Context, commentID string) error
	
	// GetPendingComments lấy comments chờ duyệt
	GetPendingComments(ctx context.Context, tenantID string, limit int) ([]*models.Comment, error)
	
	// LikeComment like comment
	LikeComment(ctx context.Context, commentID string) error
	
	// UnlikeComment unlike comment
	UnlikeComment(ctx context.Context, commentID string) error
	
	// BatchApprove phê duyệt nhiều comments
	BatchApprove(ctx context.Context, commentIDs []string) error
	
	// BatchReject từ chối nhiều comments
	BatchReject(ctx context.Context, commentIDs []string) error
	
	// BatchDelete xóa nhiều comments
	BatchDelete(ctx context.Context, commentIDs []string) error
	
	// ValidateComment kiểm tra comment hợp lệ
	ValidateComment(ctx context.Context, comment *models.Comment) error
	
	// CheckSpam kiểm tra spam
	CheckSpam(ctx context.Context, comment *models.Comment) (bool, error)
}

// MediaService định nghĩa interface cho media business logic
type MediaService interface {
	// UploadMedia upload file media
	UploadMedia(ctx context.Context, media *models.Media, fileData []byte) error
	
	// GetMedia lấy media theo ID
	GetMedia(ctx context.Context, mediaID string) (*models.Media, error)
	
	// UpdateMedia cập nhật media metadata
	UpdateMedia(ctx context.Context, mediaID string, updates map[string]interface{}) error
	
	// DeleteMedia xóa media
	DeleteMedia(ctx context.Context, mediaID string) error
	
	// PermanentlyDeleteMedia xóa vĩnh viễn media
	PermanentlyDeleteMedia(ctx context.Context, mediaID string) error
	
	// ListMedia liệt kê media files
	ListMedia(ctx context.Context, tenantID string, page, pageSize int) ([]*models.Media, int64, error)
	
	// SearchMedia tìm kiếm media
	SearchMedia(ctx context.Context, tenantID, query string, limit int) ([]*models.Media, error)
	
	// GetMediaByType lấy media theo type
	GetMediaByType(ctx context.Context, tenantID, mimeType string, page, pageSize int) ([]*models.Media, int64, error)
	
	// GetStorageUsage lấy tổng dung lượng sử dụng
	GetStorageUsage(ctx context.Context, tenantID string) (int64, error)
	
	// CleanupOrphanedMedia xóa media không sử dụng
	CleanupOrphanedMedia(ctx context.Context, days int) error
	
	// GenerateThumbnail tạo thumbnail
	GenerateThumbnail(ctx context.Context, mediaID string) error
	
	// ValidateFile kiểm tra file hợp lệ
	ValidateFile(ctx context.Context, fileName, mimeType string, size int64) error
	
	// GetAllowedTypes lấy danh sách mime types được phép
	GetAllowedTypes() []string
	
	// GetMaxFileSize lấy kích thước file tối đa
	GetMaxFileSize() int64
}

// WebsiteService định nghĩa interface cho website business logic
type WebsiteService interface {
	// GetStatistics lấy thống kê tổng quan
	GetStatistics(ctx context.Context, tenantID string) (*models.WebsiteStatistics, error)
	
	// UpdateStatistics cập nhật thống kê
	UpdateStatistics(ctx context.Context, tenantID string) error
	
	// GetDashboardData lấy dữ liệu dashboard
	GetDashboardData(ctx context.Context, tenantID string) (map[string]interface{}, error)
	
	// ExportData export dữ liệu website
	ExportData(ctx context.Context, tenantID string, format string) ([]byte, error)
	
	// ImportData import dữ liệu website
	ImportData(ctx context.Context, tenantID string, data []byte, format string) error
	
	// BackupContent backup nội dung
	BackupContent(ctx context.Context, tenantID string) (string, error)
	
	// RestoreContent restore nội dung từ backup
	RestoreContent(ctx context.Context, tenantID string, backupID string) error
}