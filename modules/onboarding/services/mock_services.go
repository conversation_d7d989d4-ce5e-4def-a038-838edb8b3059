package services

import (
	"context"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/onboarding/models"
)

// MockOnboardingService implements OnboardingService interface
type MockOnboardingService struct {
	flows     map[string]*models.OnboardingFlow
	steps     map[string]*models.OnboardingStep
	progress  map[string]*models.UserProgress
	events    []*models.OnboardingEvent
}

// NewMockOnboardingService creates a new mock onboarding service
func NewMockOnboardingService(
	onboardingRepo interface{},
	progressRepo interface{},
	stepRepo interface{},
	eventRepo interface{},
) OnboardingService {
	return &MockOnboardingService{
		flows:    make(map[string]*models.OnboardingFlow),
		steps:    make(map[string]*models.OnboardingStep),
		progress: make(map[string]*models.UserProgress),
		events:   make([]*models.OnboardingEvent, 0),
	}
}

// Flow management
func (s *MockOnboardingService) CreateFlow(ctx context.Context, flow *models.OnboardingFlow) error {
	flow.ID = "flow-" + time.Now().Format("20060102150405")
	flow.CreatedAt = time.Now()
	flow.UpdatedAt = time.Now()
	s.flows[flow.ID] = flow
	return nil
}

func (s *MockOnboardingService) GetFlow(ctx context.Context, flowID string) (*models.OnboardingFlow, error) {
	if flow, exists := s.flows[flowID]; exists {
		return flow, nil
	}
	return nil, ErrFlowNotFound
}

func (s *MockOnboardingService) UpdateFlow(ctx context.Context, flowID string, update *models.FlowUpdate) error {
	flow, exists := s.flows[flowID]
	if !exists {
		return ErrFlowNotFound
	}
	
	if update.Name != nil {
		flow.Name = *update.Name
	}
	if update.Description != nil {
		flow.Description = *update.Description
	}
	if update.Status != nil {
		flow.Status = *update.Status
	}
	
	flow.UpdatedAt = time.Now()
	return nil
}

func (s *MockOnboardingService) DeleteFlow(ctx context.Context, flowID string) error {
	if _, exists := s.flows[flowID]; !exists {
		return ErrFlowNotFound
	}
	delete(s.flows, flowID)
	return nil
}

func (s *MockOnboardingService) ListFlows(ctx context.Context, filter *models.OnboardingFlowFilter) ([]*models.OnboardingFlow, int64, error) {
	flows := make([]*models.OnboardingFlow, 0)
	
	for _, flow := range s.flows {
		if filter.TenantID != "" && flow.TenantID != filter.TenantID {
			continue
		}
		if filter.Status != "" && flow.Status != filter.Status {
			continue
		}
		flows = append(flows, flow)
	}
	
	return flows, int64(len(flows)), nil
}

// Flow operations
func (s *MockOnboardingService) PublishFlow(ctx context.Context, flowID string) error {
	flow, exists := s.flows[flowID]
	if !exists {
		return ErrFlowNotFound
	}
	
	flow.Status = models.FlowStatusActive
	now := time.Now()
	flow.PublishedAt = &now
	flow.UpdatedAt = now
	return nil
}

func (s *MockOnboardingService) UnpublishFlow(ctx context.Context, flowID string) error {
	flow, exists := s.flows[flowID]
	if !exists {
		return ErrFlowNotFound
	}
	
	flow.Status = models.FlowStatusInactive
	flow.UpdatedAt = time.Now()
	return nil
}

func (s *MockOnboardingService) DuplicateFlow(ctx context.Context, flowID string, newName string) (*models.OnboardingFlow, error) {
	original, exists := s.flows[flowID]
	if !exists {
		return nil, ErrFlowNotFound
	}
	
	newFlow := *original
	newFlow.ID = "flow-" + time.Now().Format("20060102150405")
	newFlow.Name = newName
	newFlow.Status = models.FlowStatusDraft
	newFlow.CreatedAt = time.Now()
	newFlow.UpdatedAt = time.Now()
	newFlow.PublishedAt = nil
	
	s.flows[newFlow.ID] = &newFlow
	return &newFlow, nil
}

func (s *MockOnboardingService) SetDefaultFlow(ctx context.Context, flowID string) error {
	// Unset all other default flows
	for _, flow := range s.flows {
		flow.IsDefault = false
	}
	
	// Set this flow as default
	flow, exists := s.flows[flowID]
	if !exists {
		return ErrFlowNotFound
	}
	
	flow.IsDefault = true
	flow.UpdatedAt = time.Now()
	return nil
}

// Step management
func (s *MockOnboardingService) CreateStep(ctx context.Context, step *models.OnboardingStep) error {
	step.ID = "step-" + time.Now().Format("20060102150405")
	step.CreatedAt = time.Now()
	step.UpdatedAt = time.Now()
	s.steps[step.ID] = step
	return nil
}

func (s *MockOnboardingService) GetStep(ctx context.Context, stepID string) (*models.OnboardingStep, error) {
	if step, exists := s.steps[stepID]; exists {
		return step, nil
	}
	return nil, ErrStepNotFound
}

func (s *MockOnboardingService) UpdateStep(ctx context.Context, stepID string, update *models.StepUpdate) error {
	step, exists := s.steps[stepID]
	if !exists {
		return ErrStepNotFound
	}
	
	if update.Name != nil {
		step.Name = *update.Name
	}
	if update.Title != nil {
		step.Title = *update.Title
	}
	
	step.UpdatedAt = time.Now()
	return nil
}

func (s *MockOnboardingService) DeleteStep(ctx context.Context, stepID string) error {
	if _, exists := s.steps[stepID]; !exists {
		return ErrStepNotFound
	}
	delete(s.steps, stepID)
	return nil
}

func (s *MockOnboardingService) ReorderSteps(ctx context.Context, flowID string, stepOrders []struct{ ID string; SortOrder int }) error {
	for _, order := range stepOrders {
		if step, exists := s.steps[order.ID]; exists {
			step.SortOrder = order.SortOrder
		}
	}
	return nil
}

// User progress
func (s *MockOnboardingService) StartFlow(ctx context.Context, userID, flowID string) (*models.UserProgress, error) {
	progressID := userID + "-" + flowID
	now := time.Now()
	
	progress := &models.UserProgress{
		ID:              progressID,
		UserID:          userID,
		FlowID:          flowID,
		TenantID:        "tenant-1",
		Status:          models.ProgressStatusInProgress,
		CompletedSteps:  0,
		TotalSteps:      5, // Mock value
		SkippedSteps:    0,
		FailedSteps:     0,
		StartedAt:       &now,
		LastActiveAt:    now,
		TotalTimeSpent:  0,
		ProgressData:    make(map[string]interface{}),
		Metadata:        make(map[string]interface{}),
		SessionCount:    1,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
	
	s.progress[progressID] = progress
	return progress, nil
}

func (s *MockOnboardingService) GetUserProgress(ctx context.Context, userID, flowID string) (*models.UserProgress, error) {
	progressID := userID + "-" + flowID
	if progress, exists := s.progress[progressID]; exists {
		return progress, nil
	}
	return nil, ErrProgressNotFound
}

func (s *MockOnboardingService) UpdateProgress(ctx context.Context, userID, flowID string, stepID string, data map[string]interface{}) error {
	progressID := userID + "-" + flowID
	progress, exists := s.progress[progressID]
	if !exists {
		return ErrProgressNotFound
	}
	
	progress.CurrentStepID = &stepID
	progress.LastActiveAt = time.Now()
	progress.UpdatedAt = time.Now()
	
	for k, v := range data {
		progress.ProgressData[k] = v
	}
	
	return nil
}

func (s *MockOnboardingService) CompleteStep(ctx context.Context, userID, flowID, stepID string, data map[string]interface{}) error {
	progressID := userID + "-" + flowID
	progress, exists := s.progress[progressID]
	if !exists {
		return ErrProgressNotFound
	}
	
	progress.CompletedSteps++
	progress.LastActiveAt = time.Now()
	progress.UpdatedAt = time.Now()
	
	if progress.CompletedSteps >= progress.TotalSteps {
		progress.Status = models.ProgressStatusCompleted
		now := time.Now()
		progress.CompletedAt = &now
	}
	
	return nil
}

func (s *MockOnboardingService) SkipStep(ctx context.Context, userID, flowID, stepID string, reason string) error {
	progressID := userID + "-" + flowID
	progress, exists := s.progress[progressID]
	if !exists {
		return ErrProgressNotFound
	}
	
	progress.SkippedSteps++
	progress.LastActiveAt = time.Now()
	progress.UpdatedAt = time.Now()
	
	return nil
}

func (s *MockOnboardingService) RestartFlow(ctx context.Context, userID, flowID string) error {
	progressID := userID + "-" + flowID
	progress, exists := s.progress[progressID]
	if !exists {
		return ErrProgressNotFound
	}
	
	now := time.Now()
	progress.Status = models.ProgressStatusInProgress
	progress.CompletedSteps = 0
	progress.SkippedSteps = 0
	progress.FailedSteps = 0
	progress.StartedAt = &now
	progress.CompletedAt = nil
	progress.LastActiveAt = now
	progress.UpdatedAt = now
	progress.SessionCount++
	
	return nil
}

// Progress tracking
func (s *MockOnboardingService) GetNextStep(ctx context.Context, userID, flowID string) (*models.OnboardingStep, error) {
	// Mock implementation - return nil to indicate completion
	return nil, nil
}

func (s *MockOnboardingService) GetProgressSummary(ctx context.Context, userID, flowID string) (map[string]interface{}, error) {
	progress, err := s.GetUserProgress(ctx, userID, flowID)
	if err != nil {
		return nil, err
	}
	
	return map[string]interface{}{
		"total_steps":    progress.TotalSteps,
		"completed":      progress.CompletedSteps,
		"remaining":      progress.TotalSteps - progress.CompletedSteps,
		"skipped":        progress.SkippedSteps,
		"completion_rate": float64(progress.CompletedSteps) / float64(progress.TotalSteps) * 100,
	}, nil
}

func (s *MockOnboardingService) ListUserProgresses(ctx context.Context, filter *models.UserProgressFilter) ([]*models.UserProgress, int64, error) {
	progresses := make([]*models.UserProgress, 0)
	
	for _, progress := range s.progress {
		if filter.UserID != "" && progress.UserID != filter.UserID {
			continue
		}
		if filter.FlowID != "" && progress.FlowID != filter.FlowID {
			continue
		}
		progresses = append(progresses, progress)
	}
	
	return progresses, int64(len(progresses)), nil
}

// Flow discovery
func (s *MockOnboardingService) GetRecommendedFlows(ctx context.Context, userID string) ([]*models.OnboardingFlow, error) {
	return make([]*models.OnboardingFlow, 0), nil
}

func (s *MockOnboardingService) GetFlowsForRole(ctx context.Context, tenantID, role string) ([]*models.OnboardingFlow, error) {
	flows := make([]*models.OnboardingFlow, 0)
	
	for _, flow := range s.flows {
		if flow.TenantID == tenantID && (flow.TargetRole == "" || flow.TargetRole == role) {
			flows = append(flows, flow)
		}
	}
	
	return flows, nil
}

func (s *MockOnboardingService) SearchFlows(ctx context.Context, tenantID, query string) ([]*models.OnboardingFlow, error) {
	return make([]*models.OnboardingFlow, 0), nil
}

// Analytics and statistics
func (s *MockOnboardingService) GetFlowStatistics(ctx context.Context, flowID string) (*models.FlowStatistics, error) {
	return &models.FlowStatistics{
		FlowID:           flowID,
		TotalStarts:      100,
		TotalCompletions: 75,
		CompletionRate:   75.0,
		AverageTime:      30,
		DropoffRate:      25.0,
		LastUpdated:      time.Now(),
	}, nil
}

func (s *MockOnboardingService) GetStepStatistics(ctx context.Context, stepID string) (*models.StepStatistics, error) {
	return &models.StepStatistics{
		StepID:          stepID,
		TotalViews:      100,
		TotalCompletions: 90,
		CompletionRate:  90.0,
		SkipRate:        5.0,
		FailureRate:     5.0,
		AverageTime:     180,
		LastUpdated:     time.Now(),
	}, nil
}

func (s *MockOnboardingService) GetOnboardingStatistics(ctx context.Context, tenantID string) (*models.OnboardingStatistics, error) {
	return &models.OnboardingStatistics{
		TenantID:               tenantID,
		TotalFlows:             int64(len(s.flows)),
		ActiveFlows:            1,
		TotalUsers:             50,
		CompletedUsers:         30,
		InProgressUsers:        15,
		AverageCompletionRate:  75.0,
		AverageCompletionTime:  25,
		MostPopularFlow:        "flow-001",
		LastUpdated:            time.Now(),
	}, nil
}

// Event tracking
func (s *MockOnboardingService) TrackEvent(ctx context.Context, event *models.OnboardingEvent) error {
	event.ID = "event-" + time.Now().Format("20060102150405")
	event.CreatedAt = time.Now()
	s.events = append(s.events, event)
	return nil
}

func (s *MockOnboardingService) GetUserEvents(ctx context.Context, userID string, limit int) ([]*models.OnboardingEvent, error) {
	events := make([]*models.OnboardingEvent, 0)
	count := 0
	
	for i := len(s.events) - 1; i >= 0 && count < limit; i-- {
		if s.events[i].UserID == userID {
			events = append(events, s.events[i])
			count++
		}
	}
	
	return events, nil
}

// Validation
func (s *MockOnboardingService) ValidateFlow(ctx context.Context, flow *models.OnboardingFlow) error {
	if flow.Name == "" {
		return ErrInvalidFlow
	}
	return nil
}

func (s *MockOnboardingService) ValidateStep(ctx context.Context, step *models.OnboardingStep) error {
	if step.Name == "" || step.Title == "" {
		return ErrInvalidStep
	}
	return nil
}

func (s *MockOnboardingService) ValidateStepData(ctx context.Context, stepID string, data map[string]interface{}) error {
	return nil
}

// Templates and presets
func (s *MockOnboardingService) GetFlowTemplates(ctx context.Context) ([]*models.OnboardingFlow, error) {
	templates := []*models.OnboardingFlow{
		{
			ID:          "template-basic",
			Name:        "Basic Onboarding",
			Description: "A simple welcome flow for new users",
			Status:      models.FlowStatusActive,
			TargetLevel: "beginner",
		},
		{
			ID:          "template-advanced",
			Name:        "Advanced Setup",
			Description: "Comprehensive onboarding for power users",
			Status:      models.FlowStatusActive,
			TargetLevel: "advanced",
		},
	}
	
	return templates, nil
}

func (s *MockOnboardingService) CreateFromTemplate(ctx context.Context, templateID, tenantID string, customizations map[string]interface{}) (*models.OnboardingFlow, error) {
	template := &models.OnboardingFlow{
		ID:          "flow-from-template-" + time.Now().Format("20060102150405"),
		TenantID:    tenantID,
		Name:        "Flow from Template",
		Description: "Created from template " + templateID,
		Status:      models.FlowStatusDraft,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	s.flows[template.ID] = template
	return template, nil
}

// Export and import
func (s *MockOnboardingService) ExportFlow(ctx context.Context, flowID string) ([]byte, error) {
	return []byte("mock export data"), nil
}

func (s *MockOnboardingService) ImportFlow(ctx context.Context, tenantID string, data []byte) (*models.OnboardingFlow, error) {
	flow := &models.OnboardingFlow{
		ID:          "imported-flow-" + time.Now().Format("20060102150405"),
		TenantID:    tenantID,
		Name:        "Imported Flow",
		Description: "Flow imported from external data",
		Status:      models.FlowStatusDraft,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	s.flows[flow.ID] = flow
	return flow, nil
}

// MockOnboardingStepService implements OnboardingStepService interface
type MockOnboardingStepService struct{}

// NewMockOnboardingStepService creates a new mock step service
func NewMockOnboardingStepService() OnboardingStepService {
	return &MockOnboardingStepService{}
}

func (s *MockOnboardingStepService) UpdateStepContent(ctx context.Context, stepID string, content *models.StepContent) error {
	return nil
}

func (s *MockOnboardingStepService) ValidateStepContent(ctx context.Context, stepType models.StepType, content *models.StepContent) error {
	return nil
}

func (s *MockOnboardingStepService) ProcessFormSubmission(ctx context.Context, stepID string, formData map[string]interface{}) error {
	return nil
}

func (s *MockOnboardingStepService) ValidateFormData(ctx context.Context, formDefinition *models.FormDefinition, data map[string]interface{}) error {
	return nil
}

func (s *MockOnboardingStepService) ProcessQuizSubmission(ctx context.Context, stepID string, answers map[string]interface{}) (*QuizResult, error) {
	return &QuizResult{
		Score:       80,
		TotalPoints: 100,
		Passed:      true,
		Answers:     answers,
		Feedback:    "Good job!",
	}, nil
}

func (s *MockOnboardingStepService) CalculateQuizScore(ctx context.Context, quiz *models.QuizDefinition, answers map[string]interface{}) (int, error) {
	return 80, nil
}

func (s *MockOnboardingStepService) ProcessInteractiveAction(ctx context.Context, stepID string, action string, data map[string]interface{}) error {
	return nil
}

func (s *MockOnboardingStepService) EvaluateStepConditions(ctx context.Context, userID string, step *models.OnboardingStep) (bool, error) {
	return true, nil
}

func (s *MockOnboardingStepService) CheckStepRequirements(ctx context.Context, userID, stepID string) error {
	return nil
}

// MockOnboardingAnalyticsService implements OnboardingAnalyticsService interface
type MockOnboardingAnalyticsService struct{}

// NewMockOnboardingAnalyticsService creates a new mock analytics service
func NewMockOnboardingAnalyticsService() OnboardingAnalyticsService {
	return &MockOnboardingAnalyticsService{}
}

func (s *MockOnboardingAnalyticsService) CalculateFlowMetrics(ctx context.Context, flowID string) error {
	return nil
}

func (s *MockOnboardingAnalyticsService) GetFlowFunnel(ctx context.Context, flowID string) ([]FunnelStep, error) {
	return []FunnelStep{}, nil
}

func (s *MockOnboardingAnalyticsService) GetFlowDropoffPoints(ctx context.Context, flowID string) ([]DropoffPoint, error) {
	return []DropoffPoint{}, nil
}

func (s *MockOnboardingAnalyticsService) GetUserJourney(ctx context.Context, userID, flowID string) (*UserJourney, error) {
	return &UserJourney{
		UserID:    userID,
		FlowID:    flowID,
		Steps:     []JourneyStep{},
		TotalTime: 0,
		Completed: false,
		Metadata:  make(map[string]interface{}),
	}, nil
}

func (s *MockOnboardingAnalyticsService) GetUserEngagement(ctx context.Context, userID string) (*UserEngagement, error) {
	return &UserEngagement{
		UserID:          userID,
		TotalFlows:      1,
		CompletedFlows:  0,
		TotalTime:       0,
		LastActiveAt:    time.Now(),
		EngagementScore: 0.5,
	}, nil
}

func (s *MockOnboardingAnalyticsService) IdentifyStrugglingUsers(ctx context.Context, flowID string) ([]*models.UserProgress, error) {
	return []*models.UserProgress{}, nil
}

func (s *MockOnboardingAnalyticsService) GetPerformanceMetrics(ctx context.Context, tenantID string) (*PerformanceMetrics, error) {
	return &PerformanceMetrics{
		TenantID:              tenantID,
		AverageLoadTime:       1.5,
		AverageCompletionTime: 1800,
		ErrorRate:             0.01,
		UserSatisfaction:      4.2,
		LastUpdated:           time.Now(),
	}, nil
}

func (s *MockOnboardingAnalyticsService) GetTrends(ctx context.Context, tenantID string, days int) (*OnboardingTrends, error) {
	return &OnboardingTrends{
		TenantID:        tenantID,
		DateRange:       map[string]time.Time{"start": time.Now().AddDate(0, 0, -days), "end": time.Now()},
		CompletionTrend: []TrendPoint{},
		UsersTrend:      []TrendPoint{},
		TimeTrend:       []TrendPoint{},
		Insights:        []string{"Completion rate is stable", "User engagement is increasing"},
	}, nil
}

func (s *MockOnboardingAnalyticsService) CreateExperiment(ctx context.Context, experiment *OnboardingExperiment) error {
	return nil
}

func (s *MockOnboardingAnalyticsService) GetExperimentResults(ctx context.Context, experimentID string) (*ExperimentResults, error) {
	return &ExperimentResults{
		ExperimentID:    experimentID,
		Variants:        []VariantResult{},
		Winner:          nil,
		Confidence:      0.95,
		Significance:    true,
		Insights:        []string{},
		Recommendations: []string{},
	}, nil
}

func (s *MockOnboardingAnalyticsService) GenerateReport(ctx context.Context, tenantID string, reportType string, params map[string]interface{}) ([]byte, error) {
	return []byte("mock report data"), nil
}

func (s *MockOnboardingAnalyticsService) ScheduleReport(ctx context.Context, schedule *ReportSchedule) error {
	return nil
}

// Error definitions
var (
	ErrFlowNotFound     = &ServiceError{Code: "FLOW_NOT_FOUND", Message: "Flow not found"}
	ErrStepNotFound     = &ServiceError{Code: "STEP_NOT_FOUND", Message: "Step not found"}
	ErrProgressNotFound = &ServiceError{Code: "PROGRESS_NOT_FOUND", Message: "Progress not found"}
	ErrInvalidFlow      = &ServiceError{Code: "INVALID_FLOW", Message: "Invalid flow"}
	ErrInvalidStep      = &ServiceError{Code: "INVALID_STEP", Message: "Invalid step"}
)

// ServiceError represents a service error
type ServiceError struct {
	Code    string
	Message string
}

func (e *ServiceError) Error() string {
	return e.Message
}