package services

import (
	"context"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/onboarding/models"
)

// OnboardingService định nghĩa interface cho onboarding business logic
type OnboardingService interface {
	// Flow management
	CreateFlow(ctx context.Context, flow *models.OnboardingFlow) error
	GetFlow(ctx context.Context, flowID string) (*models.OnboardingFlow, error)
	UpdateFlow(ctx context.Context, flowID string, update *models.FlowUpdate) error
	DeleteFlow(ctx context.Context, flowID string) error
	ListFlows(ctx context.Context, filter *models.OnboardingFlowFilter) ([]*models.OnboardingFlow, int64, error)
	
	// Flow operations
	PublishFlow(ctx context.Context, flowID string) error
	UnpublishFlow(ctx context.Context, flowID string) error
	DuplicateFlow(ctx context.Context, flowID string, newName string) (*models.OnboardingFlow, error)
	SetDefaultFlow(ctx context.Context, flowID string) error
	
	// Step management
	CreateStep(ctx context.Context, step *models.OnboardingStep) error
	GetStep(ctx context.Context, stepID string) (*models.OnboardingStep, error)
	UpdateStep(ctx context.Context, stepID string, update *models.StepUpdate) error
	DeleteStep(ctx context.Context, stepID string) error
	ReorderSteps(ctx context.Context, flowID string, stepOrders []struct{ ID string; SortOrder int }) error
	
	// User progress
	StartFlow(ctx context.Context, userID, flowID string) (*models.UserProgress, error)
	GetUserProgress(ctx context.Context, userID, flowID string) (*models.UserProgress, error)
	UpdateProgress(ctx context.Context, userID, flowID string, stepID string, data map[string]interface{}) error
	CompleteStep(ctx context.Context, userID, flowID, stepID string, data map[string]interface{}) error
	SkipStep(ctx context.Context, userID, flowID, stepID string, reason string) error
	RestartFlow(ctx context.Context, userID, flowID string) error
	
	// Progress tracking
	GetNextStep(ctx context.Context, userID, flowID string) (*models.OnboardingStep, error)
	GetProgressSummary(ctx context.Context, userID, flowID string) (map[string]interface{}, error)
	ListUserProgresses(ctx context.Context, filter *models.UserProgressFilter) ([]*models.UserProgress, int64, error)
	
	// Flow discovery
	GetRecommendedFlows(ctx context.Context, userID string) ([]*models.OnboardingFlow, error)
	GetFlowsForRole(ctx context.Context, tenantID, role string) ([]*models.OnboardingFlow, error)
	SearchFlows(ctx context.Context, tenantID, query string) ([]*models.OnboardingFlow, error)
	
	// Analytics and statistics
	GetFlowStatistics(ctx context.Context, flowID string) (*models.FlowStatistics, error)
	GetStepStatistics(ctx context.Context, stepID string) (*models.StepStatistics, error)
	GetOnboardingStatistics(ctx context.Context, tenantID string) (*models.OnboardingStatistics, error)
	
	// Event tracking
	TrackEvent(ctx context.Context, event *models.OnboardingEvent) error
	GetUserEvents(ctx context.Context, userID string, limit int) ([]*models.OnboardingEvent, error)
	
	// Validation
	ValidateFlow(ctx context.Context, flow *models.OnboardingFlow) error
	ValidateStep(ctx context.Context, step *models.OnboardingStep) error
	ValidateStepData(ctx context.Context, stepID string, data map[string]interface{}) error
	
	// Templates and presets
	GetFlowTemplates(ctx context.Context) ([]*models.OnboardingFlow, error)
	CreateFromTemplate(ctx context.Context, templateID, tenantID string, customizations map[string]interface{}) (*models.OnboardingFlow, error)
	
	// Export and import
	ExportFlow(ctx context.Context, flowID string) ([]byte, error)
	ImportFlow(ctx context.Context, tenantID string, data []byte) (*models.OnboardingFlow, error)
}

// OnboardingStepService định nghĩa interface riêng cho step operations
type OnboardingStepService interface {
	// Content management
	UpdateStepContent(ctx context.Context, stepID string, content *models.StepContent) error
	ValidateStepContent(ctx context.Context, stepType models.StepType, content *models.StepContent) error
	
	// Form handling
	ProcessFormSubmission(ctx context.Context, stepID string, formData map[string]interface{}) error
	ValidateFormData(ctx context.Context, formDefinition *models.FormDefinition, data map[string]interface{}) error
	
	// Quiz handling
	ProcessQuizSubmission(ctx context.Context, stepID string, answers map[string]interface{}) (*QuizResult, error)
	CalculateQuizScore(ctx context.Context, quiz *models.QuizDefinition, answers map[string]interface{}) (int, error)
	
	// Interactive content
	ProcessInteractiveAction(ctx context.Context, stepID string, action string, data map[string]interface{}) error
	
	// Conditions
	EvaluateStepConditions(ctx context.Context, userID string, step *models.OnboardingStep) (bool, error)
	CheckStepRequirements(ctx context.Context, userID, stepID string) error
}

// OnboardingAnalyticsService định nghĩa interface cho analytics
type OnboardingAnalyticsService interface {
	// Flow analytics
	CalculateFlowMetrics(ctx context.Context, flowID string) error
	GetFlowFunnel(ctx context.Context, flowID string) ([]FunnelStep, error)
	GetFlowDropoffPoints(ctx context.Context, flowID string) ([]DropoffPoint, error)
	
	// User analytics
	GetUserJourney(ctx context.Context, userID, flowID string) (*UserJourney, error)
	GetUserEngagement(ctx context.Context, userID string) (*UserEngagement, error)
	IdentifyStrugglingUsers(ctx context.Context, flowID string) ([]*models.UserProgress, error)
	
	// Performance analytics
	GetPerformanceMetrics(ctx context.Context, tenantID string) (*PerformanceMetrics, error)
	GetTrends(ctx context.Context, tenantID string, days int) (*OnboardingTrends, error)
	
	// A/B testing
	CreateExperiment(ctx context.Context, experiment *OnboardingExperiment) error
	GetExperimentResults(ctx context.Context, experimentID string) (*ExperimentResults, error)
	
	// Reporting
	GenerateReport(ctx context.Context, tenantID string, reportType string, params map[string]interface{}) ([]byte, error)
	ScheduleReport(ctx context.Context, schedule *ReportSchedule) error
}

// OnboardingNotificationService định nghĩa interface cho notifications
type OnboardingNotificationService interface {
	// User notifications
	SendWelcomeNotification(ctx context.Context, userID, flowID string) error
	SendProgressReminder(ctx context.Context, userID, flowID string) error
	SendCompletionCongratulations(ctx context.Context, userID, flowID string) error
	SendStepHelp(ctx context.Context, userID, stepID string) error
	
	// Admin notifications
	NotifyFlowCompletion(ctx context.Context, userID, flowID string) error
	NotifyUserStuck(ctx context.Context, userID, flowID string) error
	NotifyLowCompletionRate(ctx context.Context, flowID string) error
	
	// Bulk notifications
	SendBulkReminders(ctx context.Context, flowID string, userIDs []string) error
	SendFlowUpdates(ctx context.Context, flowID string) error
}

// OnboardingConfigService định nghĩa interface cho configuration
type OnboardingConfigService interface {
	// Global settings
	GetGlobalSettings(ctx context.Context, tenantID string) (*models.OnboardingSettings, error)
	UpdateGlobalSettings(ctx context.Context, tenantID string, settings *models.OnboardingSettings) error
	
	// Feature flags
	IsFeatureEnabled(ctx context.Context, tenantID, feature string) (bool, error)
	EnableFeature(ctx context.Context, tenantID, feature string) error
	DisableFeature(ctx context.Context, tenantID, feature string) error
	
	// Customization
	GetThemeSettings(ctx context.Context, tenantID string) (*models.ThemeSettings, error)
	UpdateThemeSettings(ctx context.Context, tenantID string, theme *models.ThemeSettings) error
	
	// Integration settings
	GetIntegrationConfig(ctx context.Context, tenantID, integration string) (map[string]interface{}, error)
	UpdateIntegrationConfig(ctx context.Context, tenantID, integration string, config map[string]interface{}) error
}

// Additional models for services

// QuizResult kết quả quiz
type QuizResult struct {
	Score       int                    `json:"score"`
	TotalPoints int                    `json:"total_points"`
	Passed      bool                   `json:"passed"`
	Answers     map[string]interface{} `json:"answers"`
	Feedback    string                 `json:"feedback"`
}

// FunnelStep bước trong funnel
type FunnelStep struct {
	StepID     string  `json:"step_id"`
	StepName   string  `json:"step_name"`
	Users      int64   `json:"users"`
	Conversions int64  `json:"conversions"`
	Rate       float64 `json:"rate"`
}

// DropoffPoint điểm rời bỏ
type DropoffPoint struct {
	StepID      string  `json:"step_id"`
	StepName    string  `json:"step_name"`
	DropoffRate float64 `json:"dropoff_rate"`
	Reasons     []string `json:"reasons"`
}

// UserJourney hành trình user
type UserJourney struct {
	UserID      string                 `json:"user_id"`
	FlowID      string                 `json:"flow_id"`
	Steps       []JourneyStep          `json:"steps"`
	TotalTime   int                    `json:"total_time"`
	Completed   bool                   `json:"completed"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// JourneyStep bước trong journey
type JourneyStep struct {
	StepID      string    `json:"step_id"`
	StepName    string    `json:"step_name"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	TimeSpent   int       `json:"time_spent"`
	Status      string    `json:"status"`
	Actions     []string  `json:"actions"`
}

// UserEngagement mức độ engagement của user
type UserEngagement struct {
	UserID           string    `json:"user_id"`
	TotalFlows       int       `json:"total_flows"`
	CompletedFlows   int       `json:"completed_flows"`
	TotalTime        int       `json:"total_time"`
	LastActiveAt     time.Time `json:"last_active_at"`
	EngagementScore  float64   `json:"engagement_score"`
}

// PerformanceMetrics metrics hiệu suất
type PerformanceMetrics struct {
	TenantID              string    `json:"tenant_id"`
	AverageLoadTime       float64   `json:"average_load_time"`
	AverageCompletionTime int       `json:"average_completion_time"`
	ErrorRate             float64   `json:"error_rate"`
	UserSatisfaction      float64   `json:"user_satisfaction"`
	LastUpdated           time.Time `json:"last_updated"`
}

// OnboardingTrends xu hướng onboarding
type OnboardingTrends struct {
	TenantID       string                 `json:"tenant_id"`
	DateRange      map[string]time.Time   `json:"date_range"`
	CompletionTrend []TrendPoint          `json:"completion_trend"`
	UsersTrend     []TrendPoint          `json:"users_trend"`
	TimeTrend      []TrendPoint          `json:"time_trend"`
	Insights       []string              `json:"insights"`
}

// TrendPoint điểm trong trend
type TrendPoint struct {
	Date  time.Time `json:"date"`
	Value float64   `json:"value"`
}

// OnboardingExperiment thí nghiệm A/B
type OnboardingExperiment struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	FlowID      string                 `json:"flow_id"`
	Variants    []ExperimentVariant    `json:"variants"`
	Status      string                 `json:"status"`
	StartDate   time.Time              `json:"start_date"`
	EndDate     *time.Time             `json:"end_date"`
	Config      map[string]interface{} `json:"config"`
}

// ExperimentVariant biến thể thí nghiệm
type ExperimentVariant struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Traffic     float64                `json:"traffic"`
	Changes     map[string]interface{} `json:"changes"`
}

// ExperimentResults kết quả thí nghiệm
type ExperimentResults struct {
	ExperimentID   string                 `json:"experiment_id"`
	Variants       []VariantResult        `json:"variants"`
	Winner         *string                `json:"winner"`
	Confidence     float64                `json:"confidence"`
	Significance   bool                   `json:"significance"`
	Insights       []string               `json:"insights"`
	Recommendations []string              `json:"recommendations"`
}

// VariantResult kết quả của variant
type VariantResult struct {
	VariantID      string  `json:"variant_id"`
	Users          int64   `json:"users"`
	Completions    int64   `json:"completions"`
	CompletionRate float64 `json:"completion_rate"`
	AverageTime    int     `json:"average_time"`
}

// ReportSchedule lịch trình báo cáo
type ReportSchedule struct {
	ID          string                 `json:"id"`
	TenantID    string                 `json:"tenant_id"`
	ReportType  string                 `json:"report_type"`
	Frequency   string                 `json:"frequency"`
	Recipients  []string               `json:"recipients"`
	Config      map[string]interface{} `json:"config"`
	NextRun     time.Time              `json:"next_run"`
	IsActive    bool                   `json:"is_active"`
}

// OnboardingSettings cài đặt global
type OnboardingSettings struct {
	TenantID           string                 `json:"tenant_id"`
	DefaultFlow        string                 `json:"default_flow"`
	AutoStart          bool                   `json:"auto_start"`
	SaveProgress       bool                   `json:"save_progress"`
	ShowProgress       bool                   `json:"show_progress"`
	AllowSkip          bool                   `json:"allow_skip"`
	RequireCompletion  bool                   `json:"require_completion"`
	MaxSessionTime     int                    `json:"max_session_time"`
	ReminderSettings   ReminderSettings       `json:"reminder_settings"`
	CustomSettings     map[string]interface{} `json:"custom_settings"`
}

// ReminderSettings cài đặt reminder
type ReminderSettings struct {
	Enabled         bool `json:"enabled"`
	FirstReminder   int  `json:"first_reminder"`   // hours
	SecondReminder  int  `json:"second_reminder"`  // hours
	FinalReminder   int  `json:"final_reminder"`   // hours
	MaxReminders    int  `json:"max_reminders"`
}

// ThemeSettings cài đặt theme
type ThemeSettings struct {
	TenantID      string                 `json:"tenant_id"`
	PrimaryColor  string                 `json:"primary_color"`
	SecondaryColor string                `json:"secondary_color"`
	FontFamily    string                 `json:"font_family"`
	BorderRadius  string                 `json:"border_radius"`
	Animations    bool                   `json:"animations"`
	CustomCSS     string                 `json:"custom_css"`
	CustomJS      string                 `json:"custom_js"`
	Logo          string                 `json:"logo"`
	Favicon       string                 `json:"favicon"`
}