package models

import (
	"time"
)

// FlowStatus định nghĩa trạng thái onboarding flow
type FlowStatus string

const (
	FlowStatusActive   FlowStatus = "active"
	FlowStatusInactive FlowStatus = "inactive"
	FlowStatusDraft    FlowStatus = "draft"
	FlowStatusArchived FlowStatus = "archived"
)

// StepType định nghĩa loại step
type StepType string

const (
	StepTypeWelcome      StepType = "welcome"
	StepTypeSetup        StepType = "setup"
	StepTypeTutorial     StepType = "tutorial"
	StepTypeAction       StepType = "action"
	StepTypeForm         StepType = "form"
	StepTypeVideo        StepType = "video"
	StepTypeQuiz         StepType = "quiz"
	StepTypeCompletion   StepType = "completion"
)

// ProgressStatus định nghĩa trạng thái progress
type ProgressStatus string

const (
	ProgressStatusNotStarted ProgressStatus = "not_started"
	ProgressStatusInProgress ProgressStatus = "in_progress"
	ProgressStatusCompleted  ProgressStatus = "completed"
	ProgressStatusSkipped    ProgressStatus = "skipped"
	ProgressStatusFailed     ProgressStatus = "failed"
)

// OnboardingFlow đại diện cho một flow onboarding
type OnboardingFlow struct {
	ID           string     `json:"id" gorm:"primaryKey"`
	TenantID     string     `json:"tenant_id" validate:"required" gorm:"index"`
	Name         string     `json:"name" validate:"required,min=3,max=100"`
	Description  string     `json:"description" validate:"max=500"`
	Version      string     `json:"version" validate:"required"`
	Status       FlowStatus `json:"status" validate:"required"`
	IsDefault    bool       `json:"is_default"`
	
	// Target audience
	TargetRole   string `json:"target_role" validate:"max=50"`
	TargetLevel  string `json:"target_level" validate:"oneof=beginner intermediate advanced"`
	
	// Configuration
	Config       FlowConfig `json:"config" gorm:"embedded"`
	
	// Metadata
	Tags         []string `json:"tags" gorm:"type:json"`
	Priority     int      `json:"priority" validate:"min=0,max=100"`
	EstimatedTime int     `json:"estimated_time"` // minutes
	
	// Analytics
	CompletionRate float64 `json:"completion_rate"`
	AverageTime    int     `json:"average_time"` // minutes
	TotalStarts    int64   `json:"total_starts"`
	TotalCompletions int64 `json:"total_completions"`
	
	// Timestamps
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	PublishedAt  *time.Time `json:"published_at,omitempty"`
	
	// Relationships
	Steps        []OnboardingStep `json:"steps,omitempty" gorm:"foreignKey:FlowID;constraint:OnDelete:CASCADE"`
	Progresses   []UserProgress   `json:"progresses,omitempty" gorm:"foreignKey:FlowID"`
}

// FlowConfig chứa cấu hình của flow
type FlowConfig struct {
	AllowSkip           bool   `json:"allow_skip"`
	RequireCompletion   bool   `json:"require_completion"`
	ShowProgress        bool   `json:"show_progress"`
	AutoAdvance         bool   `json:"auto_advance"`
	SaveProgress        bool   `json:"save_progress"`
	TimeoutMinutes      int    `json:"timeout_minutes"`
	MaxAttempts         int    `json:"max_attempts"`
	RequiredRole        string `json:"required_role"`
	RequiredPermissions []string `json:"required_permissions" gorm:"type:json"`
	CustomCSS           string `json:"custom_css"`
	CustomJS            string `json:"custom_js"`
}

// OnboardingStep đại diện cho một bước trong flow
type OnboardingStep struct {
	ID           string   `json:"id" gorm:"primaryKey"`
	FlowID       string   `json:"flow_id" validate:"required" gorm:"index"`
	Name         string   `json:"name" validate:"required,min=3,max=100"`
	Title        string   `json:"title" validate:"required,min=3,max=200"`
	Description  string   `json:"description" validate:"max=1000"`
	Type         StepType `json:"type" validate:"required"`
	SortOrder    int      `json:"sort_order"`
	IsRequired   bool     `json:"is_required"`
	IsSkippable  bool     `json:"is_skippable"`
	
	// Content
	Content      StepContent `json:"content" gorm:"embedded"`
	
	// Conditions
	Conditions   StepConditions `json:"conditions" gorm:"embedded"`
	
	// Actions
	Actions      []StepAction `json:"actions" gorm:"type:json"`
	
	// Validation
	Validation   StepValidation `json:"validation" gorm:"embedded"`
	
	// Analytics
	CompletionRate float64 `json:"completion_rate"`
	AverageTime    int     `json:"average_time"` // seconds
	SkipRate       float64 `json:"skip_rate"`
	
	// Timestamps
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relationships
	Flow         *OnboardingFlow `json:"flow,omitempty" gorm:"foreignKey:FlowID"`
	Progresses   []StepProgress  `json:"progresses,omitempty" gorm:"foreignKey:StepID"`
}

// StepContent chứa nội dung của step
type StepContent struct {
	Text         string            `json:"text"`
	HTML         string            `json:"html"`
	Markdown     string            `json:"markdown"`
	VideoURL     string            `json:"video_url"`
	ImageURL     string            `json:"image_url"`
	AudioURL     string            `json:"audio_url"`
	Links        []ContentLink     `json:"links" gorm:"type:json"`
	Files        []ContentFile     `json:"files" gorm:"type:json"`
	Form         *FormDefinition   `json:"form,omitempty" gorm:"type:json"`
	Quiz         *QuizDefinition   `json:"quiz,omitempty" gorm:"type:json"`
	Interactive  *InteractiveContent `json:"interactive,omitempty" gorm:"type:json"`
	CustomData   map[string]interface{} `json:"custom_data" gorm:"type:json"`
}

// ContentLink đại diện cho một link trong content
type ContentLink struct {
	Text   string `json:"text"`
	URL    string `json:"url"`
	Target string `json:"target"` // _blank, _self, etc.
}

// ContentFile đại diện cho một file trong content
type ContentFile struct {
	Name     string `json:"name"`
	URL      string `json:"url"`
	Type     string `json:"type"`
	Size     int64  `json:"size"`
}

// FormDefinition định nghĩa form trong step
type FormDefinition struct {
	Fields     []FormField `json:"fields"`
	SubmitText string      `json:"submit_text"`
	Action     string      `json:"action"`
	Method     string      `json:"method"`
}

// FormField định nghĩa field trong form
type FormField struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Label       string                 `json:"label"`
	Placeholder string                 `json:"placeholder"`
	Required    bool                   `json:"required"`
	Options     []string               `json:"options,omitempty"`
	Validation  map[string]interface{} `json:"validation,omitempty"`
}

// QuizDefinition định nghĩa quiz trong step
type QuizDefinition struct {
	Questions     []QuizQuestion `json:"questions"`
	PassingScore  int            `json:"passing_score"`
	ShowResults   bool           `json:"show_results"`
	AllowRetry    bool           `json:"allow_retry"`
	MaxAttempts   int            `json:"max_attempts"`
}

// QuizQuestion định nghĩa câu hỏi trong quiz
type QuizQuestion struct {
	Text          string   `json:"text"`
	Type          string   `json:"type"` // multiple_choice, true_false, text
	Options       []string `json:"options,omitempty"`
	CorrectAnswer string   `json:"correct_answer"`
	Points        int      `json:"points"`
	Explanation   string   `json:"explanation,omitempty"`
}

// InteractiveContent định nghĩa nội dung tương tác
type InteractiveContent struct {
	Type       string                 `json:"type"` // tour, tooltip, highlight, etc.
	Target     string                 `json:"target"`
	Position   string                 `json:"position"`
	Config     map[string]interface{} `json:"config"`
}

// StepConditions định nghĩa điều kiện để hiển thị step
type StepConditions struct {
	RequiredSteps    []string `json:"required_steps" gorm:"type:json"`
	RequiredRole     string   `json:"required_role"`
	RequiredFeatures []string `json:"required_features" gorm:"type:json"`
	CustomCondition  string   `json:"custom_condition"`
}

// StepAction định nghĩa action khi hoàn thành step
type StepAction struct {
	Type       string                 `json:"type"` // redirect, api_call, show_modal, etc.
	Target     string                 `json:"target"`
	Method     string                 `json:"method,omitempty"`
	Data       map[string]interface{} `json:"data,omitempty"`
	Condition  string                 `json:"condition,omitempty"`
}

// StepValidation định nghĩa validation cho step
type StepValidation struct {
	Required        bool     `json:"required"`
	MinTime         int      `json:"min_time"` // seconds
	MaxTime         int      `json:"max_time"` // seconds
	RequiredFields  []string `json:"required_fields" gorm:"type:json"`
	CustomValidator string   `json:"custom_validator"`
}

// UserProgress đại diện cho tiến trình của user trong flow
type UserProgress struct {
	ID              string         `json:"id" gorm:"primaryKey"`
	UserID          string         `json:"user_id" validate:"required" gorm:"index"`
	FlowID          string         `json:"flow_id" validate:"required" gorm:"index"`
	TenantID        string         `json:"tenant_id" validate:"required" gorm:"index"`
	Status          ProgressStatus `json:"status" validate:"required"`
	CurrentStepID   *string        `json:"current_step_id,omitempty"`
	CompletedSteps  int            `json:"completed_steps"`
	TotalSteps      int            `json:"total_steps"`
	SkippedSteps    int            `json:"skipped_steps"`
	FailedSteps     int            `json:"failed_steps"`
	
	// Timing
	StartedAt       *time.Time `json:"started_at,omitempty"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	LastActiveAt    time.Time  `json:"last_active_at"`
	TotalTimeSpent  int        `json:"total_time_spent"` // seconds
	
	// Progress data
	ProgressData    map[string]interface{} `json:"progress_data" gorm:"type:json"`
	Metadata        map[string]interface{} `json:"metadata" gorm:"type:json"`
	
	// Analytics
	SessionCount    int    `json:"session_count"`
	DeviceInfo      string `json:"device_info"`
	UserAgent       string `json:"user_agent"`
	IPAddress       string `json:"ip_address"`
	
	// Timestamps
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	
	// Relationships
	Flow            *OnboardingFlow `json:"flow,omitempty" gorm:"foreignKey:FlowID"`
	CurrentStep     *OnboardingStep `json:"current_step,omitempty" gorm:"foreignKey:CurrentStepID"`
	StepProgresses  []StepProgress  `json:"step_progresses,omitempty" gorm:"foreignKey:UserProgressID"`
	
	// User info
	UserName        string `json:"user_name,omitempty"`
	UserEmail       string `json:"user_email,omitempty"`
}

// StepProgress đại diện cho tiến trình của user trong một step cụ thể
type StepProgress struct {
	ID             string         `json:"id" gorm:"primaryKey"`
	UserProgressID string         `json:"user_progress_id" validate:"required" gorm:"index"`
	StepID         string         `json:"step_id" validate:"required" gorm:"index"`
	Status         ProgressStatus `json:"status" validate:"required"`
	AttemptCount   int            `json:"attempt_count"`
	
	// Timing
	StartedAt      *time.Time `json:"started_at,omitempty"`
	CompletedAt    *time.Time `json:"completed_at,omitempty"`
	TimeSpent      int        `json:"time_spent"` // seconds
	
	// Data
	StepData       map[string]interface{} `json:"step_data" gorm:"type:json"`
	UserInput      map[string]interface{} `json:"user_input" gorm:"type:json"`
	ValidationErrors []string             `json:"validation_errors" gorm:"type:json"`
	
	// Quiz specific
	QuizScore      *int `json:"quiz_score,omitempty"`
	QuizAnswers    map[string]interface{} `json:"quiz_answers" gorm:"type:json"`
	
	// Timestamps
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	
	// Relationships
	UserProgress   *UserProgress   `json:"user_progress,omitempty" gorm:"foreignKey:UserProgressID"`
	Step           *OnboardingStep `json:"step,omitempty" gorm:"foreignKey:StepID"`
}

// OnboardingEvent đại diện cho events trong quá trình onboarding
type OnboardingEvent struct {
	ID         string                 `json:"id" gorm:"primaryKey"`
	UserID     string                 `json:"user_id" validate:"required" gorm:"index"`
	FlowID     string                 `json:"flow_id" validate:"required" gorm:"index"`
	StepID     *string                `json:"step_id,omitempty" gorm:"index"`
	TenantID   string                 `json:"tenant_id" validate:"required" gorm:"index"`
	EventType  string                 `json:"event_type" validate:"required"` // start, complete, skip, fail, etc.
	EventData  map[string]interface{} `json:"event_data" gorm:"type:json"`
	
	// Context
	SessionID  string `json:"session_id"`
	DeviceInfo string `json:"device_info"`
	UserAgent  string `json:"user_agent"`
	IPAddress  string `json:"ip_address"`
	Referrer   string `json:"referrer"`
	
	// Timestamps
	CreatedAt  time.Time `json:"created_at"`
}

// Filter models

// OnboardingFlowFilter để filter flows
type OnboardingFlowFilter struct {
	Page       int        `json:"page"`
	PageSize   int        `json:"page_size"`
	TenantID   string     `json:"tenant_id,omitempty"`
	Status     FlowStatus `json:"status,omitempty"`
	TargetRole string     `json:"target_role,omitempty"`
	TargetLevel string    `json:"target_level,omitempty"`
	Tags       []string   `json:"tags,omitempty"`
	Query      string     `json:"query,omitempty"`
	SortBy     string     `json:"sort_by,omitempty"`
	SortOrder  string     `json:"sort_order,omitempty"`
}

// UserProgressFilter để filter user progress
type UserProgressFilter struct {
	Page      int            `json:"page"`
	PageSize  int            `json:"page_size"`
	UserID    string         `json:"user_id,omitempty"`
	FlowID    string         `json:"flow_id,omitempty"`
	TenantID  string         `json:"tenant_id,omitempty"`
	Status    ProgressStatus `json:"status,omitempty"`
	DateFrom  *time.Time     `json:"date_from,omitempty"`
	DateTo    *time.Time     `json:"date_to,omitempty"`
	SortBy    string         `json:"sort_by,omitempty"`
	SortOrder string         `json:"sort_order,omitempty"`
}

// Update models

// FlowUpdate để cập nhật flow
type FlowUpdate struct {
	Name          *string     `json:"name,omitempty"`
	Description   *string     `json:"description,omitempty"`
	Status        *FlowStatus `json:"status,omitempty"`
	IsDefault     *bool       `json:"is_default,omitempty"`
	TargetRole    *string     `json:"target_role,omitempty"`
	TargetLevel   *string     `json:"target_level,omitempty"`
	Config        *FlowConfig `json:"config,omitempty"`
	Tags          []string    `json:"tags,omitempty"`
	Priority      *int        `json:"priority,omitempty"`
	EstimatedTime *int        `json:"estimated_time,omitempty"`
}

// StepUpdate để cập nhật step
type StepUpdate struct {
	Name        *string         `json:"name,omitempty"`
	Title       *string         `json:"title,omitempty"`
	Description *string         `json:"description,omitempty"`
	Type        *StepType       `json:"type,omitempty"`
	SortOrder   *int            `json:"sort_order,omitempty"`
	IsRequired  *bool           `json:"is_required,omitempty"`
	IsSkippable *bool           `json:"is_skippable,omitempty"`
	Content     *StepContent    `json:"content,omitempty"`
	Conditions  *StepConditions `json:"conditions,omitempty"`
	Actions     []StepAction    `json:"actions,omitempty"`
	Validation  *StepValidation `json:"validation,omitempty"`
}

// Statistics models

// OnboardingStatistics thống kê onboarding
type OnboardingStatistics struct {
	TenantID            string    `json:"tenant_id"`
	TotalFlows          int64     `json:"total_flows"`
	ActiveFlows         int64     `json:"active_flows"`
	TotalUsers          int64     `json:"total_users"`
	CompletedUsers      int64     `json:"completed_users"`
	InProgressUsers     int64     `json:"in_progress_users"`
	AverageCompletionRate float64 `json:"average_completion_rate"`
	AverageCompletionTime int     `json:"average_completion_time"` // minutes
	MostPopularFlow     string    `json:"most_popular_flow"`
	LastUpdated         time.Time `json:"last_updated"`
}

// FlowStatistics thống kê chi tiết của flow
type FlowStatistics struct {
	FlowID            string    `json:"flow_id"`
	TotalStarts       int64     `json:"total_starts"`
	TotalCompletions  int64     `json:"total_completions"`
	CompletionRate    float64   `json:"completion_rate"`
	AverageTime       int       `json:"average_time"` // minutes
	DropoffRate       float64   `json:"dropoff_rate"`
	MostSkippedStep   string    `json:"most_skipped_step"`
	MostFailedStep    string    `json:"most_failed_step"`
	UserSatisfaction  float64   `json:"user_satisfaction"`
	LastUpdated       time.Time `json:"last_updated"`
}

// StepStatistics thống kê chi tiết của step
type StepStatistics struct {
	StepID         string    `json:"step_id"`
	TotalViews     int64     `json:"total_views"`
	TotalCompletions int64   `json:"total_completions"`
	CompletionRate float64   `json:"completion_rate"`
	SkipRate       float64   `json:"skip_rate"`
	FailureRate    float64   `json:"failure_rate"`
	AverageTime    int       `json:"average_time"` // seconds
	LastUpdated    time.Time `json:"last_updated"`
}

// OnboardingSettings represents onboarding settings
type OnboardingSettings struct {
	AutoStart        bool   `json:"auto_start"`
	AllowSkip        bool   `json:"allow_skip"`
	ShowProgress     bool   `json:"show_progress"`
	DefaultLanguage  string `json:"default_language"`
	RequiredFields   []string `json:"required_fields"`
	OptionalFields   []string `json:"optional_fields"`
	NotifyOnComplete bool   `json:"notify_on_complete"`
}

// ThemeSettings represents theme settings for onboarding UI
type ThemeSettings struct {
	PrimaryColor   string `json:"primary_color"`
	SecondaryColor string `json:"secondary_color"`
	BackgroundColor string `json:"background_color"`
	TextColor      string `json:"text_color"`
	FontFamily     string `json:"font_family"`
	BorderRadius   string `json:"border_radius"`
	Animation      bool   `json:"animation"`
}