package storage

import (
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/onboarding/models"
)

// MemoryOnboardingRepository implements in-memory storage for onboarding flows
type MemoryOnboardingRepository struct {
	flows map[string]*models.OnboardingFlow
	mu    sync.RWMutex
}

// NewMemoryOnboardingRepository creates a new in-memory onboarding repository
func NewMemoryOnboardingRepository() *MemoryOnboardingRepository {
	return &MemoryOnboardingRepository{
		flows: make(map[string]*models.OnboardingFlow),
	}
}

// MemoryProgressRepository implements in-memory storage for user progress
type MemoryProgressRepository struct {
	progress map[string]*models.UserProgress
	mu       sync.RWMutex
}

// NewMemoryProgressRepository creates a new in-memory progress repository
func NewMemoryProgressRepository() *MemoryProgressRepository {
	return &MemoryProgressRepository{
		progress: make(map[string]*models.UserProgress),
	}
}

// MemoryStepRepository implements in-memory storage for onboarding steps
type MemoryStepRepository struct {
	steps map[string]*models.OnboardingStep
	mu    sync.RWMutex
}

// NewMemoryStepRepository creates a new in-memory step repository
func NewMemoryStepRepository() *MemoryStepRepository {
	return &MemoryStepRepository{
		steps: make(map[string]*models.OnboardingStep),
	}
}

// MemoryEventRepository implements in-memory storage for onboarding events
type MemoryEventRepository struct {
	events []*models.OnboardingEvent
	mu     sync.RWMutex
}

// NewMemoryEventRepository creates a new in-memory event repository
func NewMemoryEventRepository() *MemoryEventRepository {
	return &MemoryEventRepository{
		events: make([]*models.OnboardingEvent, 0),
	}
}

// OnboardingRepository methods
func (r *MemoryOnboardingRepository) Create(flow *models.OnboardingFlow) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	flow.CreatedAt = time.Now()
	flow.UpdatedAt = time.Now()
	r.flows[flow.ID] = flow
	return nil
}

func (r *MemoryOnboardingRepository) GetByID(id string) (*models.OnboardingFlow, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if flow, exists := r.flows[id]; exists {
		return flow, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryOnboardingRepository) Update(flow *models.OnboardingFlow) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.flows[flow.ID]; !exists {
		return ErrNotFound
	}
	
	flow.UpdatedAt = time.Now()
	r.flows[flow.ID] = flow
	return nil
}

func (r *MemoryOnboardingRepository) Delete(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.flows[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.flows, id)
	return nil
}

func (r *MemoryOnboardingRepository) List(filter *models.OnboardingFlowFilter) ([]*models.OnboardingFlow, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	flows := make([]*models.OnboardingFlow, 0)
	
	for _, flow := range r.flows {
		if filter.TenantID != "" && flow.TenantID != filter.TenantID {
			continue
		}
		if filter.Status != "" && flow.Status != filter.Status {
			continue
		}
		flows = append(flows, flow)
	}
	
	return flows, int64(len(flows)), nil
}

// ProgressRepository methods
func (r *MemoryProgressRepository) Create(progress *models.UserProgress) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	progress.CreatedAt = time.Now()
	progress.UpdatedAt = time.Now()
	r.progress[progress.ID] = progress
	return nil
}

func (r *MemoryProgressRepository) GetByID(id string) (*models.UserProgress, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if progress, exists := r.progress[id]; exists {
		return progress, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryProgressRepository) GetByUserAndFlow(userID, flowID string) (*models.UserProgress, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	progressID := userID + "-" + flowID
	if progress, exists := r.progress[progressID]; exists {
		return progress, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryProgressRepository) Update(progress *models.UserProgress) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.progress[progress.ID]; !exists {
		return ErrNotFound
	}
	
	progress.UpdatedAt = time.Now()
	r.progress[progress.ID] = progress
	return nil
}

func (r *MemoryProgressRepository) Delete(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.progress[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.progress, id)
	return nil
}

func (r *MemoryProgressRepository) List(filter *models.UserProgressFilter) ([]*models.UserProgress, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	progresses := make([]*models.UserProgress, 0)
	
	for _, progress := range r.progress {
		if filter.UserID != "" && progress.UserID != filter.UserID {
			continue
		}
		if filter.FlowID != "" && progress.FlowID != filter.FlowID {
			continue
		}
		progresses = append(progresses, progress)
	}
	
	return progresses, int64(len(progresses)), nil
}

// StepRepository methods
func (r *MemoryStepRepository) Create(step *models.OnboardingStep) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	step.CreatedAt = time.Now()
	step.UpdatedAt = time.Now()
	r.steps[step.ID] = step
	return nil
}

func (r *MemoryStepRepository) GetByID(id string) (*models.OnboardingStep, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if step, exists := r.steps[id]; exists {
		return step, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryStepRepository) Update(step *models.OnboardingStep) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.steps[step.ID]; !exists {
		return ErrNotFound
	}
	
	step.UpdatedAt = time.Now()
	r.steps[step.ID] = step
	return nil
}

func (r *MemoryStepRepository) Delete(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.steps[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.steps, id)
	return nil
}

func (r *MemoryStepRepository) GetByFlowID(flowID string) ([]*models.OnboardingStep, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	steps := make([]*models.OnboardingStep, 0)
	for _, step := range r.steps {
		if step.FlowID == flowID {
			steps = append(steps, step)
		}
	}
	
	return steps, nil
}

// EventRepository methods
func (r *MemoryEventRepository) Create(event *models.OnboardingEvent) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	event.CreatedAt = time.Now()
	r.events = append(r.events, event)
	return nil
}

func (r *MemoryEventRepository) GetByUserID(userID string, limit int) ([]*models.OnboardingEvent, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	events := make([]*models.OnboardingEvent, 0)
	count := 0
	
	// Iterate in reverse order to get latest events first
	for i := len(r.events) - 1; i >= 0 && count < limit; i-- {
		if r.events[i].UserID == userID {
			events = append(events, r.events[i])
			count++
		}
	}
	
	return events, nil
}

func (r *MemoryEventRepository) GetByFlowID(flowID string, limit int) ([]*models.OnboardingEvent, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	events := make([]*models.OnboardingEvent, 0)
	count := 0
	
	// Iterate in reverse order to get latest events first
	for i := len(r.events) - 1; i >= 0 && count < limit; i-- {
		if r.events[i].FlowID == flowID {
			events = append(events, r.events[i])
			count++
		}
	}
	
	return events, nil
}

// Error definitions
var (
	ErrNotFound = &StorageError{Code: "NOT_FOUND", Message: "Record not found"}
)

// StorageError represents a storage error
type StorageError struct {
	Code    string
	Message string
}

func (e *StorageError) Error() string {
	return e.Message
}