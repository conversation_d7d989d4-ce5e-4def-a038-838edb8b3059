package services

import (
	"context"
	"time"

	userModels "github.com/blog-api-v3/blog-api-v3/modules/user/models"
)

// UserService defines the interface for user business logic
type UserService interface {
	// Get<PERSON><PERSON> retrieves a user by ID
	GetUser(ctx context.Context, userID string) (*userModels.UserProfile, error)
	
	// GetUserByEmail retrieves a user by email
	GetUserByEmail(ctx context.Context, email string) (*userModels.UserProfile, error)
	
	// GetUserByUsername retrieves a user by username
	GetUserByUsername(ctx context.Context, username string) (*userModels.UserProfile, error)
	
	// Create<PERSON>ser creates a new user
	CreateUser(ctx context.Context, user *userModels.UserProfile) error
	
	// UpdateUser updates user profile
	UpdateUser(ctx context.Context, userID string, update *userModels.UserUpdate) error
	
	// DeleteUser soft deletes a user
	DeleteUser(ctx context.Context, userID string) error
	
	// RestoreU<PERSON> restores a soft deleted user
	RestoreUser(ctx context.Context, userID string) error
	
	// PermanentlyDeleteUser permanently deletes a user
	PermanentlyDeleteUser(ctx context.Context, userID string) error
	
	// ListUsers lists users with filters
	ListUsers(ctx context.Context, filter *userModels.UserFilter) ([]*userModels.UserProfile, int64, error)
	
	// SearchUsers searches users
	SearchUsers(ctx context.Context, query string, limit int) ([]*userModels.UserProfile, error)
	
	// GetUserStatistics retrieves user statistics
	GetUserStatistics(ctx context.Context, userID string) (*userModels.ProfileStatistics, error)
	
	// GetUserActivities retrieves user activities
	GetUserActivities(ctx context.Context, userID string, limit int) ([]*userModels.UserActivity, error)
	
	// UpdateUserStatus updates user status
	UpdateUserStatus(ctx context.Context, userID string, status userModels.UserStatus) error
	
	// SuspendUser suspends a user
	SuspendUser(ctx context.Context, userID string, reason string) error
	
	// UnsuspendUser unsuspends a user
	UnsuspendUser(ctx context.Context, userID string) error
	
	// UpdateLastSeen updates user's last seen timestamp
	UpdateLastSeen(ctx context.Context, userID string) error
	
	// VerifyEmail marks email as verified
	VerifyEmail(ctx context.Context, userID string) error
	
	// VerifyPhone marks phone as verified
	VerifyPhone(ctx context.Context, userID string) error
	
	// EnableTwoFactor enables two-factor authentication
	EnableTwoFactor(ctx context.Context, userID string) error
	
	// DisableTwoFactor disables two-factor authentication
	DisableTwoFactor(ctx context.Context, userID string) error
	
	// GetPreferences retrieves user preferences
	GetPreferences(ctx context.Context, userID string) (*userModels.UserPreferences, error)
	
	// UpdatePreferences updates user preferences
	UpdatePreferences(ctx context.Context, userID string, prefs *userModels.UserPreferences) error
	
	// LogActivity logs user activity
	LogActivity(ctx context.Context, userID, action, resource, resourceID string, metadata map[string]interface{}) error
	
	// ExportUserData exports all user data (GDPR compliance)
	ExportUserData(ctx context.Context, userID string) (map[string]interface{}, error)
}

// UserRelationshipService defines the interface for user relationships
type UserRelationshipService interface {
	// FollowUser follows another user
	FollowUser(ctx context.Context, followerID, followedID string) error
	
	// UnfollowUser unfollows a user
	UnfollowUser(ctx context.Context, followerID, followedID string) error
	
	// IsFollowing checks if following
	IsFollowing(ctx context.Context, followerID, followedID string) (bool, error)
	
	// GetFollowers retrieves followers
	GetFollowers(ctx context.Context, userID string, page, pageSize int) ([]*userModels.UserProfile, int64, error)
	
	// GetFollowing retrieves following
	GetFollowing(ctx context.Context, userID string, page, pageSize int) ([]*userModels.UserProfile, int64, error)
	
	// BlockUser blocks a user
	BlockUser(ctx context.Context, blockerID, blockedID string) error
	
	// UnblockUser unblocks a user
	UnblockUser(ctx context.Context, blockerID, blockedID string) error
	
	// IsBlocked checks if blocked
	IsBlocked(ctx context.Context, blockerID, blockedID string) (bool, error)
	
	// GetBlockedUsers retrieves blocked users
	GetBlockedUsers(ctx context.Context, userID string, page, pageSize int) ([]*userModels.UserProfile, int64, error)
	
	// GetMutualFollowers retrieves mutual followers
	GetMutualFollowers(ctx context.Context, userID1, userID2 string) ([]*userModels.UserProfile, error)
	
	// GetSuggestedUsers retrieves suggested users to follow
	GetSuggestedUsers(ctx context.Context, userID string, limit int) ([]*userModels.UserProfile, error)
}

// UserNotificationService defines the interface for user notifications
type UserNotificationService interface {
	// SendWelcomeEmail sends welcome email to new user
	SendWelcomeEmail(ctx context.Context, user *userModels.UserProfile) error
	
	// SendVerificationEmail sends email verification
	SendVerificationEmail(ctx context.Context, user *userModels.UserProfile, token string) error
	
	// SendPhoneVerification sends phone verification
	SendPhoneVerification(ctx context.Context, user *userModels.UserProfile, code string) error
	
	// SendPasswordResetEmail sends password reset email
	SendPasswordResetEmail(ctx context.Context, user *userModels.UserProfile, token string) error
	
	// SendAccountSuspendedEmail sends account suspended notification
	SendAccountSuspendedEmail(ctx context.Context, user *userModels.UserProfile, reason string) error
	
	// SendAccountDeletedEmail sends account deleted notification
	SendAccountDeletedEmail(ctx context.Context, user *userModels.UserProfile) error
	
	// NotifyFollower notifies when someone follows
	NotifyFollower(ctx context.Context, follower, followed *userModels.UserProfile) error
}

// UserValidationService defines the interface for user validation
type UserValidationService interface {
	// ValidateUsername validates username format and availability
	ValidateUsername(ctx context.Context, username string, excludeUserID string) error
	
	// ValidateEmail validates email format and availability
	ValidateEmail(ctx context.Context, email string, excludeUserID string) error
	
	// ValidatePhoneNumber validates phone number format
	ValidatePhoneNumber(ctx context.Context, phoneNumber string) error
	
	// ValidateProfile validates user profile data
	ValidateProfile(ctx context.Context, profile *userModels.UserProfile) error
	
	// ValidateUpdate validates update data
	ValidateUpdate(ctx context.Context, update *userModels.UserUpdate) error
	
	// ValidateAge validates user age (must be 13+)
	ValidateAge(ctx context.Context, dateOfBirth *time.Time) error
}