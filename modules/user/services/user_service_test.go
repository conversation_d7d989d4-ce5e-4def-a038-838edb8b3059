package services

import (
	"context"
	"testing"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/modules/user/repositories"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMockUserService_CreateUser(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			ID:        utils.GenerateULID(),
			Email:     "<EMAIL>",
			Username:  "testuser123",
			FirstName: "Test",
			LastName:  "User",
		},
		Bio:      "Test bio",
		Language: "en",
		Timezone: "UTC",
	}

	// Act
	err := userService.CreateUser(ctx, user)

	// Assert
	require.NoError(t, err)
	
	// Verify user was created
	createdUser, err := userService.GetUser(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, user.Email, createdUser.Email)
	assert.Equal(t, user.Username, createdUser.Username)
}

func TestMockUserService_CreateUser_DuplicateEmail(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user1 := &models.UserProfile{
		User: models.User{
			ID:       utils.GenerateULID(),
			Email:    "<EMAIL>",
			Username: "testuser1",
		},
	}
	
	user2 := &models.UserProfile{
		User: models.User{
			ID:       utils.GenerateULID(),
			Email:    "<EMAIL>", // Same email
			Username: "testuser2",
		},
	}

	// Act
	err1 := userService.CreateUser(ctx, user1)
	err2 := userService.CreateUser(ctx, user2)

	// Assert
	require.NoError(t, err1)
	require.Error(t, err2)
	assert.Contains(t, err2.Error(), "email already exists")
}

func TestMockUserService_GetUser(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()

	// Act - get existing seeded user
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")

	// Assert
	require.NoError(t, err)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "admin", user.Username)
	assert.Equal(t, "Admin", user.FirstName)
}

func TestMockUserService_UpdateUser(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	// Get existing user
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	newBio := "Updated bio"
	newLocation := "Updated location"
	
	update := &models.UserUpdate{
		Bio:      &newBio,
		Location: &newLocation,
	}

	// Act
	err = userService.UpdateUser(ctx, user.ID, update)

	// Assert
	require.NoError(t, err)
	
	// Verify update
	updatedUser, err := userService.GetUser(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, newBio, updatedUser.Bio)
	assert.Equal(t, newLocation, updatedUser.Location)
}

func TestMockUserService_DeleteUser(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			ID:       utils.GenerateULID(),
			Email:    "<EMAIL>",
			Username: "deleteuser",
		},
	}
	
	err := userService.CreateUser(ctx, user)
	require.NoError(t, err)

	// Act
	err = userService.DeleteUser(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	
	// Verify user is marked as deleted
	deletedUser, err := userService.GetUser(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, models.UserStatusDeleted, deletedUser.Status)
	assert.False(t, deletedUser.IsActive)
}

func TestMockUserService_SearchUsers(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()

	// Act
	users, err := userService.SearchUsers(ctx, "admin", 10)

	// Assert
	require.NoError(t, err)
	assert.Len(t, users, 1)
	assert.Equal(t, "<EMAIL>", users[0].Email)
}

func TestMockUserService_ListUsers(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	filter := &models.UserFilter{
		Page:      1,
		PageSize:  20,
		Status:    models.UserStatusActive,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// Act
	users, total, err := userService.ListUsers(ctx, filter)

	// Assert
	require.NoError(t, err)
	assert.Greater(t, len(users), 0)
	assert.Greater(t, total, int64(0))
}

func TestMockUserService_SuspendUser(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	// Act
	err = userService.SuspendUser(ctx, user.ID, "Violation of terms")

	// Assert
	require.NoError(t, err)
	
	// Verify suspension
	suspendedUser, err := userService.GetUser(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, models.UserStatusSuspended, suspendedUser.Status)
	assert.False(t, suspendedUser.IsActive)
}

func TestMockUserService_VerifyEmail(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			ID:            utils.GenerateULID(),
			Email:         "<EMAIL>",
			Username:      "verifyuser",
			EmailVerified: false,
		},
	}
	
	err := userService.CreateUser(ctx, user)
	require.NoError(t, err)

	// Act
	err = userService.VerifyEmail(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	
	// Verify email verification
	verifiedUser, err := userService.GetUser(ctx, user.ID)
	require.NoError(t, err)
	assert.True(t, verifiedUser.EmailVerified)
	assert.True(t, verifiedUser.IsVerified)
}

func TestMockUserService_UpdatePreferences(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	newPrefs := &models.UserPreferences{
		EmailNotifications:   false,
		PushNotifications:    true,
		PreferredTheme:       "light",
		PreferredLanguage:    "vi",
		PublicProfile:        false,
		ShowLocation:         false,
		ShowLastSeen:         false,
		ShowEmail:            false,
		ShowPhone:            false,
		AllowMessagesFromAll: false,
		TwoFactorEnabled:     true,
	}

	// Act
	err = userService.UpdatePreferences(ctx, user.ID, newPrefs)

	// Assert
	require.NoError(t, err)
	
	// Verify preferences update
	prefs, err := userService.GetPreferences(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, newPrefs.EmailNotifications, prefs.EmailNotifications)
	assert.Equal(t, newPrefs.PreferredTheme, prefs.PreferredTheme)
	assert.Equal(t, newPrefs.PreferredLanguage, prefs.PreferredLanguage)
}

func TestMockUserService_GetUserStatistics(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	// Act
	stats, err := userService.GetUserStatistics(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	assert.Equal(t, user.ID, stats.UserID)
	assert.Greater(t, stats.TotalPosts, int64(0))
	assert.Greater(t, stats.TotalComments, int64(0))
}

func TestMockUserService_ExportUserData(t *testing.T) {
	// Arrange
	userRepo := repositories.NewMockUserRepository()
	logger := utils.NewMockLogger()
	userService := NewMockUserService(userRepo, logger)
	
	ctx := context.Background()
	
	user, err := userService.GetUserByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	// Act
	data, err := userService.ExportUserData(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	assert.Contains(t, data, "profile")
	assert.Contains(t, data, "statistics")
	assert.Contains(t, data, "activities")
	assert.Contains(t, data, "exported_at")
	
	profile := data["profile"].(*models.UserProfile)
	assert.Equal(t, user.ID, profile.ID)
}