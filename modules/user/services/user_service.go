package services

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/modules/user/repositories"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockUserService implements UserService interface
type MockUserService struct {
	userRepo   repositories.UserRepository
	logger     utils.Logger
}

// NewMockUserService creates a new mock user service
func NewMockUserService(userRepo repositories.UserRepository, logger utils.Logger) UserService {
	return &MockUserService{
		userRepo: userRepo,
		logger:   logger,
	}
}

// GetUser retrieves a user by ID
func (s *MockUserService) GetUser(ctx context.Context, userID string) (*models.UserProfile, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user")
		return nil, err
	}
	return user, nil
}

// GetUserByEmail retrieves a user by email
func (s *MockUserService) GetUserByEmail(ctx context.Context, email string) (*models.UserProfile, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

// GetUserByUsername retrieves a user by username
func (s *MockUserService) GetUserByUsername(ctx context.Context, username string) (*models.UserProfile, error) {
	return s.userRepo.GetByUsername(ctx, username)
}

// CreateUser creates a new user
func (s *MockUserService) CreateUser(ctx context.Context, user *models.UserProfile) error {
	// Validate user data
	if user.Email == "" || user.Username == "" {
		return errors.New("email and username are required")
	}
	
	// Check if email exists
	if exists, _ := s.userRepo.ExistsByEmail(ctx, user.Email); exists {
		return errors.New("email already exists")
	}
	
	// Check if username exists
	if exists, _ := s.userRepo.ExistsByUsername(ctx, user.Username); exists {
		return errors.New("username already exists")
	}
	
	// Create user
	if err := s.userRepo.Create(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to create user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, user.ID, "user_created", "user", user.ID, nil)
	
	return nil
}

// UpdateUser updates user profile
func (s *MockUserService) UpdateUser(ctx context.Context, userID string, update *models.UserUpdate) error {
	// Get existing user
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}
	
	// Build updates map
	updates := make(map[string]interface{})
	
	if update.FirstName != nil {
		updates["first_name"] = *update.FirstName
	}
	if update.LastName != nil {
		updates["last_name"] = *update.LastName
	}
	if update.Bio != nil {
		updates["bio"] = *update.Bio
	}
	if update.Location != nil {
		updates["location"] = *update.Location
	}
	if update.Website != nil {
		updates["website"] = *update.Website
	}
	if update.PhoneNumber != nil {
		updates["phone_number"] = *update.PhoneNumber
	}
	if update.Language != nil {
		updates["language"] = *update.Language
	}
	if update.Timezone != nil {
		updates["timezone"] = *update.Timezone
	}
	if update.DateOfBirth != nil {
		updates["date_of_birth"] = update.DateOfBirth
	}
	if update.Preferences != nil {
		updates["preferences"] = *update.Preferences
	}
	if update.SocialLinks != nil {
		updates["social_links"] = *update.SocialLinks
	}
	
	// Update user
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to update user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "profile_updated", "user", userID, updates)
	
	return nil
}

// DeleteUser soft deletes a user
func (s *MockUserService) DeleteUser(ctx context.Context, userID string) error {
	if err := s.userRepo.Delete(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to delete user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "user_deleted", "user", userID, nil)
	
	return nil
}

// RestoreUser restores a soft deleted user
func (s *MockUserService) RestoreUser(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"status":     models.UserStatusActive,
		"is_active":  true,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to restore user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "user_restored", "user", userID, nil)
	
	return nil
}

// PermanentlyDeleteUser permanently deletes a user
func (s *MockUserService) PermanentlyDeleteUser(ctx context.Context, userID string) error {
	if err := s.userRepo.HardDelete(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to permanently delete user")
		return err
	}
	
	return nil
}

// ListUsers lists users with filters
func (s *MockUserService) ListUsers(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, int64, error) {
	// Set defaults
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.PageSize < 1 {
		filter.PageSize = 20
	}
	if filter.PageSize > 100 {
		filter.PageSize = 100
	}
	if filter.SortBy == "" {
		filter.SortBy = "created_at"
	}
	if filter.SortOrder == "" {
		filter.SortOrder = "desc"
	}
	
	return s.userRepo.List(ctx, filter)
}

// SearchUsers searches users
func (s *MockUserService) SearchUsers(ctx context.Context, query string, limit int) ([]*models.UserProfile, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	
	return s.userRepo.Search(ctx, query, limit)
}

// GetUserStatistics retrieves user statistics
func (s *MockUserService) GetUserStatistics(ctx context.Context, userID string) (*models.ProfileStatistics, error) {
	return s.userRepo.GetStatistics(ctx, userID)
}

// GetUserActivities retrieves user activities
func (s *MockUserService) GetUserActivities(ctx context.Context, userID string, limit int) ([]*models.UserActivity, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	
	return s.userRepo.GetActivities(ctx, userID, limit)
}

// UpdateUserStatus updates user status
func (s *MockUserService) UpdateUserStatus(ctx context.Context, userID string, status models.UserStatus) error {
	if err := s.userRepo.UpdateStatus(ctx, userID, status); err != nil {
		s.logger.WithError(err).Error("Failed to update user status")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "status_changed", "user", userID, map[string]interface{}{
		"new_status": status,
	})
	
	return nil
}

// SuspendUser suspends a user
func (s *MockUserService) SuspendUser(ctx context.Context, userID string, reason string) error {
	updates := map[string]interface{}{
		"status":    models.UserStatusSuspended,
		"is_active": false,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to suspend user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "user_suspended", "user", userID, map[string]interface{}{
		"reason": reason,
	})
	
	return nil
}

// UnsuspendUser unsuspends a user
func (s *MockUserService) UnsuspendUser(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"status":    models.UserStatusActive,
		"is_active": true,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to unsuspend user")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "user_unsuspended", "user", userID, nil)
	
	return nil
}

// UpdateLastSeen updates user's last seen timestamp
func (s *MockUserService) UpdateLastSeen(ctx context.Context, userID string) error {
	return s.userRepo.UpdateLastSeen(ctx, userID)
}

// VerifyEmail marks email as verified
func (s *MockUserService) VerifyEmail(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"email_verified": true,
		"is_verified":    true,
		"verified_at":    time.Now(),
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to verify email")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "email_verified", "user", userID, nil)
	
	return nil
}

// VerifyPhone marks phone as verified
func (s *MockUserService) VerifyPhone(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"phone_verified": true,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to verify phone")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "phone_verified", "user", userID, nil)
	
	return nil
}

// EnableTwoFactor enables two-factor authentication
func (s *MockUserService) EnableTwoFactor(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"two_factor_enabled": true,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to enable 2FA")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "2fa_enabled", "user", userID, nil)
	
	return nil
}

// DisableTwoFactor disables two-factor authentication
func (s *MockUserService) DisableTwoFactor(ctx context.Context, userID string) error {
	updates := map[string]interface{}{
		"two_factor_enabled": false,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to disable 2FA")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "2fa_disabled", "user", userID, nil)
	
	return nil
}

// GetPreferences retrieves user preferences
func (s *MockUserService) GetPreferences(ctx context.Context, userID string) (*models.UserPreferences, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	return &user.Preferences, nil
}

// UpdatePreferences updates user preferences
func (s *MockUserService) UpdatePreferences(ctx context.Context, userID string, prefs *models.UserPreferences) error {
	updates := map[string]interface{}{
		"preferences": *prefs,
	}
	
	if err := s.userRepo.Update(ctx, userID, updates); err != nil {
		s.logger.WithError(err).Error("Failed to update preferences")
		return err
	}
	
	// Log activity
	s.LogActivity(ctx, userID, "preferences_updated", "user", userID, nil)
	
	return nil
}

// LogActivity logs user activity
func (s *MockUserService) LogActivity(ctx context.Context, userID, action, resource, resourceID string, metadata map[string]interface{}) error {
	// Get client info from context if available
	ipAddress := ""
	userAgent := ""
	
	// Convert metadata to JSON
	metadataJSON := "{}"
	if metadata != nil {
		data, _ := json.Marshal(metadata)
		metadataJSON = string(data)
	}
	
	activity := &models.UserActivity{
		UserID:     userID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		Metadata:   metadataJSON,
	}
	
	return s.userRepo.LogActivity(ctx, activity)
}

// ExportUserData exports all user data (GDPR compliance)
func (s *MockUserService) ExportUserData(ctx context.Context, userID string) (map[string]interface{}, error) {
	// Get user profile
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	// Get user statistics
	stats, _ := s.userRepo.GetStatistics(ctx, userID)
	
	// Get user activities
	activities, _ := s.userRepo.GetActivities(ctx, userID, 1000)
	
	// Compile all data
	data := map[string]interface{}{
		"profile":    user,
		"statistics": stats,
		"activities": activities,
		"exported_at": time.Now(),
	}
	
	// Log export
	s.LogActivity(ctx, userID, "data_exported", "user", userID, nil)
	
	return data, nil
}