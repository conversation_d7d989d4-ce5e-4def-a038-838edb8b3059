package storage

import (
	"sync"
	"time"

	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
)

// MemoryUserRepository implements in-memory storage for users
type MemoryUserRepository struct {
	users map[string]*models.User
	mu    sync.RWMutex
}

// NewMemoryUserRepository creates a new in-memory user repository
func NewMemoryUserRepository() *MemoryUserRepository {
	repo := &MemoryUserRepository{
		users: make(map[string]*models.User),
	}
	
	// Seed with some mock users
	repo.seedUsers()
	return repo
}

func (r *MemoryUserRepository) seedUsers() {
	now := time.Now()
	
	users := []*models.User{
		{
			ID:        "user-1",
			TenantID:  "tenant-1",
			Email:     "<EMAIL>",
			Username:  "admin",
			FirstName: "Admin",
			LastName:  "User",
			Status:    models.UserStatusActive,
			EmailVerified: true,
			CreatedAt: now,
			UpdatedAt: now,
		},
		{
			ID:        "user-2",
			TenantID:  "tenant-1",
			Email:     "<EMAIL>",
			Username:  "user",
			FirstName: "Regular",
			LastName:  "User",
			Status:    models.UserStatusActive,
			EmailVerified: true,
			CreatedAt: now,
			UpdatedAt: now,
		},
	}
	
	for _, user := range users {
		r.users[user.ID] = user
	}
}

func (r *MemoryUserRepository) Create(user *models.User) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	r.users[user.ID] = user
	return nil
}

func (r *MemoryUserRepository) GetByID(id string) (*models.User, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if user, exists := r.users[id]; exists {
		return user, nil
	}
	return nil, ErrNotFound
}

func (r *MemoryUserRepository) GetByEmail(email string) (*models.User, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, user := range r.users {
		if user.Email == email {
			return user, nil
		}
	}
	return nil, ErrNotFound
}

func (r *MemoryUserRepository) GetByUsername(username string) (*models.User, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, user := range r.users {
		if user.Username == username {
			return user, nil
		}
	}
	return nil, ErrNotFound
}

func (r *MemoryUserRepository) Update(user *models.User) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.users[user.ID]; !exists {
		return ErrNotFound
	}
	
	user.UpdatedAt = time.Now()
	r.users[user.ID] = user
	return nil
}

func (r *MemoryUserRepository) Delete(id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.users[id]; !exists {
		return ErrNotFound
	}
	
	delete(r.users, id)
	return nil
}

func (r *MemoryUserRepository) List(filter *models.UserFilter) ([]*models.User, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	users := make([]*models.User, 0)
	
	for _, user := range r.users {
		if filter.TenantID != "" && user.TenantID != filter.TenantID {
			continue
		}
		if filter.Status != "" && user.Status != filter.Status {
			continue
		}
		if filter.Role != "" && user.Role != filter.Role {
			continue
		}
		users = append(users, user)
	}
	
	return users, int64(len(users)), nil
}

// Error definitions
var (
	ErrNotFound = &StorageError{Code: "NOT_FOUND", Message: "Record not found"}
)

// StorageError represents a storage error
type StorageError struct {
	Code    string
	Message string
}

func (e *StorageError) Error() string {
	return e.Message
}