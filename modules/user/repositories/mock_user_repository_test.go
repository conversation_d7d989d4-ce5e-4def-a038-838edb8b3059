package repositories

import (
	"context"
	"testing"

	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMockUserRepository_Create(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			Email:     "<EMAIL>",
			Username:  "testuser",
			FirstName: "Test",
			LastName:  "User",
		},
		Bio:      "Test bio",
		Language: "en",
		Timezone: "UTC",
	}

	// Act
	err := repo.Create(ctx, user)

	// Assert
	require.NoError(t, err)
	assert.NotEmpty(t, user.ID)
	assert.Equal(t, models.UserStatusActive, user.Status)
}

func TestMockUserRepository_Create_DuplicateEmail(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user1 := &models.UserProfile{
		User: models.User{
			Email:    "<EMAIL>",
			Username: "testuser1",
		},
	}
	
	user2 := &models.UserProfile{
		User: models.User{
			Email:    "<EMAIL>", // Same email
			Username: "testuser2",
		},
	}

	// Act
	err1 := repo.Create(ctx, user1)
	err2 := repo.Create(ctx, user2)

	// Assert
	require.NoError(t, err1)
	require.Error(t, err2)
	assert.Contains(t, err2.Error(), "email already exists")
}

func TestMockUserRepository_GetByID(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act - get existing seeded user
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	foundUser, err := repo.GetByID(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	assert.Equal(t, user.ID, foundUser.ID)
	assert.Equal(t, user.Email, foundUser.Email)
}

func TestMockUserRepository_GetByEmail(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act
	user, err := repo.GetByEmail(ctx, "<EMAIL>")

	// Assert
	require.NoError(t, err)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "admin", user.Username)
}

func TestMockUserRepository_GetByUsername(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act
	user, err := repo.GetByUsername(ctx, "admin")

	// Assert
	require.NoError(t, err)
	assert.Equal(t, "admin", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
}

func TestMockUserRepository_Update(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	updates := map[string]interface{}{
		"bio":      "Updated bio",
		"location": "Updated location",
	}

	// Act
	err = repo.Update(ctx, user.ID, updates)

	// Assert
	require.NoError(t, err)
	
	// Verify update
	updatedUser, err := repo.GetByID(ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, "Updated bio", updatedUser.Bio)
	assert.Equal(t, "Updated location", updatedUser.Location)
}

func TestMockUserRepository_Delete(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			Email:    "<EMAIL>",
			Username: "deleteuser",
		},
	}
	
	err := repo.Create(ctx, user)
	require.NoError(t, err)

	// Act
	err = repo.Delete(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	
	// Verify soft delete
	deletedUser, err := repo.GetByID(ctx, user.ID)
	require.NoError(t, err)
	assert.NotNil(t, deletedUser.DeletedAt)
	assert.Equal(t, models.UserStatusDeleted, deletedUser.Status)
}

func TestMockUserRepository_HardDelete(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user := &models.UserProfile{
		User: models.User{
			Email:    "<EMAIL>",
			Username: "harddeleteuser",
		},
	}
	
	err := repo.Create(ctx, user)
	require.NoError(t, err)

	// Act
	err = repo.HardDelete(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	
	// Verify hard delete
	_, err = repo.GetByID(ctx, user.ID)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "user not found")
}

func TestMockUserRepository_Search(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act
	users, err := repo.Search(ctx, "admin", 10)

	// Assert
	require.NoError(t, err)
	assert.Len(t, users, 1)
	assert.Equal(t, "<EMAIL>", users[0].Email)
}

func TestMockUserRepository_List(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	filter := &models.UserFilter{
		Page:     1,
		PageSize: 20,
		Status:   models.UserStatusActive,
	}

	// Act
	users, total, err := repo.List(ctx, filter)

	// Assert
	require.NoError(t, err)
	assert.Greater(t, len(users), 0)
	assert.Greater(t, total, int64(0))
}

func TestMockUserRepository_Exists(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	// Act
	exists, err := repo.Exists(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	assert.True(t, exists)
	
	// Test non-existent user
	exists, err = repo.Exists(ctx, "non-existent-id")
	require.NoError(t, err)
	assert.False(t, exists)
}

func TestMockUserRepository_ExistsByEmail(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act
	exists, err := repo.ExistsByEmail(ctx, "<EMAIL>")

	// Assert
	require.NoError(t, err)
	assert.True(t, exists)
	
	// Test non-existent email
	exists, err = repo.ExistsByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	assert.False(t, exists)
}

func TestMockUserRepository_ExistsByUsername(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Act
	exists, err := repo.ExistsByUsername(ctx, "admin")

	// Assert
	require.NoError(t, err)
	assert.True(t, exists)
	
	// Test non-existent username
	exists, err = repo.ExistsByUsername(ctx, "nonexistent")
	require.NoError(t, err)
	assert.False(t, exists)
}

func TestMockUserRepository_UpdateLastSeen(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	oldLastSeen := user.LastSeenAt

	// Act
	err = repo.UpdateLastSeen(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	
	// Verify last seen was updated
	updatedUser, err := repo.GetByID(ctx, user.ID)
	require.NoError(t, err)
	assert.True(t, updatedUser.LastSeenAt.After(*oldLastSeen))
}

func TestMockUserRepository_GetStatistics(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	// Act
	stats, err := repo.GetStatistics(ctx, user.ID)

	// Assert
	require.NoError(t, err)
	assert.Equal(t, user.ID, stats.UserID)
	assert.Greater(t, stats.TotalPosts, int64(0))
	assert.Greater(t, stats.TotalComments, int64(0))
}

func TestMockUserRepository_LogActivity(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	activity := &models.UserActivity{
		UserID:     user.ID,
		Action:     "test_action",
		Resource:   "test_resource",
		ResourceID: "test_resource_id",
		IPAddress:  "127.0.0.1",
		UserAgent:  "test-agent",
		Metadata:   "{}",
	}

	// Act
	err = repo.LogActivity(ctx, activity)

	// Assert
	require.NoError(t, err)
	assert.NotEmpty(t, activity.ID)
	
	// Verify activity was logged
	activities, err := repo.GetActivities(ctx, user.ID, 10)
	require.NoError(t, err)
	assert.Greater(t, len(activities), 0)
	assert.Equal(t, "test_action", activities[0].Action)
}

func TestMockUserRepository_BatchGet(t *testing.T) {
	// Arrange
	repo := NewMockUserRepository()
	ctx := context.Background()
	
	user1, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	user2, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)
	
	userIDs := []string{user1.ID, user2.ID, "non-existent-id"}

	// Act
	users, err := repo.BatchGet(ctx, userIDs)

	// Assert
	require.NoError(t, err)
	assert.Len(t, users, 2) // Only existing users should be returned
	
	// Verify returned users
	userMap := make(map[string]*models.UserProfile)
	for _, user := range users {
		userMap[user.ID] = user
	}
	
	assert.Contains(t, userMap, user1.ID)
	assert.Contains(t, userMap, user2.ID)
	assert.NotContains(t, userMap, "non-existent-id")
}