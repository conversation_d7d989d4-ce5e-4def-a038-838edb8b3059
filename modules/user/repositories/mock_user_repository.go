package repositories

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
)

// MockUserRepository implements UserRepository with in-memory storage
type MockUserRepository struct {
	users           map[string]*models.UserProfile
	usersByEmail    map[string]*models.UserProfile
	usersByUsername map[string]*models.UserProfile
	activities      map[string][]*models.UserActivity
	mu              sync.RWMutex
}

// NewMockUserRepository creates a new mock user repository
func NewMockUserRepository() *MockUserRepository {
	repo := &MockUserRepository{
		users:           make(map[string]*models.UserProfile),
		usersByEmail:    make(map[string]*models.UserProfile),
		usersByUsername: make(map[string]*models.UserProfile),
		activities:      make(map[string][]*models.UserActivity),
	}

	// Seed with mock data
	repo.seedMockData()

	return repo
}

// seedMockData adds initial mock users
func (r *MockUserRepository) seedMockData() {
	now := time.Now()

	// Admin user (using correct auth models)
	admin := &models.UserProfile{
		User: authModels.User{
			ID:         utils.GenerateULID(),
			Email:      "<EMAIL>",
			Username:   "admin",
			FirstName:  "Admin",
			LastName:   "User",
			IsActive:   true,
			IsVerified: true,
			VerifiedAt: &now,
			CreatedAt:  now,
			UpdatedAt:  now,
		},
		Bio:           "System Administrator",
		Location:      "Vietnam",
		Language:      "en",
		Timezone:      "Asia/Ho_Chi_Minh",
		Status:        models.UserStatusActive,
		LastSeenAt:    &now,
		EmailVerified: true,
		Preferences: models.UserPreferences{
			EmailNotifications: true,
			PublicProfile:      true,
			PreferredTheme:     "dark",
			PreferredLanguage:  "en",
		},
	}

	// Regular user
	user := &models.UserProfile{
		User: authModels.User{
			ID:         utils.GenerateULID(),
			Email:      "<EMAIL>",
			Username:   "testuser",
			FirstName:  "Test",
			LastName:   "User",
			IsActive:   true,
			IsVerified: true,
			VerifiedAt: &now,
			CreatedAt:  now,
			UpdatedAt:  now,
		},
		Bio:           "Just a regular user",
		Location:      "Ho Chi Minh City",
		Language:      "vi",
		Timezone:      "Asia/Ho_Chi_Minh",
		Status:        models.UserStatusActive,
		LastSeenAt:    &now,
		EmailVerified: true,
		Preferences: models.UserPreferences{
			EmailNotifications: true,
			PublicProfile:      true,
			PreferredTheme:     "light",
			PreferredLanguage:  "vi",
		},
		SocialLinks: models.SocialLinks{
			GitHub:  "https://github.com/testuser",
			Twitter: "https://twitter.com/testuser",
		},
	}

	// Store users
	r.users[admin.ID] = admin
	r.usersByEmail[admin.Email] = admin
	r.usersByUsername[admin.Username] = admin

	r.users[user.ID] = user
	r.usersByEmail[user.Email] = user
	r.usersByUsername[user.Username] = user
}

// Create creates a new user
func (r *MockUserRepository) Create(ctx context.Context, user *models.UserProfile) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check duplicates
	if _, exists := r.users[user.ID]; exists {
		return errors.New("user already exists")
	}
	if _, exists := r.usersByEmail[user.Email]; exists {
		return errors.New("email already exists")
	}
	if _, exists := r.usersByUsername[user.Username]; exists {
		return errors.New("username already exists")
	}

	// Set defaults
	if user.ID == "" {
		user.ID = utils.GenerateULID()
	}
	if user.Status == "" {
		user.Status = models.UserStatusActive
	}
	if user.Language == "" {
		user.Language = "en"
	}
	if user.Timezone == "" {
		user.Timezone = "UTC"
	}

	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	// Store user
	r.users[user.ID] = user
	r.usersByEmail[user.Email] = user
	r.usersByUsername[user.Username] = user

	return nil
}

// GetByID retrieves a user by ID
func (r *MockUserRepository) GetByID(ctx context.Context, userID string) (*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	user, exists := r.users[userID]
	if !exists {
		return nil, errors.New("user not found")
	}

	return user, nil
}

// GetByEmail retrieves a user by email
func (r *MockUserRepository) GetByEmail(ctx context.Context, email string) (*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	user, exists := r.usersByEmail[email]
	if !exists {
		return nil, errors.New("user not found")
	}

	return user, nil
}

// GetByUsername retrieves a user by username
func (r *MockUserRepository) GetByUsername(ctx context.Context, username string) (*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	user, exists := r.usersByUsername[username]
	if !exists {
		return nil, errors.New("user not found")
	}

	return user, nil
}

// Update updates a user
func (r *MockUserRepository) Update(ctx context.Context, userID string, updates map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	user, exists := r.users[userID]
	if !exists {
		return errors.New("user not found")
	}

	// Apply updates
	for key, value := range updates {
		switch key {
		case "email":
			oldEmail := user.Email
			newEmail := value.(string)
			delete(r.usersByEmail, oldEmail)
			user.Email = newEmail
			r.usersByEmail[newEmail] = user
		case "username":
			oldUsername := user.Username
			newUsername := value.(string)
			delete(r.usersByUsername, oldUsername)
			user.Username = newUsername
			r.usersByUsername[newUsername] = user
		case "first_name":
			user.FirstName = value.(string)
		case "last_name":
			user.LastName = value.(string)
		case "bio":
			user.Bio = value.(string)
		case "location":
			user.Location = value.(string)
		case "website":
			user.Website = value.(string)
		case "phone_number":
			user.PhoneNumber = value.(string)
		case "language":
			user.Language = value.(string)
		case "timezone":
			user.Timezone = value.(string)
		case "status":
			user.Status = value.(models.UserStatus)
		case "is_active":
			user.IsActive = value.(bool)
		case "is_verified":
			user.IsVerified = value.(bool)
		case "email_verified":
			user.EmailVerified = value.(bool)
		case "phone_verified":
			user.PhoneVerified = value.(bool)
		case "two_factor_enabled":
			user.TwoFactorEnabled = value.(bool)
		case "preferences":
			user.Preferences = value.(models.UserPreferences)
		case "social_links":
			user.SocialLinks = value.(models.SocialLinks)
		case "date_of_birth":
			if dob, ok := value.(*time.Time); ok {
				user.DateOfBirth = dob
			}
		}
	}

	user.UpdatedAt = time.Now()
	return nil
}

// Delete soft deletes a user
func (r *MockUserRepository) Delete(ctx context.Context, userID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	user, exists := r.users[userID]
	if !exists {
		return errors.New("user not found")
	}

	user.Status = models.UserStatusDeleted
	user.IsActive = false

	return nil
}

// HardDelete permanently deletes a user
func (r *MockUserRepository) HardDelete(ctx context.Context, userID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	user, exists := r.users[userID]
	if !exists {
		return errors.New("user not found")
	}

	delete(r.users, userID)
	delete(r.usersByEmail, user.Email)
	delete(r.usersByUsername, user.Username)
	delete(r.activities, userID)

	return nil
}

// List retrieves users with filters and pagination
func (r *MockUserRepository) List(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Apply filters
	var filtered []*models.UserProfile
	for _, user := range r.users {
		if r.matchesFilter(user, filter) {
			filtered = append(filtered, user)
		}
	}

	total := int64(len(filtered))

	// Apply sorting
	// TODO: Implement sorting

	// Apply pagination
	page := filter.Page
	if page < 1 {
		page = 1
	}
	pageSize := filter.PageSize
	if pageSize < 1 {
		pageSize = 20
	}

	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(filtered) {
		return []*models.UserProfile{}, total, nil
	}

	if end > len(filtered) {
		end = len(filtered)
	}

	return filtered[start:end], total, nil
}

// ListWithCursor retrieves users with cursor-based pagination
func (r *MockUserRepository) ListWithCursor(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, *pagination.CursorResponse, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Apply filters
	var filtered []*models.UserProfile
	for _, user := range r.users {
		if r.matchesFilter(user, filter) {
			filtered = append(filtered, user)
		}
	}

	// For mock purposes, just return a simple cursor response
	limit := filter.Limit
	if limit <= 0 {
		limit = 20
	}

	// Simple cursor logic for mock
	var results []*models.UserProfile
	hasMore := false

	if len(filtered) > limit {
		results = filtered[:limit]
		hasMore = true
	} else {
		results = filtered
	}

	cursorResponse := &pagination.CursorResponse{
		HasMore: hasMore,
		Limit:   limit,
	}

	if hasMore && len(results) > 0 {
		// Generate a simple cursor for the last item
		lastUser := results[len(results)-1]
		cursor := pagination.NewCursorFromEntity(1, lastUser.CreatedAt, nil)
		if encodedCursor, err := cursor.String(); err == nil {
			cursorResponse.NextCursor = encodedCursor
		}
	}

	return results, cursorResponse, nil
}

// Search searches users by query
func (r *MockUserRepository) Search(ctx context.Context, query string, limit int) ([]*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	query = strings.ToLower(query)
	var results []*models.UserProfile

	for _, user := range r.users {
		if user.DeletedAt != nil {
			continue
		}

		if strings.Contains(strings.ToLower(user.Email), query) ||
			strings.Contains(strings.ToLower(user.Username), query) ||
			strings.Contains(strings.ToLower(user.FirstName), query) ||
			strings.Contains(strings.ToLower(user.LastName), query) ||
			strings.Contains(strings.ToLower(user.Bio), query) {
			results = append(results, user)

			if limit > 0 && len(results) >= limit {
				break
			}
		}
	}

	return results, nil
}

// Count counts users matching the filter
func (r *MockUserRepository) Count(ctx context.Context, filter *models.UserFilter) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	count := int64(0)
	for _, user := range r.users {
		if r.matchesFilter(user, filter) {
			count++
		}
	}

	return count, nil
}

// Exists checks if a user exists
func (r *MockUserRepository) Exists(ctx context.Context, userID string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.users[userID]
	return exists, nil
}

// ExistsByEmail checks if a user exists by email
func (r *MockUserRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.usersByEmail[email]
	return exists, nil
}

// ExistsByUsername checks if a user exists by username
func (r *MockUserRepository) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.usersByUsername[username]
	return exists, nil
}

// UpdateStatus updates user status
func (r *MockUserRepository) UpdateStatus(ctx context.Context, userID string, status models.UserStatus) error {
	return r.Update(ctx, userID, map[string]interface{}{
		"status": status,
	})
}

// UpdateLastSeen updates last seen timestamp
func (r *MockUserRepository) UpdateLastSeen(ctx context.Context, userID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	user, exists := r.users[userID]
	if !exists {
		return errors.New("user not found")
	}

	now := time.Now()
	user.LastSeenAt = &now

	return nil
}

// GetStatistics retrieves user statistics
func (r *MockUserRepository) GetStatistics(ctx context.Context, userID string) (*models.ProfileStatistics, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	user, exists := r.users[userID]
	if !exists {
		return nil, errors.New("user not found")
	}

	// Mock statistics
	stats := &models.ProfileStatistics{
		UserID:         userID,
		TotalPosts:     10,
		TotalComments:  25,
		TotalLikes:     100,
		TotalFollowers: 50,
		TotalFollowing: 30,
		LastActiveAt:   time.Now(),
		JoinedAt:       user.User.CreatedAt,
	}

	if user.LastSeenAt != nil {
		stats.LastActiveAt = *user.LastSeenAt
	}

	return stats, nil
}

// GetActivities retrieves user activities
func (r *MockUserRepository) GetActivities(ctx context.Context, userID string, limit int) ([]*models.UserActivity, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	activities, exists := r.activities[userID]
	if !exists {
		return []*models.UserActivity{}, nil
	}

	if limit > 0 && len(activities) > limit {
		return activities[:limit], nil
	}

	return activities, nil
}

// LogActivity logs user activity
func (r *MockUserRepository) LogActivity(ctx context.Context, activity *models.UserActivity) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if activity.ID == "" {
		activity.ID = utils.GenerateULID()
	}
	activity.CreatedAt = time.Now()

	r.activities[activity.UserID] = append([]*models.UserActivity{activity}, r.activities[activity.UserID]...)

	// Keep only last 100 activities per user
	if len(r.activities[activity.UserID]) > 100 {
		r.activities[activity.UserID] = r.activities[activity.UserID][:100]
	}

	return nil
}

// BatchGet retrieves multiple users by IDs
func (r *MockUserRepository) BatchGet(ctx context.Context, userIDs []string) ([]*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	users := make([]*models.UserProfile, 0, len(userIDs))
	for _, id := range userIDs {
		if user, exists := r.users[id]; exists {
			users = append(users, user)
		}
	}

	return users, nil
}

// GetInactive retrieves inactive users
func (r *MockUserRepository) GetInactive(ctx context.Context, days int) ([]*models.UserProfile, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	cutoff := time.Now().AddDate(0, 0, -days)
	var inactive []*models.UserProfile

	for _, user := range r.users {
		if user.LastSeenAt != nil && user.LastSeenAt.Before(cutoff) {
			inactive = append(inactive, user)
		}
	}

	return inactive, nil
}

// matchesFilter checks if a user matches the given filter
func (r *MockUserRepository) matchesFilter(user *models.UserProfile, filter *models.UserFilter) bool {
	// Skip deleted users unless specifically requested
	if user.DeletedAt != nil && filter.Status != models.UserStatusDeleted {
		return false
	}

	// Status filter
	if filter.Status != "" && user.Status != filter.Status {
		return false
	}

	// Verified filter
	if filter.Verified != nil && user.IsVerified != *filter.Verified {
		return false
	}

	// Active filter
	if filter.Active != nil && user.IsActive != *filter.Active {
		return false
	}

	// Date range filters
	if filter.CreatedFrom != nil && user.CreatedAt.Before(*filter.CreatedFrom) {
		return false
	}
	if filter.CreatedTo != nil && user.CreatedAt.After(*filter.CreatedTo) {
		return false
	}

	// Search query
	if filter.Query != "" {
		query := strings.ToLower(filter.Query)
		if !strings.Contains(strings.ToLower(user.Email), query) &&
			!strings.Contains(strings.ToLower(user.Username), query) &&
			!strings.Contains(strings.ToLower(user.FirstName), query) &&
			!strings.Contains(strings.ToLower(user.LastName), query) {
			return false
		}
	}

	return true
}
