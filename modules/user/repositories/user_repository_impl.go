package repositories

import (
	"context"
	"time"

	"gorm.io/gorm"
	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
	"github.com/blog-api-v3/blog-api-v3/pkg/database"
)

// userRepository implements UserRepository interface
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// ListWithCursor retrieves users with cursor-based pagination
func (r *userRepository) ListWithCursor(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, *pagination.CursorResponse, error) {
	var users []*models.UserProfile
	
	// Default sort settings
	sortBy := "created_at"
	sortOrder := "desc"
	
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	
	// Build cursor query
	cursorQuery := database.NewCursorQuery(r.db, "users").
		WithSort(sortBy, sortOrder)
	
	// Add filters
	if filter.TenantID != "" {
		cursorQuery = cursorQuery.WithCondition("tenant_id = ?", filter.TenantID)
	}
	
	if filter.Status != "" {
		cursorQuery = cursorQuery.WithCondition("status = ?", filter.Status)
	}
	
	if filter.Role != "" {
		cursorQuery = cursorQuery.WithCondition("role = ?", filter.Role)
	}
	
	if filter.EmailVerified != nil {
		cursorQuery = cursorQuery.WithCondition("email_verified = ?", *filter.EmailVerified)
	}
	
	if filter.Query != "" {
		searchQuery := "%" + filter.Query + "%"
		cursorQuery = cursorQuery.WithCondition(
			"(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR username LIKE ?)",
			searchQuery, searchQuery, searchQuery, searchQuery,
		)
	}
	
	if filter.CreatedFrom != nil {
		cursorQuery = cursorQuery.WithCondition("created_at >= ?", *filter.CreatedFrom)
	}
	
	if filter.CreatedTo != nil {
		cursorQuery = cursorQuery.WithCondition("created_at <= ?", *filter.CreatedTo)
	}
	
	// Add status filter to exclude deleted users
	cursorQuery = cursorQuery.WithCondition("status != ?", "deleted")
	
	// Build and execute query
	limit := filter.Limit
	if limit == 0 {
		limit = 20
	}
	
	query := cursorQuery.BuildQuery(filter.Cursor, limit)
	
	if err := query.WithContext(ctx).Find(&users).Error; err != nil {
		return nil, nil, err
	}
	
	// Create cursor response
	hasMore := len(users) > limit
	if hasMore {
		users = users[:limit] // Remove the extra record
	}
	
	response := &pagination.CursorResponse{
		HasMore:     hasMore,
		HasPrevious: filter.Cursor != "",
		Limit:       limit,
	}
	
	// Set cursors if we have results
	if len(users) > 0 {
		// Set next cursor if there are more records
		if hasMore {
			lastUser := users[len(users)-1]
			idInt := database.ParseStringID(lastUser.ID)
			cursor := database.CreateCursorFromModel(idInt, lastUser.CreatedAt)
			if encodedCursor, err := cursor.String(); err == nil {
				response.NextCursor = encodedCursor
			}
		}
		
		// Set previous cursor if we had a current cursor
		if response.HasPrevious {
			firstUser := users[0]
			idInt := database.ParseStringID(firstUser.ID)
			cursor := database.CreateCursorFromModel(idInt, firstUser.CreatedAt)
			if encodedCursor, err := cursor.String(); err == nil {
				response.PreviousCursor = encodedCursor
			}
		}
	}
	
	return users, response, nil
}

// GetFollowersWithCursor retrieves followers with cursor-based pagination
func (r *userRepository) GetFollowersWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error) {
	var users []*models.UserProfile
	
	cursorQuery := database.NewCursorQuery(r.db, "users").
		WithSort("created_at", "desc").
		WithCondition(`id IN (
			SELECT follower_id FROM user_relationships 
			WHERE followed_id = ? AND type = 'follow' AND status != 'deleted'
		)`, userID).
		WithCondition("status != ?", "deleted")
	
	query := cursorQuery.BuildQuery(cursor, limit)
	
	if err := query.WithContext(ctx).Find(&users).Error; err != nil {
		return nil, nil, err
	}
	
	// Create response
	hasMore := len(users) > limit
	if hasMore {
		users = users[:limit]
	}
	
	response := &pagination.CursorResponse{
		HasMore:     hasMore,
		HasPrevious: cursor != "",
		Limit:       limit,
	}
	
	if len(users) > 0 {
		if hasMore {
			lastUser := users[len(users)-1]
			idInt := database.ParseStringID(lastUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, lastUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.NextCursor = encodedCursor
			}
		}
		if response.HasPrevious {
			firstUser := users[0]
			idInt := database.ParseStringID(firstUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, firstUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.PreviousCursor = encodedCursor
			}
		}
	}
	
	return users, response, nil
}

// GetFollowingWithCursor retrieves following users with cursor-based pagination
func (r *userRepository) GetFollowingWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error) {
	var users []*models.UserProfile
	
	cursorQuery := database.NewCursorQuery(r.db, "users").
		WithSort("created_at", "desc").
		WithCondition(`id IN (
			SELECT followed_id FROM user_relationships 
			WHERE follower_id = ? AND type = 'follow' AND status != 'deleted'
		)`, userID).
		WithCondition("status != ?", "deleted")
	
	query := cursorQuery.BuildQuery(cursor, limit)
	
	if err := query.WithContext(ctx).Find(&users).Error; err != nil {
		return nil, nil, err
	}
	
	// Create response
	hasMore := len(users) > limit
	if hasMore {
		users = users[:limit]
	}
	
	response := &pagination.CursorResponse{
		HasMore:     hasMore,
		HasPrevious: cursor != "",
		Limit:       limit,
	}
	
	if len(users) > 0 {
		if hasMore {
			lastUser := users[len(users)-1]
			idInt := database.ParseStringID(lastUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, lastUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.NextCursor = encodedCursor
			}
		}
		if response.HasPrevious {
			firstUser := users[0]
			idInt := database.ParseStringID(firstUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, firstUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.PreviousCursor = encodedCursor
			}
		}
	}
	
	return users, response, nil
}

// GetBlockedWithCursor retrieves blocked users with cursor-based pagination
func (r *userRepository) GetBlockedWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error) {
	var users []*models.UserProfile
	
	cursorQuery := database.NewCursorQuery(r.db, "users").
		WithSort("created_at", "desc").
		WithCondition(`id IN (
			SELECT blocked_id FROM user_relationships 
			WHERE blocker_id = ? AND type = 'block' AND status != 'deleted'
		)`, userID).
		WithCondition("status != ?", "deleted")
	
	query := cursorQuery.BuildQuery(cursor, limit)
	
	if err := query.WithContext(ctx).Find(&users).Error; err != nil {
		return nil, nil, err
	}
	
	// Create response
	hasMore := len(users) > limit
	if hasMore {
		users = users[:limit]
	}
	
	response := &pagination.CursorResponse{
		HasMore:     hasMore,
		HasPrevious: cursor != "",
		Limit:       limit,
	}
	
	if len(users) > 0 {
		if hasMore {
			lastUser := users[len(users)-1]
			idInt := database.ParseStringID(lastUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, lastUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.NextCursor = encodedCursor
			}
		}
		if response.HasPrevious {
			firstUser := users[0]
			idInt := database.ParseStringID(firstUser.ID)
			cursorObj := database.CreateCursorFromModel(idInt, firstUser.CreatedAt)
			if encodedCursor, err := cursorObj.String(); err == nil {
				response.PreviousCursor = encodedCursor
			}
		}
	}
	
	return users, response, nil
}

// Implement the remaining methods from the interface as stubs for now
// These would need to be implemented based on your actual database schema

func (r *userRepository) Create(ctx context.Context, user *models.UserProfile) error {
	return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) GetByID(ctx context.Context, userID string) (*models.UserProfile, error) {
	var user models.UserProfile
	err := r.db.WithContext(ctx).Where("id = ? AND status != 'deleted'", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByEmail(ctx context.Context, email string) (*models.UserProfile, error) {
	var user models.UserProfile
	err := r.db.WithContext(ctx).Where("email = ? AND status != 'deleted'", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByUsername(ctx context.Context, username string) (*models.UserProfile, error) {
	var user models.UserProfile
	err := r.db.WithContext(ctx).Where("username = ? AND status != 'deleted'", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) Update(ctx context.Context, userID string, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("id = ?", userID).Updates(updates).Error
}

func (r *userRepository) Delete(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("id = ?", userID).Update("status", "deleted").Error
}

func (r *userRepository) HardDelete(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).Unscoped().Delete(&models.UserProfile{}, "id = ?", userID).Error
}

func (r *userRepository) List(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, int64, error) {
	// Legacy implementation - use ListWithCursor instead
	var users []*models.UserProfile
	var total int64
	
	query := r.db.Model(&models.UserProfile{}).Where("status != 'deleted'")
	
	// Apply filters similar to cursor implementation but for offset-based
	// ... implementation details ...
	
	err := query.WithContext(ctx).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	offset := 0
	limit := 20
	if filter.Page > 0 && filter.PageSize > 0 {
		offset = (filter.Page - 1) * filter.PageSize
		limit = filter.PageSize
	}
	
	err = query.Offset(offset).Limit(limit).Find(&users).Error
	return users, total, err
}

// Add stub implementations for remaining interface methods
func (r *userRepository) Search(ctx context.Context, query string, limit int) ([]*models.UserProfile, error) {
	var users []*models.UserProfile
	searchQuery := "%" + query + "%"
	err := r.db.WithContext(ctx).Where(
		"(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?) AND status != 'deleted'",
		searchQuery, searchQuery, searchQuery,
	).Limit(limit).Find(&users).Error
	return users, err
}

func (r *userRepository) Count(ctx context.Context, filter *models.UserFilter) (int64, error) {
	var count int64
	query := r.db.Model(&models.UserProfile{}).Where("status != 'deleted'")
	// Apply filters...
	return count, query.WithContext(ctx).Count(&count).Error
}

func (r *userRepository) Exists(ctx context.Context, userID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("id = ? AND status != 'deleted'", userID).Count(&count).Error
	return count > 0, err
}

func (r *userRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("email = ? AND status != 'deleted'", email).Count(&count).Error
	return count > 0, err
}

func (r *userRepository) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("username = ? AND status != 'deleted'", username).Count(&count).Error
	return count > 0, err
}

func (r *userRepository) UpdateStatus(ctx context.Context, userID string, status models.UserStatus) error {
	return r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("id = ?", userID).Update("status", status).Error
}

func (r *userRepository) UpdateLastSeen(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).Model(&models.UserProfile{}).Where("id = ?", userID).Update("last_seen_at", time.Now()).Error
}

func (r *userRepository) GetStatistics(ctx context.Context, userID string) (*models.ProfileStatistics, error) {
	// Implementation would depend on your ProfileStatistics model
	return nil, nil
}

func (r *userRepository) GetActivities(ctx context.Context, userID string, limit int) ([]*models.UserActivity, error) {
	var activities []*models.UserActivity
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Limit(limit).Find(&activities).Error
	return activities, err
}

func (r *userRepository) LogActivity(ctx context.Context, activity *models.UserActivity) error {
	return r.db.WithContext(ctx).Create(activity).Error
}

func (r *userRepository) BatchGet(ctx context.Context, userIDs []string) ([]*models.UserProfile, error) {
	var users []*models.UserProfile
	err := r.db.WithContext(ctx).Where("id IN ? AND status != 'deleted'", userIDs).Find(&users).Error
	return users, err
}

func (r *userRepository) GetInactive(ctx context.Context, days int) ([]*models.UserProfile, error) {
	var users []*models.UserProfile
	cutoffDate := time.Now().AddDate(0, 0, -days)
	err := r.db.WithContext(ctx).Where("last_seen_at < ? AND status != 'deleted'", cutoffDate).Find(&users).Error
	return users, err
}

// Legacy methods for relationship repository
func (r *userRepository) GetFollowers(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error) {
	// Legacy implementation - use GetFollowersWithCursor instead
	var users []*models.UserProfile
	var total int64
	// Implementation...
	return users, total, nil
}

func (r *userRepository) GetFollowing(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error) {
	// Legacy implementation - use GetFollowingWithCursor instead
	var users []*models.UserProfile
	var total int64
	// Implementation...
	return users, total, nil
}

func (r *userRepository) GetBlocked(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error) {
	// Legacy implementation - use GetBlockedWithCursor instead
	var users []*models.UserProfile
	var total int64
	// Implementation...
	return users, total, nil
}