package repositories

import (
	"context"

	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// UserRepository defines the interface for user data access
type UserRepository interface {
	// Create creates a new user
	Create(ctx context.Context, user *models.UserProfile) error
	
	// GetByID retrieves a user by ID
	GetByID(ctx context.Context, userID string) (*models.UserProfile, error)
	
	// GetByEmail retrieves a user by email
	GetByEmail(ctx context.Context, email string) (*models.UserProfile, error)
	
	// GetByUsername retrieves a user by username
	GetByUsername(ctx context.Context, username string) (*models.UserProfile, error)
	
	// Update updates a user
	Update(ctx context.Context, userID string, updates map[string]interface{}) error
	
	// Delete soft deletes a user
	Delete(ctx context.Context, userID string) error
	
	// HardDelete permanently deletes a user
	HardDelete(ctx context.Context, userID string) error
	
	// List retrieves users with filters and pagination
	List(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, int64, error)
	
	// ListWithCursor retrieves users with cursor-based pagination
	ListWithCursor(ctx context.Context, filter *models.UserFilter) ([]*models.UserProfile, *pagination.CursorResponse, error)
	
	// Search searches users by query
	Search(ctx context.Context, query string, limit int) ([]*models.UserProfile, error)
	
	// Count counts users matching the filter
	Count(ctx context.Context, filter *models.UserFilter) (int64, error)
	
	// Exists checks if a user exists
	Exists(ctx context.Context, userID string) (bool, error)
	
	// ExistsByEmail checks if a user exists by email
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	
	// ExistsByUsername checks if a user exists by username
	ExistsByUsername(ctx context.Context, username string) (bool, error)
	
	// UpdateStatus updates user status
	UpdateStatus(ctx context.Context, userID string, status models.UserStatus) error
	
	// UpdateLastSeen updates last seen timestamp
	UpdateLastSeen(ctx context.Context, userID string) error
	
	// GetStatistics retrieves user statistics
	GetStatistics(ctx context.Context, userID string) (*models.ProfileStatistics, error)
	
	// GetActivities retrieves user activities
	GetActivities(ctx context.Context, userID string, limit int) ([]*models.UserActivity, error)
	
	// LogActivity logs user activity
	LogActivity(ctx context.Context, activity *models.UserActivity) error
	
	// BatchGet retrieves multiple users by IDs
	BatchGet(ctx context.Context, userIDs []string) ([]*models.UserProfile, error)
	
	// GetInactive retrieves inactive users
	GetInactive(ctx context.Context, days int) ([]*models.UserProfile, error)
}

// UserPreferencesRepository defines the interface for user preferences
type UserPreferencesRepository interface {
	// GetPreferences retrieves user preferences
	GetPreferences(ctx context.Context, userID string) (*models.UserPreferences, error)
	
	// UpdatePreferences updates user preferences
	UpdatePreferences(ctx context.Context, userID string, prefs *models.UserPreferences) error
	
	// GetDefaultPreferences returns default preferences
	GetDefaultPreferences() *models.UserPreferences
}

// UserRelationshipRepository defines the interface for user relationships (follow, block, etc.)
type UserRelationshipRepository interface {
	// Follow creates a follow relationship
	Follow(ctx context.Context, followerID, followedID string) error
	
	// Unfollow removes a follow relationship
	Unfollow(ctx context.Context, followerID, followedID string) error
	
	// IsFollowing checks if user A follows user B
	IsFollowing(ctx context.Context, followerID, followedID string) (bool, error)
	
	// GetFollowers retrieves followers of a user (deprecated)
	GetFollowers(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error)
	
	// GetFollowersWithCursor retrieves followers with cursor-based pagination
	GetFollowersWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error)
	
	// GetFollowing retrieves users that a user follows (deprecated)
	GetFollowing(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error)
	
	// GetFollowingWithCursor retrieves following users with cursor-based pagination
	GetFollowingWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error)
	
	// Block blocks a user
	Block(ctx context.Context, blockerID, blockedID string) error
	
	// Unblock unblocks a user
	Unblock(ctx context.Context, blockerID, blockedID string) error
	
	// IsBlocked checks if user A has blocked user B
	IsBlocked(ctx context.Context, blockerID, blockedID string) (bool, error)
	
	// GetBlocked retrieves blocked users (deprecated)
	GetBlocked(ctx context.Context, userID string, offset, limit int) ([]*models.UserProfile, int64, error)
	
	// GetBlockedWithCursor retrieves blocked users with cursor-based pagination
	GetBlockedWithCursor(ctx context.Context, userID, cursor string, limit int) ([]*models.UserProfile, *pagination.CursorResponse, error)
}