package handlers

import (
	"net/http"

	pkghttp "github.com/blog-api-v3/blog-api-v3/pkg/http"
	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/pkg/pagination"
)

// Example of cursor-based pagination handler
func (h *UserHandler) ListUsersWithCursor(w http.ResponseWriter, r *http.Request) {
	req := pkghttp.NewRequest(r)
	resp := pkghttp.NewResponse(w)
	
	// Parse cursor pagination parameters
	cursorPagination, err := req.GetCursorPagination(20) // default limit 20
	if err != nil {
		resp.BadRequest("Invalid pagination parameters")
		return
	}
	
	// Create filter with cursor pagination
	_ = &models.UserFilter{
		Cursor: cursorPagination.Cursor,
		Limit:  cursorPagination.Limit,
		// Add other filters from query params
		Status: models.UserStatus(req.QueryParam("status")),
		Query:  req.QueryParam("q"),
	}
	
	// Get users from service (example - method needs to be implemented)
	// users, cursorResponse, err := h.userService.ListUsersWithCursor(r.Context(), filter)
	// if err != nil {
	//	resp.InternalServerError("Failed to get users")
	//	return
	// }
	
	// Temporary mock response
	var users []models.User
	cursorResponse := pagination.CursorResponse{}
	
	// Send response with cursor pagination metadata
	resp.CursorPaginated(users, cursorResponse)
}

// Example of converting from old pagination to cursor (for backward compatibility)
func (h *UserHandler) ListUsersCompat(w http.ResponseWriter, r *http.Request) {
	req := pkghttp.NewRequest(r)
	resp := pkghttp.NewResponse(w)
	
	// Check if cursor parameter exists
	if cursor := req.QueryParam("cursor"); cursor != "" {
		// Use new cursor-based pagination
		h.ListUsersWithCursor(w, r)
		return
	}
	
	// Otherwise use legacy offset-based pagination
	pagination := req.GetPagination(20, 100)
	
	_ = &models.UserFilter{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		Status:   models.UserStatus(req.QueryParam("status")),
		Query:    req.QueryParam("q"),
	}
	
	// Get users using legacy method (example - method needs to be implemented)
	// users, total, err := h.userService.ListUsers(r.Context(), filter)
	// if err != nil {
	//	resp.InternalServerError("Failed to get users")
	//	return
	// }
	
	// Temporary mock response
	var users []models.User
	total := int64(0)
	
	// Send legacy paginated response
	resp.Paginated(users, pagination.Page, pagination.PageSize, int(total))
}