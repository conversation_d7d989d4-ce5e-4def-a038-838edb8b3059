package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	"github.com/blog-api-v3/blog-api-v3/modules/user/models"
	"github.com/blog-api-v3/blog-api-v3/modules/user/services"
	"github.com/blog-api-v3/blog-api-v3/pkg/http/middleware"
	httpPkg "github.com/blog-api-v3/blog-api-v3/pkg/http"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
	"github.com/gorilla/mux"
)

// UserHandler handles user-related endpoints
type UserHandler struct {
	userService services.UserService
	validator   *validator.RequestValidator
	logger      utils.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService services.UserService, v validator.Validator, logger utils.Logger) *UserHandler {
	return &UserHandler{
		userService: userService,
		validator:   validator.NewRequestValidator(v),
		logger:      logger,
	}
}

// RegisterRoutes registers user routes
func (h *UserHandler) RegisterRoutes(router *mux.Router, authService interface{}) {
	// Public routes
	router.HandleFunc("/users/{id}", h.GetUser).Methods(http.MethodGet)
	router.HandleFunc("/users/search", h.SearchUsers).Methods(http.MethodGet)
	
	// Protected routes
	protected := router.PathPrefix("/users").Subrouter()
	protected.Use(middleware.AuthMiddleware(authService.(middleware.AuthService)))
	
	protected.HandleFunc("", h.ListUsers).Methods(http.MethodGet)
	protected.HandleFunc("/me", h.GetCurrentUser).Methods(http.MethodGet)
	protected.HandleFunc("/me", h.UpdateCurrentUser).Methods(http.MethodPut, http.MethodPatch)
	protected.HandleFunc("/me/preferences", h.GetPreferences).Methods(http.MethodGet)
	protected.HandleFunc("/me/preferences", h.UpdatePreferences).Methods(http.MethodPut)
	protected.HandleFunc("/me/activities", h.GetActivities).Methods(http.MethodGet)
	protected.HandleFunc("/me/statistics", h.GetStatistics).Methods(http.MethodGet)
	protected.HandleFunc("/me/export", h.ExportData).Methods(http.MethodPost)
	protected.HandleFunc("/me/verify-email", h.VerifyEmail).Methods(http.MethodPost)
	protected.HandleFunc("/me/verify-phone", h.VerifyPhone).Methods(http.MethodPost)
	protected.HandleFunc("/me/2fa", h.ToggleTwoFactor).Methods(http.MethodPost)
	
	// Admin routes
	admin := router.PathPrefix("/admin/users").Subrouter()
	admin.Use(middleware.AuthMiddleware(authService.(middleware.AuthService)))
	admin.Use(middleware.RequireRoleMiddleware(authModels.RoleAdmin, authModels.RoleSuperAdmin))
	
	admin.HandleFunc("", h.AdminListUsers).Methods(http.MethodGet)
	admin.HandleFunc("/{id}", h.AdminGetUser).Methods(http.MethodGet)
	admin.HandleFunc("/{id}", h.AdminUpdateUser).Methods(http.MethodPut)
	admin.HandleFunc("/{id}", h.AdminDeleteUser).Methods(http.MethodDelete)
	admin.HandleFunc("/{id}/suspend", h.SuspendUser).Methods(http.MethodPost)
	admin.HandleFunc("/{id}/unsuspend", h.UnsuspendUser).Methods(http.MethodPost)
	admin.HandleFunc("/{id}/restore", h.RestoreUser).Methods(http.MethodPost)
}

// GetUser retrieves a user by ID (public profile)
func (h *UserHandler) GetUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	user, err := h.userService.GetUser(r.Context(), userID)
	if err != nil {
		resp.Error(http.StatusNotFound, "User not found")
		return
	}
	
	// Return public profile only
	profile := h.toPublicProfile(user)
	resp.Success(profile)
}

// SearchUsers searches for users
func (h *UserHandler) SearchUsers(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	query := r.URL.Query().Get("q")
	if query == "" {
		resp.Error(http.StatusBadRequest, "Search query is required")
		return
	}
	
	limitStr := r.URL.Query().Get("limit")
	limit := 10
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	users, err := h.userService.SearchUsers(r.Context(), query, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users")
		resp.Error(http.StatusInternalServerError, "Search failed")
		return
	}
	
	// Return public profiles
	profiles := make([]interface{}, len(users))
	for i, user := range users {
		profiles[i] = h.toPublicProfile(user)
	}
	
	resp.Success(map[string]interface{}{
		"users": profiles,
		"count": len(profiles),
		"query": query,
	})
}

// ListUsers lists users with pagination
func (h *UserHandler) ListUsers(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Parse filter from query params
	filter := &models.UserFilter{
		Page:     1,
		PageSize: 20,
	}
	
	// Parse pagination
	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			filter.PageSize = ps
		}
	}
	
	// Parse status filter
	if status := r.URL.Query().Get("status"); status != "" {
		filter.Status = models.UserStatus(status)
	}
	
	// Parse other filters
	filter.Query = r.URL.Query().Get("q")
	filter.SortBy = r.URL.Query().Get("sort_by")
	filter.SortOrder = r.URL.Query().Get("sort_order")
	
	users, total, err := h.userService.ListUsers(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		resp.Error(http.StatusInternalServerError, "Failed to list users")
		return
	}
	
	// Return public profiles
	profiles := make([]interface{}, len(users))
	for i, user := range users {
		profiles[i] = h.toPublicProfile(user)
	}
	
	resp.Paginated(profiles, filter.Page, filter.PageSize, int(total))
}

// GetCurrentUser retrieves the current user's profile
func (h *UserHandler) GetCurrentUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	user, err := h.userService.GetUser(r.Context(), claims.UserID)
	if err != nil {
		resp.Error(http.StatusNotFound, "User not found")
		return
	}
	
	// Return full profile for own user
	resp.Success(user)
}

// UpdateCurrentUser updates the current user's profile
func (h *UserHandler) UpdateCurrentUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var update models.UserUpdate
	if err := h.validator.ValidateJSON(r, &update); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	if err := h.userService.UpdateUser(r.Context(), claims.UserID, &update); err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		resp.Error(http.StatusInternalServerError, "Failed to update profile")
		return
	}
	
	// Get updated user
	user, _ := h.userService.GetUser(r.Context(), claims.UserID)
	resp.Success(user)
}

// GetPreferences retrieves user preferences
func (h *UserHandler) GetPreferences(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	prefs, err := h.userService.GetPreferences(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get preferences")
		resp.Error(http.StatusInternalServerError, "Failed to get preferences")
		return
	}
	
	resp.Success(prefs)
}

// UpdatePreferences updates user preferences
func (h *UserHandler) UpdatePreferences(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var prefs models.UserPreferences
	if err := json.NewDecoder(r.Body).Decode(&prefs); err != nil {
		resp.Error(http.StatusBadRequest, "Invalid request body")
		return
	}
	
	if err := h.userService.UpdatePreferences(r.Context(), claims.UserID, &prefs); err != nil {
		h.logger.WithError(err).Error("Failed to update preferences")
		resp.Error(http.StatusInternalServerError, "Failed to update preferences")
		return
	}
	
	resp.Success(map[string]string{"message": "Preferences updated successfully"})
}

// GetActivities retrieves user activities
func (h *UserHandler) GetActivities(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	limitStr := r.URL.Query().Get("limit")
	limit := 20
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	activities, err := h.userService.GetUserActivities(r.Context(), claims.UserID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get activities")
		resp.Error(http.StatusInternalServerError, "Failed to get activities")
		return
	}
	
	resp.Success(map[string]interface{}{
		"activities": activities,
		"count":      len(activities),
	})
}

// GetStatistics retrieves user statistics
func (h *UserHandler) GetStatistics(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	stats, err := h.userService.GetUserStatistics(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get statistics")
		resp.Error(http.StatusInternalServerError, "Failed to get statistics")
		return
	}
	
	resp.Success(stats)
}

// ExportData exports user data (GDPR)
func (h *UserHandler) ExportData(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	data, err := h.userService.ExportUserData(r.Context(), claims.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to export data")
		resp.Error(http.StatusInternalServerError, "Failed to export data")
		return
	}
	
	// In a real implementation, this would generate a downloadable file
	resp.Success(data)
}

// VerifyEmail verifies user's email
func (h *UserHandler) VerifyEmail(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	// In a real implementation, this would require a verification token
	if err := h.userService.VerifyEmail(r.Context(), claims.UserID); err != nil {
		h.logger.WithError(err).Error("Failed to verify email")
		resp.Error(http.StatusInternalServerError, "Failed to verify email")
		return
	}
	
	resp.Success(map[string]string{"message": "Email verified successfully"})
}

// VerifyPhone verifies user's phone
func (h *UserHandler) VerifyPhone(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	// In a real implementation, this would require a verification code
	if err := h.userService.VerifyPhone(r.Context(), claims.UserID); err != nil {
		h.logger.WithError(err).Error("Failed to verify phone")
		resp.Error(http.StatusInternalServerError, "Failed to verify phone")
		return
	}
	
	resp.Success(map[string]string{"message": "Phone verified successfully"})
}

// ToggleTwoFactor toggles two-factor authentication
func (h *UserHandler) ToggleTwoFactor(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	claims := middleware.GetTokenClaims(r)
	if claims == nil {
		resp.Error(http.StatusUnauthorized, "Unauthorized")
		return
	}
	
	var request struct {
		Enable bool `json:"enable"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		resp.Error(http.StatusBadRequest, "Invalid request body")
		return
	}
	
	var err error
	if request.Enable {
		err = h.userService.EnableTwoFactor(r.Context(), claims.UserID)
	} else {
		err = h.userService.DisableTwoFactor(r.Context(), claims.UserID)
	}
	
	if err != nil {
		h.logger.WithError(err).Error("Failed to toggle 2FA")
		resp.Error(http.StatusInternalServerError, "Failed to toggle 2FA")
		return
	}
	
	status := "disabled"
	if request.Enable {
		status = "enabled"
	}
	
	resp.Success(map[string]string{"message": "Two-factor authentication " + status})
}

// Admin endpoints

// AdminListUsers lists all users (admin)
func (h *UserHandler) AdminListUsers(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	// Parse filter
	filter := &models.UserFilter{
		Page:     1,
		PageSize: 50,
	}
	
	// Include all statuses for admin
	if status := r.URL.Query().Get("status"); status != "" {
		filter.Status = models.UserStatus(status)
	}
	
	users, total, err := h.userService.ListUsers(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		resp.Error(http.StatusInternalServerError, "Failed to list users")
		return
	}
	
	resp.Paginated(users, filter.Page, filter.PageSize, int(total))
}

// AdminGetUser retrieves full user details (admin)
func (h *UserHandler) AdminGetUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	user, err := h.userService.GetUser(r.Context(), userID)
	if err != nil {
		resp.Error(http.StatusNotFound, "User not found")
		return
	}
	
	// Get statistics and activities
	stats, _ := h.userService.GetUserStatistics(r.Context(), userID)
	activities, _ := h.userService.GetUserActivities(r.Context(), userID, 10)
	
	resp.Success(map[string]interface{}{
		"user":       user,
		"statistics": stats,
		"activities": activities,
	})
}

// AdminUpdateUser updates a user (admin)
func (h *UserHandler) AdminUpdateUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	var update models.UserUpdate
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		resp.Error(http.StatusBadRequest, "Invalid request body")
		return
	}
	
	if err := h.userService.UpdateUser(r.Context(), userID, &update); err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		resp.Error(http.StatusInternalServerError, "Failed to update user")
		return
	}
	
	resp.Success(map[string]string{"message": "User updated successfully"})
}

// AdminDeleteUser deletes a user (admin)
func (h *UserHandler) AdminDeleteUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	// Check for permanent delete
	permanent := r.URL.Query().Get("permanent") == "true"
	
	var err error
	if permanent {
		err = h.userService.PermanentlyDeleteUser(r.Context(), userID)
	} else {
		err = h.userService.DeleteUser(r.Context(), userID)
	}
	
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete user")
		resp.Error(http.StatusInternalServerError, "Failed to delete user")
		return
	}
	
	resp.Success(map[string]string{"message": "User deleted successfully"})
}

// SuspendUser suspends a user (admin)
func (h *UserHandler) SuspendUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	var request struct {
		Reason string `json:"reason" validate:"required"`
	}
	
	if err := h.validator.ValidateJSON(r, &request); err != nil {
		resp.Error(http.StatusBadRequest, err.Error())
		return
	}
	
	if err := h.userService.SuspendUser(r.Context(), userID, request.Reason); err != nil {
		h.logger.WithError(err).Error("Failed to suspend user")
		resp.Error(http.StatusInternalServerError, "Failed to suspend user")
		return
	}
	
	resp.Success(map[string]string{"message": "User suspended successfully"})
}

// UnsuspendUser unsuspends a user (admin)
func (h *UserHandler) UnsuspendUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	if err := h.userService.UnsuspendUser(r.Context(), userID); err != nil {
		h.logger.WithError(err).Error("Failed to unsuspend user")
		resp.Error(http.StatusInternalServerError, "Failed to unsuspend user")
		return
	}
	
	resp.Success(map[string]string{"message": "User unsuspended successfully"})
}

// RestoreUser restores a deleted user (admin)
func (h *UserHandler) RestoreUser(w http.ResponseWriter, r *http.Request) {
	resp := httpPkg.NewResponse(w)
	
	vars := mux.Vars(r)
	userID := vars["id"]
	
	if err := h.userService.RestoreUser(r.Context(), userID); err != nil {
		h.logger.WithError(err).Error("Failed to restore user")
		resp.Error(http.StatusInternalServerError, "Failed to restore user")
		return
	}
	
	resp.Success(map[string]string{"message": "User restored successfully"})
}

// Helper methods

// toPublicProfile converts user to public profile
func (h *UserHandler) toPublicProfile(user *models.UserProfile) map[string]interface{} {
	profile := map[string]interface{}{
		"id":           user.ID,
		"username":     user.Username,
		"first_name":   user.FirstName,
		"last_name":    user.LastName,
		"avatar":       user.Avatar,
		"bio":          user.Bio,
		"created_at":   user.CreatedAt,
		"is_verified":  user.IsVerified,
		"last_seen_at": user.LastSeenAt,
	}
	
	// Add optional fields based on privacy settings
	if user.Preferences.ShowLocation {
		profile["location"] = user.Location
	}
	
	if user.Preferences.PublicProfile {
		profile["website"] = user.Website
		profile["social_links"] = user.SocialLinks
	}
	
	return profile
}