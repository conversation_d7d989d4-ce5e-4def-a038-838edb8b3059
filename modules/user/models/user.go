package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)


// User đ<PERSON><PERSON> diện cho một user trong hệ thống
type User struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	TenantID  string     `json:"tenant_id" validate:"required" gorm:"index"`
	Email     string     `json:"email" validate:"required,email" gorm:"uniqueIndex:idx_tenant_email"`
	Username  string     `json:"username" validate:"required,min=3,max=50" gorm:"uniqueIndex:idx_tenant_username"`
	FirstName string     `json:"first_name" validate:"required,min=1,max=50"`
	LastName  string     `json:"last_name" validate:"required,min=1,max=50"`
	
	// Authentication
	PasswordHash    string `json:"-" gorm:"column:password_hash"`
	EmailVerified   bool   `json:"email_verified"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty"`
	
	// Profile info
	Avatar      string `json:"avatar" validate:"omitempty,url"`
	Bio         string `json:"bio" validate:"max=500"`
	Website     string `json:"website" validate:"omitempty,url"`
	Location    string `json:"location" validate:"max=100"`
	DateOfBirth *time.Time `json:"date_of_birth,omitempty"`
	
	// Status and role
	Status UserStatus `json:"status" validate:"required"`
	Role   string     `json:"role" validate:"required"`
	
	// Preferences
	Language  string `json:"language" validate:"max=10"`
	Timezone  string `json:"timezone" validate:"max=50"`
	Theme     string `json:"theme" validate:"max=20"`
	
	// Two-factor authentication
	TwoFactorEnabled bool   `json:"two_factor_enabled"`
	TwoFactorSecret  string `json:"-" gorm:"column:two_factor_secret"`
	
	// Social logins
	GoogleID   string `json:"-" gorm:"column:google_id"`
	FacebookID string `json:"-" gorm:"column:facebook_id"`
	GitHubID   string `json:"-" gorm:"column:github_id"`
	
	// Activity tracking
	LastLoginAt    *time.Time `json:"last_login_at,omitempty"`
	LastActiveAt   *time.Time `json:"last_active_at,omitempty"`
	LoginCount     int64      `json:"login_count"`
	FailedLogins   int        `json:"failed_logins"`
	LockedUntil    *time.Time `json:"locked_until,omitempty"`
	
	// Verification tokens
	VerificationToken    string     `json:"-" gorm:"column:verification_token"`
	VerificationExpiry   *time.Time `json:"-" gorm:"column:verification_expiry"`
	PasswordResetToken   string     `json:"-" gorm:"column:password_reset_token"`
	PasswordResetExpiry  *time.Time `json:"-" gorm:"column:password_reset_expiry"`
	
	// Timestamps
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	
	// Computed fields
	FullName string `json:"full_name" gorm:"-"`
}

// Filter models


// Update models

// UserUpdate để cập nhật user
type UserUpdate struct {
	Email        *string          `json:"email,omitempty"`
	Username     *string          `json:"username,omitempty"`
	FirstName    *string          `json:"first_name,omitempty"`
	LastName     *string          `json:"last_name,omitempty"`
	Avatar       *string          `json:"avatar,omitempty"`
	Bio          *string          `json:"bio,omitempty"`
	Website      *string          `json:"website,omitempty"`
	Location     *string          `json:"location,omitempty"`
	DateOfBirth  *time.Time       `json:"date_of_birth,omitempty"`
	PhoneNumber  *string          `json:"phone_number,omitempty"`
	Language     *string          `json:"language,omitempty"`
	Timezone     *string          `json:"timezone,omitempty"`
	Theme        *string          `json:"theme,omitempty"`
	Role         *string          `json:"role,omitempty"`
	Preferences  *UserPreferences `json:"preferences,omitempty"`
	SocialLinks  *SocialLinks     `json:"social_links,omitempty"`
}

// UserPasswordUpdate để cập nhật password
type UserPasswordUpdate struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// UserRegistration để đăng ký user mới
type UserRegistration struct {
	TenantID        string `json:"tenant_id" validate:"required"`
	Email           string `json:"email" validate:"required,email"`
	Username        string `json:"username" validate:"required,min=3,max=50"`
	Password        string `json:"password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=Password"`
	FirstName       string `json:"first_name" validate:"required,min=1,max=50"`
	LastName        string `json:"last_name" validate:"required,min=1,max=50"`
	AcceptTerms     bool   `json:"accept_terms" validate:"required"`
}

// Statistics models

// UserStatistics thống kê user
type UserStatistics struct {
	TenantID          string    `json:"tenant_id"`
	TotalUsers        int64     `json:"total_users"`
	ActiveUsers       int64     `json:"active_users"`
	InactiveUsers     int64     `json:"inactive_users"`
	SuspendedUsers    int64     `json:"suspended_users"`
	PendingUsers      int64     `json:"pending_users"`
	VerifiedUsers     int64     `json:"verified_users"`
	UnverifiedUsers   int64     `json:"unverified_users"`
	TwoFactorUsers    int64     `json:"two_factor_users"`
	NewUsersToday     int64     `json:"new_users_today"`
	NewUsersThisWeek  int64     `json:"new_users_this_week"`
	NewUsersThisMonth int64     `json:"new_users_this_month"`
	LastUpdated       time.Time `json:"last_updated"`
}

// UserPreferences represents user preferences and settings
type UserPreferences struct {
	EmailNotifications      bool   `json:"email_notifications"`
	PushNotifications       bool   `json:"push_notifications"`
	NewsletterSubscribed    bool   `json:"newsletter_subscribed"`
	PublicProfile           bool   `json:"public_profile"`
	ShowEmail               bool   `json:"show_email"`
	ShowLocation            bool   `json:"show_location"`
	ShowBirthDate           bool   `json:"show_birth_date"`
	PreferredTheme          string `json:"preferred_theme"` // light, dark, auto
	PreferredLanguage       string `json:"preferred_language"`
	EmailFrequency          string `json:"email_frequency"` // instant, daily, weekly
	PrivacyLevel            string `json:"privacy_level"`   // public, friends, private
}

// Value implements driver.Valuer interface for GORM JSON support
func (p UserPreferences) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan implements sql.Scanner interface for GORM JSON support
func (p *UserPreferences) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return json.Unmarshal([]byte(value.(string)), p)
	}
	return json.Unmarshal(bytes, p)
}

// SocialLinks represents user's social media links
type SocialLinks struct {
	Facebook  string `json:"facebook"`
	Twitter   string `json:"twitter"`
	LinkedIn  string `json:"linkedin"`
	Instagram string `json:"instagram"`
	GitHub    string `json:"github"`
	YouTube   string `json:"youtube"`
	Website   string `json:"website"`
}

// Value implements driver.Valuer interface for GORM JSON support
func (s SocialLinks) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements sql.Scanner interface for GORM JSON support
func (s *SocialLinks) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return json.Unmarshal([]byte(value.(string)), s)
	}
	return json.Unmarshal(bytes, s)
}