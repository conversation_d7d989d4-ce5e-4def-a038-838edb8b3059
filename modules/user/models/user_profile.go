package models

import (
	"time"

	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
)

// UserStatus type from user.go to avoid circular imports
type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
	UserStatusPending   UserStatus = "pending"
	UserStatusDeleted   UserStatus = "deleted"
)

// UserProfile extends the base User model with additional profile information
type UserProfile struct {
	authModels.User
	Bio           string          `json:"bio" gorm:"type:text"`
	Location      string          `json:"location" gorm:"size:200"`
	Website       string          `json:"website" gorm:"size:500"`
	DateOfBirth   *time.Time      `json:"date_of_birth"`
	PhoneNumber   string          `json:"phone_number" gorm:"size:20"`
	Language      string          `json:"language" gorm:"size:10;default:'en'"`
	Timezone      string          `json:"timezone" gorm:"size:50;default:'UTC'"`
	Status        UserStatus      `json:"status" gorm:"size:20;default:'active'"`
	Preferences   UserPreferences `json:"preferences" gorm:"type:json"`
	SocialLinks   SocialLinks     `json:"social_links" gorm:"type:json"`
	LastSeenAt    *time.Time      `json:"last_seen_at"`
	EmailVerified bool            `json:"email_verified" gorm:"default:false"`
	PhoneVerified bool            `json:"phone_verified" gorm:"default:false"`
	TwoFactorEnabled bool         `json:"two_factor_enabled" gorm:"default:false"`
}

// TableName returns the table name for UserProfile
func (UserProfile) TableName() string {
	return "users"
}



// ProfileUpdate represents allowed fields for user profile update
type ProfileUpdate struct {
	FirstName        *string          `json:"first_name" validate:"omitempty,min=1,max=100"`
	LastName         *string          `json:"last_name" validate:"omitempty,min=1,max=100"`
	Bio              *string          `json:"bio" validate:"omitempty,max=500"`
	Location         *string          `json:"location" validate:"omitempty,max=200"`
	Website          *string          `json:"website" validate:"omitempty,url"`
	DateOfBirth      *time.Time       `json:"date_of_birth" validate:"omitempty"`
	PhoneNumber      *string          `json:"phone_number" validate:"omitempty,e164"`
	Language         *string          `json:"language" validate:"omitempty,len=2"`
	Timezone         *string          `json:"timezone" validate:"omitempty"`
	Preferences      *UserPreferences `json:"preferences" validate:"omitempty"`
	SocialLinks      *SocialLinks     `json:"social_links" validate:"omitempty"`
}

// ProfileStatistics represents user profile statistics
type ProfileStatistics struct {
	UserID         string    `json:"user_id"`
	TotalPosts     int64     `json:"total_posts"`
	TotalComments  int64     `json:"total_comments"`
	TotalLikes     int64     `json:"total_likes"`
	TotalFollowers int64     `json:"total_followers"`
	TotalFollowing int64     `json:"total_following"`
	LastActiveAt   time.Time `json:"last_active_at"`
	JoinedAt       time.Time `json:"joined_at"`
}

// UserActivity represents user activity log
type UserActivity struct {
	ID          string    `json:"id" gorm:"primaryKey;size:26"`
	UserID      string    `json:"user_id" gorm:"size:26;index"`
	Action      string    `json:"action" gorm:"size:100;index"`      // login, logout, create_post, etc.
	Resource    string    `json:"resource" gorm:"size:100"`           // post, comment, user, etc.
	ResourceID  string    `json:"resource_id" gorm:"size:26"`
	IPAddress   string    `json:"ip_address" gorm:"size:45"`
	UserAgent   string    `json:"user_agent" gorm:"size:500"`
	Metadata    string    `json:"metadata" gorm:"type:json"`          // Additional data as JSON
	CreatedAt   time.Time `json:"created_at" gorm:"index"`
}

// TableName returns the table name for UserActivity
func (UserActivity) TableName() string {
	return "user_activities"
}

// UserFilter represents filters for user profile queries  
type UserFilter struct {
	Cursor       string     `json:"cursor,omitempty"`       // Cursor for pagination
	Limit        int        `json:"limit,omitempty"`        // Number of items per page
	TenantID     string     `json:"tenant_id,omitempty"`
	Status       UserStatus `json:"status,omitempty"`
	Role         string     `json:"role,omitempty"`
	EmailVerified *bool     `json:"email_verified,omitempty"`
	Verified     *bool      `json:"verified"`
	Active       *bool      `json:"active"`
	Query        string     `json:"query,omitempty"`       // Search in name, email, username
	SortBy       string     `json:"sort_by,omitempty"`     // created_at, updated_at, name, email
	SortOrder    string     `json:"sort_order,omitempty"`  // asc, desc
	CreatedFrom  *time.Time `json:"created_from,omitempty"`
	CreatedTo    *time.Time `json:"created_to,omitempty"`
	
	// Deprecated: Use Cursor and Limit instead
	Page         int        `json:"page,omitempty"`
	PageSize     int        `json:"page_size,omitempty"`
}