# User Module

Module quản lý người dùng với đầy đủ các tính năng cho hệ thống blog.

## Tổng quan

User Module cung cấp các chức năng quản lý người dùng bao gồm:

- <PERSON><PERSON><PERSON><PERSON> lý profile người dùng (CRUD operations)
- Hệ thống phân quyền và vai trò
- Quản lý preferences và settings
- Activity logging và statistics
- User verification (email, phone, 2FA)
- Search và filtering người dùng
- Admin operations (suspend, restore, delete)
- GDPR compliance (data export)

## Kiến trúc

```
modules/user/
├── models/           # Data models và structures
│   ├── user.go      # Core user models
│   ├── activity.go  # User activity models  
│   └── preferences.go # User preferences
├── repositories/     # Data access layer
│   ├── interface.go # Repository interfaces
│   └── mock_user_repository.go # Mock implementation
├── services/        # Business logic layer
│   ├── interface.go # Service interfaces
│   └── user_service.go # Mock service implementation
├── handlers/        # HTTP handlers
│   └── user_handler.go # REST endpoints
└── README.md       # Documentation
```

## Models

### UserProfile
```go
type UserProfile struct {
    User                 // Embedded base user
    Bio         string   // User biography
    Location    string   // User location
    Website     string   // Personal website
    // ... additional profile fields
}
```

### User Statuses
- `active`: Người dùng hoạt động bình thường
- `inactive`: Tài khoản không hoạt động
- `suspended`: Tài khoản bị tạm khóa
- `deleted`: Tài khoản đã bị xóa

## API Endpoints

### Public Endpoints
- `GET /users/{id}` - Lấy public profile của user
- `GET /users/search` - Tìm kiếm users công khai

### Protected Endpoints (Yêu cầu authentication)
- `GET /users` - Liệt kê users với pagination
- `GET /users/me` - Lấy profile của user hiện tại
- `PUT /users/me` - Cập nhật profile của user hiện tại
- `GET /users/me/preferences` - Lấy preferences
- `PUT /users/me/preferences` - Cập nhật preferences
- `GET /users/me/activities` - Lấy activity history
- `GET /users/me/statistics` - Lấy thống kê cá nhân
- `POST /users/me/export` - Export dữ liệu (GDPR)
- `POST /users/me/verify-email` - Xác thực email
- `POST /users/me/verify-phone` - Xác thực số điện thoại
- `POST /users/me/2fa` - Bật/tắt 2FA

### Admin Endpoints (Yêu cầu admin role)
- `GET /admin/users` - Liệt kê tất cả users (admin view)
- `GET /admin/users/{id}` - Xem chi tiết user (admin view)
- `PUT /admin/users/{id}` - Cập nhật user (admin)
- `DELETE /admin/users/{id}` - Xóa user
- `POST /admin/users/{id}/suspend` - Tạm khóa user
- `POST /admin/users/{id}/unsuspend` - Mở khóa user
- `POST /admin/users/{id}/restore` - Khôi phục user đã xóa

## Usage Examples

### Tạo User Service
```go
userRepo := repositories.NewMockUserRepository()
logger := utils.NewMockLogger()
userService := services.NewMockUserService(userRepo, logger)
```

### Tạo User Handler
```go
validator := validator.NewValidator()
userHandler := handlers.NewUserHandler(userService, validator, logger)

// Register routes
router := mux.NewRouter()
userHandler.RegisterRoutes(router, authService)
```

### Tạo User mới
```go
user := &models.UserProfile{
    User: models.User{
        Email:     "<EMAIL>",
        Username:  "testuser",
        FirstName: "Test",
        LastName:  "User",
    },
    Bio:      "Hello world!",
    Language: "vi",
    Timezone: "Asia/Ho_Chi_Minh",
}

err := userService.CreateUser(ctx, user)
```

### Cập nhật User Profile
```go
bio := "Updated bio"
location := "Ho Chi Minh City"

update := &models.UserUpdate{
    Bio:      &bio,
    Location: &location,
}

err := userService.UpdateUser(ctx, userID, update)
```

### Tìm kiếm Users
```go
users, err := userService.SearchUsers(ctx, "john", 10)
```

### Lấy User Statistics
```go
stats, err := userService.GetUserStatistics(ctx, userID)
fmt.Printf("Posts: %d, Comments: %d", stats.TotalPosts, stats.TotalComments)
```

## Testing

Module bao gồm comprehensive unit tests:

```bash
# Chạy tests cho services
go test ./modules/user/services/...

# Chạy tests cho repositories  
go test ./modules/user/repositories/...

# Chạy tất cả tests với coverage
go test -cover ./modules/user/...
```

## Mock Data

Repository mock bao gồm sẵn 2 users để test:

1. **Admin User**
   - Email: `<EMAIL>`
   - Username: `admin` 
   - Role: Administrator

2. **Regular User**
   - Email: `<EMAIL>`
   - Username: `testuser`
   - Role: User

## Security Features

- **Input Validation**: Tất cả input được validate bằng go-playground/validator
- **Rate Limiting**: Hỗ trợ rate limiting cho các endpoints
- **Privacy Controls**: User có thể điều khiển privacy settings
- **Activity Logging**: Tất cả actions được log để audit
- **GDPR Compliance**: Hỗ trợ data export và right to be forgotten

## Privacy Settings

Users có thể điều khiển:
- Public profile visibility
- Location sharing
- Last seen visibility  
- Email/phone visibility
- Message permissions
- Activity visibility

## Admin Features

Admins có thể:
- Xem tất cả user data
- Suspend/unsuspend users
- Delete/restore users
- View detailed analytics
- Export user data for legal requests

## Dependencies

- `github.com/gorilla/mux` - HTTP routing
- `github.com/go-playground/validator/v10` - Input validation
- `gorm.io/gorm` - ORM (for real database implementation)
- Local packages: `pkg/utils`, `pkg/validator`, `pkg/http`

## Future Enhancements

- Real database implementation với GORM
- User relationships (follow/unfollow)
- Email/SMS notifications  
- OAuth integration
- Advanced analytics và reporting
- Content moderation tools