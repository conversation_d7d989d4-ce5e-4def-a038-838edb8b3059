headers {
  User-Agent: Bruno/1.0 (Blog API v3 Tests)
  Accept: application/json
  Content-Type: application/json
}

auth {
  mode: inherit
}

script:pre-request {
  // Auto-add authorization header if access_token is available
  const token = bru.getEnvVar("access_token");
  if (token && !req.getHeader("Authorization")) {
    req.setHeader("Authorization", `Bearer ${token}`);
  }
}

script:post-response {
  // Global response logging for debugging
  if (res.getStatus() >= 400) {
    console.log(`Request failed: ${res.getStatus()}`);
    console.log(`Response: ${JSON.stringify(res.getBody(), null, 2)}`);
  }
  
  // Handle token refresh automatically
  if (res.getStatus() === 401) {
    const refreshToken = bru.getEnvVar("refresh_token");
    if (refreshToken) {
      console.log("Access token expired, attempting refresh...");
      // Note: In real scenarios, you might want to implement auto-refresh
    }
  }
}

tests {
  // Global tests that apply to all requests
  test("Response time is reasonable", function() {
    expect(res.getResponseTime()).to.be.below(5000);
  });
  
  test("Response has valid JSON format for API endpoints", function() {
    if (req.getUrl().includes("/api/")) {
      expect(res.getBody()).to.be.an('object');
    }
  });
}