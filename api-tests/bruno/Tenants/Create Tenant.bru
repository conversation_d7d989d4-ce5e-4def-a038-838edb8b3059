meta {
  name: Create Tenant
  type: http
  seq: 1
}

post {
  url: {{api_url}}/tenants
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Test Company",
    "slug": "test-company",
    "description": "A test company for API testing",
    "website": "https://test.example.com",
    "industry": "Technology",
    "size": "11-50",
    "country": "VN"
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data && body.data.tenant) {
      bru.setEnvVar("tenant_id", body.data.tenant.id);
    }
  }
}

tests {
  test("Create tenant returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response contains tenant data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenant');
    expect(body.data.tenant).to.have.property('id');
  });
  
  test("Tenant ID is saved to environment", function() {
    expect(bru.getEnvVar("tenant_id")).to.not.be.empty;
  });
}