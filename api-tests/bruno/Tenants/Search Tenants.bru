meta {
  name: Search Tenants
  type: http
  seq: 6
}

get {
  url: {{api_url}}/tenants/search
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  q: test
  limit: 10
}

tests {
  test("Search tenants returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains search results", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenants');
    expect(body.data.tenants).to.be.an('array');
  });
}