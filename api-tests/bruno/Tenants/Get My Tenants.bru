meta {
  name: Get My Tenants
  type: http
  seq: 5
}

get {
  url: {{api_url}}/tenants/my
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get my tenants returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains tenants array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenants');
    expect(body.data.tenants).to.be.an('array');
  });
}