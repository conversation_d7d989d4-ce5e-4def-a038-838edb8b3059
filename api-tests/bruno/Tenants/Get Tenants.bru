meta {
  name: Get Tenants
  type: http
  seq: 2
}

get {
  url: {{api_url}}/tenants
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
  status: active
}

tests {
  test("Get tenants returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains tenants array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenants');
    expect(body.data.tenants).to.be.an('array');
  });
}