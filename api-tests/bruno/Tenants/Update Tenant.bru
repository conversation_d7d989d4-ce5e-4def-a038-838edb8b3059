meta {
  name: Update Tenant
  type: http
  seq: 4
}

put {
  url: {{api_url}}/tenants/{{tenant_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Updated Company Name",
    "description": "Updated description",
    "website": "https://updated.example.com",
    "industry": "Software",
    "size": "51-200"
  }
}

tests {
  test("Update tenant returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}