meta {
  name: Get Tenant by ID
  type: http
  seq: 3
}

get {
  url: {{api_url}}/tenants/{{tenant_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get tenant by ID returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains tenant data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenant');
    expect(body.data.tenant).to.have.property('id');
  });
}