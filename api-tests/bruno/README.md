# Blog API v3 - Bruno API Testing Collection

This collection contains comprehensive API tests for the Blog API v3 using Bruno REST client.

## Overview

The collection is organized into the following modules:

- **Health** - Health check endpoints
- **Auth** - Authentication and authorization
- **Users** - User management
- **Tenants** - Tenant management
- **Websites** - Website and content management
- **Admin** - Administrative operations

## Setup

### 1. Install Bruno

```bash
# Install Bruno CLI
npm install -g @usebruno/cli

# Or use Bruno Desktop App
# Download from: https://www.usebruno.com/
```

### 2. Environment Configuration

The collection includes two environments:

- **Local** - For local development (`http://localhost:9077`)
- **Development** - For development server (`https://api-dev.blogapi.com`)

### 3. Environment Variables

Key variables used across the collection:

| Variable | Description |
|----------|-------------|
| `base_url` | Base API URL |
| `api_prefix` | API prefix path (`/api/cms/v1`) |
| `api_url` | Complete API URL |
| `access_token` | JWT access token (auto-populated) |
| `refresh_token` | JWT refresh token (auto-populated) |
| `tenant_id` | Current tenant ID (auto-populated) |
| `user_id` | Current user ID (auto-populated) |

## Usage

### 1. Running Tests with Bruno CLI

```bash
# Run all tests in local environment
bru run --env Local

# Run specific folder
bru run --env Local --folder Auth

# Run with output
bru run --env Local --output results.json
```

### 2. Running Tests with Bruno Desktop

1. Open Bruno Desktop App
2. Open the collection folder: `api-tests/bruno`
3. Select environment (Local/Development)
4. Run individual tests or entire folders

### 3. Authentication Flow

The collection automatically handles authentication:

1. **Login** - Sets `access_token` and `refresh_token`
2. **Auto-Auth** - Adds Authorization header automatically
3. **Token Refresh** - Use the refresh endpoint when needed

#### Typical Flow:

```
1. Auth/Login.bru          # Login and get tokens
2. Users/Get Users.bru     # Use authenticated endpoints
3. Auth/Refresh Token.bru  # Refresh when needed
4. Auth/Logout.bru         # Clear tokens
```

## Test Structure

### Request Organization

Each endpoint test includes:

```
meta {
  name: Test Name
  type: http
  seq: 1
}

# HTTP method and URL
post {
  url: {{api_url}}/endpoint
  body: json
  auth: bearer
}

# Authentication (when needed)
auth:bearer {
  token: {{access_token}}
}

# Request body (for POST/PUT)
body:json {
  {
    "field": "value"
  }
}

# Post-response scripts (for variable extraction)
script:post-response {
  // Extract data to environment variables
}

# Assertions
tests {
  test("Description", function() {
    expect(res.getStatus()).to.equal(200);
  });
}
```

### Global Configuration

The `collection.bru` file contains:

- **Global Headers** - Common headers for all requests
- **Pre-request Scripts** - Auto-authentication logic
- **Post-response Scripts** - Error handling and logging
- **Global Tests** - Response time and format validation

## Test Categories

### Health Endpoints

- `Health/Health Check.bru` - Basic health check
- `Health/Liveness Probe.bru` - Kubernetes liveness probe
- `Health/Readiness Probe.bru` - Kubernetes readiness probe

### Authentication

- `Auth/Register.bru` - User registration
- `Auth/Login.bru` - User login (saves tokens)
- `Auth/Refresh Token.bru` - Token refresh
- `Auth/Get Profile.bru` - Get user profile
- `Auth/Logout.bru` - User logout (clears tokens)
- `Auth/Forgot Password.bru` - Password reset request
- `Auth/Change Password.bru` - Change password

### User Management

- `Users/Get Users.bru` - List users with pagination
- `Users/Get User by ID.bru` - Get specific user
- `Users/Update User.bru` - Update user profile
- `Users/Search Users.bru` - Search users
- `Users/Get User Statistics.bru` - Get user statistics

### Tenant Management

- `Tenants/Create Tenant.bru` - Create new tenant
- `Tenants/Get Tenants.bru` - List tenants
- `Tenants/Get Tenant by ID.bru` - Get specific tenant
- `Tenants/Update Tenant.bru` - Update tenant
- `Tenants/Get My Tenants.bru` - Get user's tenants
- `Tenants/Search Tenants.bru` - Search tenants

### Website Management

- `Websites/Create Website.bru` - Create new website
- `Websites/Get Websites.bru` - List websites
- `Websites/Create Post.bru` - Create blog post
- `Websites/Get Posts.bru` - List blog posts

### Admin Operations

- `Admin/Get All Tenants.bru` - Admin: List all tenants
- `Admin/Get All Users.bru` - Admin: List all users
- `Admin/Suspend Tenant.bru` - Admin: Suspend tenant

## Advanced Features

### Automatic Token Management

The collection automatically:

1. **Saves tokens** from login responses
2. **Adds Authorization headers** to protected endpoints
3. **Handles token expiration** (logs 401 errors)
4. **Clears tokens** on logout

### Variable Extraction

Important IDs are automatically saved:

```javascript
script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.data && body.data.tenant) {
      bru.setEnvVar("tenant_id", body.data.tenant.id);
    }
  }
}
```

### Error Handling

Global error logging for debugging:

```javascript
script:post-response {
  if (res.getStatus() >= 400) {
    console.log(`Request failed: ${res.getStatus()}`);
    console.log(`Response: ${JSON.stringify(res.getBody(), null, 2)}`);
  }
}
```

## Testing Best Practices

### 1. Sequential Testing

Run tests in order for dependent operations:

```
Auth/Login → Tenants/Create Tenant → Websites/Create Website → Websites/Create Post
```

### 2. Environment Separation

- Use **Local** environment for development
- Use **Development** environment for integration testing
- Keep production credentials separate

### 3. Data Cleanup

Remember to clean up test data:

- Delete created tenants, websites, posts
- Use admin endpoints for cleanup when needed

### 4. Assertion Patterns

Common test patterns:

```javascript
// Status code
test("Returns 200", function() {
  expect(res.getStatus()).to.equal(200);
});

// Response structure
test("Has required fields", function() {
  const body = res.getBody();
  expect(body).to.have.property('success', true);
  expect(body.data).to.have.property('user');
});

// Array responses
test("Returns array", function() {
  const body = res.getBody();
  expect(body.data.users).to.be.an('array');
});
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Run `Auth/Login.bru` first
   - Check if access_token is saved in environment

2. **404 Not Found**
   - Verify API server is running on correct port
   - Check environment base_url configuration

3. **400 Bad Request**
   - Verify request body format
   - Check required fields in request

4. **500 Internal Server Error**
   - Check API server logs
   - Verify database connectivity

### Debugging

Enable verbose logging:

```bash
# CLI with debug output
bru run --env Local --verbose

# Check environment variables
bru run --env Local --env-var
```

## Contributing

When adding new endpoints:

1. Create appropriately named `.bru` files
2. Add proper authentication if required
3. Include response validation tests
4. Extract important IDs to environment variables
5. Update this README with new endpoints

## Related Documentation

- [API Documentation](../../docs/api/)
- [Authentication Guide](../../docs/auth.md)
- [Multi-tenancy Guide](../../docs/tenant.md)
- [Bruno Documentation](https://docs.usebruno.com/)