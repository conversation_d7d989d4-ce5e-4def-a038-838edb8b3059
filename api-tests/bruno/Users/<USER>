meta {
  name: Get Users
  type: http
  seq: 1
}

get {
  url: {{api_url}}/users
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
  status: active
}

tests {
  test("Get users returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains users array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('users');
    expect(body.data.users).to.be.an('array');
  });
  
  test("Response contains pagination info", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('pagination');
  });
}