meta {
  name: Update User
  type: http
  seq: 3
}

put {
  url: {{api_url}}/users/{{user_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "first_name": "Updated",
    "last_name": "Name",
    "bio": "Updated user bio",
    "location": "Ho Chi Minh City",
    "website": "https://example.com"
  }
}

tests {
  test("Update user returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}