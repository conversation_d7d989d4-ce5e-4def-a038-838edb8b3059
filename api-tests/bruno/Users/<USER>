meta {
  name: Search Users
  type: http
  seq: 4
}

get {
  url: {{api_url}}/users/search
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  q: test
  limit: 10
}

tests {
  test("Search users returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains search results", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('users');
    expect(body.data.users).to.be.an('array');
  });
}