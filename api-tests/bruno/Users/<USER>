meta {
  name: Get User by ID
  type: http
  seq: 2
}

get {
  url: {{api_url}}/users/{{user_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get user by ID returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains user data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('user');
    expect(body.data.user).to.have.property('id');
  });
}