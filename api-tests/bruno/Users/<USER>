meta {
  name: Get User Statistics
  type: http
  seq: 5
}

get {
  url: {{api_url}}/users/{{user_id}}/statistics
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get user statistics returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains statistics", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('statistics');
  });
}