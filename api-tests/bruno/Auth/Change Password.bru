meta {
  name: Change Password
  type: http
  seq: 7
}

post {
  url: {{api_url}}/auth/change-password
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "current_password": "oldpassword",
    "new_password": "NewPassword123!",
    "confirm_password": "NewPassword123!"
  }
}

tests {
  test("Change password returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}