meta {
  name: Forgot Password
  type: http
  seq: 6
}

post {
  url: {{api_url}}/auth/forgot-password
  body: json
  auth: none
}

body:json {
  {
    "email": "<EMAIL>"
  }
}

tests {
  test("Forgot password returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}