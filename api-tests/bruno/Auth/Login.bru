meta {
  name: Login
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/login
  body: json
  auth: none
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "password"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("access_token", body.data.access_token);
      bru.setEnvVar("refresh_token", body.data.refresh_token);
      
      if (body.data.user) {
        bru.setEnvVar("user_id", body.data.user.id);
      }
    }
  }
}

tests {
  test("Login returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains tokens", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('access_token');
    expect(body.data).to.have.property('refresh_token');
  });
  
  test("Access token is saved to environment", function() {
    expect(bru.getEnvVar("access_token")).to.not.be.empty;
  });
}