meta {
  name: Refresh Token
  type: http
  seq: 3
}

post {
  url: {{api_url}}/auth/refresh
  body: json
  auth: none
}

body:json {
  {
    "refresh_token": "{{refresh_token}}"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("access_token", body.data.access_token);
      if (body.data.refresh_token) {
        bru.setEnvVar("refresh_token", body.data.refresh_token);
      }
    }
  }
}

tests {
  test("Refresh token returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains new access token", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('access_token');
  });
}