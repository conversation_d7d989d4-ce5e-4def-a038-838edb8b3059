meta {
  name: Logout
  type: http
  seq: 5
}

post {
  url: {{api_url}}/auth/logout
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

script:post-response {
  if (res.getStatus() === 200) {
    bru.setEnvVar("access_token", "");
    bru.setEnvVar("refresh_token", "");
  }
}

tests {
  test("Logout returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}