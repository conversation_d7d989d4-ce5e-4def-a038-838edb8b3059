meta {
  name: Register
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "Password123!",
    "first_name": "Test",
    "last_name": "User",
    "username": "testuser"
  }
}

tests {
  test("Registration returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains user data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('user');
  });
}