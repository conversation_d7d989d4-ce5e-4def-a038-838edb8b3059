meta {
  name: Get Profile
  type: http
  seq: 4
}

get {
  url: {{api_url}}/auth/profile
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Profile returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains user profile", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('user');
    expect(body.data.user).to.have.property('id');
    expect(body.data.user).to.have.property('email');
  });
}