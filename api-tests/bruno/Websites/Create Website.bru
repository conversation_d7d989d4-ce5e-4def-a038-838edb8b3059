meta {
  name: Create Website
  type: http
  seq: 1
}

post {
  url: {{api_url}}/websites
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Test Blog",
    "slug": "test-blog",
    "description": "A test blog website",
    "domain": "blog.test.com",
    "theme": "default",
    "language": "en",
    "timezone": "UTC"
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data && body.data.website) {
      bru.setEnvVar("website_id", body.data.website.id);
    }
  }
}

tests {
  test("Create website returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response contains website data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('website');
    expect(body.data.website).to.have.property('id');
  });
}