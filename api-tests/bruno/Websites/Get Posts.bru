meta {
  name: Get Posts
  type: http
  seq: 4
}

get {
  url: {{api_url}}/websites/{{website_id}}/posts
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
  status: published
}

tests {
  test("Get posts returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains posts array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('posts');
    expect(body.data.posts).to.be.an('array');
  });
}