meta {
  name: Get Websites
  type: http
  seq: 2
}

get {
  url: {{api_url}}/websites
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
}

tests {
  test("Get websites returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains websites array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('websites');
    expect(body.data.websites).to.be.an('array');
  });
}