meta {
  name: Create Post
  type: http
  seq: 3
}

post {
  url: {{api_url}}/websites/{{website_id}}/posts
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "title": "Test Blog Post",
    "slug": "test-blog-post",
    "content": "This is a test blog post content",
    "excerpt": "Test excerpt",
    "status": "published",
    "featured": false,
    "tags": ["test", "api"],
    "categories": ["general"]
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data && body.data.post) {
      bru.setEnvVar("post_id", body.data.post.id);
    }
  }
}

tests {
  test("Create post returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response contains post data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('post');
    expect(body.data.post).to.have.property('id');
  });
}