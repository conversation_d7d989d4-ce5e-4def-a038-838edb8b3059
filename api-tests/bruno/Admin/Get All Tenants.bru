meta {
  name: Get All Tenants (Admin)
  type: http
  seq: 1
}

get {
  url: {{api_url}}/admin/tenants
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
  status: active
}

tests {
  test("Admin get all tenants returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains tenants array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('tenants');
    expect(body.data.tenants).to.be.an('array');
  });
}