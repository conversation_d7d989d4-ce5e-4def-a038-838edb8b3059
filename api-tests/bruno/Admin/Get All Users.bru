meta {
  name: Get All Users (Admin)
  type: http
  seq: 2
}

get {
  url: {{api_url}}/admin/users
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

params:query {
  page: 1
  page_size: 20
  status: active
}

tests {
  test("Admin get all users returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains users array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('users');
    expect(body.data.users).to.be.an('array');
  });
}