meta {
  name: Suspend Tenant (Admin)
  type: http
  seq: 3
}

post {
  url: {{api_url}}/admin/tenants/{{tenant_id}}/suspend
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "reason": "Policy violation",
    "duration_days": 30
  }
}

tests {
  test("Admin suspend tenant returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}