package seeders

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	"github.com/blog-api-v3/blog-api-v3/internal/database/seeders/user"
	"github.com/blog-api-v3/blog-api-v3/internal/database/seeders/rbac"
)

// Manager handles database seeding operations
type Manager struct {
	db      *sql.DB
	seeders map[string]Seeder
	order   []string
}

// NewManager creates a new seeder manager
func NewManager(db *sql.DB) *Manager {
	manager := &Manager{
		db:      db,
		seeders: make(map[string]Seeder),
		order:   []string{},
	}

	// Register all seeders here
	manager.registerSeeders()

	return manager
}

// registerSeeders registers all available seeders
func (m *Manager) registerSeeders() {
	// User module seeders
	userSeeder := user.NewUserSeeder(m.db)
	m.register(userSeeder)
	
	// RBAC module seeders
	rbacSeeder := rbac.NewRBACSeeder(m.db)
	m.register(rbacSeeder)
}

// register adds a seeder to the manager
func (m *Manager) register(seeder Seeder) {
	name := seeder.Name()
	if _, exists := m.seeders[name]; exists {
		log.Printf("Warning: Seeder %s already registered, skipping", name)
		return
	}
	m.seeders[name] = seeder
	m.order = append(m.order, name)
}

// RunAll executes all registered seeders
func (m *Manager) RunAll() error {
	log.Println("Starting database seeding...")
	
	ctx := context.Background()
	for _, name := range m.order {
		seeder := m.seeders[name]
		log.Printf("Running seeder: %s", name)
		
		if err := seeder.Seed(ctx); err != nil {
			return fmt.Errorf("failed to run seeder %s: %w", name, err)
		}
		
		log.Printf("✅ Seeder %s completed successfully", name)
	}
	
	log.Println("✅ All seeders completed successfully")
	return nil
}

// RunSpecific runs specific seeders
func (m *Manager) RunSpecific(names []string) error {
	log.Println("Running specific seeders...")
	
	ctx := context.Background()
	for _, name := range names {
		seeder, exists := m.seeders[name]
		if !exists {
			return fmt.Errorf("seeder not found: %s", name)
		}
		
		log.Printf("Running seeder: %s", name)
		if err := seeder.Seed(ctx); err != nil {
			return fmt.Errorf("failed to run seeder %s: %w", name, err)
		}
		
		log.Printf("✅ Seeder %s completed successfully", name)
	}
	
	return nil
}

// List returns a list of all registered seeders
func (m *Manager) List() []string {
	return m.order
}

// RollbackAll rolls back all seeders in reverse order
func (m *Manager) RollbackAll() error {
	log.Println("Rolling back all seeders...")
	
	// Check if any seeders implement LegacySeeder interface
	for i := len(m.order) - 1; i >= 0; i-- {
		name := m.order[i]
		seeder := m.seeders[name]
		
		// Check if the seeder implements LegacySeeder interface
		if legacySeeder, ok := seeder.(LegacySeeder); ok {
			log.Printf("Rolling back seeder: %s", name)
			
			if err := legacySeeder.Rollback(m.db); err != nil {
				return fmt.Errorf("failed to rollback seeder %s: %w", name, err)
			}
			
			log.Printf("✅ Seeder %s rolled back successfully", name)
		} else {
			log.Printf("⚠️ Seeder %s does not support rollback", name)
		}
	}
	
	log.Println("✅ All seeders rolled back successfully")
	return nil
}