package rbac

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
)

// RBACSeeder implements the RBAC data seeding
type RBACSeeder struct {
	db *sql.DB
}

// NewRBACSeeder creates a new RBAC seeder
func NewRBACSeeder(db *sql.DB) *RBACSeeder {
	return &RBACSeeder{db: db}
}

// Name returns the seeder name
func (s *RBACSeeder) Name() string {
	return "rbac_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *RBACSeeder) Dependencies() []string {
	return []string{"tenant_seeder", "website_seeder", "user_seeder"}
}

// Seed executes the RBAC seeding process
func (s *RBACSeeder) Seed(ctx context.Context) error {
	log.Println("Starting RBAC module seeding...")

	// 1. Create permission groups
	if err := s.seedPermissionGroups(ctx); err != nil {
		return fmt.Errorf("failed to seed permission groups: %w", err)
	}

	// 2. Create permissions
	if err := s.seedPermissions(ctx); err != nil {
		return fmt.Errorf("failed to seed permissions: %w", err)
	}

	// 3. Create roles for each tenant
	if err := s.seedRoles(ctx); err != nil {
		return fmt.Errorf("failed to seed roles: %w", err)
	}

	// 4. Create role-permission mappings
	if err := s.seedRolePermissions(ctx); err != nil {
		return fmt.Errorf("failed to seed role-permission mappings: %w", err)
	}

	// 5. Assign roles to users
	if err := s.seedUserRoles(ctx); err != nil {
		return fmt.Errorf("failed to seed user-role assignments: %w", err)
	}

	log.Println("RBAC module seeding completed successfully")
	return nil
}

// seedPermissionGroups creates permission groups for organizing permissions
func (s *RBACSeeder) seedPermissionGroups(ctx context.Context) error {
	log.Println("Seeding permission groups...")

	groups := []PermissionGroup{
		// User Management Groups
		{
			Name:           "user-management",
			DisplayName:    "User Management",
			Description:    "Permissions for managing users, profiles, and user data",
			Module:         "user",
			Category:       "core",
			Level:          1,
			SortOrder:      1,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#007bff",
			Icon:           "users",
		},
		{
			Name:           "user-profiles",
			DisplayName:    "User Profiles",
			Description:    "Permissions for managing user profiles and personal information",
			Module:         "user",
			Category:       "feature",
			Level:          2,
			SortOrder:      1,
			IsSystemGroup:  true,
			Color:          "#28a745",
			Icon:           "user-circle",
		},
		
		// Website Management Groups
		{
			Name:           "website-management",
			DisplayName:    "Website Management",
			Description:    "Permissions for managing websites and website configuration",
			Module:         "website",
			Category:       "core",
			Level:          1,
			SortOrder:      2,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#6f42c1",
			Icon:           "globe",
		},
		{
			Name:           "content-management",
			DisplayName:    "Content Management",
			Description:    "Permissions for managing website content and pages",
			Module:         "website",
			Category:       "feature",
			Level:          2,
			SortOrder:      1,
			IsSystemGroup:  true,
			Color:          "#fd7e14",
			Icon:           "file-text",
		},
		
		// Auth Management Groups
		{
			Name:           "auth-management",
			DisplayName:    "Authentication Management",
			Description:    "Permissions for managing authentication systems and security",
			Module:         "auth",
			Category:       "core",
			Level:          1,
			SortOrder:      3,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#dc3545",
			Icon:           "shield",
		},
		
		// RBAC Management Groups
		{
			Name:           "rbac-management",
			DisplayName:    "Role & Permission Management",
			Description:    "Permissions for managing roles, permissions, and access control",
			Module:         "rbac",
			Category:       "administrative",
			Level:          1,
			SortOrder:      4,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#17a2b8",
			Icon:           "key",
		},
		
		// Tenant Management Groups
		{
			Name:           "tenant-management",
			DisplayName:    "Tenant Management",
			Description:    "Permissions for managing tenants and multi-tenancy features",
			Module:         "tenant",
			Category:       "administrative",
			Level:          1,
			SortOrder:      5,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#6c757d",
			Icon:           "building",
		},
		
		// Onboarding Groups
		{
			Name:           "onboarding-management",
			DisplayName:    "Onboarding Management",
			Description:    "Permissions for managing user onboarding flows and processes",
			Module:         "onboarding",
			Category:       "feature",
			Level:          1,
			SortOrder:      6,
			IsSystemGroup:  true,
			Color:          "#20c997",
			Icon:           "compass",
		},
		
		// System Administration Groups
		{
			Name:           "system-administration",
			DisplayName:    "System Administration",
			Description:    "High-level system administration permissions",
			Module:         "system",
			Category:       "administrative",
			Level:          1,
			SortOrder:      7,
			IsSystemGroup:  true,
			IsRequired:     true,
			Color:          "#e83e8c",
			Icon:           "settings",
		},
	}

	// First, create all parent groups (level 1)
	var parentGroups []PermissionGroup
	var childGroups []PermissionGroup
	
	for _, group := range groups {
		if group.Level == 1 {
			parentGroups = append(parentGroups, group)
		} else {
			childGroups = append(childGroups, group)
		}
	}

	// Create parent groups first
	parentGroupIDs := make(map[string]int)
	for _, group := range parentGroups {
		groupID, err := s.createPermissionGroupWithID(ctx, group)
		if err != nil {
			return fmt.Errorf("failed to create permission group %s: %w", group.Name, err)
		}
		parentGroupIDs[group.Name] = groupID
	}

	// Create child groups with correct parent references
	for _, group := range childGroups {
		// Set parent group ID based on module
		if group.Module == "user" && group.Level == 2 {
			if parentID, exists := parentGroupIDs["user-management"]; exists {
				group.ParentGroupID = &parentID
			}
		}
		if group.Module == "website" && group.Level == 2 {
			if parentID, exists := parentGroupIDs["website-management"]; exists {
				group.ParentGroupID = &parentID
			}
		}
		
		if _, err := s.createPermissionGroupWithID(ctx, group); err != nil {
			return fmt.Errorf("failed to create permission group %s: %w", group.Name, err)
		}
	}

	log.Printf("Created %d permission groups", len(groups))
	return nil
}

// seedPermissions creates comprehensive permissions for all modules
func (s *RBACSeeder) seedPermissions(ctx context.Context) error {
	log.Println("Seeding permissions...")

	permissions := []Permission{
		// User Management Permissions
		{Name: "users.create", DisplayName: "Create Users", Description: "Create new user accounts", Module: "user", Resource: "users", Action: "create", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "users.read", DisplayName: "Read Users", Description: "View user accounts and profiles", Module: "user", Resource: "users", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "users.update", DisplayName: "Update Users", Description: "Edit user accounts and profiles", Module: "user", Resource: "users", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "users.delete", DisplayName: "Delete Users", Description: "Delete user accounts", Module: "user", Resource: "users", Action: "delete", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "users.list", DisplayName: "List Users", Description: "View list of all users", Module: "user", Resource: "users", Action: "list", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "users.impersonate", DisplayName: "Impersonate Users", Description: "Login as another user", Module: "user", Resource: "users", Action: "impersonate", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		
		// User Profile Permissions
		{Name: "user-profiles.create", DisplayName: "Create User Profiles", Description: "Create user profile information", Module: "user", Resource: "user-profiles", Action: "create", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true, RequiresOwnership: true},
		{Name: "user-profiles.read", DisplayName: "Read User Profiles", Description: "View user profile information", Module: "user", Resource: "user-profiles", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "user-profiles.update", DisplayName: "Update User Profiles", Description: "Edit user profile information", Module: "user", Resource: "user-profiles", Action: "update", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true, RequiresOwnership: true},
		{Name: "user-profiles.delete", DisplayName: "Delete User Profiles", Description: "Delete user profile information", Module: "user", Resource: "user-profiles", Action: "delete", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true, RequiresOwnership: true},
		
		// Website Management Permissions
		{Name: "websites.create", DisplayName: "Create Websites", Description: "Create new websites", Module: "website", Resource: "websites", Action: "create", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "websites.read", DisplayName: "Read Websites", Description: "View website information", Module: "website", Resource: "websites", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "websites.update", DisplayName: "Update Websites", Description: "Edit website configuration", Module: "website", Resource: "websites", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "websites.delete", DisplayName: "Delete Websites", Description: "Delete websites", Module: "website", Resource: "websites", Action: "delete", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "websites.list", DisplayName: "List Websites", Description: "View list of all websites", Module: "website", Resource: "websites", Action: "list", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "websites.manage-settings", DisplayName: "Manage Website Settings", Description: "Configure website settings", Module: "website", Resource: "websites", Action: "manage-settings", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		
		// Content Management Permissions
		{Name: "content.create", DisplayName: "Create Content", Description: "Create website content", Module: "website", Resource: "content", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.read", DisplayName: "Read Content", Description: "View website content", Module: "website", Resource: "content", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.update", DisplayName: "Update Content", Description: "Edit website content", Module: "website", Resource: "content", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.delete", DisplayName: "Delete Content", Description: "Delete website content", Module: "website", Resource: "content", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "content.publish", DisplayName: "Publish Content", Description: "Publish website content", Module: "website", Resource: "content", Action: "publish", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		
		// Authentication Permissions
		{Name: "auth.manage-sessions", DisplayName: "Manage Sessions", Description: "Manage user authentication sessions", Module: "auth", Resource: "sessions", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.manage-tokens", DisplayName: "Manage Auth Tokens", Description: "Manage authentication tokens", Module: "auth", Resource: "tokens", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.manage-providers", DisplayName: "Manage Auth Providers", Description: "Manage OAuth providers and settings", Module: "auth", Resource: "providers", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.view-logs", DisplayName: "View Auth Logs", Description: "View authentication logs and audit trails", Module: "auth", Resource: "logs", Action: "read", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		
		// RBAC Management Permissions
		{Name: "rbac.roles.create", DisplayName: "Create Roles", Description: "Create new roles", Module: "rbac", Resource: "roles", Action: "create", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.roles.read", DisplayName: "Read Roles", Description: "View roles and role details", Module: "rbac", Resource: "roles", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "rbac.roles.update", DisplayName: "Update Roles", Description: "Edit roles and role permissions", Module: "rbac", Resource: "roles", Action: "update", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.roles.delete", DisplayName: "Delete Roles", Description: "Delete roles", Module: "rbac", Resource: "roles", Action: "delete", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.permissions.read", DisplayName: "Read Permissions", Description: "View available permissions", Module: "rbac", Resource: "permissions", Action: "read", Scope: "global", RiskLevel: "low", IsSystemPermission: true},
		{Name: "rbac.user-roles.assign", DisplayName: "Assign User Roles", Description: "Assign roles to users", Module: "rbac", Resource: "user-roles", Action: "assign", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.user-roles.revoke", DisplayName: "Revoke User Roles", Description: "Remove roles from users", Module: "rbac", Resource: "user-roles", Action: "revoke", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		
		// Tenant Management Permissions
		{Name: "tenants.create", DisplayName: "Create Tenants", Description: "Create new tenants", Module: "tenant", Resource: "tenants", Action: "create", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "tenants.read", DisplayName: "Read Tenants", Description: "View tenant information", Module: "tenant", Resource: "tenants", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "tenants.update", DisplayName: "Update Tenants", Description: "Edit tenant configuration", Module: "tenant", Resource: "tenants", Action: "update", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenants.delete", DisplayName: "Delete Tenants", Description: "Delete tenants", Module: "tenant", Resource: "tenants", Action: "delete", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "tenants.manage-settings", DisplayName: "Manage Tenant Settings", Description: "Configure tenant settings", Module: "tenant", Resource: "tenants", Action: "manage-settings", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		
		// Onboarding Permissions
		{Name: "onboarding.create", DisplayName: "Create Onboarding Flows", Description: "Create onboarding flows", Module: "onboarding", Resource: "flows", Action: "create", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "onboarding.read", DisplayName: "Read Onboarding Flows", Description: "View onboarding flows", Module: "onboarding", Resource: "flows", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "onboarding.update", DisplayName: "Update Onboarding Flows", Description: "Edit onboarding flows", Module: "onboarding", Resource: "flows", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "onboarding.delete", DisplayName: "Delete Onboarding Flows", Description: "Delete onboarding flows", Module: "onboarding", Resource: "flows", Action: "delete", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "onboarding.manage-steps", DisplayName: "Manage Onboarding Steps", Description: "Configure onboarding steps", Module: "onboarding", Resource: "steps", Action: "manage", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		
		// System Administration Permissions
		{Name: "system.maintenance", DisplayName: "System Maintenance", Description: "Perform system maintenance operations", Module: "system", Resource: "maintenance", Action: "manage", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "system.monitoring", DisplayName: "System Monitoring", Description: "Access system monitoring and metrics", Module: "system", Resource: "monitoring", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "system.logs", DisplayName: "System Logs", Description: "Access system logs and audit trails", Module: "system", Resource: "logs", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "system.configuration", DisplayName: "System Configuration", Description: "Configure system-wide settings", Module: "system", Resource: "configuration", Action: "manage", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
	}

	for _, perm := range permissions {
		if err := s.createPermission(ctx, perm); err != nil {
			return fmt.Errorf("failed to create permission %s: %w", perm.Name, err)
		}
	}

	log.Printf("Created %d permissions", len(permissions))
	return nil
}

// Data structures for seeding
type PermissionGroup struct {
	ID            int
	Name          string
	DisplayName   string
	Description   string
	ParentGroupID *int
	Level         int
	SortOrder     int
	Module        string
	Category      string
	Color         string
	Icon          string
	IsSystemGroup bool
	IsRequired    bool
}

type Permission struct {
	ID                   int
	Name                 string
	DisplayName          string
	Description          string
	Module               string
	Resource             string
	Action               string
	Scope                string
	RequiresOwnership    bool
	IsSystemPermission   bool
	RiskLevel            string
}

type Role struct {
	ID           int
	TenantID     int
	Name         string
	DisplayName  string
	Description  string
	IsSystemRole bool
	IsDefaultRole bool
	Level        int
	Scope        string
	ContextID    *int
	Color        string
	Icon         string
	Capabilities []string
	Permissions  []string
}

// createPermissionGroupWithID creates a permission group record and returns the ID
func (s *RBACSeeder) createPermissionGroupWithID(ctx context.Context, group PermissionGroup) (int, error) {
	// First check if the group already exists
	var existingID int
	checkQuery := `SELECT id FROM rbac_permission_groups WHERE module = ? AND name = ?`
	err := s.db.QueryRowContext(ctx, checkQuery, group.Module, group.Name).Scan(&existingID)
	if err == nil {
		// Group exists, update it
		updateQuery := `
			UPDATE rbac_permission_groups SET
				display_name = ?, description = ?, parent_group_id = ?, level = ?, sort_order = ?,
				color = ?, icon = ?, updated_at = NOW()
			WHERE id = ?
		`
		
		_, err = s.db.ExecContext(ctx, updateQuery,
			group.DisplayName, group.Description, group.ParentGroupID, group.Level, group.SortOrder,
			group.Color, group.Icon, existingID,
		)
		if err != nil {
			return 0, err
		}
		
		return existingID, nil
	} else if err != sql.ErrNoRows {
		return 0, err
	}

	// Group doesn't exist, create it
	defaultPerms, _ := json.Marshal([]string{})
	metadata, _ := json.Marshal(map[string]interface{}{})

	insertQuery := `
		INSERT INTO rbac_permission_groups (
			name, display_name, description, parent_group_id, level, sort_order,
			module, category, color, icon, is_system_group, is_required,
			default_permissions, group_metadata, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	result, err := s.db.ExecContext(ctx, insertQuery,
		group.Name, group.DisplayName, group.Description, group.ParentGroupID, group.Level, group.SortOrder,
		group.Module, group.Category, group.Color, group.Icon, group.IsSystemGroup, group.IsRequired,
		string(defaultPerms), string(metadata),
	)
	if err != nil {
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return int(id), nil
}

// createPermission creates a permission record
func (s *RBACSeeder) createPermission(ctx context.Context, perm Permission) error {
	conditions, _ := json.Marshal(map[string]interface{}{})
	limitations, _ := json.Marshal(map[string]interface{}{})

	query := `
		INSERT INTO rbac_permissions (
			name, display_name, description, module, resource, action, scope,
			requires_ownership, is_system_permission, risk_level, conditions,
			limitations, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
			display_name = VALUES(display_name),
			description = VALUES(description),
			requires_ownership = VALUES(requires_ownership),
			risk_level = VALUES(risk_level),
			updated_at = NOW()
	`

	_, err := s.db.ExecContext(ctx, query,
		perm.Name, perm.DisplayName, perm.Description, perm.Module, perm.Resource, perm.Action, perm.Scope,
		perm.RequiresOwnership, perm.IsSystemPermission, perm.RiskLevel, string(conditions), string(limitations),
	)

	return err
}

// seedRoles creates roles for each tenant
func (s *RBACSeeder) seedRoles(ctx context.Context) error {
	log.Println("Seeding roles...")

	// Get available tenants
	tenantIDs, err := s.getAvailableTenants(ctx)
	if err != nil {
		return fmt.Errorf("failed to get available tenants: %w", err)
	}

	if len(tenantIDs) == 0 {
		return fmt.Errorf("no tenants available for seeding roles")
	}

	// Create roles for each tenant
	for _, tenantID := range tenantIDs {
		roles := s.getRolesForTenant(tenantID)
		
		for _, role := range roles {
			if err := s.createRole(ctx, role); err != nil {
				return fmt.Errorf("failed to create role %s for tenant %d: %w", role.Name, tenantID, err)
			}
		}
	}

	log.Printf("Created roles for %d tenants", len(tenantIDs))
	return nil
}

// seedRolePermissions creates role-permission mappings
func (s *RBACSeeder) seedRolePermissions(ctx context.Context) error {
	log.Println("Seeding role-permission mappings...")

	// Get all roles
	roles, err := s.getAllRoles(ctx)
	if err != nil {
		return fmt.Errorf("failed to get roles: %w", err)
	}

	// Get all permissions
	permissions, err := s.getAllPermissions(ctx)
	if err != nil {
		return fmt.Errorf("failed to get permissions: %w", err)
	}

	// Create permission maps for easy lookup
	permissionMap := make(map[string]int)
	for _, perm := range permissions {
		permissionMap[perm.Name] = perm.ID
	}

	// Assign permissions to roles
	for _, role := range roles {
		rolePermissions := s.getPermissionsForRole(role.Name)
		
		for _, permName := range rolePermissions {
			if permID, exists := permissionMap[permName]; exists {
				if err := s.createRolePermission(ctx, role.ID, permID); err != nil {
					return fmt.Errorf("failed to assign permission %s to role %s: %w", permName, role.Name, err)
				}
			}
		}
	}

	log.Printf("Created role-permission mappings for %d roles", len(roles))
	return nil
}

// seedUserRoles assigns roles to users
func (s *RBACSeeder) seedUserRoles(ctx context.Context) error {
	log.Println("Seeding user-role assignments...")

	// Get all users
	users, err := s.getAllUsers(ctx)
	if err != nil {
		return fmt.Errorf("failed to get users: %w", err)
	}

	// Get all roles
	roles, err := s.getAllRoles(ctx)
	if err != nil {
		return fmt.Errorf("failed to get roles: %w", err)
	}

	// Create role maps for easy lookup
	roleMap := make(map[string]map[int]int) // [role_name][tenant_id] = role_id
	for _, role := range roles {
		if roleMap[role.Name] == nil {
			roleMap[role.Name] = make(map[int]int)
		}
		roleMap[role.Name][role.TenantID] = role.ID
	}

	// Assign roles to users based on their current role in the users table
	for _, user := range users {
		var targetRoleName string
		
		// Map user.Role to RBAC role name
		switch user.Role {
		case "admin":
			targetRoleName = "admin"
		case "user":
			targetRoleName = "editor"
		case "guest":
			targetRoleName = "viewer"
		default:
			targetRoleName = "viewer"
		}

		// Find the role for this tenant
		if rolesByTenant, exists := roleMap[targetRoleName]; exists {
			if roleID, exists := rolesByTenant[user.TenantID]; exists {
				if err := s.createUserRole(ctx, user.ID, roleID, user.TenantID); err != nil {
					return fmt.Errorf("failed to assign role %s to user %d: %w", targetRoleName, user.ID, err)
				}
			}
		}
	}

	log.Printf("Created user-role assignments for %d users", len(users))
	return nil
}

// Helper methods
func (s *RBACSeeder) getAvailableTenants(ctx context.Context) ([]int, error) {
	query := `SELECT id FROM tenants WHERE status = 'active' ORDER BY id LIMIT 10`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tenantIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		tenantIDs = append(tenantIDs, id)
	}

	return tenantIDs, nil
}

func (s *RBACSeeder) getRolesForTenant(tenantID int) []Role {
	return []Role{
		{
			TenantID:     tenantID,
			Name:         "admin",
			DisplayName:  "Administrator",
			Description:  "Full administrative access to all tenant resources",
			IsSystemRole: true,
			Level:        100,
			Scope:        "tenant",
			Color:        "#dc3545",
			Icon:         "crown",
			Capabilities: []string{"manage_all", "delete_all", "create_all", "read_all", "update_all"},
		},
		{
			TenantID:     tenantID,
			Name:         "editor",
			DisplayName:  "Editor",
			Description:  "Can create, edit, and manage content and users",
			IsSystemRole: true,
			Level:        75,
			Scope:        "tenant",
			Color:        "#28a745",
			Icon:         "edit",
			Capabilities: []string{"create_content", "edit_content", "manage_users", "read_all"},
		},
		{
			TenantID:     tenantID,
			Name:         "viewer",
			DisplayName:  "Viewer",
			Description:  "Read-only access to tenant resources",
			IsSystemRole: true,
			IsDefaultRole: true,
			Level:        25,
			Scope:        "tenant",
			Color:        "#6c757d",
			Icon:         "eye",
			Capabilities: []string{"read_content", "read_profile"},
		},
		{
			TenantID:     tenantID,
			Name:         "moderator",
			DisplayName:  "Moderator",
			Description:  "Can moderate content and manage basic user interactions",
			IsSystemRole: true,
			Level:        50,
			Scope:        "tenant",
			Color:        "#ffc107",
			Icon:         "shield-check",
			Capabilities: []string{"moderate_content", "read_all", "basic_user_management"},
		},
	}
}

func (s *RBACSeeder) getPermissionsForRole(roleName string) []string {
	switch roleName {
	case "admin":
		return []string{
			// User management
			"users.create", "users.read", "users.update", "users.delete", "users.list", "users.impersonate",
			"user-profiles.create", "user-profiles.read", "user-profiles.update", "user-profiles.delete",
			
			// Website management
			"websites.create", "websites.read", "websites.update", "websites.delete", "websites.list", "websites.manage-settings",
			"content.create", "content.read", "content.update", "content.delete", "content.publish",
			
			// Auth management
			"auth.manage-sessions", "auth.manage-tokens", "auth.manage-providers", "auth.view-logs",
			
			// RBAC management
			"rbac.roles.create", "rbac.roles.read", "rbac.roles.update", "rbac.roles.delete",
			"rbac.permissions.read", "rbac.user-roles.assign", "rbac.user-roles.revoke",
			
			// Tenant management
			"tenants.read", "tenants.update", "tenants.manage-settings",
			
			// Onboarding
			"onboarding.create", "onboarding.read", "onboarding.update", "onboarding.delete", "onboarding.manage-steps",
			
			// System
			"system.monitoring", "system.logs",
		}
	case "editor":
		return []string{
			// User management (limited)
			"users.read", "users.update", "users.list",
			"user-profiles.create", "user-profiles.read", "user-profiles.update",
			
			// Website management
			"websites.read", "websites.list",
			"content.create", "content.read", "content.update", "content.delete", "content.publish",
			
			// Basic RBAC
			"rbac.roles.read", "rbac.permissions.read",
			
			// Onboarding
			"onboarding.read", "onboarding.update", "onboarding.manage-steps",
		}
	case "moderator":
		return []string{
			// User management (very limited)
			"users.read", "users.list",
			"user-profiles.read",
			
			// Website management (content only)
			"websites.read", "websites.list",
			"content.read", "content.update", "content.delete",
			
			// Basic RBAC
			"rbac.roles.read", "rbac.permissions.read",
			
			// Onboarding
			"onboarding.read",
		}
	case "viewer":
		return []string{
			// User management (self only)
			"users.read",
			"user-profiles.create", "user-profiles.read", "user-profiles.update",
			
			// Website management (read only)
			"websites.read", "websites.list",
			"content.read",
			
			// Basic RBAC
			"rbac.roles.read", "rbac.permissions.read",
			
			// Onboarding
			"onboarding.read",
		}
	default:
		return []string{}
	}
}

func (s *RBACSeeder) getAllRoles(ctx context.Context) ([]Role, error) {
	query := `SELECT id, tenant_id, name, display_name FROM rbac_roles ORDER BY tenant_id, name`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var roles []Role
	for rows.Next() {
		var role Role
		if err := rows.Scan(&role.ID, &role.TenantID, &role.Name, &role.DisplayName); err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (s *RBACSeeder) getAllPermissions(ctx context.Context) ([]Permission, error) {
	query := `SELECT id, name, display_name, module, resource, action FROM rbac_permissions ORDER BY module, resource, action`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var perm Permission
		if err := rows.Scan(&perm.ID, &perm.Name, &perm.DisplayName, &perm.Module, &perm.Resource, &perm.Action); err != nil {
			return nil, err
		}
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

func (s *RBACSeeder) getAllUsers(ctx context.Context) ([]struct{ ID, TenantID int; Role string }, error) {
	query := `SELECT id, tenant_id, role FROM users ORDER BY tenant_id, id`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []struct{ ID, TenantID int; Role string }
	for rows.Next() {
		var user struct{ ID, TenantID int; Role string }
		if err := rows.Scan(&user.ID, &user.TenantID, &user.Role); err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}

func (s *RBACSeeder) createRole(ctx context.Context, role Role) error {
	capabilities, _ := json.Marshal(role.Capabilities)
	restrictions, _ := json.Marshal(map[string]interface{}{})

	query := `
		INSERT INTO rbac_roles (
			tenant_id, name, display_name, description, is_system_role, is_default_role,
			level, scope, context_id, color, icon, capabilities, restrictions,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
			display_name = VALUES(display_name),
			description = VALUES(description),
			level = VALUES(level),
			color = VALUES(color),
			icon = VALUES(icon),
			capabilities = VALUES(capabilities),
			updated_at = NOW()
	`

	_, err := s.db.ExecContext(ctx, query,
		role.TenantID, role.Name, role.DisplayName, role.Description, role.IsSystemRole, role.IsDefaultRole,
		role.Level, role.Scope, role.ContextID, role.Color, role.Icon, string(capabilities), string(restrictions),
	)

	return err
}

func (s *RBACSeeder) createRolePermission(ctx context.Context, roleID, permissionID int) error {
	conditions, _ := json.Marshal(map[string]interface{}{})
	limitations, _ := json.Marshal(map[string]interface{}{})

	query := `
		INSERT INTO rbac_role_permissions (
			role_id, permission_id, context_type, context_id, conditions, limitations,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err := s.db.ExecContext(ctx, query,
		roleID, permissionID, "tenant", nil, string(conditions), string(limitations),
	)

	return err
}

func (s *RBACSeeder) createUserRole(ctx context.Context, userID, roleID, tenantID int) error {
	conditions, _ := json.Marshal(map[string]interface{}{})
	limitations, _ := json.Marshal(map[string]interface{}{})

	query := `
		INSERT INTO rbac_user_roles (
			user_id, role_id, context_type, context_id, conditions, limitations,
			is_primary, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err := s.db.ExecContext(ctx, query,
		userID, roleID, "tenant", tenantID, string(conditions), string(limitations), true,
	)

	return err
}

// Rollback removes the seeded RBAC data
func (s *RBACSeeder) Rollback(db *sql.DB) error {
	ctx := context.Background()
	
	// Delete in reverse order of creation
	tables := []string{
		"rbac_user_roles",
		"rbac_role_permissions", 
		"rbac_roles",
		"rbac_permissions",
		"rbac_permission_groups",
	}

	for _, table := range tables {
		query := fmt.Sprintf("DELETE FROM %s WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)", table)
		if _, err := db.ExecContext(ctx, query); err != nil {
			return err
		}
	}

	return nil
}