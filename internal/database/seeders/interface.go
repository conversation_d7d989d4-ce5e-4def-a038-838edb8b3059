package seeders

import (
	"context"
	"database/sql"
)

// Seeder defines the interface for all database seeders
type Seeder interface {
	// Seed executes the seeder logic to populate data
	Seed(ctx context.Context) error
	
	// Name returns the name of the seeder
	Name() string
}

// LegacySeeder defines the legacy interface for GORM-based seeders
type LegacySeeder interface {
	// Seed executes the seeder logic to populate data
	Seed(db *sql.DB) error
	
	// Rollback removes the seeded data (if applicable)
	Rollback(db *sql.DB) error
	
	// Name returns the name of the seeder
	Name() string
	
	// Dependencies returns the names of seeders that must run before this one
	Dependencies() []string
}