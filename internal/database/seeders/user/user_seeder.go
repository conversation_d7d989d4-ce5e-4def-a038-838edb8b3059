package user

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// UserSeeder implements the user data seeding
type UserSeeder struct {
	db *sql.DB
}

// NewUserSeeder creates a new user seeder
func NewUserSeeder(db *sql.DB) *UserSeeder {
	return &UserSeeder{db: db}
}

// Name returns the seeder name
func (s *UserSeeder) Name() string {
	return "user_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *UserSeeder) Dependencies() []string {
	return []string{"tenant_seeder", "website_seeder"}
}

// Seed executes the user seeding process
func (s *UserSeeder) Seed(ctx context.Context) error {
	log.Println("Starting user module seeding...")

	// Get available tenants
	tenantIDs, err := s.getAvailableTenants(ctx)
	if err != nil {
		return fmt.Errorf("failed to get available tenants: %w", err)
	}

	if len(tenantIDs) == 0 {
		return fmt.Errorf("no tenants available for seeding users")
	}

	// Create users for each tenant
	for _, tenantID := range tenantIDs {
		if err := s.seedUsersForTenant(ctx, tenantID); err != nil {
			return fmt.Errorf("failed to seed users for tenant %d: %w", tenantID, err)
		}
	}

	log.Println("User module seeding completed successfully")
	return nil
}

// getAvailableTenants retrieves active tenant IDs
func (s *UserSeeder) getAvailableTenants(ctx context.Context) ([]int, error) {
	query := `SELECT id FROM tenants WHERE status = 'active' ORDER BY id LIMIT 5`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tenantIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		tenantIDs = append(tenantIDs, id)
	}

	return tenantIDs, nil
}

// seedUsersForTenant creates sample users for a specific tenant
func (s *UserSeeder) seedUsersForTenant(ctx context.Context, tenantID int) error {
	users := s.getSampleUsers(tenantID)

	for _, user := range users {
		userID, err := s.createUser(ctx, user)
		if err != nil {
			return fmt.Errorf("failed to create user %s: %w", user.Email, err)
		}

		// Create user profile
		if err := s.createUserProfile(ctx, userID, user.Profile); err != nil {
			return fmt.Errorf("failed to create profile for user %d: %w", userID, err)
		}

		// Create user preferences
		if err := s.createUserPreferences(ctx, userID, user.Preferences); err != nil {
			return fmt.Errorf("failed to create preferences for user %d: %w", userID, err)
		}

		// Create social links
		if err := s.createUserSocialLinks(ctx, userID, user.SocialLinks); err != nil {
			return fmt.Errorf("failed to create social links for user %d: %w", userID, err)
		}

		log.Printf("Created user: %s (ID: %d, Tenant: %d)", user.Email, userID, tenantID)
	}

	return nil
}

// SampleUser represents a sample user with all related data
type SampleUser struct {
	TenantID     int
	Email        string
	Username     string
	FirstName    string
	LastName     string
	DisplayName  string
	Password     string
	Status       string
	Role         string
	Phone        string
	AvatarURL    string
	Timezone     string
	Language     string
	Profile      SampleUserProfile
	Preferences  SampleUserPreferences
	SocialLinks  []SampleSocialLink
}

// SampleUserProfile represents sample profile data
type SampleUserProfile struct {
	Bio          string
	Title        string
	Company      string
	Location     string
	Website      string
	BirthDate    *time.Time
	Gender       string
	AddressLine1 string
	City         string
	State        string
	PostalCode   string
	Country      string
	JobTitle     string
	Department   string
	Skills       []string
	Interests    []string
}

// SampleUserPreferences represents sample preference data
type SampleUserPreferences struct {
	EmailNotifications    bool
	PushNotifications     bool
	MarketingEmails       bool
	NewsletterSubscription bool
	ProfileVisibility     string
	Theme                 string
	ItemsPerPage          int
}

// SampleSocialLink represents sample social link data
type SampleSocialLink struct {
	Platform     string
	Username     string
	URL          string
	DisplayOrder int
	IsPublic     bool
}

// getSampleUsers returns sample user data for seeding
func (s *UserSeeder) getSampleUsers(tenantID int) []SampleUser {
	birthDate1985 := time.Date(1985, 6, 15, 0, 0, 0, 0, time.UTC)
	birthDate1990 := time.Date(1990, 3, 22, 0, 0, 0, 0, time.UTC)
	birthDate1995 := time.Date(1995, 11, 8, 0, 0, 0, 0, time.UTC)

	return []SampleUser{
		{
			TenantID:    tenantID,
			Email:       fmt.Sprintf("<EMAIL>", tenantID),
			Username:    fmt.Sprintf("admin_t%d", tenantID),
			FirstName:   "Admin",
			LastName:    "User",
			DisplayName: "Admin User",
			Password:    "password123",
			Status:      "active",
			Role:        "admin",
			Phone:       "+15550101",
			AvatarURL:   "https://via.placeholder.com/150/0066CC/FFFFFF?text=AU",
			Timezone:    "America/New_York",
			Language:    "en",
			Profile: SampleUserProfile{
				Bio:          "System administrator with 10+ years of experience managing multi-tenant platforms.",
				Title:        "System Administrator",
				Company:      "TechCorp Inc.",
				Location:     "New York, NY",
				Website:      "https://techcorp.example.com",
				BirthDate:    &birthDate1985,
				Gender:       "prefer_not_to_say",
				AddressLine1: "123 Tech Street",
				City:         "New York",
				State:        "NY",
				PostalCode:   "10001",
				Country:      "United States",
				JobTitle:     "Senior System Administrator",
				Department:   "IT Operations",
				Skills:       []string{"System Administration", "Cloud Computing", "Security", "Automation"},
				Interests:    []string{"Technology", "Cybersecurity", "Open Source"},
			},
			Preferences: SampleUserPreferences{
				EmailNotifications:     true,
				PushNotifications:      true,
				MarketingEmails:        false,
				NewsletterSubscription: true,
				ProfileVisibility:      "public",
				Theme:                  "dark",
				ItemsPerPage:           25,
			},
			SocialLinks: []SampleSocialLink{
				{Platform: "linkedin", Username: "admin-user", URL: "https://linkedin.com/in/admin-user", DisplayOrder: 1, IsPublic: true},
				{Platform: "github", Username: "admin-dev", URL: "https://github.com/admin-dev", DisplayOrder: 2, IsPublic: true},
			},
		},
		{
			TenantID:    tenantID,
			Email:       fmt.Sprintf("<EMAIL>", tenantID),
			Username:    fmt.Sprintf("johndoe_t%d", tenantID),
			FirstName:   "John",
			LastName:    "Doe",
			DisplayName: "John Doe",
			Password:    "password123",
			Status:      "active",
			Role:        "user",
			Phone:       "+15550102",
			AvatarURL:   "https://via.placeholder.com/150/28A745/FFFFFF?text=JD",
			Timezone:    "America/Los_Angeles",
			Language:    "en",
			Profile: SampleUserProfile{
				Bio:          "Software developer passionate about creating innovative solutions and clean code.",
				Title:        "Software Developer",
				Company:      "InnovateLab",
				Location:     "San Francisco, CA",
				Website:      "https://johndoe.dev",
				BirthDate:    &birthDate1990,
				Gender:       "male",
				AddressLine1: "456 Innovation Drive",
				City:         "San Francisco",
				State:        "CA",
				PostalCode:   "94105",
				Country:      "United States",
				JobTitle:     "Senior Software Developer",
				Department:   "Engineering",
				Skills:       []string{"Go", "React", "Docker", "Kubernetes", "MySQL"},
				Interests:    []string{"Programming", "Music", "Photography", "Travel"},
			},
			Preferences: SampleUserPreferences{
				EmailNotifications:     true,
				PushNotifications:      false,
				MarketingEmails:        true,
				NewsletterSubscription: true,
				ProfileVisibility:      "public",
				Theme:                  "light",
				ItemsPerPage:           20,
			},
			SocialLinks: []SampleSocialLink{
				{Platform: "github", Username: "johndoe-dev", URL: "https://github.com/johndoe-dev", DisplayOrder: 1, IsPublic: true},
				{Platform: "twitter", Username: "johndoe_dev", URL: "https://twitter.com/johndoe_dev", DisplayOrder: 2, IsPublic: true},
				{Platform: "linkedin", Username: "john-doe-dev", URL: "https://linkedin.com/in/john-doe-dev", DisplayOrder: 3, IsPublic: true},
			},
		},
		{
			TenantID:    tenantID,
			Email:       fmt.Sprintf("<EMAIL>", tenantID),
			Username:    fmt.Sprintf("janesmith_t%d", tenantID),
			FirstName:   "Jane",
			LastName:    "Smith",
			DisplayName: "Jane Smith",
			Password:    "password123",
			Status:      "active",
			Role:        "user",
			Phone:       "+15550103",
			AvatarURL:   "https://via.placeholder.com/150/DC3545/FFFFFF?text=JS",
			Timezone:    "Europe/London",
			Language:    "en",
			Profile: SampleUserProfile{
				Bio:          "UX/UI designer focused on creating intuitive and accessible digital experiences.",
				Title:        "UX/UI Designer",
				Company:      "DesignStudio",
				Location:     "London, UK",
				Website:      "https://janesmith.design",
				BirthDate:    &birthDate1995,
				Gender:       "female",
				AddressLine1: "789 Design Lane",
				City:         "London",
				State:        "England",
				PostalCode:   "SW1A 1AA",
				Country:      "United Kingdom",
				JobTitle:     "Senior UX/UI Designer",
				Department:   "Design",
				Skills:       []string{"Figma", "Adobe Creative Suite", "User Research", "Prototyping", "Accessibility"},
				Interests:    []string{"Design", "Art", "Architecture", "Sustainability"},
			},
			Preferences: SampleUserPreferences{
				EmailNotifications:     true,
				PushNotifications:      true,
				MarketingEmails:        false,
				NewsletterSubscription: false,
				ProfileVisibility:      "friends_only",
				Theme:                  "system",
				ItemsPerPage:           15,
			},
			SocialLinks: []SampleSocialLink{
				{Platform: "dribbble", Username: "janesmith", URL: "https://dribbble.com/janesmith", DisplayOrder: 1, IsPublic: true},
				{Platform: "behance", Username: "jane-smith", URL: "https://behance.net/jane-smith", DisplayOrder: 2, IsPublic: true},
				{Platform: "instagram", Username: "janesmith_design", URL: "https://instagram.com/janesmith_design", DisplayOrder: 3, IsPublic: false},
			},
		},
		{
			TenantID:    tenantID,
			Email:       fmt.Sprintf("<EMAIL>", tenantID),
			Username:    fmt.Sprintf("guestuser_t%d", tenantID),
			FirstName:   "Guest",
			LastName:    "User",
			DisplayName: "Guest User",
			Password:    "password123",
			Status:      "pending_verification",
			Role:        "guest",
			Phone:       "",
			AvatarURL:   "",
			Timezone:    "UTC",
			Language:    "en",
			Profile: SampleUserProfile{
				Bio:         "New user exploring the platform.",
				Title:       "",
				Company:     "",
				Location:    "",
				Website:     "",
				BirthDate:   nil,
				Gender:      "",
				JobTitle:    "",
				Department:  "",
				Skills:      []string{},
				Interests:   []string{"Technology"},
			},
			Preferences: SampleUserPreferences{
				EmailNotifications:     true,
				PushNotifications:      false,
				MarketingEmails:        false,
				NewsletterSubscription: false,
				ProfileVisibility:      "private",
				Theme:                  "light",
				ItemsPerPage:           20,
			},
			SocialLinks: []SampleSocialLink{},
		},
		{
			TenantID:    tenantID,
			Email:       fmt.Sprintf("<EMAIL>", tenantID),
			Username:    fmt.Sprintf("suspendeduser_t%d", tenantID),
			FirstName:   "Suspended",
			LastName:    "User",
			DisplayName: "Suspended User",
			Password:    "password123",
			Status:      "suspended",
			Role:        "user",
			Phone:       "+********",
			AvatarURL:   "",
			Timezone:    "America/Chicago",
			Language:    "en",
			Profile: SampleUserProfile{
				Bio:        "User account temporarily suspended.",
				Title:      "Former User",
				Company:    "",
				Location:   "Chicago, IL",
				Website:    "",
				BirthDate:  nil,
				Gender:     "",
				JobTitle:   "",
				Department: "",
				Skills:     []string{},
				Interests:  []string{},
			},
			Preferences: SampleUserPreferences{
				EmailNotifications:     false,
				PushNotifications:      false,
				MarketingEmails:        false,
				NewsletterSubscription: false,
				ProfileVisibility:      "private",
				Theme:                  "light",
				ItemsPerPage:           20,
			},
			SocialLinks: []SampleSocialLink{},
		},
	}
}

// hashPassword creates a bcrypt hash of the password
func (s *UserSeeder) hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// createUser creates a user record in the database
func (s *UserSeeder) createUser(ctx context.Context, user SampleUser) (int, error) {
	passwordHash, err := s.hashPassword(user.Password)
	if err != nil {
		return 0, fmt.Errorf("failed to hash password: %w", err)
	}

	// Handle empty phone and avatar_url by converting to NULL
	var phone interface{} = user.Phone
	if user.Phone == "" {
		phone = nil
	}
	
	var avatarURL interface{} = user.AvatarURL
	if user.AvatarURL == "" {
		avatarURL = nil
	}

	query := `
		INSERT INTO users (
			tenant_id, email, username, first_name, last_name, display_name,
			password_hash, status, role, phone, avatar_url, timezone, language,
			email_verified, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	result, err := s.db.ExecContext(ctx, query,
		user.TenantID, user.Email, user.Username, user.FirstName, user.LastName, user.DisplayName,
		passwordHash, user.Status, user.Role, phone, avatarURL, user.Timezone, user.Language,
		user.Status == "active", // email_verified = true for active users
	)
	if err != nil {
		return 0, err
	}

	userID, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return int(userID), nil
}

// createUserProfile creates a user profile record
func (s *UserSeeder) createUserProfile(ctx context.Context, userID int, profile SampleUserProfile) error {
	skillsJSON, _ := json.Marshal(profile.Skills)
	interestsJSON, _ := json.Marshal(profile.Interests)

	// Handle empty gender by converting to NULL
	var gender interface{} = profile.Gender
	if profile.Gender == "" {
		gender = nil
	}

	// Handle empty optional fields
	var website interface{} = profile.Website
	if profile.Website == "" {
		website = nil
	}

	var title interface{} = profile.Title
	if profile.Title == "" {
		title = nil
	}

	var company interface{} = profile.Company
	if profile.Company == "" {
		company = nil
	}

	var location interface{} = profile.Location
	if profile.Location == "" {
		location = nil
	}

	var jobTitle interface{} = profile.JobTitle
	if profile.JobTitle == "" {
		jobTitle = nil
	}

	var department interface{} = profile.Department
	if profile.Department == "" {
		department = nil
	}

	var addressLine1 interface{} = profile.AddressLine1
	if profile.AddressLine1 == "" {
		addressLine1 = nil
	}

	var city interface{} = profile.City
	if profile.City == "" {
		city = nil
	}

	var state interface{} = profile.State
	if profile.State == "" {
		state = nil
	}

	var postalCode interface{} = profile.PostalCode
	if profile.PostalCode == "" {
		postalCode = nil
	}

	var country interface{} = profile.Country
	if profile.Country == "" {
		country = nil
	}

	query := `
		INSERT INTO user_profiles (
			user_id, bio, title, company, location, website, birth_date, gender,
			address_line1, city, state, postal_code, country, job_title, department,
			skills, interests, display_profile, allow_contact, profile_completed,
			completion_percentage, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err := s.db.ExecContext(ctx, query,
		userID, profile.Bio, title, company, location, website,
		profile.BirthDate, gender, addressLine1, city, state,
		postalCode, country, jobTitle, department,
		string(skillsJSON), string(interestsJSON), true, true, true, 85,
	)

	return err
}

// createUserPreferences creates user preference records
func (s *UserSeeder) createUserPreferences(ctx context.Context, userID int, prefs SampleUserPreferences) error {
	query := `
		INSERT INTO user_preferences (
			user_id, email_notifications, push_notifications, marketing_emails,
			newsletter_subscription, profile_visibility, theme, items_per_page,
			auto_save, keyboard_shortcuts, tooltips_enabled, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err := s.db.ExecContext(ctx, query,
		userID, prefs.EmailNotifications, prefs.PushNotifications, prefs.MarketingEmails,
		prefs.NewsletterSubscription, prefs.ProfileVisibility, prefs.Theme, prefs.ItemsPerPage,
		true, true, true, // auto_save, keyboard_shortcuts, tooltips_enabled defaults
	)

	return err
}

// createUserSocialLinks creates social link records
func (s *UserSeeder) createUserSocialLinks(ctx context.Context, userID int, links []SampleSocialLink) error {
	if len(links) == 0 {
		return nil // No social links to create
	}

	query := `
		INSERT INTO user_social_links (
			user_id, platform, username, url, display_order, is_public, is_verified,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	for _, link := range links {
		_, err := s.db.ExecContext(ctx, query,
			userID, link.Platform, link.Username, link.URL, link.DisplayOrder,
			link.IsPublic, false, // is_verified = false for sample data
		)
		if err != nil {
			return err
		}
	}

	return nil
}

// Rollback removes the seeded user data
func (s *UserSeeder) Rollback(db *sql.DB) error {
	ctx := context.Background()
	
	// Delete seeded users (based on known email patterns)
	emailPatterns := []string{
		"%<EMAIL>",
		"%<EMAIL>",
		"%<EMAIL>",
		"%<EMAIL>",
		"%<EMAIL>",
	}

	for _, pattern := range emailPatterns {
		query := "DELETE FROM users WHERE email LIKE ?"
		if _, err := db.ExecContext(ctx, query, pattern); err != nil {
			return err
		}
	}

	return nil
}