package tenant

import (
	"encoding/json"
	"time"
	"gorm.io/gorm"
)

// TenantPlan represents a subscription plan
type TenantPlan struct {
	ID               uint                   `gorm:"primaryKey"`
	Name             string                 `gorm:"not null"`
	Slug             string                 `gorm:"uniqueIndex;not null"`
	Description      string
	PriceMonthly     float64
	PriceYearly      float64
	Currency         string                 `gorm:"default:'USD'"`
	MaxUsers         int
	MaxWebsites      int
	MaxStorageGB     int
	MaxBandwidthGB   int
	MaxPostsPerMonth *int
	MaxAPICallsPerDay *int
	Features         string                 `gorm:"type:json"`
	DisplayOrder     int
	IsFeatured       bool
	IsVisible        bool                   `gorm:"default:true"`
	Status           string                 `gorm:"default:'active'"`
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

// TableName sets the table name for TenantPlan
func (TenantPlan) TableName() string {
	return "tenant_plans"
}

// TenantPlanSeeder handles seeding of tenant plans
type TenantPlanSeeder struct{}

// Seed creates default tenant plans
func (s *TenantPlanSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert map to JSON string
	toJSON := func(m map[string]interface{}) string {
		b, _ := json.Marshal(m)
		return string(b)
	}

	plans := []TenantPlan{
		{
			Name:           "Free",
			Slug:           "free",
			Description:    "Perfect for personal blogs and small websites",
			PriceMonthly:   0,
			PriceYearly:    0,
			Currency:       "USD",
			MaxUsers:       1,
			MaxWebsites:    1,
			MaxStorageGB:   1,
			MaxBandwidthGB: 10,
			Features: toJSON(map[string]interface{}{
				"custom_domain":     false,
				"api_access":        false,
				"priority_support":  false,
				"analytics":         "basic",
				"ssl":               true,
			}),
			DisplayOrder: 1,
			IsFeatured:   false,
			IsVisible:    true,
			Status:       "active",
		},
		{
			Name:           "Starter",
			Slug:           "starter",
			Description:    "Great for growing blogs and small businesses",
			PriceMonthly:   9.99,
			PriceYearly:    99.99,
			Currency:       "USD",
			MaxUsers:       5,
			MaxWebsites:    3,
			MaxStorageGB:   10,
			MaxBandwidthGB: 100,
			Features: toJSON(map[string]interface{}{
				"custom_domain":     true,
				"api_access":        false,
				"priority_support":  false,
				"analytics":         "standard",
				"ssl":               true,
			}),
			DisplayOrder: 2,
			IsFeatured:   true,
			IsVisible:    true,
			Status:       "active",
		},
		{
			Name:           "Professional",
			Slug:           "professional",
			Description:    "For professional bloggers and content creators",
			PriceMonthly:   29.99,
			PriceYearly:    299.99,
			Currency:       "USD",
			MaxUsers:       20,
			MaxWebsites:    10,
			MaxStorageGB:   50,
			MaxBandwidthGB: 500,
			Features: toJSON(map[string]interface{}{
				"custom_domain":     true,
				"api_access":        true,
				"priority_support":  true,
				"analytics":         "advanced",
				"ssl":               true,
			}),
			DisplayOrder: 3,
			IsFeatured:   false,
			IsVisible:    true,
			Status:       "active",
		},
		{
			Name:           "Enterprise",
			Slug:           "enterprise",
			Description:    "Custom solutions for large organizations",
			PriceMonthly:   0,
			PriceYearly:    0,
			Currency:       "USD",
			MaxUsers:       -1, // Unlimited
			MaxWebsites:    -1, // Unlimited
			MaxStorageGB:   -1, // Unlimited
			MaxBandwidthGB: -1, // Unlimited
			Features: toJSON(map[string]interface{}{
				"custom_domain":      true,
				"api_access":         true,
				"priority_support":   true,
				"analytics":          "enterprise",
				"ssl":                true,
				"sla":                true,
				"dedicated_support":  true,
			}),
			DisplayOrder: 4,
			IsFeatured:   false,
			IsVisible:    true,
			Status:       "active",
		},
	}

	for _, plan := range plans {
		var existing TenantPlan
		if err := db.Where("slug = ?", plan.Slug).First(&existing).Error; err == nil {
			// Update existing plan
			db.Model(&existing).Updates(plan)
		} else {
			// Create new plan
			if err := db.Create(&plan).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// Rollback removes seeded tenant plans
func (s *TenantPlanSeeder) Rollback(db *gorm.DB) error {
	slugs := []string{"free", "starter", "professional", "enterprise"}
	return db.Where("slug IN ?", slugs).Delete(&TenantPlan{}).Error
}

// Name returns the name of this seeder
func (s *TenantPlanSeeder) Name() string {
	return "tenant_plans"
}

// Dependencies returns the dependencies for this seeder
func (s *TenantPlanSeeder) Dependencies() []string {
	return []string{}
}