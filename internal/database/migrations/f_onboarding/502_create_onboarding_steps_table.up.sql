-- Migration: 025_create_onboarding_steps_table
-- Description: Create the onboarding steps table for step configurations (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS onboarding_steps (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    journey_id INT UNSIGNED NOT NULL,
    
    -- Step Information
    name VARCHAR(255) NOT NULL COMMENT 'Step name',
    slug VARCHAR(255) NOT NULL COMMENT 'URL-friendly step identifier',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable step name',
    description TEXT COMMENT 'Step description and instructions',
    
    -- Step Ordering and Hierarchy
    step_order INT UNSIGNED NOT NULL COMMENT 'Order of step within journey',
    parent_step_id INT UNSIGNED NULL COMMENT 'Parent step ID for nested steps',
    level INT UNSIGNED DEFAULT 0 COMMENT 'Step hierarchy level',
    
    -- Step Type and Configuration
    step_type VARCHAR(50) NOT NULL DEFAULT 'action' COMMENT 'Step type: action, form, tutorial, verification, conditional',
    component_type VARCHAR(100) COMMENT 'UI component type for rendering',
    
    -- Step Content
    title VARCHAR(255) COMMENT 'Step title displayed to user',
    content JSON DEFAULT (JSON_OBJECT()) COMMENT 'Step content and configuration in JSON format',
    ui_config JSON DEFAULT (JSON_OBJECT()) COMMENT 'UI-specific configuration',
    
    -- Step Behavior
    is_required BOOLEAN DEFAULT TRUE COMMENT 'Whether step is required',
    is_skippable BOOLEAN DEFAULT FALSE COMMENT 'Whether step can be skipped',
    is_blocking BOOLEAN DEFAULT TRUE COMMENT 'Whether step blocks progression',
    auto_advance BOOLEAN DEFAULT FALSE COMMENT 'Whether step auto-advances on completion',
    
    -- Step Conditions
    entry_conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON conditions for step entry',
    completion_conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON conditions for step completion',
    validation_rules JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON validation rules for step data',
    
    -- Step Actions
    on_entry_actions JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON actions to execute on step entry',
    on_complete_actions JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON actions to execute on step completion',
    on_skip_actions JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON actions to execute on step skip',
    
    -- Step Timing
    estimated_duration INT UNSIGNED COMMENT 'Estimated completion time in seconds',
    timeout_duration INT UNSIGNED COMMENT 'Step timeout in seconds',
    
    -- Step Analytics
    total_entries INT UNSIGNED DEFAULT 0 COMMENT 'Total entries to this step',
    total_completions INT UNSIGNED DEFAULT 0 COMMENT 'Total completions of this step',
    total_skips INT UNSIGNED DEFAULT 0 COMMENT 'Total skips of this step',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Step completion rate percentage',
    average_duration INT UNSIGNED DEFAULT 0 COMMENT 'Average completion time in seconds',
    
    -- Step Metadata
    help_text TEXT COMMENT 'Help text for the step',
    error_messages JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object of error messages',
    success_message VARCHAR(500) COMMENT 'Success message on completion',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Step status: active, inactive, draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_steps_journey_id FOREIGN KEY (journey_id) REFERENCES onboarding_journeys(id) ON DELETE CASCADE,
    CONSTRAINT fk_onboarding_steps_parent_step FOREIGN KEY (parent_step_id) REFERENCES onboarding_steps(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_onboarding_steps_status CHECK (status IN ('active', 'inactive', 'draft')),
    CONSTRAINT chk_onboarding_steps_step_type CHECK (step_type IN ('action', 'form', 'tutorial', 'verification', 'conditional', 'decision')),
    CONSTRAINT chk_onboarding_steps_completion_rate CHECK (completion_rate >= 0 AND completion_rate <= 100),
    CONSTRAINT chk_onboarding_steps_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$'),
    CONSTRAINT chk_onboarding_steps_step_order CHECK (step_order > 0),
    CONSTRAINT chk_onboarding_steps_level CHECK (level >= 0),
    
    -- Unique Constraints
    UNIQUE KEY uk_onboarding_steps_journey_slug (journey_id, slug),
    UNIQUE KEY uk_onboarding_steps_journey_order (journey_id, step_order),
    
    -- Indexes
    INDEX idx_onboarding_steps_journey_id (journey_id, status),
    INDEX idx_onboarding_steps_parent_step (parent_step_id, level),
    INDEX idx_onboarding_steps_step_order (journey_id, step_order),
    INDEX idx_onboarding_steps_step_type (step_type, status),
    INDEX idx_onboarding_steps_required (is_required, status),
    INDEX idx_onboarding_steps_skippable (is_skippable, status),
    INDEX idx_onboarding_steps_blocking (is_blocking, status),
    INDEX idx_onboarding_steps_completion_rate (completion_rate),
    INDEX idx_onboarding_steps_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_onboarding_steps_journey_order_status (journey_id, step_order, status),
    INDEX idx_onboarding_steps_hierarchy (journey_id, parent_step_id, level, step_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding steps table for defining individual steps within onboarding journeys.';