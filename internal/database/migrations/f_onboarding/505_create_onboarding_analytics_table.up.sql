-- Migration: 028_create_onboarding_analytics_table
-- Description: Create the onboarding analytics table for metrics and insights (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS onboarding_analytics (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Analytics Context
    journey_id INT UNSIGNED NULL COMMENT 'Journey being tracked (NULL for system-wide analytics)',
    step_id INT UNSIGNED NULL COMMENT 'Step being tracked (NULL for journey-level analytics)',
    user_id INT UNSIGNED NULL COMMENT 'User being tracked (NULL for aggregate analytics)',
    
    -- Event Information
    event_type VARCHAR(100) NOT NULL COMMENT 'Type of event: journey_start, step_complete, journey_abandon, etc.',
    event_category VARCHAR(50) NOT NULL DEFAULT 'journey' COMMENT 'Event category: journey, step, user, system',
    event_action VARCHAR(100) NOT NULL COMMENT 'Specific action taken',
    event_label VARCHAR(255) COMMENT 'Optional event label for additional context',
    
    -- Analytics Data
    metric_name VARCHAR(100) NOT NULL COMMENT 'Name of the metric being tracked',
    metric_value DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'Numeric value of the metric',
    metric_unit VARCHAR(50) COMMENT 'Unit of measurement (seconds, percentage, count, etc.)',
    
    -- Dimensional Data
    dimensions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object containing dimensional data for analysis',
    properties JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object containing event properties',
    
    -- Time-based Analysis
    time_period VARCHAR(20) DEFAULT 'instant' COMMENT 'Time period: instant, daily, weekly, monthly',
    period_start TIMESTAMP NULL COMMENT 'Start of the time period',
    period_end TIMESTAMP NULL COMMENT 'End of the time period',
    
    -- Segmentation
    segment_type VARCHAR(50) COMMENT 'Segment type: demographic, behavioral, geographic, etc.',
    segment_value VARCHAR(255) COMMENT 'Segment value for grouping',
    
    -- Context Information
    device_type VARCHAR(50) COMMENT 'Device type: desktop, mobile, tablet',
    browser VARCHAR(100) COMMENT 'Browser information',
    os VARCHAR(100) COMMENT 'Operating system',
    referrer VARCHAR(500) COMMENT 'Referrer URL',
    utm_source VARCHAR(255) COMMENT 'UTM source parameter',
    utm_medium VARCHAR(255) COMMENT 'UTM medium parameter',
    utm_campaign VARCHAR(255) COMMENT 'UTM campaign parameter',
    
    -- Geographic Data
    country VARCHAR(100) COMMENT 'Country code',
    region VARCHAR(100) COMMENT 'Region or state',
    city VARCHAR(100) COMMENT 'City name',
    timezone VARCHAR(100) COMMENT 'User timezone',
    
    -- Performance Metrics
    page_load_time INT UNSIGNED COMMENT 'Page load time in milliseconds',
    interaction_time INT UNSIGNED COMMENT 'Time to interaction in milliseconds',
    completion_time INT UNSIGNED COMMENT 'Time to completion in milliseconds',
    
    -- Aggregation Fields
    count_value INT UNSIGNED DEFAULT 1 COMMENT 'Count for aggregation purposes',
    sum_value DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'Sum for aggregation purposes',
    avg_value DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'Average for aggregation purposes',
    min_value DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'Minimum for aggregation purposes',
    max_value DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'Maximum for aggregation purposes',
    
    -- Data Quality
    data_quality_score DECIMAL(3,2) DEFAULT 1.00 COMMENT 'Data quality score (0-1)',
    is_processed BOOLEAN DEFAULT FALSE COMMENT 'Whether this record has been processed',
    is_aggregated BOOLEAN DEFAULT FALSE COMMENT 'Whether this is an aggregated record',
    
    -- Timestamps
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the event occurred',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL COMMENT 'When the record was processed',
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_analytics_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_onboarding_analytics_journey_id FOREIGN KEY (journey_id) REFERENCES onboarding_journeys(id) ON DELETE SET NULL,
    CONSTRAINT fk_onboarding_analytics_step_id FOREIGN KEY (step_id) REFERENCES onboarding_steps(id) ON DELETE SET NULL,
    CONSTRAINT fk_onboarding_analytics_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_onboarding_analytics_event_category CHECK (event_category IN ('journey', 'step', 'user', 'system', 'custom')),
    CONSTRAINT chk_onboarding_analytics_time_period CHECK (time_period IN ('instant', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    CONSTRAINT chk_onboarding_analytics_data_quality CHECK (data_quality_score >= 0 AND data_quality_score <= 1),
    CONSTRAINT chk_onboarding_analytics_count_value CHECK (count_value >= 0),
    
    -- Indexes
    INDEX idx_onboarding_analytics_tenant_id (tenant_id, event_timestamp),
    INDEX idx_onboarding_analytics_journey_id (journey_id, event_timestamp),
    INDEX idx_onboarding_analytics_step_id (step_id, event_timestamp),
    INDEX idx_onboarding_analytics_user_id (user_id, event_timestamp),
    INDEX idx_onboarding_analytics_event_type (event_type, event_timestamp),
    INDEX idx_onboarding_analytics_event_category (event_category, event_timestamp),
    INDEX idx_onboarding_analytics_metric_name (metric_name, event_timestamp),
    INDEX idx_onboarding_analytics_time_period (time_period, period_start, period_end),
    INDEX idx_onboarding_analytics_segment (segment_type, segment_value),
    INDEX idx_onboarding_analytics_device_type (device_type, event_timestamp),
    INDEX idx_onboarding_analytics_country (country, event_timestamp),
    INDEX idx_onboarding_analytics_processed (is_processed, event_timestamp),
    INDEX idx_onboarding_analytics_aggregated (is_aggregated, event_timestamp),
    INDEX idx_onboarding_analytics_event_timestamp (event_timestamp),
    INDEX idx_onboarding_analytics_created_at (created_at),
    
    -- Composite indexes for analytics queries
    INDEX idx_onboarding_analytics_tenant_journey_event (tenant_id, journey_id, event_type, event_timestamp),
    INDEX idx_onboarding_analytics_journey_step_metric (journey_id, step_id, metric_name, event_timestamp),
    INDEX idx_onboarding_analytics_user_journey_progress (user_id, journey_id, event_category, event_timestamp),
    INDEX idx_onboarding_analytics_time_series (time_period, period_start, metric_name, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding analytics table for tracking metrics, events, and insights across onboarding journeys.';