-- Migration: 026_create_onboarding_progress_table
-- Description: Create the onboarding progress table for user progress tracking (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS onboarding_progress (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    journey_id INT UNSIGNED NOT NULL,
    step_id INT UNSIGNED NULL COMMENT 'Current step (NULL if journey not started or completed)',
    
    -- Progress Status
    progress_status VARCHAR(50) NOT NULL DEFAULT 'not_started' COMMENT 'Progress status: not_started, in_progress, completed, abandoned, paused',
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Journey completion percentage',
    
    -- Progress Tracking
    steps_completed INT UNSIGNED DEFAULT 0 COMMENT 'Number of steps completed',
    steps_skipped INT UNSIGNED DEFAULT 0 COMMENT 'Number of steps skipped',
    total_steps INT UNSIGNED DEFAULT 0 COMMENT 'Total number of steps in journey',
    
    -- Progress Timeline
    started_at TIMESTAMP NULL COMMENT 'When user started the journey',
    completed_at TIMESTAMP NULL COMMENT 'When user completed the journey',
    last_activity_at TIMESTAMP NULL COMMENT 'Last activity timestamp',
    paused_at TIMESTAMP NULL COMMENT 'When journey was paused',
    abandoned_at TIMESTAMP NULL COMMENT 'When journey was abandoned',
    
    -- Progress Data
    progress_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object storing progress-specific data',
    step_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object storing data collected from steps',
    user_responses JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object storing user responses and inputs',
    
    -- Progress Metrics
    time_spent INT UNSIGNED DEFAULT 0 COMMENT 'Total time spent in seconds',
    attempts INT UNSIGNED DEFAULT 1 COMMENT 'Number of attempts (for retries)',
    errors_encountered INT UNSIGNED DEFAULT 0 COMMENT 'Number of errors encountered',
    
    -- Progress Context
    context_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON context data at progress start',
    device_info JSON DEFAULT (JSON_OBJECT()) COMMENT 'Device and browser information',
    referrer_info JSON DEFAULT (JSON_OBJECT()) COMMENT 'Referrer and source information',
    
    -- Progress Flags
    is_first_time BOOLEAN DEFAULT TRUE COMMENT 'Whether this is first time doing this journey',
    is_guided BOOLEAN DEFAULT FALSE COMMENT 'Whether user received guided assistance',
    has_feedback BOOLEAN DEFAULT FALSE COMMENT 'Whether user provided feedback',
    
    -- Progress Notes
    user_notes TEXT COMMENT 'User-provided notes or comments',
    admin_notes TEXT COMMENT 'Admin notes about this progress',
    abandonment_reason VARCHAR(255) COMMENT 'Reason for abandonment if applicable',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_progress_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_onboarding_progress_journey_id FOREIGN KEY (journey_id) REFERENCES onboarding_journeys(id) ON DELETE CASCADE,
    CONSTRAINT fk_onboarding_progress_step_id FOREIGN KEY (step_id) REFERENCES onboarding_steps(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_onboarding_progress_status CHECK (progress_status IN ('not_started', 'in_progress', 'completed', 'abandoned', 'paused')),
    CONSTRAINT chk_onboarding_progress_completion_percentage CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CONSTRAINT chk_onboarding_progress_steps_completed CHECK (steps_completed >= 0),
    CONSTRAINT chk_onboarding_progress_steps_skipped CHECK (steps_skipped >= 0),
    CONSTRAINT chk_onboarding_progress_total_steps CHECK (total_steps >= 0),
    CONSTRAINT chk_onboarding_progress_time_spent CHECK (time_spent >= 0),
    CONSTRAINT chk_onboarding_progress_attempts CHECK (attempts >= 1),
    
    -- Unique Constraints (one progress record per user per journey)
    UNIQUE KEY uk_onboarding_progress_user_journey (user_id, journey_id),
    
    -- Indexes
    INDEX idx_onboarding_progress_user_id (user_id, progress_status),
    INDEX idx_onboarding_progress_journey_id (journey_id, progress_status),
    INDEX idx_onboarding_progress_step_id (step_id),
    INDEX idx_onboarding_progress_status (progress_status, updated_at),
    INDEX idx_onboarding_progress_completion (completion_percentage),
    INDEX idx_onboarding_progress_started_at (started_at),
    INDEX idx_onboarding_progress_completed_at (completed_at),
    INDEX idx_onboarding_progress_last_activity (last_activity_at),
    INDEX idx_onboarding_progress_first_time (is_first_time),
    INDEX idx_onboarding_progress_guided (is_guided),
    INDEX idx_onboarding_progress_created_at (created_at),
    
    -- Composite indexes for analytics and reporting
    INDEX idx_onboarding_progress_journey_status_completion (journey_id, progress_status, completion_percentage),
    INDEX idx_onboarding_progress_user_status_activity (user_id, progress_status, last_activity_at),
    INDEX idx_onboarding_progress_completion_timeline (completed_at, time_spent, attempts)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding progress table for tracking user progress through onboarding journeys.';