-- Migration: 027_create_onboarding_templates_table
-- Description: Create the onboarding templates table for reusable templates (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS onboarding_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Template Information
    name VARCHAR(255) NOT NULL COMMENT 'Template name',
    slug VARCHAR(255) NOT NULL COMMENT 'URL-friendly template identifier',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable template name',
    description TEXT COMMENT 'Template description and purpose',
    
    -- Template Type and Category
    template_type VARCHAR(50) NOT NULL DEFAULT 'journey' COMMENT 'Template type: journey, step, component, widget',
    category VARCHAR(50) DEFAULT 'general' COMMENT 'Template category for organization',
    use_case VARCHAR(100) COMMENT 'Primary use case for this template',
    
    -- Template Version and Compatibility
    version VARCHAR(20) DEFAULT '1.0' COMMENT 'Template version',
    compatibility_version VARCHAR(20) COMMENT 'Minimum system version required',
    
    -- Template Content
    template_data JSON NOT NULL COMMENT 'JSON template data and structure',
    default_config JSON DEFAULT (JSON_OBJECT()) COMMENT 'Default configuration for template',
    variable_definitions JSON DEFAULT (JSON_OBJECT()) COMMENT 'Template variables and their definitions',
    
    -- Template Metadata
    author VARCHAR(255) COMMENT 'Template author or creator',
    tags JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of template tags',
    keywords JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of searchable keywords',
    
    -- Template Usage
    usage_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of times template has been used',
    last_used_at TIMESTAMP NULL COMMENT 'When template was last used',
    
    -- Template Ratings and Feedback
    rating_average DECIMAL(3,2) DEFAULT 0.00 COMMENT 'Average template rating (0-5)',
    rating_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of ratings received',
    feedback_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of feedback items',
    
    -- Template Permissions
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether template is publicly available',
    is_featured BOOLEAN DEFAULT FALSE COMMENT 'Whether template is featured',
    is_system_template BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-provided template',
    
    -- Template Validation
    validation_rules JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON validation rules for template data',
    required_fields JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of required fields',
    
    -- Template Customization
    customization_options JSON DEFAULT (JSON_OBJECT()) COMMENT 'Available customization options',
    style_options JSON DEFAULT (JSON_OBJECT()) COMMENT 'Available styling options',
    
    -- Template Preview
    preview_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'Preview data for template demonstration',
    screenshot_url VARCHAR(500) COMMENT 'URL to template screenshot',
    demo_url VARCHAR(500) COMMENT 'URL to live demo',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Template status: active, inactive, deprecated, draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL COMMENT 'When template was published',
    deprecated_at TIMESTAMP NULL COMMENT 'When template was deprecated',
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_templates_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_onboarding_templates_status CHECK (status IN ('active', 'inactive', 'deprecated', 'draft')),
    CONSTRAINT chk_onboarding_templates_template_type CHECK (template_type IN ('journey', 'step', 'component', 'widget', 'layout')),
    CONSTRAINT chk_onboarding_templates_category CHECK (category IN ('general', 'ecommerce', 'saas', 'educational', 'marketing', 'support')),
    CONSTRAINT chk_onboarding_templates_rating_average CHECK (rating_average >= 0 AND rating_average <= 5),
    CONSTRAINT chk_onboarding_templates_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$'),
    
    -- Unique Constraints
    UNIQUE KEY uk_onboarding_templates_tenant_slug (tenant_id, slug),
    UNIQUE KEY uk_onboarding_templates_tenant_name (tenant_id, name),
    
    -- Indexes
    INDEX idx_onboarding_templates_tenant_id (tenant_id, status),
    INDEX idx_onboarding_templates_slug (slug),
    INDEX idx_onboarding_templates_template_type (template_type, status),
    INDEX idx_onboarding_templates_category (category, status),
    INDEX idx_onboarding_templates_public (is_public, status),
    INDEX idx_onboarding_templates_featured (is_featured, status),
    INDEX idx_onboarding_templates_system (is_system_template, status),
    INDEX idx_onboarding_templates_usage_count (usage_count),
    INDEX idx_onboarding_templates_rating (rating_average, rating_count),
    INDEX idx_onboarding_templates_last_used (last_used_at),
    INDEX idx_onboarding_templates_published (published_at, status),
    INDEX idx_onboarding_templates_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_onboarding_templates_tenant_type_status (tenant_id, template_type, status),
    INDEX idx_onboarding_templates_public_featured (is_public, is_featured, status),
    INDEX idx_onboarding_templates_category_rating (category, rating_average, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding templates table for storing reusable onboarding journey and step templates.';