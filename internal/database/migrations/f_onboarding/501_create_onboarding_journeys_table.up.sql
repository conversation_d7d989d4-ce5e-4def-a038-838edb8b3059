-- Migration: 024_create_onboarding_journeys_table
-- Description: Create the onboarding journeys table for journey definitions (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS onboarding_journeys (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Journey Information
    name VARCHAR(255) NOT NULL COMMENT 'Journey name',
    slug VARCHAR(255) NOT NULL COMMENT 'URL-friendly journey identifier',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable journey name',
    description TEXT COMMENT 'Journey description and purpose',
    
    -- Journey Type and Context
    journey_type VARCHAR(50) NOT NULL DEFAULT 'user' COMMENT 'Journey type: user, admin, setup, feature',
    trigger_type VARCHAR(50) NOT NULL DEFAULT 'manual' COMMENT 'Trigger type: manual, automatic, conditional',
    context_type VARCHAR(50) COMMENT 'Context type: tenant, website, user, global',
    context_id INT UNSIGNED COMMENT 'Context ID for scoped journeys',
    
    -- Journey Configuration
    version VARCHAR(20) DEFAULT '1.0' COMMENT 'Journey version',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether journey is active',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'Whether journey is required for users',
    is_skippable BOOLEAN DEFAULT TRUE COMMENT 'Whether journey can be skipped',
    
    -- Journey Flow Control
    start_condition JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON conditions for journey start',
    completion_criteria JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON criteria for journey completion',
    branching_logic JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON logic for journey branching',
    
    -- Journey Timing
    estimated_duration INT UNSIGNED COMMENT 'Estimated completion time in minutes',
    timeout_duration INT UNSIGNED COMMENT 'Journey timeout in minutes',
    
    -- Journey Analytics
    total_users INT UNSIGNED DEFAULT 0 COMMENT 'Total users who started this journey',
    completed_users INT UNSIGNED DEFAULT 0 COMMENT 'Total users who completed this journey',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Journey completion rate percentage',
    
    -- Journey Metadata
    tags JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of journey tags',
    metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for additional journey metadata',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Journey status: active, inactive, draft, archived',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL COMMENT 'When journey was published',
    archived_at TIMESTAMP NULL COMMENT 'When journey was archived',
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_journeys_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_onboarding_journeys_status CHECK (status IN ('active', 'inactive', 'draft', 'archived')),
    CONSTRAINT chk_onboarding_journeys_journey_type CHECK (journey_type IN ('user', 'admin', 'setup', 'feature', 'integration')),
    CONSTRAINT chk_onboarding_journeys_trigger_type CHECK (trigger_type IN ('manual', 'automatic', 'conditional', 'scheduled')),
    CONSTRAINT chk_onboarding_journeys_context_type CHECK (context_type IS NULL OR context_type IN ('tenant', 'website', 'user', 'global')),
    CONSTRAINT chk_onboarding_journeys_completion_rate CHECK (completion_rate >= 0 AND completion_rate <= 100),
    CONSTRAINT chk_onboarding_journeys_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$'),
    
    -- Unique Constraints
    UNIQUE KEY uk_onboarding_journeys_tenant_slug (tenant_id, slug),
    UNIQUE KEY uk_onboarding_journeys_tenant_name (tenant_id, name),
    
    -- Indexes
    INDEX idx_onboarding_journeys_tenant_id (tenant_id, status),
    INDEX idx_onboarding_journeys_slug (slug),
    INDEX idx_onboarding_journeys_journey_type (journey_type, status),
    INDEX idx_onboarding_journeys_trigger_type (trigger_type, status),
    INDEX idx_onboarding_journeys_context (context_type, context_id),
    INDEX idx_onboarding_journeys_active (is_active, status),
    INDEX idx_onboarding_journeys_required (is_required, status),
    INDEX idx_onboarding_journeys_completion_rate (completion_rate),
    INDEX idx_onboarding_journeys_published (published_at, status),
    INDEX idx_onboarding_journeys_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_onboarding_journeys_tenant_type_status (tenant_id, journey_type, status),
    INDEX idx_onboarding_journeys_context_active (context_type, context_id, is_active, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding journeys table for defining user onboarding flows and processes.';