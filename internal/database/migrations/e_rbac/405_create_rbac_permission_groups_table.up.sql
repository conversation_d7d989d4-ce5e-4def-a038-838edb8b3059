-- Migration: 023_create_rbac_permission_groups_table
-- Description: Create the RBAC permission groups table for categorization (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS rbac_permission_groups (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Group Information
    name VARCHAR(100) NOT NULL COMMENT 'Group name (e.g., user-management, content-management)',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable group name',
    description TEXT COMMENT 'Group description and purpose',
    
    -- Group Hierarchy
    parent_group_id INT UNSIGNED NULL COMMENT 'Parent group ID for hierarchical structure',
    level INT UNSIGNED DEFAULT 0 COMMENT 'Group hierarchy level',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT 'Sort order within the same level',
    
    -- Group Categorization
    module VARCHAR(50) NOT NULL COMMENT 'Module this group belongs to (user, website, auth, etc.)',
    category VARCHAR(50) DEFAULT 'general' COMMENT 'Group category (core, feature, administrative, etc.)',
    
    -- Group Metadata
    color VARCHAR(7) COMMENT 'Group color for UI display (hex format)',
    icon VARCHAR(100) COMMENT 'Group icon identifier',
    
    -- Group Configuration
    is_system_group BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-defined group',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'Whether this group is required for the system',
    
    -- Group Capabilities
    default_permissions JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of default permissions for this group',
    group_metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for additional group metadata',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Group status: active, inactive, deprecated',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_rbac_permission_groups_parent FOREIGN KEY (parent_group_id) REFERENCES rbac_permission_groups(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_rbac_permission_groups_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_rbac_permission_groups_category CHECK (category IN ('core', 'feature', 'administrative', 'integration', 'general')),
    CONSTRAINT chk_rbac_permission_groups_name_format CHECK (name REGEXP '^[a-z0-9_-]+$'),
    CONSTRAINT chk_rbac_permission_groups_color_format CHECK (color IS NULL OR color REGEXP '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT chk_rbac_permission_groups_level CHECK (level >= 0 AND level <= 10),
    CONSTRAINT chk_rbac_permission_groups_sort_order CHECK (sort_order >= 0),
    
    -- Unique Constraints (group names unique per module)
    UNIQUE KEY uk_rbac_permission_groups_module_name (module, name),
    
    -- Indexes
    INDEX idx_rbac_permission_groups_module (module, status),
    INDEX idx_rbac_permission_groups_category (category, status),
    INDEX idx_rbac_permission_groups_parent (parent_group_id, level),
    INDEX idx_rbac_permission_groups_system_group (is_system_group),
    INDEX idx_rbac_permission_groups_required (is_required),
    INDEX idx_rbac_permission_groups_sort_order (level, sort_order),
    INDEX idx_rbac_permission_groups_name (name),
    INDEX idx_rbac_permission_groups_created_at (created_at),
    
    -- Composite indexes for hierarchy queries
    INDEX idx_rbac_permission_groups_hierarchy (parent_group_id, level, sort_order),
    INDEX idx_rbac_permission_groups_module_hierarchy (module, parent_group_id, level, sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC permission groups table for categorizing and organizing permissions.';