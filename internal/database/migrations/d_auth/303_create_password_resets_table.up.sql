-- Migration: 003_create_password_resets_table
-- Description: Create password resets table with expiry tracking (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_password_resets (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Reset Token Information
    token VARCHAR(255) NOT NULL UNIQUE COMMENT 'Password reset token',
    email VARCHAR(255) NOT NULL COMMENT 'Email address for reset',
    
    -- Usage Tracking
    used_at TIMESTAMP NULL COMMENT 'When the reset token was used',
    used_by_ip VARCHAR(45) COMMENT 'IP address that used the token',
    
    -- Security
    attempts INT UNSIGNED DEFAULT 0 COMMENT 'Number of failed attempts to use this token',
    max_attempts INT UNSIGNED DEFAULT 3 COMMENT 'Maximum allowed attempts',
    
    -- Expiration
    expires_at TIMESTAMP NOT NULL COMMENT 'When the reset token expires',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_password_resets_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_password_resets_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_auth_password_resets_attempts CHECK (attempts <= max_attempts),
    
    -- Indexes
    INDEX idx_auth_password_resets_website_id (website_id),
    INDEX idx_auth_password_resets_user_id (user_id),
    INDEX idx_auth_password_resets_token (token),
    INDEX idx_auth_password_resets_email (email),
    INDEX idx_auth_password_resets_expires_at (expires_at),
    INDEX idx_auth_password_resets_used_at (used_at),
    INDEX idx_auth_password_resets_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_auth_password_resets_user_active (user_id, used_at, expires_at),
    INDEX idx_auth_password_resets_cleanup (used_at, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens with expiry tracking and security features';

