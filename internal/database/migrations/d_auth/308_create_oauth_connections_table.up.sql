-- Migration: 308_create_oauth_connections_table
-- Description: Create OAuth connections table for user third-party authentication (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_oauth_connections (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    provider_id INT UNSIGNED NOT NULL,
    
    -- OAuth Connection Information
    provider_user_id VARCHAR(255) NOT NULL COMMENT 'User ID from OAuth provider',
    provider_username VARCHAR(255) COMMENT 'Username from OAuth provider',
    provider_email VARCHAR(255) COMMENT 'Email from OAuth provider',
    
    -- Tokens
    access_token VARCHAR(1000) COMMENT 'OAuth access token (encrypted)',
    refresh_token VARCHAR(1000) COMMENT 'OAuth refresh token (encrypted)',
    token_expires_at TIMESTAMP NULL COMMENT 'When access token expires',
    
    -- Profile Data
    profile_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'Additional profile data from provider',
    
    -- Connection Status
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether connection is active',
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Last time connection was used',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_oauth_connections_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_oauth_connections_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_oauth_connections_provider_id FOREIGN KEY (provider_id) REFERENCES auth_oauth_providers(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_auth_oauth_connections_provider_user (provider_id, provider_user_id),
    UNIQUE KEY uk_auth_oauth_connections_user_provider (user_id, provider_id),
    
    -- Indexes for Performance
    INDEX idx_auth_oauth_connections_website_id (website_id),
    INDEX idx_auth_oauth_connections_user_id (user_id),
    INDEX idx_auth_oauth_connections_provider_id (provider_id),
    INDEX idx_auth_oauth_connections_provider_user_id (provider_user_id),
    INDEX idx_auth_oauth_connections_is_active (is_active),
    INDEX idx_auth_oauth_connections_last_used_at (last_used_at),
    
    -- Composite indexes for common queries
    INDEX idx_auth_oauth_connections_user_active (user_id, is_active),
    INDEX idx_auth_oauth_connections_provider_active (provider_id, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User OAuth connections for third-party authentication';