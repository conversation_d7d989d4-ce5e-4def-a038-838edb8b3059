-- Migration: 307_create_password_history_table
-- Description: Create password history table for preventing password reuse (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_password_history (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Password Hash (for comparison)
    password_hash VARCHAR(255) NOT NULL COMMENT 'Previous password hash',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_password_history_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_password_history_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_auth_password_history_website_id (website_id),
    INDEX idx_auth_password_history_user_id (user_id),
    INDEX idx_auth_password_history_created_at (created_at),
    
    -- Composite index for user password history
    INDEX idx_auth_password_history_user_date (user_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password history for preventing password reuse';