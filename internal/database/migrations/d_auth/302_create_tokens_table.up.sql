-- Migration: 002_create_tokens_table
-- Description: Create tokens table for API tokens and token blacklist (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_tokens (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Token Information
    name VARCHAR(255) NOT NULL COMMENT 'Token name/description',
    token VARCHAR(255) NOT NULL UNIQUE COMMENT 'API token value',
    token_type VARCHAR(50) NOT NULL DEFAULT 'api' COMMENT 'Token type: api, personal, app',
    
    -- Permissions and Scopes
    scopes JSON DEFAULT (JSON_ARRAY()) COMMENT 'Token scopes and permissions',
    
    -- Usage Tracking
    last_used_at TIMESTAMP NULL COMMENT 'Last time token was used',
    usage_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of times token has been used',
    
    -- Security
    is_revoked BOOLEAN DEFAULT FALSE COMMENT 'Whether token is revoked',
    revoked_at TIMESTAMP NULL COMMENT 'When token was revoked',
    revoked_by INT UNSIGNED NULL COMMENT 'User who revoked the token',
    
    -- Expiration
    expires_at TIMESTAMP NULL COMMENT 'Token expiration time (NULL = never expires)',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_tokens_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_tokens_revoked_by FOREIGN KEY (revoked_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT chk_auth_tokens_token_type CHECK (token_type IN ('api', 'personal', 'app')),
    
    -- Indexes
    INDEX idx_auth_tokens_website_id (website_id),
    INDEX idx_auth_tokens_user_id (user_id),
    INDEX idx_auth_tokens_token (token),
    INDEX idx_auth_tokens_is_revoked (is_revoked),
    INDEX idx_auth_tokens_expires_at (expires_at),
    INDEX idx_auth_tokens_last_used_at (last_used_at),
    
    -- Composite indexes for common queries
    INDEX idx_auth_tokens_user_active (user_id, is_revoked, expires_at),
    INDEX idx_auth_tokens_website_active (website_id, is_revoked, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API tokens table for authentication and authorization';

