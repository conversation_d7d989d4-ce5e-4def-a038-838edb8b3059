-- Migration: 004_create_login_attempts_table
-- Description: Create login attempts table for rate limiting and security monitoring (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_login_attempts (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NULL,
    
    -- Attempt Information
    email VARCHAR(255) NOT NULL COMMENT 'Email used for login attempt',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP address of login attempt',
    user_agent TEXT COMMENT 'User agent string',
    
    -- Attempt Result
    success BOOLEAN DEFAULT FALSE COMMENT 'Whether login attempt was successful',
    failure_reason VARCHAR(100) COMMENT 'Reason for failure: invalid_credentials, account_locked, etc.',
    
    -- Device Information
    device_name VARCHAR(100) COMMENT 'Device name (optional)',
    device_type VARCHAR(50) COMMENT 'Device type: mobile, desktop, tablet',
    os VARCHAR(50) COMMENT 'Operating system',
    browser VARCHAR(50) COMMENT 'Browser name',
    device_id VARCHAR(64) COMMENT 'Unique device identifier',
    
    -- Location Information
    country VARCHAR(100) COMMENT 'Country name',
    city VARCHAR(100) COMMENT 'City name',
    
    -- Security Context
    is_suspicious BOOLEAN DEFAULT FALSE COMMENT 'Whether attempt is flagged as suspicious',
    risk_score INT UNSIGNED DEFAULT 0 COMMENT 'Risk score (0-100)',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_login_attempts_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_login_attempts_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_auth_login_attempts_device_type CHECK (device_type IN ('mobile', 'desktop', 'tablet', 'unknown')),
    CONSTRAINT chk_auth_login_attempts_risk_score CHECK (risk_score BETWEEN 0 AND 100),
    
    -- Indexes for Performance
    INDEX idx_auth_login_attempts_website_id (website_id),
    INDEX idx_auth_login_attempts_user_id (user_id),
    INDEX idx_auth_login_attempts_email (email),
    INDEX idx_auth_login_attempts_ip_address (ip_address),
    INDEX idx_auth_login_attempts_success (success),
    INDEX idx_auth_login_attempts_is_suspicious (is_suspicious),
    INDEX idx_auth_login_attempts_created_at (created_at),
    INDEX idx_auth_login_attempts_device_id (device_id),
    
    -- Composite indexes for rate limiting and security queries
    INDEX idx_auth_login_attempts_email_time (email, created_at),
    INDEX idx_auth_login_attempts_ip_time (ip_address, created_at),
    INDEX idx_auth_login_attempts_user_time (user_id, created_at),
    INDEX idx_auth_login_attempts_security (is_suspicious, risk_score, created_at),
    INDEX idx_auth_login_attempts_device_tracking (device_id, created_at),
    
    -- Composite index for rate limiting by email/IP
    INDEX idx_auth_login_attempts_rate_limit (email, ip_address, success, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Login attempts table for rate limiting and security monitoring';