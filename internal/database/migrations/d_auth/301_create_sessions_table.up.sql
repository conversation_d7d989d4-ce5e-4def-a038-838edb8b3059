-- Migration: 001_create_sessions_table
-- Description: Create sessions table for user session management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Session Information
    token VARCHAR(255) NOT NULL UNIQUE COMMENT 'Session token',
    
    -- Device Information
    device_name VARCHAR(100) COMMENT 'Device name (optional)',
    device_type VARCHAR(50) COMMENT 'Device type: mobile, desktop, tablet',
    os VARCHAR(50) COMMENT 'Operating system',
    browser VARCHAR(50) COMMENT 'Browser name',
    device_id VARCHAR(64) COMMENT 'Unique device identifier',
    
    -- Location Information
    ip_address VARCHAR(45) COMMENT 'IP address (IPv4/IPv6)',
    country VARCHAR(100) COMMENT 'Country name',
    city VARCHAR(100) COMMENT 'City name',
    
    -- Session State
    is_revoked B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE COMMENT 'Whether session is revoked',
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Last activity time',
    expires_at TIMESTAMP NOT NULL COMMENT 'Session expiration time',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_user_sessions_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_user_sessions_device_type CHECK (device_type IN ('mobile', 'desktop', 'tablet', 'unknown')),
    
    -- Indexes for Performance
    INDEX idx_user_sessions_website_id (website_id),
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_token (token),
    INDEX idx_user_sessions_device_id (device_id),
    INDEX idx_user_sessions_expires_at (expires_at),
    INDEX idx_user_sessions_is_revoked (is_revoked),
    INDEX idx_user_sessions_last_used_at (last_used_at),
    
    -- Composite indexes for common queries
    INDEX idx_user_sessions_user_active (user_id, is_revoked, expires_at),
    INDEX idx_user_sessions_website_active (website_id, is_revoked, expires_at),
    INDEX idx_user_sessions_cleanup (is_revoked, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User sessions table for session management and device tracking';