-- Migration: 003_create_website_themes_table
-- Description: Create website themes table for theme customizations (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS website_themes (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Theme Information
    name VARCHAR(100) NOT NULL COMMENT 'Theme name',
    version VARCHAR(20) COMMENT 'Theme version',
    author VARCHAR(255) COMMENT 'Theme author',
    description TEXT COMMENT 'Theme description',
    
    -- Theme Configuration
    config JSON DEFAULT (JSON_OBJECT()) COMMENT 'Theme configuration settings',
    
    -- Customization Data
    custom_colors JSON DEFAULT (JSON_OBJECT()) COMMENT 'Custom color scheme settings',
    custom_fonts JSON DEFAULT (JSON_OBJECT()) COMMENT 'Custom font settings',
    custom_css LONGTEXT COMMENT 'Custom CSS overrides',
    custom_js TEXT COMMENT 'Custom JavaScript code',
    
    -- Asset Paths
    stylesheet_path VARCHAR(500) COMMENT 'Path to theme stylesheet',
    javascript_path VARCHAR(500) COMMENT 'Path to theme JavaScript',
    preview_image VARCHAR(500) COMMENT 'Theme preview image URL',
    assets_path VARCHAR(500) COMMENT 'Path to theme assets directory',
    
    -- Template Structure
    templates JSON DEFAULT (JSON_OBJECT()) COMMENT 'Theme template files and structure',
    
    -- Theme Capabilities
    supported_blocks JSON DEFAULT (JSON_ARRAY()) COMMENT 'List of supported page builder blocks',
    features JSON DEFAULT (JSON_ARRAY()) COMMENT 'List of theme features and capabilities',
    
    -- Theme Metadata
    is_default BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the default theme',
    is_active BOOLEAN DEFAULT FALSE COMMENT 'Whether this theme is currently active',
    parent_theme_id INT UNSIGNED NULL COMMENT 'Parent theme ID for child themes',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'inactive' COMMENT 'Theme status: active, inactive, archived',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_website_themes_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_themes_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_themes_parent_theme_id FOREIGN KEY (parent_theme_id) REFERENCES website_themes(id) ON DELETE SET NULL,
    CONSTRAINT chk_website_themes_status CHECK (status IN ('active', 'inactive', 'archived')),
    CONSTRAINT chk_website_themes_name_length CHECK (CHAR_LENGTH(name) >= 1),
    
    -- Indexes
    INDEX idx_website_themes_website_theme (website_id, name),
    INDEX idx_website_themes_tenant_id (tenant_id),
    INDEX idx_website_themes_status (status),
    INDEX idx_website_themes_is_active (is_active),
    INDEX idx_website_themes_parent_theme_id (parent_theme_id),
    INDEX idx_website_themes_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Website themes table for theme management and customization';