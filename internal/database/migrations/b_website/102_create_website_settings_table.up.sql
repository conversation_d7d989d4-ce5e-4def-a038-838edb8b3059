-- Migration: 002_create_website_settings_table
-- Description: Create website settings table for website configuration options (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS website_settings (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Setting Information
    setting_key VARCHAR(255) NOT NULL COMMENT 'Setting key identifier',
    setting_name VARCHAR(255) NOT NULL COMMENT 'Human-readable setting name',
    category VARCHAR(100) NOT NULL COMMENT 'Setting category: general, seo, social, security, etc.',
    
    -- Setting Value
    setting_value TEXT COMMENT 'Setting value (can be JSON, string, number, etc.)',
    default_value TEXT COMMENT 'Default value for this setting',
    
    -- Setting Configuration
    data_type VARCHAR(50) NOT NULL DEFAULT 'string' COMMENT 'Data type: string, number, boolean, json, array',
    is_public BOOLEAN DEFAULT TRUE COMMENT 'Whether setting is publicly readable',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'Whether setting is required',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Whether setting value is encrypted',
    
    -- Validation Rules
    validation_rules JSON DEFAULT (JSON_OBJECT()) COMMENT 'Validation rules for the setting value',
    
    -- Metadata
    description TEXT COMMENT 'Setting description and usage info',
    options JSON DEFAULT (JSON_ARRAY()) COMMENT 'Available options for enum/select settings',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_website_settings_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_settings_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uk_website_settings_key UNIQUE KEY (website_id, setting_key),
    CONSTRAINT chk_website_settings_data_type CHECK (data_type IN ('string', 'number', 'boolean', 'json', 'array')),
    CONSTRAINT chk_website_settings_category CHECK (category IN ('general', 'seo', 'social', 'security', 'theme', 'analytics', 'comments', 'media')),
    
    -- Indexes
    INDEX idx_website_settings_website_id (website_id),
    INDEX idx_website_settings_tenant_id (tenant_id),
    INDEX idx_website_settings_category (category),
    INDEX idx_website_settings_key (setting_key),
    INDEX idx_website_settings_public (is_public),
    INDEX idx_website_settings_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Website settings table for configuration options and preferences';