# Database Migrations for MySQL 8

This directory contains all database migrations for the Blog API v3 project, organized by module. The project uses MySQL 8 as the database system.

## MySQL 8 Requirements

- MySQL version 8.0.16 or higher (for full CHECK constraint support)
- InnoDB storage engine
- utf8mb4 character set

## Migration Structure

Migrations are organized into module-specific directories following our numbering convention:

```
migrations/
├── tenant/          # 001-004: Core tenant tables
├── website/         # 005-008: Website management tables  
├── user/            # 009-013: User management tables
├── auth/            # 014-018: Authentication tables
├── rbac/            # 019-023: Role-based access control
├── onboarding/      # 024-028: User onboarding system
├── blog/            # 029-034: Blog content management
├── media/           # 035-037: Media file management
├── website_content/ # 038-041: Pages, menus, widgets
├── notification/    # 042-044: Notification system
├── socket/          # 045-046: Real-time communication
├── seo/             # 047-049: SEO optimization
└── payment/         # 050-053: Payment processing
```

## Migration Naming Convention

Each migration file follows the pattern: `{number}_{description}.sql`

Example: `001_create_tenants_table.sql`

## Migration File Structure

Each migration file contains:

1. **Header Comments**
   - Migration number and name
   - Description
   - Author
   - Date

2. **Up Migration**
   - CREATE TABLE statements
   - Indexes
   - Constraints
   - Triggers
   - Initial data (if needed)
   - Comments for documentation

3. **Down Migration** (commented out)
   - DROP statements in reverse order
   - Used for rollback if needed

## Running Migrations

### Using the migrate command:

```bash
# Run all pending migrations
make migrate-up

# Rollback last migration
make migrate-down

# Check migration status
make migrate-status

# Create a new migration
make migrate-create name=create_new_table
```

### Using the CLI directly:

```bash
# Run migrations
./bin/migrate -database "postgres://user:pass@localhost/blog_api_v3" -path internal/database/migrations up

# Rollback
./bin/migrate -database "postgres://user:pass@localhost/blog_api_v3" -path internal/database/migrations down 1

# Force a specific version
./bin/migrate -database "postgres://user:pass@localhost/blog_api_v3" -path internal/database/migrations force 001
```

## Migration Best Practices

1. **Always include rollback scripts** (as comments) for easy reversal
2. **Add appropriate indexes** for foreign keys and frequently queried columns
3. **Use transactions** implicitly (each migration file is run in a transaction)
4. **Document with comments** - explain complex logic or business rules
5. **Test migrations** on a copy of production data before deploying
6. **Never modify existing migrations** - create new ones for changes
7. **Keep migrations idempotent** - use IF NOT EXISTS clauses where appropriate

## Module Dependencies

Migrations must be run in order due to foreign key dependencies:

1. **Tenant** (001-004) - No dependencies
2. **Website** (005-008) - Depends on Tenant
3. **User** (009-013) - Depends on Tenant
4. **Auth** (014-018) - Depends on User
5. **RBAC** (019-023) - Depends on User, Tenant
6. **All other modules** - Depend on the above core modules

## Common Patterns

### Adding updated_at trigger
```sql
CREATE TRIGGER update_{table}_updated_at BEFORE UPDATE ON {table}
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### Soft deletes
```sql
deleted_at TIMESTAMP WITH TIME ZONE,
-- Include in indexes
CREATE INDEX idx_{table}_{column} ON {table}({column}) WHERE deleted_at IS NULL;
```

### Multi-tenant isolation
```sql
tenant_id BIGINT NOT NULL,
website_id BIGINT NOT NULL,
-- Foreign keys
CONSTRAINT fk_{table}_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
CONSTRAINT fk_{table}_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
-- Composite indexes
CREATE INDEX idx_{table}_tenant_website ON {table}(tenant_id, website_id);
```

## Troubleshooting

### Migration Locks
If a migration gets stuck, check for locks:
```sql
SELECT * FROM schema_migrations;
```

### Failed Migrations
1. Check the error message
2. Fix the issue in the migration file
3. Force the version back: `migrate force {previous_version}`
4. Run migrations again

### Performance Issues
For large tables:
1. Consider running migrations during maintenance windows
2. Use CONCURRENTLY for index creation when possible
3. Split large data migrations into batches