-- Migration: 003_create_tenant_settings_table
-- Description: Create tenant settings table for granular configuration management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS tenant_settings (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Setting identification
    category VARCHAR(100) NOT NULL COMMENT 'Setting category for grouping (e.g., general, email, security)',
    `key` VARCHAR(255) NOT NULL COMMENT 'Unique setting key within the category',
    value JSON NOT NULL COMMENT 'Setting value stored as JSO<PERSON> for flexibility',
    
    -- Metadata
    description TEXT,
    data_type VARCHAR(50) NOT NULL DEFAULT 'string',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Whether the value should be encrypted at rest',
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether the setting can be exposed to public APIs',
    
    -- Validation
    validation_rules JSON,
    default_value JSON,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT UNSIGNED,
    
    -- Constraints
    CONSTRAINT fk_tenant_settings_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uk_tenant_settings_key UNIQUE KEY (tenant_id, category, `key`),
    CONSTRAINT chk_data_type CHECK (data_type IN ('string', 'number', 'boolean', 'json', 'array')),
    
    -- Indexes
    INDEX idx_tenant_settings_tenant_id (tenant_id),
    INDEX idx_tenant_settings_category (tenant_id, category),
    INDEX idx_tenant_settings_key (tenant_id, `key`, is_public),
    INDEX idx_tenant_settings_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Granular configuration settings for each tenant';

-- Note: Default settings initialization for new tenants will be handled in application code
-- This provides better control over default values and easier maintenance