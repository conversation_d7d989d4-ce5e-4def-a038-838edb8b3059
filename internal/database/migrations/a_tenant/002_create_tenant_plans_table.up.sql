-- Migration: 002_create_tenant_plans_table
-- Description: Create tenant plans table for subscription management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS tenant_plans (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Plan Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    
    -- Pricing
    price_monthly DECIMAL(10, 2) NOT NULL DEFAULT 0,
    price_yearly DECIMAL(10, 2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Resource Limits
    max_users INTEGER NOT NULL DEFAULT 5 COMMENT 'Maximum number of users allowed, -1 for unlimited',
    max_websites INTEGER NOT NULL DEFAULT 1 COMMENT 'Maximum number of websites allowed, -1 for unlimited',
    max_storage_gb INTEGER NOT NULL DEFAULT 10,
    max_bandwidth_gb INTEGER NOT NULL DEFAULT 100,
    max_posts_per_month INTEGER,
    max_api_calls_per_day INTEGER,
    
    -- Features (<PERSON><PERSON><PERSON> in MySQL 5.7+)
    features <PERSON>SO<PERSON> DEFAULT (JSON_OBJECT()) COMMENT 'JSON object containing feature flags and configuration for the plan',
    
    -- Display
    display_order INTEGER NOT NULL DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_visible BOOLEAN DEFAULT TRUE,
    
    -- Status
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_tenant_plans_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_tenant_plans_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$'),
    CONSTRAINT chk_tenant_plans_price_check CHECK (price_monthly >= 0 AND price_yearly >= 0),
    
    -- Indexes
    INDEX idx_tenant_plans_slug (slug, status),
    INDEX idx_tenant_plans_status (status),
    INDEX idx_tenant_plans_display_order (display_order, is_visible)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Subscription plans available for tenants with resource limits and features';

-- Add foreign key constraint to tenants table
-- Note: This will be added after tenant plans are seeded
-- ALTER TABLE tenants 
--     ADD CONSTRAINT fk_tenants_plan_id 
--     FOREIGN KEY (plan_id) 
--     REFERENCES tenant_plans(id) 
--     ON DELETE SET NULL;

-- Note: Default tenant plans will be created through the seeding system