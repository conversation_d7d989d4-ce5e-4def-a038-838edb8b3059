-- Migration: 010_create_user_profiles_table
-- Description: Create the user profiles table for extended profile information (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS user_profiles (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    
    -- Personal Information
    bio TEXT,
    title VARCHAR(255),
    company VARCHAR(255),
    location VARCHAR(255),
    website VARCHAR(500),
    
    -- Demographics
    birth_date DATE,
    gender VARCHAR(50),
    
    -- Address Information
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    
    -- Professional Information
    job_title VARCHAR(255),
    department VARCHAR(255),
    skills JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of user skills',
    interests JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of user interests',
    
    -- Social Information
    display_profile BOOLEAN DEFAULT TRUE,
    allow_contact BOOLEAN DEFAULT TRUE,
    show_email BOOLEAN DEFAULT FALSE,
    show_phone BOOLEAN DEFAULT FALSE,
    
    -- Profile Completion
    profile_completed BOOLEAN DEFAULT FALSE,
    completion_percentage TINYINT UNSIGNED DEFAULT 0,
    
    -- Custom Fields
    custom_fields JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for tenant-specific custom profile fields',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_user_profiles_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_user_profiles_gender CHECK (gender IS NULL OR gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    CONSTRAINT chk_user_profiles_completion CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CONSTRAINT chk_user_profiles_website_format CHECK (website IS NULL OR website REGEXP '^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$'),
    
    -- Unique Constraints
    UNIQUE KEY uk_user_profiles_user_id (user_id),
    
    -- Indexes
    INDEX idx_user_profiles_user_id (user_id),
    INDEX idx_user_profiles_location (location),
    INDEX idx_user_profiles_company (company),
    INDEX idx_user_profiles_completion (profile_completed, completion_percentage),
    INDEX idx_user_profiles_display (display_profile),
    INDEX idx_user_profiles_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Extended user profile information including personal, professional, and social details.';