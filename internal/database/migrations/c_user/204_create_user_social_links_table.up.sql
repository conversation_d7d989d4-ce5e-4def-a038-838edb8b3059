-- Migration: 012_create_user_social_links_table
-- Description: Create the user social links table for social media connections (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS user_social_links (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    
    -- Social Platform Information
    platform VARCHAR(50) NOT NULL COMMENT 'Social media platform: twitter, linkedin, github, facebook, instagram, youtube, etc.',
    username VARCHA<PERSON>(255) NOT NULL COMMENT 'Username or handle on the platform',
    url VARCHAR(500) NOT NULL COMMENT 'Full URL to the social media profile',
    
    -- Display Settings
    display_order INT UNSIGNED DEFAULT 0 COMMENT 'Order in which to display the social links',
    is_public BOOLEAN DEFAULT TRUE COMMENT 'Whether to show this link publicly',
    is_verified BOOLEAN DEFAULT FALSE COMMENT 'Whether this social link has been verified',
    
    -- Metadata
    profile_data JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for storing platform-specific profile data',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Foreign Keys
    CONSTRAINT fk_user_social_links_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_user_social_links_platform CHECK (platform IN (
        'twitter', 'linkedin', 'github', 'facebook', 'instagram', 'youtube', 
        'tiktok', 'snapchat', 'discord', 'twitch', 'reddit', 'pinterest', 
        'medium', 'dev', 'stackoverflow', 'behance', 'dribbble', 'website', 'other'
    )),
    CONSTRAINT chk_user_social_links_url_format CHECK (url REGEXP '^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$'),
    CONSTRAINT chk_user_social_links_username_length CHECK (CHAR_LENGTH(username) >= 1 AND CHAR_LENGTH(username) <= 255),
    
    -- Unique Constraints (one link per platform per user)
    UNIQUE KEY uk_user_social_links_user_platform (user_id, platform),
    
    -- Indexes
    INDEX idx_user_social_links_user_id (user_id),
    INDEX idx_user_social_links_platform (platform),
    INDEX idx_user_social_links_public (is_public),
    INDEX idx_user_social_links_verified (is_verified),
    INDEX idx_user_social_links_display_order (user_id, display_order),
    INDEX idx_user_social_links_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User social media links and connections for profiles.';