package api

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"

	"github.com/blog-api-v3/blog-api-v3/internal/config"
	authHandlers "github.com/blog-api-v3/blog-api-v3/modules/auth/handlers"
	authModels "github.com/blog-api-v3/blog-api-v3/modules/auth/models"
	authServices "github.com/blog-api-v3/blog-api-v3/modules/auth/services"
	onboardingHandlers "github.com/blog-api-v3/blog-api-v3/modules/onboarding/handlers"
	onboardingServices "github.com/blog-api-v3/blog-api-v3/modules/onboarding/services"
	onboardingStorage "github.com/blog-api-v3/blog-api-v3/modules/onboarding/storage"
	tenantHandlers "github.com/blog-api-v3/blog-api-v3/modules/tenant/handlers"
	tenantRepositories "github.com/blog-api-v3/blog-api-v3/modules/tenant/repositories"
	tenantServices "github.com/blog-api-v3/blog-api-v3/modules/tenant/services"
	userHandlers "github.com/blog-api-v3/blog-api-v3/modules/user/handlers"
	userRepositories "github.com/blog-api-v3/blog-api-v3/modules/user/repositories"
	userServices "github.com/blog-api-v3/blog-api-v3/modules/user/services"
	"github.com/blog-api-v3/blog-api-v3/pkg/http/middleware"
	"github.com/blog-api-v3/blog-api-v3/pkg/utils"
	"github.com/blog-api-v3/blog-api-v3/pkg/validator"
)

// Server represents the API server
type Server struct {
	config       *config.Config
	db           *sql.DB
	rawValidator validator.Validator
	validator    *validator.RequestValidator
	logger       utils.Logger
	router       *mux.Router
	httpServer   *http.Server

	// Services
	authService       authServices.AuthService
	userService       userServices.UserService
	onboardingService onboardingServices.OnboardingService
	tenantService     tenantServices.TenantService
	// websiteService     websiteServices.WebsiteService // TODO: implement

	// Handlers
	authHandler       *authHandlers.AuthHandler
	userHandler       *userHandlers.UserHandler
	onboardingHandler *onboardingHandlers.OnboardingHandler
	tenantHandler     *tenantHandlers.TenantHandler
	// websiteHandler     *websiteHandlers.WebsiteHandler // TODO: implement
}

// NewServer creates a new API server instance
func NewServer(cfg *config.Config, db *sql.DB, v validator.Validator, logger utils.Logger) (*Server, error) {
	server := &Server{
		config:       cfg,
		db:           db,
		rawValidator: v,
		validator:    validator.NewRequestValidator(v),
		logger:       logger,
	}

	// Initialize services and handlers
	if err := server.initializeServices(); err != nil {
		return nil, err
	}

	if err := server.initializeHandlers(); err != nil {
		return nil, err
	}

	// Setup router
	server.setupRouter()

	return server, nil
}

// initializeServices initializes all module services
func (s *Server) initializeServices() error {
	// Auth service - create simple mock services
	tokenService := &MockTokenService{}
	passwordService := &MockPasswordService{}
	s.authService = authServices.NewMockAuthService(tokenService, passwordService)

	// User service
	userRepo := userRepositories.NewMockUserRepository()
	s.userService = userServices.NewMockUserService(userRepo, s.logger)

	// Onboarding service
	onboardingRepo := onboardingStorage.NewMemoryOnboardingRepository()
	progressRepo := onboardingStorage.NewMemoryProgressRepository()
	stepRepo := onboardingStorage.NewMemoryStepRepository()
	eventRepo := onboardingStorage.NewMemoryEventRepository()
	s.onboardingService = onboardingServices.NewMockOnboardingService(
		onboardingRepo, progressRepo, stepRepo, eventRepo,
	)

	// Tenant service
	tenantRepo := tenantRepositories.NewMockTenantRepository()
	planRepo := tenantRepositories.NewMockPlanRepository()
	s.tenantService = tenantServices.NewMockTenantService(tenantRepo, planRepo, s.logger)

	// Website service - TODO: implement mock service
	// websiteRepo := websiteStorage.NewMemoryWebsiteRepository()
	// s.websiteService = websiteServices.NewMockWebsiteService(websiteRepo, s.logger)

	return nil
}

// initializeHandlers initializes all module handlers
func (s *Server) initializeHandlers() error {
	// Validate required dependencies
	if s.authService == nil {
		return errors.New("auth service not initialized")
	}
	if s.userService == nil {
		return errors.New("user service not initialized")
	}
	if s.rawValidator == nil {
		return errors.New("validator not initialized")
	}
	if s.logger == nil {
		return errors.New("logger not initialized")
	}

	s.logger.Info("Initializing handlers...")

	// Auth handler
	userService := authServices.NewMockUserService() // Mock user service for auth
	if userService == nil {
		return errors.New("failed to create mock user service")
	}

	s.authHandler = authHandlers.NewAuthHandler(
		s.authService,
		userService,
		s.rawValidator,
		s.logger,
	)
	if s.authHandler == nil {
		return errors.New("failed to create auth handler")
	}

	// User handler
	s.userHandler = userHandlers.NewUserHandler(
		s.userService,
		s.rawValidator,
		s.logger,
	)

	// Onboarding handler - requires all related services
	onboardingStepService := onboardingServices.NewMockOnboardingStepService()
	onboardingAnalyticsService := onboardingServices.NewMockOnboardingAnalyticsService()
	s.onboardingHandler = onboardingHandlers.NewOnboardingHandler(
		s.onboardingService,
		onboardingStepService,
		onboardingAnalyticsService,
		s.rawValidator,
		s.logger,
	)

	// Tenant handler - use tenant service as plan service for now
	s.tenantHandler = tenantHandlers.NewTenantHandler(
		s.tenantService,
		nil, // planService placeholder
		s.rawValidator,
		s.logger,
	)

	// Website handler - TODO: implement after service is ready
	// s.websiteHandler = websiteHandlers.NewWebsiteHandler(
	//	 s.websiteService,
	//	 s.rawValidator,
	//	 s.logger,
	// )

	return nil
}

// setupRouter configures the router with all routes and middleware
func (s *Server) setupRouter() {
	s.router = mux.NewRouter()

	// Global middleware
	s.router.Use(middleware.LoggingMiddleware(s.logger))
	s.router.Use(middleware.RequestIDMiddleware())
	s.router.Use(middleware.CORSMiddleware())

	if s.config.Features.EnableRateLimit {
		s.router.Use(middleware.RateLimitMiddleware())
	}

	// Health check endpoints
	if s.config.Features.EnableHealth {
		s.setupHealthRoutes()
	}

	// API v1 routes
	v1 := s.router.PathPrefix("/api/cms/v1").Subrouter()

	// Register module routes
	s.authHandler.RegisterRoutes(v1)
	s.userHandler.RegisterRoutes(v1, s.authService)
	s.onboardingHandler.RegisterRoutes(v1, s.authService)
	s.tenantHandler.RegisterRoutes(v1, s.authService)
	// s.websiteHandler.RegisterRoutes(v1, s.authService) // TODO: uncomment when implemented

	// Log all registered routes
	s.logRoutes()

	// Metrics endpoint
	if s.config.Features.EnableMetrics {
		s.setupMetricsRoutes()
	}

	// Swagger documentation
	if s.config.Features.EnableSwagger {
		s.setupSwaggerRoutes()
	}

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins:   s.config.Server.AllowedOrigins,
		AllowedMethods:   s.config.Server.AllowedMethods,
		AllowedHeaders:   s.config.Server.AllowedHeaders,
		AllowCredentials: true,
		MaxAge:           300,
	})

	s.httpServer = &http.Server{
		Handler:      c.Handler(s.router),
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(s.config.Server.IdleTimeout) * time.Second,
	}
}

// setupHealthRoutes configures health check routes
func (s *Server) setupHealthRoutes() {
	health := s.router.PathPrefix("/health").Subrouter()

	health.HandleFunc("/live", s.handleLiveness).Methods("GET")
	health.HandleFunc("/ready", s.handleReadiness).Methods("GET")
	health.HandleFunc("/status", s.handleStatus).Methods("GET")
}

// setupMetricsRoutes configures metrics routes
func (s *Server) setupMetricsRoutes() {
	metrics := s.router.PathPrefix("/metrics").Subrouter()

	metrics.HandleFunc("", s.handleMetrics).Methods("GET")
	metrics.HandleFunc("/stats", s.handleStats).Methods("GET")
}

// setupSwaggerRoutes configures Swagger documentation routes
func (s *Server) setupSwaggerRoutes() {
	docs := s.router.PathPrefix("/docs").Subrouter()

	docs.HandleFunc("/", s.handleSwagger).Methods("GET")
	docs.HandleFunc("/api.json", s.handleSwaggerJSON).Methods("GET")
}

// Start starts the HTTP server
func (s *Server) Start(addr string) error {
	s.httpServer.Addr = addr
	s.logger.WithField("addr", addr).Info("Starting HTTP server")
	return s.httpServer.ListenAndServe()
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down HTTP server")
	return s.httpServer.Shutdown(ctx)
}

// Health check handlers

func (s *Server) handleLiveness(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"ok","message":"Server is alive"}`))
}

func (s *Server) handleReadiness(w http.ResponseWriter, r *http.Request) {
	// Check if all services are ready
	ready := true
	checks := make(map[string]bool)

	// Check database connection (skip if no database in mock mode)
	if s.db != nil {
		if err := s.db.Ping(); err != nil {
			ready = false
			checks["database"] = false
		} else {
			checks["database"] = true
		}
	} else {
		// In mock mode, database check is skipped
		checks["database"] = true
	}

	// Check services availability
	checks["auth_service"] = s.authService != nil
	checks["user_service"] = s.userService != nil
	checks["onboarding_service"] = s.onboardingService != nil
	checks["tenant_service"] = s.tenantService != nil
	// checks["website_service"] = s.websiteService != nil // TODO: enable when implemented

	for _, check := range checks {
		if !check {
			ready = false
			break
		}
	}

	w.Header().Set("Content-Type", "application/json")

	if ready {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ready","checks":` + s.formatChecks(checks) + `}`))
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`{"status":"not ready","checks":` + s.formatChecks(checks) + `}`))
	}
}

func (s *Server) handleStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// Simple JSON marshaling
	response := `{
		"service": "blog-api-v3",
		"version": "1.0.0",
		"timestamp": "` + time.Now().UTC().Format(time.RFC3339) + `",
		"environment": "development",
		"modules": ["auth", "user", "onboarding", "tenant", "website"],
		"features": {
			"registration": ` + formatBool(s.config.Features.EnableRegistration) + `,
			"comments": ` + formatBool(s.config.Features.EnableComments) + `,
			"notifications": ` + formatBool(s.config.Features.EnableNotifications) + `,
			"analytics": ` + formatBool(s.config.Features.EnableAnalytics) + `,
			"cache": ` + formatBool(s.config.Features.EnableCache) + `,
			"rate_limit": ` + formatBool(s.config.Features.EnableRateLimit) + `,
			"swagger": ` + formatBool(s.config.Features.EnableSwagger) + `,
			"metrics": ` + formatBool(s.config.Features.EnableMetrics) + `,
			"health": ` + formatBool(s.config.Features.EnableHealth) + `
		}
	}`

	w.Write([]byte(response))
}

// Metrics handlers

func (s *Server) handleMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("# HELP blog_api_info Information about the blog API\n" +
		"# TYPE blog_api_info gauge\n" +
		"blog_api_info{version=\"1.0.0\",environment=\"development\"} 1\n"))
}

func (s *Server) handleStats(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{
		"requests_total": 0,
		"requests_per_second": 0,
		"active_connections": 0,
		"response_time_avg": 0,
		"error_rate": 0
	}`))
}

// Swagger handlers

func (s *Server) handleSwagger(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`<!DOCTYPE html>
<html>
<head>
	<title>Blog API v3 Documentation</title>
	<link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
</head>
<body>
	<div id="swagger-ui"></div>
	<script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
	<script>
		SwaggerUIBundle({
			url: '/docs/api.json',
			dom_id: '#swagger-ui',
			presets: [
				SwaggerUIBundle.presets.apis,
				SwaggerUIBundle.presets.standalone
			]
		});
	</script>
</body>
</html>`))
}

func (s *Server) handleSwaggerJSON(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{
		"openapi": "3.0.0",
		"info": {
			"title": "Blog API v3",
			"version": "1.0.0",
			"description": "A comprehensive blog API with multi-tenant support"
		},
		"servers": [
			{
				"url": "/api/cms/v1",
				"description": "Development server"
			}
		],
		"paths": {
			"/auth/login": {
				"post": {
					"summary": "User login",
					"tags": ["Authentication"],
					"responses": {
						"200": {
							"description": "Login successful"
						}
					}
				}
			}
		}
	}`))
}

// Helper functions

func (s *Server) formatChecks(checks map[string]bool) string {
	result := "{"
	first := true
	for key, value := range checks {
		if !first {
			result += ","
		}
		result += `"` + key + `":` + formatBool(value)
		first = false
	}
	result += "}"
	return result
}

func formatBool(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

// Mock services for basic functionality

// MockTokenService implements a basic token service
type MockTokenService struct{}

func (ts *MockTokenService) GenerateAccessToken(claims *authModels.TokenClaims) (string, error) {
	return "mock_access_token", nil
}

func (ts *MockTokenService) GenerateRefreshToken() (string, error) {
	return "mock_refresh_token", nil
}

func (ts *MockTokenService) GenerateTokenPair(user *authModels.User, sessionID string) (*authModels.TokenPair, error) {
	return &authModels.TokenPair{
		AccessToken:  "mock_access_token",
		RefreshToken: "mock_refresh_token",
		ExpiresIn:    3600,
		TokenType:    "Bearer",
	}, nil
}

func (ts *MockTokenService) ValidateAccessToken(token string) (*authModels.TokenClaims, error) {
	return &authModels.TokenClaims{
		UserID:    "mock_user_id",
		Username:  "mock_user",
		Email:     "<EMAIL>",
		SessionID: "mock_session_id",
	}, nil
}

func (ts *MockTokenService) ValidateRefreshToken(token string) (string, error) {
	return "mock_session_id", nil
}

func (ts *MockTokenService) RevokeToken(token string) error {
	return nil
}

func (ts *MockTokenService) IsTokenRevoked(token string) (bool, error) {
	return false, nil
}

func (ts *MockTokenService) CleanupRevokedTokens() error {
	return nil
}

// MockPasswordService implements a basic password service
type MockPasswordService struct{}

func (ps *MockPasswordService) HashPassword(password string) (string, error) {
	return "hashed_" + password, nil
}

func (ps *MockPasswordService) VerifyPassword(password, hash string) error {
	if hash == "hashed_"+password {
		return nil
	}
	return errors.New("invalid password")
}

func (ps *MockPasswordService) ValidatePasswordStrength(password string) error {
	if len(password) < 8 {
		return errors.New("password too short")
	}
	return nil
}

func (ps *MockPasswordService) GenerateRandomPassword(length int) (string, error) {
	return "generated_password", nil
}

// logRoutes logs all registered routes for debugging
func (s *Server) logRoutes() {
	s.logger.Info("🚀 Registered API Routes:")

	// Walk through router to find all routes
	err := s.router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		pathTemplate, err := route.GetPathTemplate()
		if err != nil {
			return nil
		}

		methods, err := route.GetMethods()
		if err != nil {
			methods = []string{"ALL"}
		}

		// Create full clickable URL
		fullURL := fmt.Sprintf("http://localhost:9077%s", pathTemplate)
		methodStr := strings.Join(methods, ",")

		s.logger.WithFields(map[string]interface{}{
			"method": methodStr,
			"path":   pathTemplate,
			"url":    fullURL,
		}).Info(fmt.Sprintf("  📍 [%-8s] %s -> %s", methodStr, pathTemplate, fullURL))
		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("Failed to walk routes")
	}
}
