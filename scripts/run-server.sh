#!/bin/bash

# Script to run Blog API server with different log formats
# Usage: ./scripts/run-server.sh [format] [port]
# Formats: pretty, json, text
# Default: pretty, port 9077

set -e

FORMAT=${1:-"pretty"}
PORT=${2:-"9077"}

echo "🚀 Starting Blog API Server..."
echo "📊 Format: $FORMAT"
echo "🔌 Port: $PORT"
echo "🔗 URLs will be available at: http://localhost:$PORT"
echo ""

# Build server
echo "🔨 Building server..."
go build -o bin/server cmd/server/main.go

# Export environment variables
export LOG_LEVEL="info"
export LOG_FORMAT="$FORMAT"
export APP_PORT="$PORT"

echo "🌟 Starting server with $FORMAT logging on port $PORT..."
echo "💡 Use Ctrl+C to stop the server"
echo ""

# Run server
./bin/server