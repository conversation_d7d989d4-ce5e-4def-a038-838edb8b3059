#!/bin/bash

# E2E Test Runner Script
# Usage: ./scripts/run-e2e-tests.sh [options]

set -e

# Default configuration
DEFAULT_SERVER_HOST="localhost"
DEFAULT_SERVER_PORT="8081"
DEFAULT_TEST_SUITE="all"
DEFAULT_TIMEOUT="30s"
DEFAULT_PARALLEL="true"
DEFAULT_DEBUG="false"
DEFAULT_CLEANUP="true"

# Parse command line arguments
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    cat << EOF
🧪 E2E Test Runner

Usage: $0 [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -s, --suite SUITE       Test suite to run (all|auth|user|tenant|onboarding|health)
    -H, --host HOST         Server host (default: $DEFAULT_SERVER_HOST)
    -p, --port PORT         Server port (default: $DEFAULT_SERVER_PORT)
    -t, --timeout TIMEOUT  Test timeout (default: $DEFAULT_TIMEOUT)
    -P, --parallel BOOL     Run tests in parallel (default: $DEFAULT_PARALLEL)
    -d, --debug             Enable debug mode
    -C, --no-cleanup        Disable test cleanup
    -b, --build             Build server before running tests
    -k, --kill-server       Kill existing server processes
    -v, --verbose           Verbose output
    --server-only           Start server only (don't run tests)
    --tests-only            Run tests only (assume server is running)

TEST SUITES:
    all                     Run all E2E test suites
    auth                    Authentication flow tests
    user                    User management tests
    tenant                  Tenant management tests
    onboarding              Onboarding flow tests
    health                  Health monitoring tests
    complete                Complete end-to-end flow test

EXAMPLES:
    $0                                          # Run all tests with defaults
    $0 -s auth -d                              # Run auth tests with debug
    $0 -s health --host localhost --port 9077  # Run health tests on specific host/port
    $0 --build -s all -v                       # Build server and run all tests verbosely
    $0 --server-only                           # Start server only for manual testing
    $0 --tests-only -s user                    # Run user tests (server already running)

EOF
}

# Initialize variables
SERVER_HOST="$DEFAULT_SERVER_HOST"
SERVER_PORT="$DEFAULT_SERVER_PORT"
TEST_SUITE="$DEFAULT_TEST_SUITE"
TIMEOUT="$DEFAULT_TIMEOUT"
PARALLEL="$DEFAULT_PARALLEL"
DEBUG="$DEFAULT_DEBUG"
CLEANUP="$DEFAULT_CLEANUP"
BUILD_SERVER="false"
KILL_EXISTING="false"
VERBOSE="false"
SERVER_ONLY="false"
TESTS_ONLY="false"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--suite)
            TEST_SUITE="$2"
            shift 2
            ;;
        -H|--host)
            SERVER_HOST="$2"
            shift 2
            ;;
        -p|--port)
            SERVER_PORT="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -P|--parallel)
            PARALLEL="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG="true"
            shift
            ;;
        -C|--no-cleanup)
            CLEANUP="false"
            shift
            ;;
        -b|--build)
            BUILD_SERVER="true"
            shift
            ;;
        -k|--kill-server)
            KILL_EXISTING="true"
            shift
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        --server-only)
            SERVER_ONLY="true"
            shift
            ;;
        --tests-only)
            TESTS_ONLY="true"
            shift
            ;;
        *)
            echo "❌ Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Validate test suite
case $TEST_SUITE in
    all|auth|user|tenant|onboarding|health|complete)
        ;;
    *)
        echo "❌ Invalid test suite: $TEST_SUITE"
        echo "Valid options: all, auth, user, tenant, onboarding, health, complete"
        exit 1
        ;;
esac

# Set verbose output
if [ "$VERBOSE" = "true" ]; then
    set -x
fi

# Color output functions
red() { echo -e "\033[31m$1\033[0m"; }
green() { echo -e "\033[32m$1\033[0m"; }
yellow() { echo -e "\033[33m$1\033[0m"; }
blue() { echo -e "\033[34m$1\033[0m"; }
cyan() { echo -e "\033[36m$1\033[0m"; }

log_info() { echo "$(blue "ℹ️  $1")"; }
log_success() { echo "$(green "✅ $1")"; }
log_warning() { echo "$(yellow "⚠️  $1")"; }
log_error() { echo "$(red "❌ $1")"; }

# Print configuration
print_config() {
    echo "$(cyan "🧪 E2E Test Configuration")"
    echo "========================="
    echo "  Server Host: $SERVER_HOST"
    echo "  Server Port: $SERVER_PORT"
    echo "  Test Suite: $TEST_SUITE"
    echo "  Timeout: $TIMEOUT"
    echo "  Parallel: $PARALLEL"
    echo "  Debug: $DEBUG"
    echo "  Cleanup: $CLEANUP"
    echo "  Build Server: $BUILD_SERVER"
    echo "  Kill Existing: $KILL_EXISTING"
    echo "  Server Only: $SERVER_ONLY"
    echo "  Tests Only: $TESTS_ONLY"
    echo ""
}

# Kill existing server processes
kill_server() {
    log_info "Killing existing server processes..."
    pkill -f "bin/server" || true
    pkill -f "cmd/server/main.go" || true
    sleep 2
}

# Build server
build_server() {
    log_info "Building server..."
    cd "$PROJECT_DIR"
    
    if [ ! -d "bin" ]; then
        mkdir -p bin
    fi
    
    go build -o bin/server cmd/server/main.go
    chmod +x bin/server
    
    log_success "Server built successfully"
}

# Start server
start_server() {
    log_info "Starting test server on $SERVER_HOST:$SERVER_PORT..."
    cd "$PROJECT_DIR"
    
    # Set environment variables
    export PORT="$SERVER_PORT"
    export HOST="$SERVER_HOST"
    export LOG_LEVEL="info"
    export LOG_FORMAT="pretty"
    export JWT_SECRET="test-jwt-secret-for-e2e-tests"
    export ENABLE_HEALTH="true"
    export ENABLE_METRICS="true"
    export ENABLE_SWAGGER="true"
    
    # Start server in background
    ./bin/server &
    SERVER_PID=$!
    echo "$SERVER_PID" > /tmp/e2e-server.pid
    
    # Wait for server to be ready
    log_info "Waiting for server to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://$SERVER_HOST:$SERVER_PORT/health/ready" > /dev/null 2>&1; then
            log_success "Server is ready!"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "Server failed to start after $max_attempts attempts"
            return 1
        fi
        
        echo "🔄 Waiting for server... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
}

# Stop server
stop_server() {
    if [ -f /tmp/e2e-server.pid ]; then
        local pid=$(cat /tmp/e2e-server.pid)
        log_info "Stopping server (PID: $pid)..."
        kill "$pid" 2>/dev/null || true
        wait "$pid" 2>/dev/null || true
        rm -f /tmp/e2e-server.pid
    fi
    
    # Kill any remaining processes
    kill_server
}

# Run tests
run_tests() {
    log_info "Running E2E tests..."
    cd "$PROJECT_DIR"
    
    # Set test environment variables
    export E2E_SERVER_HOST="$SERVER_HOST"
    export E2E_SERVER_PORT="$SERVER_PORT"
    export E2E_TIMEOUT="$TIMEOUT"
    export E2E_PARALLEL="$PARALLEL"
    export E2E_DEBUG="$DEBUG"
    export E2E_CLEANUP="$CLEANUP"
    
    # Determine which tests to run
    local test_cmd
    case $TEST_SUITE in
        "auth")
            log_info "🔐 Running authentication tests..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EAuthFlow"
            ;;
        "user")
            log_info "👤 Running user management tests..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EUserManagement"
            ;;
        "tenant")
            log_info "🏢 Running tenant management tests..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2ETenantManagement"
            ;;
        "onboarding")
            log_info "📝 Running onboarding flow tests..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EOnboardingFlow"
            ;;
        "health")
            log_info "🏥 Running health monitoring tests..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EHealthMonitoring"
            ;;
        "complete")
            log_info "🎯 Running complete E2E flow test..."
            test_cmd="go test -v -timeout 10m ./tests/e2e/suite -run TestE2ECompleteFlow"
            ;;
        "all"|*)
            log_info "🎯 Running all E2E tests..."
            test_cmd="go test -v -timeout 15m ./tests/e2e/scenarios/... && go test -v -timeout 10m ./tests/e2e/suite -run TestE2ECompleteFlow"
            ;;
    esac
    
    # Run the tests
    if eval "$test_cmd"; then
        log_success "All tests passed!"
        return 0
    else
        log_error "Some tests failed!"
        return 1
    fi
}

# Cleanup function
cleanup() {
    if [ "$CLEANUP" = "true" ]; then
        log_info "Cleaning up..."
        stop_server
    fi
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_config
    
    # Kill existing servers if requested
    if [ "$KILL_EXISTING" = "true" ]; then
        kill_server
    fi
    
    # Build server if requested
    if [ "$BUILD_SERVER" = "true" ]; then
        build_server
    fi
    
    # Start server (unless tests-only mode)
    if [ "$TESTS_ONLY" = "false" ]; then
        if [ ! -f "$PROJECT_DIR/bin/server" ]; then
            log_warning "Server binary not found. Building server..."
            build_server
        fi
        
        start_server || {
            log_error "Failed to start server"
            exit 1
        }
    fi
    
    # Run tests (unless server-only mode)
    if [ "$SERVER_ONLY" = "false" ]; then
        run_tests || {
            log_error "Tests failed"
            exit 1
        }
    else
        log_info "Server started in server-only mode"
        log_info "Server URLs:"
        log_info "  - Health: http://$SERVER_HOST:$SERVER_PORT/health/status"
        log_info "  - Ready: http://$SERVER_HOST:$SERVER_PORT/health/ready"
        log_info "  - Auth: http://$SERVER_HOST:$SERVER_PORT/api/cms/v1/auth"
        log_info ""
        log_info "Press Ctrl+C to stop the server"
        
        # Wait for Ctrl+C
        while true; do
            sleep 1
        done
    fi
    
    log_success "E2E test execution completed successfully!"
}

# Run main function
main "$@"