name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test suite to run'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - auth
          - user
          - tenant
          - onboarding
          - health
      debug_mode:
        description: 'Enable debug mode'
        required: false
        default: false
        type: boolean

env:
  GO_VERSION: '1.21'
  MYSQL_VERSION: '8.0'

jobs:
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: blog_api_v3_e2e_test
          MYSQL_USER: testuser
          MYSQL_PASSWORD: testpassword
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: 📦 Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: 📚 Download Go modules
      run: go mod download

    - name: 🏗️ Build application
      run: |
        echo "🔨 Building server binary..."
        go build -o bin/server cmd/server/main.go
        chmod +x bin/server

    - name: ⏳ Wait for MySQL
      run: |
        echo "⏳ Waiting for MySQL to be ready..."
        for i in {1..30}; do
          if mysqladmin ping -h 127.0.0.1 -P 3306 -u root -ptestpassword --silent; then
            echo "✅ MySQL is ready!"
            break
          fi
          echo "🔄 Waiting for MySQL... ($i/30)"
          sleep 2
        done

    - name: 🗄️ Setup test database
      env:
        MYSQL_HOST: 127.0.0.1
        MYSQL_PORT: 3306
        MYSQL_USER: root
        MYSQL_PASSWORD: testpassword
        MYSQL_DATABASE: blog_api_v3_e2e_test
      run: |
        echo "🗄️ Setting up test database..."
        mysql -h $MYSQL_HOST -P $MYSQL_PORT -u $MYSQL_USER -p$MYSQL_PASSWORD -e "
          CREATE DATABASE IF NOT EXISTS $MYSQL_DATABASE;
          GRANT ALL PRIVILEGES ON $MYSQL_DATABASE.* TO 'testuser'@'%';
          FLUSH PRIVILEGES;
        "
        echo "✅ Test database setup complete"

    - name: 🚀 Start test server
      env:
        # Server configuration
        PORT: 8081
        HOST: 0.0.0.0
        
        # Database configuration  
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_NAME: blog_api_v3_e2e_test
        DB_USER: testuser
        DB_PASSWORD: testpassword
        
        # Logging configuration
        LOG_LEVEL: info
        LOG_FORMAT: json
        
        # Auth configuration
        JWT_SECRET: test-jwt-secret-key-for-e2e-tests
        
        # Feature flags
        ENABLE_HEALTH: true
        ENABLE_METRICS: true
        ENABLE_SWAGGER: true
      run: |
        echo "🚀 Starting test server..."
        ./bin/server &
        SERVER_PID=$!
        echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV
        
        # Wait for server to be ready
        echo "⏳ Waiting for server to be ready..."
        for i in {1..30}; do
          if curl -f http://localhost:8081/health/ready > /dev/null 2>&1; then
            echo "✅ Server is ready!"
            break
          fi
          echo "🔄 Waiting for server... ($i/30)"
          sleep 2
        done

    - name: 🧪 Run E2E Tests
      env:
        # E2E test configuration
        E2E_SERVER_HOST: localhost
        E2E_SERVER_PORT: 8081
        E2E_TIMEOUT: 30s
        E2E_PARALLEL: true
        E2E_DEBUG: ${{ github.event.inputs.debug_mode || 'false' }}
        E2E_CLEANUP: true
        
        # Database configuration for tests
        E2E_DB_HOST: 127.0.0.1
        E2E_DB_PORT: 3306
        E2E_DB_NAME: blog_api_v3_e2e_test
        E2E_DB_USER: testuser
        E2E_DB_PASSWORD: testpassword
      run: |
        echo "🧪 Running E2E tests..."
        
        # Determine which tests to run
        TEST_SUITE="${{ github.event.inputs.test_suite || 'all' }}"
        echo "📋 Test suite: $TEST_SUITE"
        
        case $TEST_SUITE in
          "auth")
            echo "🔐 Running authentication tests..."
            go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EAuthFlow
            ;;
          "user")
            echo "👤 Running user management tests..."
            go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EUserManagement
            ;;
          "tenant")
            echo "🏢 Running tenant management tests..."
            go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2ETenantManagement
            ;;
          "onboarding")
            echo "📝 Running onboarding flow tests..."
            go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EOnboardingFlow
            ;;
          "health")
            echo "🏥 Running health monitoring tests..."
            go test -v -timeout 10m ./tests/e2e/scenarios -run TestE2EHealthMonitoring
            ;;
          "all"|*)
            echo "🎯 Running all E2E tests..."
            go test -v -timeout 15m ./tests/e2e/scenarios/...
            go test -v -timeout 10m ./tests/e2e/suite -run TestE2ECompleteFlow
            ;;
        esac

    - name: 📊 Generate test report
      if: always()
      run: |
        echo "📊 Generating test report..."
        
        # Create test report directory
        mkdir -p test-reports
        
        # Generate JUnit XML report (if gotestsum is available)
        if command -v gotestsum &> /dev/null; then
          echo "📋 Generating JUnit XML report..."
          gotestsum --junitfile test-reports/e2e-tests.xml --format testname -- -timeout 15m ./tests/e2e/...
        fi
        
        # Generate coverage report
        echo "📈 Generating coverage report..."
        go test -coverprofile=test-reports/coverage.out -covermode=atomic ./tests/e2e/... || true
        
        if [ -f test-reports/coverage.out ]; then
          go tool cover -html=test-reports/coverage.out -o test-reports/coverage.html
        fi

    - name: 📤 Upload test artifacts
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results
        path: |
          test-reports/
          *.log
        retention-days: 30

    - name: 🧹 Cleanup test server
      if: always()
      run: |
        echo "🧹 Stopping test server..."
        if [ -n "$SERVER_PID" ]; then
          kill $SERVER_PID || true
          wait $SERVER_PID 2>/dev/null || true
        fi
        
        # Kill any remaining server processes
        pkill -f "bin/server" || true

    - name: 📈 Test Results Summary
      if: always()
      run: |
        echo "📈 E2E Test Results Summary"
        echo "=========================="
        
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ Status: PASSED"
          echo "🎉 All E2E tests completed successfully!"
        else
          echo "❌ Status: FAILED"
          echo "💥 Some E2E tests failed. Check the logs above for details."
        fi
        
        echo ""
        echo "🔗 Test Environment:"
        echo "  - Server: http://localhost:8081"
        echo "  - Database: MySQL 8.0"
        echo "  - Go Version: ${{ env.GO_VERSION }}"
        echo "  - Test Suite: ${{ github.event.inputs.test_suite || 'all' }}"
        echo "  - Debug Mode: ${{ github.event.inputs.debug_mode || 'false' }}"

  # Job to run performance tests (optional, can be enabled later)
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    needs: e2e-tests
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: 🏗️ Build application
      run: |
        go build -o bin/server cmd/server/main.go
        chmod +x bin/server

    - name: ⚡ Run performance tests
      env:
        E2E_SERVER_HOST: localhost
        E2E_SERVER_PORT: 8082
        E2E_PARALLEL: true
      run: |
        echo "⚡ Running performance tests..."
        
        # Start server for performance testing
        PORT=8082 ./bin/server &
        SERVER_PID=$!
        
        # Wait for server
        sleep 10
        
        # Run performance-focused tests
        go test -v -timeout 30m -tags performance ./tests/e2e/scenarios -run "Performance"
        
        # Cleanup
        kill $SERVER_PID || true

  # Job to test against multiple Go versions (optional)
  matrix-tests:
    name: Matrix Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    strategy:
      matrix:
        go-version: ['1.20', '1.21', '1.22']
        
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}

    - name: 🧪 Run basic E2E tests
      run: |
        echo "🧪 Running basic E2E tests with Go ${{ matrix.go-version }}..."
        
        # Build and test with current Go version
        go build -o bin/server cmd/server/main.go
        
        # Run only health checks for matrix testing
        go test -v -timeout 5m ./tests/e2e/scenarios -run TestE2EHealthMonitoring