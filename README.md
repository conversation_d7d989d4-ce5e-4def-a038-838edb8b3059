# Blog API v3

API backend cho hệ thống blog với kiến trúc modular, hỗ trợ multi-tenancy.

## Tổng Quan

Blog API v3 được xây dựng với Go, sử dụng kiến trúc clean architecture với các module độc lập. Dự án được thiết kế để tránh circular dependencies và dễ dàng mở rộng.

## Cấu Trúc Dự Án

```
blog-api-v3/
├── cmd/                    # Entry points cho ứng dụng
│   └── server/            # Main application server
├── internal/              # Private application code
│   ├── config/           # Configuration
│   ├── middleware/       # HTTP middleware
│   └── modules/          # Business logic modules
│       ├── auth/         # Authentication module
│       ├── user/         # User management module
│       ├── tenant/       # Multi-tenancy module
│       ├── website/      # Website management module
│       └── onboarding/   # User onboarding module
├── pkg/                   # Public packages (có thể được sử dụng bởi external projects)
│   ├── database/         # Database utilities
│   ├── http/            # HTTP utilities
│   ├── utils/           # Common utilities
│   ├── validator/       # Validation utilities
│   └── types/           # Shared types
├── docs/                  # Documentation
├── scripts/              # Build and deployment scripts
├── tests/                # Test files
│   ├── unit/            # Unit tests
│   └── integration/     # Integration tests
└── Makefile             # Build automation
```

## Yêu Cầu Hệ Thống

- Go 1.21 hoặc mới hơn
- MySQL 8.0 hoặc mới hơn
- Make (cho build automation)

## Cài Đặt

1. Clone repository:
```bash
git clone https://github.com/yourusername/blog-api-v3.git
cd blog-api-v3
```

2. Cài đặt dependencies:
```bash
make deps
```

3. Cấu hình environment variables:
```bash
cp .env.example .env
# Chỉnh sửa .env với thông tin database của bạn
```

4. Chạy migrations:
```bash
make migrate-up
```

## Development

### Chạy server ở chế độ development:
```bash
make dev
```

### Build ứng dụng:
```bash
make build
```

### Chạy tests:
```bash
make test
```

### Kiểm tra code quality:
```bash
make check
```

## API Documentation

API documentation được generate tự động bằng Swagger. Sau khi chạy server, truy cập:
- Swagger UI: http://localhost:9077/swagger/index.html

Để regenerate documentation:
```bash
make swagger
```

## Modules

### Auth Module
Xử lý authentication và authorization cho hệ thống.

### User Module
Quản lý thông tin người dùng và profile.

### Tenant Module
Hỗ trợ multi-tenancy cho phép nhiều organization sử dụng chung hệ thống.

### Website Module
Quản lý thông tin các website/blog.

### Onboarding Module
Xử lý quy trình đăng ký và onboarding người dùng mới.

## Contributing

Vui lòng đọc [CONTRIBUTING.md](docs/CONTRIBUTING.md) để biết thêm chi tiết về quy trình đóng góp.

## License

[MIT License](LICENSE)