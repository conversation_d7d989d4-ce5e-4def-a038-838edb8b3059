<!-- BACKLOG.MD GUIDELINES START -->
# Instructions for the usage of Backlog.md CLI Tool

## 1. Source of Truth

- Tasks live under **`backlog/tasks/`** (drafts under **`backlog/drafts/`**).
- Every implementation decision starts with reading the corresponding Markdown task file.
- Project documentation is in **`backlog/docs/`**.
- Project decisions are in **`backlog/decisions/`**.

## 2. Defining Tasks

### **Title**

Use a clear brief title that summarizes the task.

### **Description**: (The **"why"**)

Provide a concise summary of the task purpose and its goal. Do not add implementation details here. It
should explain the purpose and context of the task. Code snippets should be avoided.

### **Acceptance Criteria**: (The **"what"**)

List specific, measurable outcomes that define what means to reach the goal from the description. Use checkboxes (`- [ ]`) for tracking.
When defining `## Acceptance Criteria` for a task, focus on **outcomes, behaviors, and verifiable requirements** rather
than step-by-step implementation details.
Acceptance Criteria (AC) define *what* conditions must be met for the task to be considered complete.
They should be testable and confirm that the core purpose of the task is achieved.
**Key Principles for Good ACs:**

- **Outcome-Oriented:** Focus on the result, not the method.
- **Testable/Verifiable:** Each criterion should be something that can be objectively tested or verified.
- **Clear and Concise:** Unambiguous language.
- **Complete:** Collectively, ACs should cover the scope of the task.
- **User-Focused (where applicable):** Frame ACs from the perspective of the end-user or the system's external behavior.

    - *Good Example:* "- [ ] User can successfully log in with valid credentials."
    - *Good Example:* "- [ ] System processes 1000 requests per second without errors."
    - *Bad Example (Implementation Step):* "- [ ] Add a new function `handleLogin()` in `auth.ts`."

### Task file

Once a task is created it will be stored in `backlog/tasks/` directory as a Markdown file with the format
`task-<id> - <title>.md` (e.g. `task-42 - Add GraphQL resolver.md`).

### Additional task requirements

- Tasks must be **atomic** and **testable**. If a task is too large, break it down into smaller subtasks.
  Each task should represent a single unit of work that can be completed in a single PR.

- **Never** reference tasks that are to be done in the future or that are not yet created. You can only reference
  previous
  tasks (id < current task id).

- When creating multiple tasks, ensure they are **independent** and they do not depend on future tasks.   
  Example of wrong tasks splitting: task 1: "Add API endpoint for user data", task 2: "Define the user model and DB
  schema".  
  Example of correct tasks splitting: task 1: "Add system for handling API requests", task 2: "Add user model and DB
  schema", task 3: "Add API endpoint for user data".

## 3. Recommended Task Anatomy

```markdown
# task‑42 - Add GraphQL resolver

## Description (the why)

Short, imperative explanation of the goal of the task and why it is needed.

## Acceptance Criteria (the what)

- [ ] Resolver returns correct data for happy path
- [ ] Error response matches REST
- [ ] P95 latency ≤ 50 ms under 100 RPS

## Implementation Plan (the how)

1. Research existing GraphQL resolver patterns
2. Implement basic resolver with error handling
3. Add performance monitoring
4. Write unit and integration tests
5. Benchmark performance under load

## Implementation Notes (only added after working on the task)

- Approach taken
- Features implemented or modified
- Technical decisions and trade-offs
- Modified or added files
```

## 6. Implementing Tasks

Mandatory sections for every task:

- **Implementation Plan**: (The **"how"**) Outline the steps to achieve the task. Because the implementation details may
  change after the task is created, **the implementation notes must be added only after putting the task in progress**
  and before starting working on the task.
- **Implementation Notes**: Document your approach, decisions, challenges, and any deviations from the plan. This
  section is added after you are done working on the task. It should summarize what you did and why you did it. Keep it
  concise but informative.

**IMPORTANT**: Do not implement anything else that deviates from the **Acceptance Criteria**. If you need to
implement something that is not in the AC, update the AC first and then implement it or create a new task for it.

## 2. Typical Workflow

```bash
# 1 Identify work
backlog task list -s "To Do" --plain

# 2 Read details & documentation
backlog task 42 --plain
# Read also all documentation files in `backlog/docs/` directory.
# Read also all decision files in `backlog/decisions/` directory.

# 3 Start work: assign yourself & move column
backlog task edit 42 -a @{yourself} -s "In Progress"

# 4 Add implementation plan before starting
backlog task edit 42 --plan "1. Analyze current implementation\n2. Identify bottlenecks\n3. Refactor in phases"

# 5 Break work down if needed by creating subtasks or additional tasks
backlog task create "Refactor DB layer" -p 42 -a @{yourself} -d "Description" --ac "Tests pass,Performance improved"

# 6 Complete and mark Done
backlog task edit 42 -s Done --notes "Implemented GraphQL resolver with error handling and performance monitoring"
```

### 7. Final Steps Before Marking a Task as Done

Always ensure you have:

1. ✅ Marked all acceptance criteria as completed (change `- [ ]` to `- [x]`)
2. ✅ Added an `## Implementation Notes` section documenting your approach
3. ✅ Run all tests and linting checks
4. ✅ Updated relevant documentation

## 8. Definition of Done (DoD)

A task is **Done** only when **ALL** of the following are complete:

1. **Acceptance criteria** checklist in the task file is fully checked (all `- [ ]` changed to `- [x]`).
2. **Implementation plan** was followed or deviations were documented in Implementation Notes.
3. **Automated tests** (unit + integration) cover new logic.
4. **Static analysis**: linter & formatter succeed.
5. **Documentation**:
    - All relevant docs updated (any relevant README file, backlog/docs, backlog/decisions, etc.).
    - Task file **MUST** have an `## Implementation Notes` section added summarising:
        - Approach taken
        - Features implemented or modified
        - Technical decisions and trade-offs
        - Modified or added files
6. **Review**: self review code.
7. **Task hygiene**: status set to **Done** via CLI (`backlog task edit <id> -s Done`).
8. **No regressions**: performance, security and licence checks green.

⚠️ **IMPORTANT**: Never mark a task as Done without completing ALL items above.

## 9. Handy CLI Commands

| Purpose          | Command                                                                |
|------------------|------------------------------------------------------------------------|
| Create task      | `backlog task create "Add OAuth"`                                      |
| Create with desc | `backlog task create "Feature" -d "Enables users to use this feature"` |
| Create with AC   | `backlog task create "Feature" --ac "Must work,Must be tested"`        |
| Create with deps | `backlog task create "Feature" --dep task-1,task-2`                    |
| Create sub task  | `backlog task create -p 14 "Add Google auth"`                          |
| List tasks       | `backlog task list --plain`                                            |
| View detail      | `backlog task 7 --plain`                                               |
| Edit             | `backlog task edit 7 -a @{yourself} -l auth,backend`                   |
| Add plan         | `backlog task edit 7 --plan "Implementation approach"`                 |
| Add AC           | `backlog task edit 7 --ac "New criterion,Another one"`                 |
| Add deps         | `backlog task edit 7 --dep task-1,task-2`                              |
| Add notes        | `backlog task edit 7 --notes "We added this and that feature because"` |
| Mark as done     | `backlog task edit 7 -s "Done"`                                        |
| Archive          | `backlog task archive 7`                                               |
| Draft flow       | `backlog draft create "Spike GraphQL"` → `backlog draft promote 3.1`   |
| Demote to draft  | `backlog task demote <task-id>`                                        |

## 10. Tips for AI Agents

- **Always use `--plain` flag** when listing or viewing tasks for AI-friendly text output instead of using Backlog.md
  interactive UI.
- When users mention to create a task, they mean to create a task using Backlog.md CLI tool.

<!-- BACKLOG.MD GUIDELINES END -->

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

# Database System - MYSQL 8 ONLY

**IMPORTANT**: This project uses **MySQL 8 ONLY**. All PostgreSQL support has been removed.

## MySQL Version Requirements
- MySQL 8.0.16 or higher (for full CHECK constraint support)
- InnoDB storage engine (default)
- utf8mb4 character set for full Unicode support

## Database Design Conventions

### Data Types
- **Use INT UNSIGNED for all ID fields** - Do NOT use BIGINT
  - INT UNSIGNED supports values up to 4,294,967,295 (over 4 billion)
  - This is sufficient for all primary keys and foreign keys
  - Example: `id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY`
  
### Table Structure
- **Separate feature_catalog from tenant-specific tables**
  - `feature_catalog` is a global table and should have its own migration file
  - `tenant_features` is tenant-specific and references feature_catalog
  
### Migration Best Practices
- **One table per migration file** for better organization
- **Split up and down migrations** into separate .up.sql and .down.sql files
- **Use proper golang-migrate naming**: `XXX_description.up.sql` and `XXX_description.down.sql`

## Migration Location
All database migrations are located in: `internal/database/migrations/`

The migrations are organized by module:
- `tenant/` - Multi-tenancy core tables (001-005)
  - 001: tenants table
  - 002: tenant_plans table  
  - 003: tenant_settings table
  - 004: tenant_features table
  - 005: feature_catalog table (global features)
- `website/` - Website management
- `user/` - User management
- `auth/` - Authentication
- `rbac/` - Role-based access control
- `onboarding/` - User onboarding

## MySQL-Specific Features Used
1. **JSON Data Type** - Native JSON support for flexible data storage
2. **CHECK Constraints** - Data validation at database level
3. **ON UPDATE CURRENT_TIMESTAMP** - Automatic timestamp updates
4. **Do NOT use stored procedures** - Handle initialization logic in application code
5. **Do NOT put seed data in migrations** - Use the seeding system instead

## Table Naming Convention
- **All tables MUST have module prefix** - This ensures clear separation between modules
  - Tenant module: `tenants`, `tenant_plans`, `tenant_settings`, `tenant_features`
  - Website module: `websites`, `website_settings`, `website_themes`
  - User module: `users`, `user_profiles`, `user_preferences`, `user_social_links`
  - Auth module: `auth_sessions`, `auth_tokens`, `auth_oauth_providers`, `auth_password_resets`, `auth_login_attempts`, `auth_token_blacklist`, `auth_password_history`, `auth_oauth_connections`
  - RBAC module: `rbac_roles`, `rbac_permissions`, `rbac_role_permissions`, `rbac_user_roles`, `rbac_permission_groups`
  - Onboarding module: `onboarding_journeys`, `onboarding_steps`, `onboarding_progress`, `onboarding_templates`, `onboarding_analytics`

## Migration System Guidelines

### **Migration File Structure**
- **One migration file = One table ONLY** - Never create multiple tables in a single migration
- **Filename-based tracking**: Migrations tracked by filename (e.g., `001_create_tenants_table`) not version numbers
- **Module-specific numbering**: Each module uses its own numbering range:
  - Tenant module: 001-099
  - Website module: 101-199  
  - User module: 201-299
  - Auth module: 301-399
  - RBAC module: 401-499
  - Onboarding module: 501-599
- **Directory structure**: Use alphabetic prefixes for proper dependency order:
  - `a_tenant/` → `b_website/` → `c_user/` → `d_auth/` → `e_rbac/` → `f_onboarding/`

### **Migration Best Practices**
- **Always add module prefix to table names** - Auth module MUST use `auth_` prefix
- **Use descriptive migration names** - `001_create_sessions_table` not `001_sessions`
- **Include both .up.sql and .down.sql files** - Rollback support is mandatory
- **Test migration rollback** - Always verify down migration works before marking complete
- **Update constraint/index names** - Match table prefix (e.g., `fk_auth_sessions_user_id`)

### **Custom Migration Manager**
- Uses filename-based tracking instead of version/dirty system
- Supports nested directory structure with proper ordering
- Automatic .env file loading for database connection
- No dirty state issues - migrations are atomic
- Module-specific migration support with `-module` flag

### **Commands**
```bash
# Run all migrations
make migrate-up

# Run specific module migrations  
make migrate-up MODULE=d_auth

# Check migration status
make migrate-status

# Rollback last migration
make migrate-down

# Check specific module status
make migrate-status MODULE=d_auth
```

## Important Notes
- Do NOT add PostgreSQL support or migrations
- All new migrations must be MySQL 8 compatible
- Use JSON type with proper defaults: `JSON DEFAULT (JSON_OBJECT())` or `JSON DEFAULT (JSON_ARRAY())`
- Always test migrations with rollback before marking as complete
- Never create multiple tables in a single migration file

## Soft Delete Strategy - Status-Based Approach

**IMPORTANT**: This project uses **status-based soft deletes** instead of `deleted_at` timestamps.

### Why Status-Based Instead of deleted_at?
- **Better Performance**: Indexed enum fields perform better than NULL timestamp checks
- **More Flexible**: Supports multiple states beyond just deleted/not deleted  
- **Clearer Business Logic**: Status values are more intuitive and maintainable
- **Consistent Architecture**: All modules use the same soft delete strategy

### Implementation Guidelines
- **Status Field**: Use `status` ENUM field with 'deleted' as one of the options
- **Status Values**: Common patterns:
  - Users: `'active', 'inactive', 'suspended', 'pending_verification', 'deleted'`
  - Tenants: `'active', 'suspended', 'inactive', 'trial', 'deleted'`  
  - Posts: `'draft', 'published', 'archived', 'scheduled', 'deleted'`
  - Comments: `'pending', 'approved', 'rejected', 'spam', 'deleted'`

### Query Patterns
- **Instead of**: `WHERE deleted_at IS NULL`
- **Use**: `WHERE status != 'deleted'`

- **Instead of**: `UPDATE table SET deleted_at = NOW() WHERE id = ?`
- **Use**: `UPDATE table SET status = 'deleted' WHERE id = ?`

### Migration Requirements
- **DO NOT include `deleted_at` fields** in new migrations
- **Use status ENUM fields** with appropriate values including 'deleted'
- **Index status fields** for query performance
- **Remove deleted_at references** from existing documentation and code

### Model Definitions
- **DO NOT use GORM's DeletedAt** soft delete feature
- **Use custom status field** with appropriate validation
- **Implement custom soft delete logic** in repositories and services