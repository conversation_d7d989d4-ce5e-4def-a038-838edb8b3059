---
id: task-23
title: change bigint to int UNSIGNED
status: Done
assignee:
  - '@claude'
created_date: '2025-07-15'
updated_date: '2025-07-15'
labels: []
dependencies: []
---

## Description

search bigint -> sửa thành int UNSIGNED

## Implementation Notes

Changed all BIGINT UNSIGNED to INT UNSIGNED:\n- Rolled back all 5 tenant migrations\n- Updated all migration files (001-005) to use INT UNSIGNED\n- Re-applied all migrations successfully\n- Re-ran seeders to populate data\n- Updated documentation files for consistency:\n  - docs/modules/rbac/models-schema.md (19 replacements)\n  - docs/modules/website/models-schema.md (7 replacements)\n  - docs/database/migrations.md (8 replacements)\n- All database tables now use INT UNSIGNED for IDs and foreign keys
