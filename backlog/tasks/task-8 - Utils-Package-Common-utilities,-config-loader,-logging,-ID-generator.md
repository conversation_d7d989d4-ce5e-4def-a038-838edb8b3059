---
id: task-8
title: 'Utils Package - Common utilities, config loader, logging, ID generator'
status: Done
assignee:
  - '@max'
created_date: '2025-07-15'
updated_date: '2025-07-15'
labels: []
dependencies: []
---

## Description

Build a utilities package containing common helper functions, configuration loading system, structured logging interface, and unique ID generation capabilities that will be used across all modules and packages.

## Acceptance Criteria

- [x] Config loader supporting environment variables and files
- [x] Structured logging interface with levels and context
- [x] ID generator for UUIDs and custom formats
- [x] String manipulation utilities
- [x] Time utilities for formatting and parsing
- [x] Error wrapping and handling utilities
- [x] Environment detection helpers
- [x] Unit tests achieving 90% coverage

## Implementation Plan

1. Tạo config loader với environment variables và file support\n2. Implement structured logging với multiple adapters\n3. Tạo ID generator (UUID, ULID, custom formats)\n4. Implement string utilities (slug, random, etc.)\n5. Tạo time utilities và formatters\n6. Implement error handling utilities\n7. Tạo environment helpers\n8. Viết unit tests\n9. Tạo documentation

## Implementation Notes

Đã hoàn thành Utils Package với đầy đủ chức năng:\n- Config loader sử dụng Viper, hỗ trợ YAML/JSON và environment variables\n- Structured logging với Logrus, hỗ trợ JSON/text format và context propagation\n- ID generators: UUID, ULID, NanoID, short codes, API keys\n- String utilities: slugify, case conversion, validation, Vietnamese support\n- Time utilities: formatting, parsing, calculations, relative time\n- Error handling với AppError structure, stack traces, error types\n- Environment helpers: type conversion, detection, validation\n- Unit tests cho các chức năng chính\n- Documentation chi tiết với usage examples
