# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
Thumbs.db

# Environment files
.env
.env.local
.env.*.local

# Log files
*.log
logs/

# Temporary files
tmp/
temp/

# Air - Live reload for Go apps
.air.toml

# Database files
*.db
*.sqlite
*.sqlite3

# Uploaded files
uploads/

# Config files with sensitive data
config/local.yaml
config/production.yaml

# SSL certificates
*.pem
*.key
*.crt

# Profiling files
*.prof
*.mem
*.cpu

# Binary
blog-api

# Migration temporary files
*.sql-e
./bin