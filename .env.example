# Application
APP_NAME=blog-api-v3
APP_ENV=development
APP_PORT=9077
APP_HOST=0.0.0.0

# Database
DATABASE_URL=mysql://root:password@tcp(localhost:3307)/blog_api_v3?charset=utf8mb4&parseTime=True&loc=Local
DB_HOST=localhost
DB_PORT=3307
DB_NAME=blog_api_v3
DB_USER=root
DB_PASSWORD=root
DB_CHARSET=utf8mb4

# JWT
JWT_SECRET=your-secret-key-here
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# Redis (optional)
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# Storage
STORAGE_TYPE=local
STORAGE_PATH=./uploads
# For S3
# STORAGE_TYPE=s3
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=us-east-1
# AWS_BUCKET=blog-api-uploads

# Logging
LOG_LEVEL=debug
LOG_FORMAT=json

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_RPS=10
RATE_LIMIT_BURST=20

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090

# Feature Flags
FEATURE_MULTI_TENANCY=true
FEATURE_ONBOARDING=true
FEATURE_NOTIFICATIONS=true