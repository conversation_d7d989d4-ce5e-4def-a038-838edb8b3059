package helpers

import (
	"fmt"
	"math/rand"
	"time"
)

// TestDataGenerator provides utilities for generating test data
type TestDataGenerator struct {
	rand *rand.Rand
}

// NewTestDataGenerator creates a new test data generator
func NewTestDataGenerator() *TestDataGenerator {
	return &TestDataGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// User data generators

// GenerateTestUser creates test user data
func (g *TestDataGenerator) GenerateTestUser() RegisterRequest {
	id := g.rand.Intn(10000)
	return RegisterRequest{
		Email:     fmt.Sprintf("<EMAIL>", id),
		Password:  "TestPassword123!",
		FirstName: fmt.Sprintf("TestUser%d", id),
		LastName:  "LastName",
	}
}

// GenerateTestUserWithEmail creates test user with specific email
func (g *TestDataGenerator) GenerateTestUserWithEmail(email string) RegisterRequest {
	return RegisterRequest{
		Email:     email,
		Password:  "TestPassword123!",
		FirstName: "TestUser",
		LastName:  "LastName",
	}
}

// GenerateAdminUser creates admin user data
func (g *TestDataGenerator) GenerateAdminUser() RegisterRequest {
	id := g.rand.Intn(1000)
	return RegisterRequest{
		Email:     fmt.Sprintf("<EMAIL>", id),
		Password:  "AdminPassword123!",
		FirstName: fmt.Sprintf("Admin%d", id),
		LastName:  "Administrator",
	}
}

// Tenant data generators

// GenerateTestTenant creates test tenant data
func (g *TestDataGenerator) GenerateTestTenant() TenantRequest {
	id := g.rand.Intn(10000)
	return TenantRequest{
		Name:        fmt.Sprintf("Test Tenant %d", id),
		Slug:        fmt.Sprintf("test-tenant-%d", id),
		Description: fmt.Sprintf("Test tenant description for tenant %d", id),
		Plan:        "basic",
	}
}

// GenerateTestTenantWithSlug creates test tenant with specific slug
func (g *TestDataGenerator) GenerateTestTenantWithSlug(slug string) TenantRequest {
	return TenantRequest{
		Name:        fmt.Sprintf("Test Tenant %s", slug),
		Slug:        slug,
		Description: fmt.Sprintf("Test tenant with slug %s", slug),
		Plan:        "basic",
	}
}

// GeneratePremiumTenant creates premium tenant data
func (g *TestDataGenerator) GeneratePremiumTenant() TenantRequest {
	id := g.rand.Intn(1000)
	return TenantRequest{
		Name:        fmt.Sprintf("Premium Tenant %d", id),
		Slug:        fmt.Sprintf("premium-tenant-%d", id),
		Description: fmt.Sprintf("Premium tenant description for tenant %d", id),
		Plan:        "premium",
	}
}

// Onboarding data generators

// GenerateOnboardingStepData creates onboarding step data
func (g *TestDataGenerator) GenerateOnboardingStepData(stepType string) map[string]interface{} {
	switch stepType {
	case "profile_setup":
		return map[string]interface{}{
			"company_size":     "10-50",
			"industry":         "technology",
			"use_case":         "content_management",
			"marketing_source": "google_search",
		}
	case "tenant_configuration":
		return map[string]interface{}{
			"timezone":     "UTC",
			"currency":     "USD",
			"language":     "en",
			"date_format":  "YYYY-MM-DD",
			"time_format":  "24h",
		}
	case "integration_setup":
		return map[string]interface{}{
			"email_provider":    "sendgrid",
			"storage_provider":  "s3",
			"analytics_enabled": true,
			"notifications_enabled": true,
		}
	case "team_invitation":
		return map[string]interface{}{
			"team_members": []map[string]interface{}{
				{
					"email": "<EMAIL>",
					"role":  "editor",
				},
				{
					"email": "<EMAIL>", 
					"role":  "viewer",
				},
			},
		}
	default:
		return map[string]interface{}{
			"step_type": stepType,
			"completed": true,
		}
	}
}

// Utility methods

// GenerateRandomString generates a random string of specified length
func (g *TestDataGenerator) GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[g.rand.Intn(len(charset))]
	}
	return string(b)
}

// GenerateRandomEmail generates a random email address
func (g *TestDataGenerator) GenerateRandomEmail() string {
	username := g.GenerateRandomString(8)
	domain := g.GenerateRandomString(6)
	return fmt.Sprintf("%s@%s.com", username, domain)
}

// GenerateRandomSlug generates a random slug
func (g *TestDataGenerator) GenerateRandomSlug() string {
	prefix := g.GenerateRandomString(6)
	suffix := g.rand.Intn(1000)
	return fmt.Sprintf("%s-%d", prefix, suffix)
}

// PredefinedTestData provides commonly used test data sets
type PredefinedTestData struct{}

// NewPredefinedTestData creates predefined test data sets
func NewPredefinedTestData() *PredefinedTestData {
	return &PredefinedTestData{}
}

// GetTestUsers returns a set of predefined test users
func (p *PredefinedTestData) GetTestUsers() []RegisterRequest {
	return []RegisterRequest{
		{
			Email:     "<EMAIL>",
			Password:  "AlicePassword123!",
			FirstName: "Alice",
			LastName:  "Johnson",
		},
		{
			Email:     "<EMAIL>",
			Password:  "BobPassword123!",
			FirstName: "Bob",
			LastName:  "Smith",
		},
		{
			Email:     "<EMAIL>",
			Password:  "CharliePassword123!",
			FirstName: "Charlie",
			LastName:  "Brown",
		},
	}
}

// GetTestTenants returns a set of predefined test tenants
func (p *PredefinedTestData) GetTestTenants() []TenantRequest {
	return []TenantRequest{
		{
			Name:        "Acme Corporation",
			Slug:        "acme-corp",
			Description: "Leading provider of innovative solutions",
			Plan:        "enterprise",
		},
		{
			Name:        "TechStart Inc",
			Slug:        "techstart",
			Description: "Startup focused on cutting-edge technology",
			Plan:        "basic",
		},
		{
			Name:        "Global Enterprises",
			Slug:        "global-ent",
			Description: "Multinational corporation with global reach",
			Plan:        "premium",
		},
	}
}

// GetInvalidTestData returns invalid data for negative testing
func (p *PredefinedTestData) GetInvalidTestData() map[string]interface{} {
	return map[string]interface{}{
		"invalid_emails": []string{
			"invalid-email",
			"@example.com",
			"user@",
			"",
		},
		"weak_passwords": []string{
			"123",
			"password",
			"12345678",
			"",
		},
		"invalid_slugs": []string{
			"",
			"slug with spaces",
			"slug@special",
			"UPPERCASE",
		},
		"invalid_plans": []string{
			"",
			"invalid_plan",
			"FREE",
		},
	}
}

// TestScenarios provides common test scenario configurations
type TestScenarios struct{}

// NewTestScenarios creates test scenario configurations
func NewTestScenarios() *TestScenarios {
	return &TestScenarios{}
}

// GetAuthFlowScenarios returns authentication flow test scenarios
func (s *TestScenarios) GetAuthFlowScenarios() map[string]interface{} {
	return map[string]interface{}{
		"successful_registration": map[string]interface{}{
			"description": "User successfully registers with valid data",
			"steps": []string{
				"register_user",
				"verify_email",
				"login_user",
			},
		},
		"successful_login": map[string]interface{}{
			"description": "User successfully logs in with valid credentials",
			"steps": []string{
				"login_user",
				"verify_token",
				"access_protected_resource",
			},
		},
		"token_refresh": map[string]interface{}{
			"description": "User successfully refreshes expired token",
			"steps": []string{
				"login_user",
				"wait_for_token_expiry",
				"refresh_token",
				"verify_new_token",
			},
		},
	}
}

// GetOnboardingFlowScenarios returns onboarding flow test scenarios
func (s *TestScenarios) GetOnboardingFlowScenarios() map[string]interface{} {
	return map[string]interface{}{
		"complete_basic_onboarding": map[string]interface{}{
			"description": "User completes basic onboarding flow",
			"flow_type":   "basic",
			"steps": []string{
				"profile_setup",
				"tenant_configuration",
				"integration_setup",
			},
		},
		"complete_advanced_onboarding": map[string]interface{}{
			"description": "User completes advanced onboarding flow",
			"flow_type":   "advanced",
			"steps": []string{
				"profile_setup",
				"tenant_configuration",
				"integration_setup",
				"team_invitation",
				"billing_setup",
			},
		},
	}
}

// GetTenantManagementScenarios returns tenant management test scenarios
func (s *TestScenarios) GetTenantManagementScenarios() map[string]interface{} {
	return map[string]interface{}{
		"create_and_manage_tenant": map[string]interface{}{
			"description": "Create tenant and perform management operations",
			"steps": []string{
				"create_tenant",
				"update_tenant",
				"add_members",
				"manage_roles",
			},
		},
		"tenant_plan_upgrade": map[string]interface{}{
			"description": "Upgrade tenant from basic to premium plan",
			"steps": []string{
				"create_basic_tenant",
				"upgrade_to_premium",
				"verify_premium_features",
			},
		},
	}
}