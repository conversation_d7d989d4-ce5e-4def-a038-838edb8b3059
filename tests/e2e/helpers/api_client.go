package helpers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
)

// APIClient wraps resty client for E2E testing
type APIClient struct {
	client *resty.Client
	config *config.E2EConfig
	token  string
}

// NewAPIClient creates a new API client for testing
func NewAPIClient(cfg *config.E2EConfig) *APIClient {
	client := resty.New()
	
	// Set base configuration
	client.SetHostURL(cfg.BaseURL)
	client.SetTimeout(cfg.TestTimeout)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(1 * time.Second)
	
	// Set debug mode if enabled
	if cfg.DebugMode {
		client.SetDebug(true)
	}
	
	return &APIClient{
		client: client,
		config: cfg,
	}
}

// SetAuthToken sets the authentication token for subsequent requests
func (c *APIClient) SetAuthToken(token string) {
	c.token = token
	c.client.SetAuthToken(token)
}

// ClearAuth clears the authentication token
func (c *APIClient) ClearAuth() {
	c.token = ""
	c.client.SetAuthToken("")
}

// Health endpoints

// GetHealth performs a health check
func (c *APIClient) GetHealth() (*resty.Response, error) {
	return c.client.R().
		SetHeader("Content-Type", "application/json").
		Get("/health/status")
}

// GetHealthReady checks if the server is ready
func (c *APIClient) GetHealthReady() (*resty.Response, error) {
	return c.client.R().
		SetHeader("Content-Type", "application/json").
		Get("/health/ready")
}

// Authentication endpoints

// AuthRequest represents authentication request
type AuthRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegisterRequest represents user registration request
type RegisterRequest struct {
	Email     string `json:"email"`
	Password  string `json:"password"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// AuthResponse represents authentication response
type AuthResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	User         User   `json:"user"`
}

// User represents user data in responses
type User struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Status    string `json:"status"`
	CreatedAt string `json:"created_at"`
}

// Login performs user authentication
func (c *APIClient) Login(email, password string) (*AuthResponse, *resty.Response, error) {
	var authResp AuthResponse
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(AuthRequest{
			Email:    email,
			Password: password,
		}).
		SetResult(&authResp).
		Post("/api/cms/v1/auth/login")
	
	if err != nil {
		return nil, resp, err
	}
	
	// Set auth token if login successful
	if resp.StatusCode() == http.StatusOK && authResp.AccessToken != "" {
		c.SetAuthToken(authResp.AccessToken)
	}
	
	return &authResp, resp, nil
}

// Register creates a new user account
func (c *APIClient) Register(req RegisterRequest) (*AuthResponse, *resty.Response, error) {
	var authResp AuthResponse
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetResult(&authResp).
		Post("/api/cms/v1/auth/register")
	
	return &authResp, resp, err
}

// RefreshToken refreshes the access token
func (c *APIClient) RefreshToken(refreshToken string) (*AuthResponse, *resty.Response, error) {
	var authResp AuthResponse
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]string{
			"refresh_token": refreshToken,
		}).
		SetResult(&authResp).
		Post("/api/cms/v1/auth/refresh")
	
	return &authResp, resp, err
}

// Logout logs out the current user
func (c *APIClient) Logout() (*resty.Response, error) {
	defer c.ClearAuth()
	
	return c.client.R().
		SetHeader("Content-Type", "application/json").
		Post("/api/cms/v1/auth/logout")
}

// GetProfile gets the current user's profile
func (c *APIClient) GetProfile() (*User, *resty.Response, error) {
	var user User
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&user).
		Get("/api/cms/v1/auth/profile")
	
	return &user, resp, err
}

// User management endpoints

// GetUser gets a user by ID
func (c *APIClient) GetUser(userID string) (*User, *resty.Response, error) {
	var user User
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&user).
		Get(fmt.Sprintf("/api/cms/v1/users/%s", userID))
	
	return &user, resp, err
}

// SearchUsers searches for users
func (c *APIClient) SearchUsers(query string) ([]User, *resty.Response, error) {
	var users []User
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParam("q", query).
		SetResult(&users).
		Get("/api/cms/v1/users/search")
	
	return users, resp, err
}

// Tenant management endpoints

// TenantRequest represents tenant creation request
type TenantRequest struct {
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description"`
	Plan        string `json:"plan"`
}

// Tenant represents tenant data
type Tenant struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description"`
	Plan        string `json:"plan"`
	Status      string `json:"status"`
	CreatedAt   string `json:"created_at"`
}

// CreateTenant creates a new tenant
func (c *APIClient) CreateTenant(req TenantRequest) (*Tenant, *resty.Response, error) {
	var tenant Tenant
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetResult(&tenant).
		Post("/api/cms/v1/tenants")
	
	return &tenant, resp, err
}

// GetTenant gets a tenant by ID
func (c *APIClient) GetTenant(tenantID string) (*Tenant, *resty.Response, error) {
	var tenant Tenant
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&tenant).
		Get(fmt.Sprintf("/api/cms/v1/tenants/%s", tenantID))
	
	return &tenant, resp, err
}

// GetMyTenants gets current user's tenants
func (c *APIClient) GetMyTenants() ([]Tenant, *resty.Response, error) {
	var tenants []Tenant
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&tenants).
		Get("/api/cms/v1/tenants/my")
	
	return tenants, resp, err
}

// Onboarding endpoints

// OnboardingFlow represents onboarding flow data
type OnboardingFlow struct {
	ID          string                 `json:"id"`
	TenantID    string                 `json:"tenant_id"`
	UserID      string                 `json:"user_id"`
	FlowType    string                 `json:"flow_type"`
	CurrentStep int                    `json:"current_step"`
	TotalSteps  int                    `json:"total_steps"`
	Completed   bool                   `json:"completed"`
	Data        map[string]interface{} `json:"data"`
	CreatedAt   string                 `json:"created_at"`
}

// StartOnboarding starts an onboarding flow
func (c *APIClient) StartOnboarding(tenantID, flowType string) (*OnboardingFlow, *resty.Response, error) {
	var flow OnboardingFlow
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]interface{}{
			"tenant_id":  tenantID,
			"flow_type": flowType,
		}).
		SetResult(&flow).
		Post("/api/cms/v1/onboarding/flows")
	
	return &flow, resp, err
}

// GetOnboardingFlow gets an onboarding flow by ID
func (c *APIClient) GetOnboardingFlow(flowID string) (*OnboardingFlow, *resty.Response, error) {
	var flow OnboardingFlow
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&flow).
		Get(fmt.Sprintf("/api/cms/v1/onboarding/flows/%s", flowID))
	
	return &flow, resp, err
}

// UpdateOnboardingStep updates a step in onboarding flow
func (c *APIClient) UpdateOnboardingStep(flowID string, stepData map[string]interface{}) (*OnboardingFlow, *resty.Response, error) {
	var flow OnboardingFlow
	
	resp, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(stepData).
		SetResult(&flow).
		Put(fmt.Sprintf("/api/cms/v1/onboarding/flows/%s/step", flowID))
	
	return &flow, resp, err
}

// Utility methods

// WaitForServer waits for the server to be ready
func (c *APIClient) WaitForServer(maxWait time.Duration) error {
	start := time.Now()
	for time.Since(start) < maxWait {
		resp, err := c.GetHealthReady()
		if err == nil && resp.StatusCode() == http.StatusOK {
			return nil
		}
		time.Sleep(1 * time.Second)
	}
	return fmt.Errorf("server not ready after %v", maxWait)
}

// ParseErrorResponse parses error response from API
func (c *APIClient) ParseErrorResponse(resp *resty.Response) map[string]interface{} {
	var errorResp map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &errorResp); err != nil {
		return map[string]interface{}{
			"error": "Failed to parse error response",
			"body":  string(resp.Body()),
		}
	}
	return errorResp
}