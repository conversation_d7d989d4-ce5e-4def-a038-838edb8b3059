package helpers

import (
	"encoding/json"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// HTTPAssertion provides HTTP-specific assertions for E2E tests
type HTTPAssertion struct {
	t    *testing.T
	resp *resty.Response
}

// NewHTTPAssertion creates a new HTTP assertion helper
func NewHTTPAssertion(t *testing.T, resp *resty.Response) *HTTPAssertion {
	return &HTTPAssertion{
		t:    t,
		resp: resp,
	}
}

// StatusCode asserts the HTTP status code
func (a *HTTPAssertion) StatusCode(expected int) *HTTPAssertion {
	assert.Equal(a.t, expected, a.resp.StatusCode(), 
		"Expected status code %d but got %d. Response: %s", 
		expected, a.resp.StatusCode(), string(a.resp.Body()))
	return a
}

// StatusOK asserts that the status code is 200
func (a *HTTPAssertion) StatusOK() *HTTPAssertion {
	return a.StatusCode(http.StatusOK)
}

// StatusCreated asserts that the status code is 201
func (a *HTTPAssertion) StatusCreated() *HTTPAssertion {
	return a.StatusCode(http.StatusCreated)
}

// StatusBadRequest asserts that the status code is 400
func (a *HTTPAssertion) StatusBadRequest() *HTTPAssertion {
	return a.StatusCode(http.StatusBadRequest)
}

// StatusUnauthorized asserts that the status code is 401
func (a *HTTPAssertion) StatusUnauthorized() *HTTPAssertion {
	return a.StatusCode(http.StatusUnauthorized)
}

// StatusForbidden asserts that the status code is 403
func (a *HTTPAssertion) StatusForbidden() *HTTPAssertion {
	return a.StatusCode(http.StatusForbidden)
}

// StatusNotFound asserts that the status code is 404
func (a *HTTPAssertion) StatusNotFound() *HTTPAssertion {
	return a.StatusCode(http.StatusNotFound)
}

// StatusInternalServerError asserts that the status code is 500
func (a *HTTPAssertion) StatusInternalServerError() *HTTPAssertion {
	return a.StatusCode(http.StatusInternalServerError)
}

// HasHeader asserts that a header exists
func (a *HTTPAssertion) HasHeader(headerName string) *HTTPAssertion {
	assert.NotEmpty(a.t, a.resp.Header().Get(headerName), 
		"Expected header '%s' to be present", headerName)
	return a
}

// HeaderEquals asserts that a header has a specific value
func (a *HTTPAssertion) HeaderEquals(headerName, expected string) *HTTPAssertion {
	actual := a.resp.Header().Get(headerName)
	assert.Equal(a.t, expected, actual, 
		"Expected header '%s' to be '%s' but got '%s'", 
		headerName, expected, actual)
	return a
}

// ContentType asserts the Content-Type header
func (a *HTTPAssertion) ContentType(expected string) *HTTPAssertion {
	return a.HeaderEquals("Content-Type", expected)
}

// JSONContentType asserts JSON content type
func (a *HTTPAssertion) JSONContentType() *HTTPAssertion {
	contentType := a.resp.Header().Get("Content-Type")
	assert.True(a.t, strings.Contains(contentType, "application/json"),
		"Expected JSON content type but got '%s'", contentType)
	return a
}

// BodyContains asserts that the response body contains a string
func (a *HTTPAssertion) BodyContains(substr string) *HTTPAssertion {
	body := string(a.resp.Body())
	assert.Contains(a.t, body, substr,
		"Expected response body to contain '%s'", substr)
	return a
}

// BodyNotContains asserts that the response body does not contain a string
func (a *HTTPAssertion) BodyNotContains(substr string) *HTTPAssertion {
	body := string(a.resp.Body())
	assert.NotContains(a.t, body, substr,
		"Expected response body not to contain '%s'", substr)
	return a
}

// BodyEquals asserts that the response body equals expected string
func (a *HTTPAssertion) BodyEquals(expected string) *HTTPAssertion {
	body := string(a.resp.Body())
	assert.Equal(a.t, expected, body)
	return a
}

// BodyNotEmpty asserts that the response body is not empty
func (a *HTTPAssertion) BodyNotEmpty() *HTTPAssertion {
	assert.NotEmpty(a.t, a.resp.Body(), "Expected response body not to be empty")
	return a
}

// JSONPath asserts a value at a specific JSON path
func (a *HTTPAssertion) JSONPath(path string, expected interface{}) *HTTPAssertion {
	var body map[string]interface{}
	err := json.Unmarshal(a.resp.Body(), &body)
	require.NoError(a.t, err, "Failed to parse JSON response")
	
	actual := getJSONPath(body, path)
	assert.Equal(a.t, expected, actual,
		"Expected JSON path '%s' to be '%v' but got '%v'", path, expected, actual)
	return a
}

// JSONPathExists asserts that a JSON path exists
func (a *HTTPAssertion) JSONPathExists(path string) *HTTPAssertion {
	var body map[string]interface{}
	err := json.Unmarshal(a.resp.Body(), &body)
	require.NoError(a.t, err, "Failed to parse JSON response")
	
	exists := hasJSONPath(body, path)
	assert.True(a.t, exists, "Expected JSON path '%s' to exist", path)
	return a
}

// JSONPathNotExists asserts that a JSON path does not exist
func (a *HTTPAssertion) JSONPathNotExists(path string) *HTTPAssertion {
	var body map[string]interface{}
	err := json.Unmarshal(a.resp.Body(), &body)
	require.NoError(a.t, err, "Failed to parse JSON response")
	
	exists := hasJSONPath(body, path)
	assert.False(a.t, exists, "Expected JSON path '%s' not to exist", path)
	return a
}

// ResponseTime asserts that the response time is within acceptable limits
func (a *HTTPAssertion) ResponseTime(maxDuration time.Duration) *HTTPAssertion {
	responseTime := a.resp.Time()
	assert.True(a.t, responseTime <= maxDuration,
		"Expected response time to be <= %v but got %v", maxDuration, responseTime)
	return a
}

// ValidJSON asserts that the response body is valid JSON
func (a *HTTPAssertion) ValidJSON() *HTTPAssertion {
	var js json.RawMessage
	err := json.Unmarshal(a.resp.Body(), &js)
	assert.NoError(a.t, err, "Response body is not valid JSON: %s", string(a.resp.Body()))
	return a
}

// AuthAssertion provides authentication-specific assertions
type AuthAssertion struct {
	t        *testing.T
	authResp *AuthResponse
}

// NewAuthAssertion creates a new authentication assertion helper
func NewAuthAssertion(t *testing.T, authResp *AuthResponse) *AuthAssertion {
	return &AuthAssertion{
		t:        t,
		authResp: authResp,
	}
}

// HasAccessToken asserts that access token is present
func (a *AuthAssertion) HasAccessToken() *AuthAssertion {
	assert.NotEmpty(a.t, a.authResp.AccessToken, "Expected access token to be present")
	return a
}

// HasRefreshToken asserts that refresh token is present
func (a *AuthAssertion) HasRefreshToken() *AuthAssertion {
	assert.NotEmpty(a.t, a.authResp.RefreshToken, "Expected refresh token to be present")
	return a
}

// TokenExpiresIn asserts that token expiration is reasonable
func (a *AuthAssertion) TokenExpiresIn(minSeconds, maxSeconds int) *AuthAssertion {
	assert.GreaterOrEqual(a.t, a.authResp.ExpiresIn, minSeconds,
		"Expected token to expire in at least %d seconds", minSeconds)
	assert.LessOrEqual(a.t, a.authResp.ExpiresIn, maxSeconds,
		"Expected token to expire in at most %d seconds", maxSeconds)
	return a
}

// UserHasID asserts that user has an ID
func (a *AuthAssertion) UserHasID() *AuthAssertion {
	assert.NotEmpty(a.t, a.authResp.User.ID, "Expected user to have an ID")
	return a
}

// UserHasEmail asserts that user has the expected email
func (a *AuthAssertion) UserHasEmail(expectedEmail string) *AuthAssertion {
	assert.Equal(a.t, expectedEmail, a.authResp.User.Email,
		"Expected user email to be '%s'", expectedEmail)
	return a
}

// UserHasStatus asserts that user has the expected status
func (a *AuthAssertion) UserHasStatus(expectedStatus string) *AuthAssertion {
	assert.Equal(a.t, expectedStatus, a.authResp.User.Status,
		"Expected user status to be '%s'", expectedStatus)
	return a
}

// TenantAssertion provides tenant-specific assertions
type TenantAssertion struct {
	t      *testing.T
	tenant *Tenant
}

// NewTenantAssertion creates a new tenant assertion helper
func NewTenantAssertion(t *testing.T, tenant *Tenant) *TenantAssertion {
	return &TenantAssertion{
		t:      t,
		tenant: tenant,
	}
}

// HasID asserts that tenant has an ID
func (a *TenantAssertion) HasID() *TenantAssertion {
	assert.NotEmpty(a.t, a.tenant.ID, "Expected tenant to have an ID")
	return a
}

// HasName asserts that tenant has the expected name
func (a *TenantAssertion) HasName(expectedName string) *TenantAssertion {
	assert.Equal(a.t, expectedName, a.tenant.Name,
		"Expected tenant name to be '%s'", expectedName)
	return a
}

// HasSlug asserts that tenant has the expected slug
func (a *TenantAssertion) HasSlug(expectedSlug string) *TenantAssertion {
	assert.Equal(a.t, expectedSlug, a.tenant.Slug,
		"Expected tenant slug to be '%s'", expectedSlug)
	return a
}

// HasStatus asserts that tenant has the expected status
func (a *TenantAssertion) HasStatus(expectedStatus string) *TenantAssertion {
	assert.Equal(a.t, expectedStatus, a.tenant.Status,
		"Expected tenant status to be '%s'", expectedStatus)
	return a
}

// HasPlan asserts that tenant has the expected plan
func (a *TenantAssertion) HasPlan(expectedPlan string) *TenantAssertion {
	assert.Equal(a.t, expectedPlan, a.tenant.Plan,
		"Expected tenant plan to be '%s'", expectedPlan)
	return a
}

// OnboardingAssertion provides onboarding-specific assertions
type OnboardingAssertion struct {
	t    *testing.T
	flow *OnboardingFlow
}

// NewOnboardingAssertion creates a new onboarding assertion helper
func NewOnboardingAssertion(t *testing.T, flow *OnboardingFlow) *OnboardingAssertion {
	return &OnboardingAssertion{
		t:    t,
		flow: flow,
	}
}

// HasID asserts that onboarding flow has an ID
func (a *OnboardingAssertion) HasID() *OnboardingAssertion {
	assert.NotEmpty(a.t, a.flow.ID, "Expected onboarding flow to have an ID")
	return a
}

// HasTenantID asserts that onboarding flow has the expected tenant ID
func (a *OnboardingAssertion) HasTenantID(expectedTenantID string) *OnboardingAssertion {
	assert.Equal(a.t, expectedTenantID, a.flow.TenantID,
		"Expected onboarding flow tenant ID to be '%s'", expectedTenantID)
	return a
}

// HasFlowType asserts that onboarding flow has the expected type
func (a *OnboardingAssertion) HasFlowType(expectedType string) *OnboardingAssertion {
	assert.Equal(a.t, expectedType, a.flow.FlowType,
		"Expected onboarding flow type to be '%s'", expectedType)
	return a
}

// AtStep asserts that onboarding flow is at the expected step
func (a *OnboardingAssertion) AtStep(expectedStep int) *OnboardingAssertion {
	assert.Equal(a.t, expectedStep, a.flow.CurrentStep,
		"Expected onboarding flow to be at step %d", expectedStep)
	return a
}

// HasTotalSteps asserts that onboarding flow has the expected total steps
func (a *OnboardingAssertion) HasTotalSteps(expectedTotal int) *OnboardingAssertion {
	assert.Equal(a.t, expectedTotal, a.flow.TotalSteps,
		"Expected onboarding flow to have %d total steps", expectedTotal)
	return a
}

// IsCompleted asserts that onboarding flow is completed
func (a *OnboardingAssertion) IsCompleted() *OnboardingAssertion {
	assert.True(a.t, a.flow.Completed, "Expected onboarding flow to be completed")
	return a
}

// IsNotCompleted asserts that onboarding flow is not completed
func (a *OnboardingAssertion) IsNotCompleted() *OnboardingAssertion {
	assert.False(a.t, a.flow.Completed, "Expected onboarding flow not to be completed")
	return a
}

// Helper functions for JSON path operations

func getJSONPath(data map[string]interface{}, path string) interface{} {
	parts := strings.Split(path, ".")
	current := data
	
	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}
		
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}
	
	return nil
}

func hasJSONPath(data map[string]interface{}, path string) bool {
	parts := strings.Split(path, ".")
	current := data
	
	for i, part := range parts {
		if i == len(parts)-1 {
			_, exists := current[part]
			return exists
		}
		
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return false
		}
	}
	
	return false
}

// Performance assertion helpers

// AssertResponseTime asserts that a function completes within the specified duration
func AssertResponseTime(t *testing.T, maxDuration time.Duration, fn func()) {
	start := time.Now()
	fn()
	elapsed := time.Since(start)
	
	assert.LessOrEqual(t, elapsed, maxDuration,
		"Expected operation to complete within %v but took %v", maxDuration, elapsed)
}

// AssertNoMemoryLeaks performs basic memory leak detection (placeholder)
func AssertNoMemoryLeaks(t *testing.T) {
	// TODO: Implement memory leak detection
	// This could use runtime.ReadMemStats() to check for memory growth
	t.Log("Memory leak detection not implemented yet")
}