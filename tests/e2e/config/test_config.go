package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// E2EConfig holds configuration for E2E tests
type E2EConfig struct {
	// Server configuration
	ServerHost string
	ServerPort int
	BaseURL    string
	
	// Database configuration
	DBHost     string
	DBPort     int
	DBName     string
	DBUser     string
	DBPassword string
	
	// Test configuration
	TestTimeout    time.Duration
	ParallelTests  bool
	DebugMode      bool
	CleanupEnabled bool
	
	// External services
	EmailProvider string
	StorageProvider string
}

// NewE2EConfig creates a new E2E test configuration
func NewE2EConfig() *E2EConfig {
	config := &E2EConfig{
		// Default server configuration
		ServerHost: getEnv("E2E_SERVER_HOST", "localhost"),
		ServerPort: getEnvAsInt("E2E_SERVER_PORT", 8081),
		
		// Default database configuration
		DBHost:     getEnv("E2E_DB_HOST", "localhost"),
		DBPort:     getEnvAsInt("E2E_DB_PORT", 3306),
		DBName:     getEnv("E2E_DB_NAME", "blog_api_v3_e2e_test"),
		DBUser:     getEnv("E2E_DB_USER", "root"),
		DBPassword: getEnv("E2E_DB_PASSWORD", ""),
		
		// Default test configuration
		TestTimeout:    getEnvAsDuration("E2E_TIMEOUT", 30*time.Second),
		ParallelTests:  getEnvAsBool("E2E_PARALLEL", true),
		DebugMode:      getEnvAsBool("E2E_DEBUG", false),
		CleanupEnabled: getEnvAsBool("E2E_CLEANUP", true),
		
		// Default external services
		EmailProvider:   getEnv("E2E_EMAIL_PROVIDER", "mock"),
		StorageProvider: getEnv("E2E_STORAGE_PROVIDER", "local"),
	}
	
	// Set base URL
	config.BaseURL = fmt.Sprintf("http://%s:%d", config.ServerHost, config.ServerPort)
	
	return config
}

// GetAPIURL returns the full URL for an API endpoint
func (c *E2EConfig) GetAPIURL(path string) string {
	return fmt.Sprintf("%s%s", c.BaseURL, path)
}

// GetHealthURL returns the health check URL
func (c *E2EConfig) GetHealthURL() string {
	return c.GetAPIURL("/health/status")
}

// GetAuthURL returns the authentication base URL
func (c *E2EConfig) GetAuthURL() string {
	return c.GetAPIURL("/api/cms/v1/auth")
}

// GetUsersURL returns the users API base URL
func (c *E2EConfig) GetUsersURL() string {
	return c.GetAPIURL("/api/cms/v1/users")
}

// GetTenantsURL returns the tenants API base URL
func (c *E2EConfig) GetTenantsURL() string {
	return c.GetAPIURL("/api/cms/v1/tenants")
}

// GetOnboardingURL returns the onboarding API base URL
func (c *E2EConfig) GetOnboardingURL() string {
	return c.GetAPIURL("/api/cms/v1/onboarding")
}

// Helper functions to read environment variables

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	valueStr := getEnv(key, "")
	if value, err := time.ParseDuration(valueStr); err == nil {
		return value
	}
	return defaultValue
}