{"test_users": [{"id": "test-user-1", "email": "<EMAIL>", "password": "AliceTestPassword123!", "first_name": "<PERSON>", "last_name": "<PERSON>", "role": "user", "status": "active", "verified": true}, {"id": "test-user-2", "email": "<EMAIL>", "password": "BobTestPassword123!", "first_name": "<PERSON>", "last_name": "<PERSON>", "role": "user", "status": "active", "verified": true}, {"id": "test-admin-1", "email": "<EMAIL>", "password": "AdminTestPassword123!", "first_name": "Admin", "last_name": "User", "role": "admin", "status": "active", "verified": true}, {"id": "test-editor-1", "email": "<EMAIL>", "password": "EditorTestPassword123!", "first_name": "Editor", "last_name": "User", "role": "editor", "status": "active", "verified": true}, {"id": "test-viewer-1", "email": "<EMAIL>", "password": "ViewerTestPassword123!", "first_name": "Viewer", "last_name": "User", "role": "viewer", "status": "active", "verified": true}], "invalid_users": [{"description": "Invalid email format", "email": "invalid-email", "password": "ValidPassword123!", "first_name": "Invalid", "last_name": "User"}, {"description": "Weak password", "email": "<EMAIL>", "password": "123", "first_name": "Weak", "last_name": "Password"}, {"description": "Missing required fields", "email": "<EMAIL>", "password": "ValidPassword123!"}], "login_credentials": {"valid": [{"email": "<EMAIL>", "password": "AliceTestPassword123!"}, {"email": "<EMAIL>", "password": "AdminTestPassword123!"}], "invalid": [{"description": "Wrong password", "email": "<EMAIL>", "password": "WrongPassword123!"}, {"description": "Non-existent user", "email": "<EMAIL>", "password": "SomePassword123!"}, {"description": "Empty credentials", "email": "", "password": ""}]}, "user_profiles": [{"user_id": "test-user-1", "bio": "Software engineer passionate about clean code and testing", "avatar_url": "https://example.com/avatars/alice.jpg", "timezone": "UTC", "language": "en", "preferences": {"email_notifications": true, "push_notifications": false, "theme": "light"}}, {"user_id": "test-user-2", "bio": "Product manager focused on user experience", "avatar_url": "https://example.com/avatars/bob.jpg", "timezone": "America/New_York", "language": "en", "preferences": {"email_notifications": false, "push_notifications": true, "theme": "dark"}}]}