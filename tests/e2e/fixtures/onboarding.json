{"onboarding_flows": [{"id": "basic_flow", "name": "Basic Onboarding", "description": "Simple onboarding flow for new users", "flow_type": "basic", "total_steps": 3, "steps": [{"step_number": 1, "step_type": "profile_setup", "title": "Complete Your Profile", "description": "Tell us a bit about yourself and your company", "required_fields": ["company_size", "industry", "use_case"], "optional_fields": ["marketing_source", "goals"]}, {"step_number": 2, "step_type": "tenant_configuration", "title": "Configure Your Workspace", "description": "Set up your workspace preferences", "required_fields": ["timezone", "language"], "optional_fields": ["currency", "date_format", "time_format"]}, {"step_number": 3, "step_type": "integration_setup", "title": "Connect Your Tools", "description": "Set up integrations with your favorite tools", "required_fields": [], "optional_fields": ["email_provider", "storage_provider", "analytics_enabled", "notifications_enabled"]}]}, {"id": "advanced_flow", "name": "Advanced Onboarding", "description": "Comprehensive onboarding flow for enterprise users", "flow_type": "advanced", "total_steps": 5, "steps": [{"step_number": 1, "step_type": "profile_setup", "title": "Complete Your Profile", "description": "Tell us about yourself and your organization", "required_fields": ["company_size", "industry", "use_case", "annual_revenue"], "optional_fields": ["marketing_source", "goals", "existing_tools"]}, {"step_number": 2, "step_type": "tenant_configuration", "title": "Configure Your Workspace", "description": "Set up your workspace preferences and policies", "required_fields": ["timezone", "language", "security_policy"], "optional_fields": ["currency", "date_format", "time_format", "branding"]}, {"step_number": 3, "step_type": "team_invitation", "title": "Invite Your Team", "description": "Add team members and assign roles", "required_fields": [], "optional_fields": ["team_members", "default_role", "approval_required"]}, {"step_number": 4, "step_type": "integration_setup", "title": "Connect Your Tools", "description": "Set up integrations and external services", "required_fields": ["email_provider"], "optional_fields": ["storage_provider", "analytics_provider", "monitoring_tools", "deployment_tools"]}, {"step_number": 5, "step_type": "billing_setup", "title": "Set Up Billing", "description": "Configure billing and payment information", "required_fields": ["billing_plan"], "optional_fields": ["payment_method", "billing_contact", "purchase_order"]}]}], "test_onboarding_sessions": [{"id": "session-1", "tenant_id": "test-tenant-1", "user_id": "test-user-1", "flow_type": "basic", "current_step": 1, "total_steps": 3, "completed": false, "started_at": "2024-01-01T10:00:00Z", "data": {}}, {"id": "session-2", "tenant_id": "test-tenant-2", "user_id": "test-user-2", "flow_type": "advanced", "current_step": 3, "total_steps": 5, "completed": false, "started_at": "2024-01-01T11:00:00Z", "data": {"profile_setup": {"company_size": "10-50", "industry": "technology", "use_case": "content_management", "annual_revenue": "1M-10M"}, "tenant_configuration": {"timezone": "America/New_York", "language": "en", "security_policy": "standard", "currency": "USD"}}}], "step_data_templates": {"profile_setup": {"basic": {"company_size": "1-10", "industry": "technology", "use_case": "content_management", "marketing_source": "google_search", "goals": "improve_productivity"}, "enterprise": {"company_size": "500+", "industry": "financial_services", "use_case": "enterprise_cms", "annual_revenue": "100M+", "marketing_source": "sales_team", "goals": "digital_transformation", "existing_tools": ["wordpress", "drupal", "salesforce"]}}, "tenant_configuration": {"basic": {"timezone": "UTC", "language": "en", "currency": "USD", "date_format": "YYYY-MM-DD", "time_format": "24h"}, "enterprise": {"timezone": "America/New_York", "language": "en", "security_policy": "strict", "currency": "USD", "date_format": "MM/DD/YYYY", "time_format": "12h", "branding": {"logo_url": "https://example.com/logo.png", "primary_color": "#007bff", "secondary_color": "#6c757d"}}}, "team_invitation": {"basic": {"team_members": [{"email": "<EMAIL>", "role": "editor"}], "default_role": "viewer", "approval_required": false}, "enterprise": {"team_members": [{"email": "<EMAIL>", "role": "admin"}, {"email": "<EMAIL>", "role": "editor"}, {"email": "<EMAIL>", "role": "editor"}, {"email": "<EMAIL>", "role": "viewer"}], "default_role": "viewer", "approval_required": true}}, "integration_setup": {"basic": {"email_provider": "mock", "storage_provider": "local", "analytics_enabled": true, "notifications_enabled": true}, "enterprise": {"email_provider": "sendgrid", "storage_provider": "s3", "analytics_provider": "google_analytics", "monitoring_tools": ["datadog", "newrelic"], "deployment_tools": ["github_actions"], "sso_provider": "okta"}}, "billing_setup": {"basic": {"billing_plan": "basic", "payment_method": "credit_card"}, "enterprise": {"billing_plan": "enterprise", "payment_method": "invoice", "billing_contact": {"name": "Billing Manager", "email": "<EMAIL>", "phone": "******-0123"}, "purchase_order": "PO-12345"}}}, "validation_rules": {"profile_setup": {"company_size": {"type": "string", "enum": ["1-10", "10-50", "50-200", "200-500", "500+"]}, "industry": {"type": "string", "enum": ["technology", "healthcare", "finance", "education", "retail", "manufacturing", "other"]}, "use_case": {"type": "string", "enum": ["content_management", "e_commerce", "documentation", "marketing", "internal_tools", "other"]}}, "tenant_configuration": {"timezone": {"type": "string", "pattern": "^[A-Za-z_]+/[A-Za-z_]+$"}, "language": {"type": "string", "enum": ["en", "es", "fr", "de", "it", "pt", "zh", "ja"]}, "currency": {"type": "string", "enum": ["USD", "EUR", "GBP", "JPY", "CNY"]}}}}