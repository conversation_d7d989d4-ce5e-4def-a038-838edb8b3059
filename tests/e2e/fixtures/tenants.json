{"test_tenants": [{"id": "test-tenant-1", "name": "Acme Corporation", "slug": "acme-corp", "description": "Leading provider of innovative business solutions", "plan": "enterprise", "status": "active", "owner_id": "test-user-1", "settings": {"timezone": "UTC", "currency": "USD", "language": "en", "date_format": "YYYY-MM-DD", "time_format": "24h"}}, {"id": "test-tenant-2", "name": "TechStart Inc", "slug": "techstart", "description": "Startup focused on cutting-edge technology solutions", "plan": "basic", "status": "active", "owner_id": "test-user-2", "settings": {"timezone": "America/New_York", "currency": "USD", "language": "en", "date_format": "MM/DD/YYYY", "time_format": "12h"}}, {"id": "test-tenant-3", "name": "Global Enterprises", "slug": "global-ent", "description": "Multinational corporation with worldwide operations", "plan": "premium", "status": "active", "owner_id": "test-admin-1", "settings": {"timezone": "Europe/London", "currency": "EUR", "language": "en", "date_format": "DD/MM/YYYY", "time_format": "24h"}}], "tenant_plans": [{"id": "basic", "name": "Basic Plan", "description": "Perfect for small teams getting started", "price": 0, "currency": "USD", "billing_cycle": "monthly", "features": {"max_users": 5, "max_posts": 100, "storage_gb": 1, "api_calls_per_month": 10000, "custom_domain": false, "advanced_analytics": false, "priority_support": false}}, {"id": "premium", "name": "Premium Plan", "description": "For growing businesses with advanced needs", "price": 29, "currency": "USD", "billing_cycle": "monthly", "features": {"max_users": 25, "max_posts": 1000, "storage_gb": 10, "api_calls_per_month": 100000, "custom_domain": true, "advanced_analytics": true, "priority_support": false}}, {"id": "enterprise", "name": "Enterprise Plan", "description": "For large organizations requiring maximum flexibility", "price": 99, "currency": "USD", "billing_cycle": "monthly", "features": {"max_users": -1, "max_posts": -1, "storage_gb": 100, "api_calls_per_month": -1, "custom_domain": true, "advanced_analytics": true, "priority_support": true}}], "tenant_members": [{"tenant_id": "test-tenant-1", "user_id": "test-user-1", "role": "owner", "status": "active", "joined_at": "2024-01-01T00:00:00Z"}, {"tenant_id": "test-tenant-1", "user_id": "test-editor-1", "role": "editor", "status": "active", "joined_at": "2024-01-02T00:00:00Z"}, {"tenant_id": "test-tenant-1", "user_id": "test-viewer-1", "role": "viewer", "status": "active", "joined_at": "2024-01-03T00:00:00Z"}, {"tenant_id": "test-tenant-2", "user_id": "test-user-2", "role": "owner", "status": "active", "joined_at": "2024-01-01T00:00:00Z"}], "invalid_tenants": [{"description": "Tenant with invalid slug", "name": "<PERSON><PERSON><PERSON>", "slug": "invalid slug", "plan": "basic"}, {"description": "Missing required fields", "name": "Incomplete Tenant", "slug": "incomplete"}, {"description": "Tenant with invalid plan", "name": "Bad Plan Tenant", "slug": "bad-plan", "plan": "invalid_plan"}], "tenant_invitations": [{"tenant_id": "test-tenant-1", "email": "<EMAIL>", "role": "editor", "invited_by": "test-user-1", "status": "pending"}, {"tenant_id": "test-tenant-2", "email": "<EMAIL>", "role": "viewer", "invited_by": "test-user-2", "status": "pending"}]}