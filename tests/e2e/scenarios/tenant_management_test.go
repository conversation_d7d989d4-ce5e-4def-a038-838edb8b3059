package scenarios

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

// TenantManagementTestSuite tests tenant management flows
type TenantManagementTestSuite struct {
	suite.Suite
	config      *config.E2EConfig
	client      *helpers.APIClient
	adminClient *helpers.APIClient
	dataGen     *helpers.TestDataGenerator
	predefined  *helpers.PredefinedTestData
	testUser    *helpers.AuthResponse
	adminUser   *helpers.AuthResponse
}

// SetupSuite runs once before all tests in the suite
func (suite *TenantManagementTestSuite) SetupSuite() {
	suite.config = config.NewE2EConfig()
	suite.client = helpers.NewAPIClient(suite.config)
	suite.adminClient = helpers.NewAPIClient(suite.config)
	suite.dataGen = helpers.NewTestDataGenerator()
	suite.predefined = helpers.NewPredefinedTestData()

	// Wait for server to be ready
	err := suite.client.WaitForServer(30 * time.Second)
	suite.Require().NoError(err, "Server should be ready")

	// Create test user
	userData := suite.dataGen.GenerateTestUser()
	authResp, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	suite.testUser = authResp

	// Create admin user
	adminData := suite.dataGen.GenerateAdminUser()
	adminResp, _, err := suite.adminClient.Register(adminData)
	suite.Require().NoError(err)
	suite.adminUser = adminResp
}

// SetupTest runs before each test
func (suite *TenantManagementTestSuite) SetupTest() {
	// Set auth tokens for both clients
	suite.client.SetAuthToken(suite.testUser.AccessToken)
	suite.adminClient.SetAuthToken(suite.adminUser.AccessToken)
}

// TearDownTest runs after each test
func (suite *TenantManagementTestSuite) TearDownTest() {
	// Keep authentication for reuse between tests
}

// TearDownSuite runs once after all tests in the suite
func (suite *TenantManagementTestSuite) TearDownSuite() {
	// Cleanup test users
	suite.client.Logout()
	suite.adminClient.Logout()
}

// TestCreateTenant tests creating a new tenant
func (suite *TenantManagementTestSuite) TestCreateTenant() {
	tenantData := suite.dataGen.GenerateTestTenant()
	
	tenant, resp, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusCreated().
		JSONContentType().
		ResponseTime(3 * time.Second)

	// Assert tenant response
	helpers.NewTenantAssertion(suite.T(), tenant).
		HasID().
		HasName(tenantData.Name).
		HasSlug(tenantData.Slug).
		HasStatus("active").
		HasPlan(tenantData.Plan)

	suite.Equal(tenantData.Description, tenant.Description)
	suite.NotEmpty(tenant.CreatedAt)
}

// TestCreateTenantWithInvalidData tests tenant creation with invalid data
func (suite *TenantManagementTestSuite) TestCreateTenantWithInvalidData() {
	invalidTenants := []struct {
		name string
		data helpers.TenantRequest
	}{
		{
			name: "Empty name",
			data: helpers.TenantRequest{
				Name:        "",
				Slug:        "valid-slug",
				Description: "Valid description",
				Plan:        "basic",
			},
		},
		{
			name: "Invalid slug with spaces",
			data: helpers.TenantRequest{
				Name:        "Valid Name",
				Slug:        "invalid slug",
				Description: "Valid description",
				Plan:        "basic",
			},
		},
		{
			name: "Invalid plan",
			data: helpers.TenantRequest{
				Name:        "Valid Name",
				Slug:        "valid-slug",
				Description: "Valid description",
				Plan:        "invalid_plan",
			},
		},
		{
			name: "Missing required fields",
			data: helpers.TenantRequest{
				Name: "Valid Name",
				// Missing slug, description, plan
			},
		},
	}

	for _, tc := range invalidTenants {
		suite.Run(tc.name, func() {
			_, resp, err := suite.client.CreateTenant(tc.data)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).
				StatusBadRequest().
				JSONContentType()

			// Should contain validation error
			errorResp := suite.client.ParseErrorResponse(resp)
			suite.Contains(errorResp, "error")
		})
	}
}

// TestCreateDuplicateTenant tests creating tenant with duplicate slug
func (suite *TenantManagementTestSuite) TestCreateDuplicateTenant() {
	// Create first tenant
	tenantData := suite.dataGen.GenerateTestTenant()
	_, resp1, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp1).StatusCreated()

	// Try to create second tenant with same slug
	duplicateData := tenantData
	duplicateData.Name = "Different Name"

	_, resp2, err := suite.client.CreateTenant(duplicateData)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp2).
		StatusCode(http.StatusConflict).
		JSONContentType()
}

// TestGetTenant tests retrieving a tenant by ID
func (suite *TenantManagementTestSuite) TestGetTenant() {
	// Create tenant first
	tenantData := suite.dataGen.GenerateTestTenant()
	createdTenant, _, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)

	// Get tenant by ID
	tenant, resp, err := suite.client.GetTenant(createdTenant.ID)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second)

	// Assert tenant data
	helpers.NewTenantAssertion(suite.T(), tenant).
		HasID().
		HasName(tenantData.Name).
		HasSlug(tenantData.Slug).
		HasStatus("active").
		HasPlan(tenantData.Plan)

	suite.Equal(createdTenant.ID, tenant.ID)
	suite.Equal(tenantData.Description, tenant.Description)
}

// TestGetNonExistentTenant tests retrieving non-existent tenant
func (suite *TenantManagementTestSuite) TestGetNonExistentTenant() {
	_, resp, err := suite.client.GetTenant("non-existent-tenant-id")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusNotFound().
		JSONContentType()
}

// TestGetMyTenants tests retrieving current user's tenants
func (suite *TenantManagementTestSuite) TestGetMyTenants() {
	// Create multiple tenants
	var createdTenants []*helpers.Tenant
	for i := 0; i < 3; i++ {
		tenantData := suite.dataGen.GenerateTestTenant()
		tenant, _, err := suite.client.CreateTenant(tenantData)
		suite.Require().NoError(err)
		createdTenants = append(createdTenants, tenant)
	}

	// Get user's tenants
	tenants, resp, err := suite.client.GetMyTenants()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(3 * time.Second)

	// Should return at least the created tenants
	suite.GreaterOrEqual(len(tenants), len(createdTenants))

	// Verify all created tenants are in the response
	for _, createdTenant := range createdTenants {
		found := false
		for _, tenant := range tenants {
			if tenant.ID == createdTenant.ID {
				found = true
				break
			}
		}
		suite.True(found, "Created tenant should be in user's tenants list")
	}
}

// TestTenantAccessControl tests tenant access control
func (suite *TenantManagementTestSuite) TestTenantAccessControl() {
	// Create tenant with first user
	tenantData := suite.dataGen.GenerateTestTenant()
	tenant, _, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)

	// Create another user
	userData := suite.dataGen.GenerateTestUser()
	otherClient := helpers.NewAPIClient(suite.config)
	_, _, err := otherClient.Register(userData)
	suite.Require().NoError(err)

	// Other user should not be able to access the tenant
	_, resp, err := otherClient.GetTenant(tenant.ID)
	suite.Require().NoError(err)

	// Should be forbidden or not found depending on implementation
	suite.True(resp.StatusCode() == http.StatusForbidden || resp.StatusCode() == http.StatusNotFound)

	// Cleanup
	otherClient.Logout()
}

// TestTenantPlanValidation tests tenant plan validation
func (suite *TenantManagementTestSuite) TestTenantPlanValidation() {
	validPlans := []string{"basic", "premium", "enterprise"}

	for _, plan := range validPlans {
		suite.Run("Valid plan: "+plan, func() {
			tenantData := suite.dataGen.GenerateTestTenant()
			tenantData.Plan = plan

			tenant, resp, err := suite.client.CreateTenant(tenantData)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()
			helpers.NewTenantAssertion(suite.T(), tenant).HasPlan(plan)
		})
	}
}

// TestTenantSlugValidation tests tenant slug validation
func (suite *TenantManagementTestSuite) TestTenantSlugValidation() {
	validSlugs := []string{
		"valid-slug",
		"valid123",
		"test-tenant-1",
		"a",
		"very-long-slug-name-that-should-still-be-valid",
	}

	for _, slug := range validSlugs {
		suite.Run("Valid slug: "+slug, func() {
			tenantData := suite.dataGen.GenerateTestTenant()
			tenantData.Slug = slug

			tenant, resp, err := suite.client.CreateTenant(tenantData)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()
			helpers.NewTenantAssertion(suite.T(), tenant).HasSlug(slug)
		})
	}

	invalidSlugs := []string{
		"",
		"Invalid Slug",
		"invalid@slug",
		"UPPERCASE",
		"slug_with_underscore",
	}

	for _, slug := range invalidSlugs {
		suite.Run("Invalid slug: "+slug, func() {
			tenantData := suite.dataGen.GenerateTestTenant()
			tenantData.Slug = slug

			_, resp, err := suite.client.CreateTenant(tenantData)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).StatusBadRequest()
		})
	}
}

// TestTenantPerformance tests tenant operations performance
func (suite *TenantManagementTestSuite) TestTenantPerformance() {
	tenantData := suite.dataGen.GenerateTestTenant()

	// Test tenant creation performance
	var createdTenant *helpers.Tenant
	helpers.AssertResponseTime(suite.T(), 3*time.Second, func() {
		tenant, resp, err := suite.client.CreateTenant(tenantData)
		suite.Require().NoError(err)
		suite.Equal(http.StatusCreated, resp.StatusCode())
		createdTenant = tenant
	})

	// Test tenant retrieval performance
	helpers.AssertResponseTime(suite.T(), 2*time.Second, func() {
		_, resp, err := suite.client.GetTenant(createdTenant.ID)
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})

	// Test get my tenants performance
	helpers.AssertResponseTime(suite.T(), 3*time.Second, func() {
		_, resp, err := suite.client.GetMyTenants()
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})
}

// TestConcurrentTenantOperations tests concurrent tenant operations
func (suite *TenantManagementTestSuite) TestConcurrentTenantOperations() {
	const numConcurrentOps = 5

	// Test concurrent tenant creation
	results := make(chan error, numConcurrentOps)

	for i := 0; i < numConcurrentOps; i++ {
		go func(index int) {
			client := helpers.NewAPIClient(suite.config)
			client.SetAuthToken(suite.testUser.AccessToken)

			tenantData := suite.dataGen.GenerateTestTenant()
			_, resp, err := client.CreateTenant(tenantData)
			if err != nil {
				results <- err
				return
			}

			if resp.StatusCode() != http.StatusCreated {
				results <- fmt.Errorf("Expected 201 but got %d", resp.StatusCode())
				return
			}

			results <- nil
		}(i)
	}

	// Wait for all operations to complete
	for i := 0; i < numConcurrentOps; i++ {
		err := <-results
		suite.NoError(err, "Concurrent tenant operations should succeed")
	}
}

// TestTenantDataConsistency tests data consistency across operations
func (suite *TenantManagementTestSuite) TestTenantDataConsistency() {
	// Create tenant
	tenantData := suite.dataGen.GenerateTestTenant()
	createdTenant, _, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)

	// Get tenant multiple times and verify consistency
	var tenants []*helpers.Tenant

	for i := 0; i < 3; i++ {
		tenant, resp, err := suite.client.GetTenant(createdTenant.ID)
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()
		tenants = append(tenants, tenant)

		// Small delay between requests
		time.Sleep(100 * time.Millisecond)
	}

	// All responses should be identical
	for i := 1; i < len(tenants); i++ {
		suite.Equal(tenants[0].ID, tenants[i].ID)
		suite.Equal(tenants[0].Name, tenants[i].Name)
		suite.Equal(tenants[0].Slug, tenants[i].Slug)
		suite.Equal(tenants[0].Description, tenants[i].Description)
		suite.Equal(tenants[0].Plan, tenants[i].Plan)
		suite.Equal(tenants[0].Status, tenants[i].Status)
		suite.Equal(tenants[0].CreatedAt, tenants[i].CreatedAt)
	}
}

// TestTenantLifecycle tests complete tenant lifecycle
func (suite *TenantManagementTestSuite) TestTenantLifecycle() {
	// 1. Create tenant
	tenantData := suite.dataGen.GenerateTestTenant()
	tenant, resp, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()

	// 2. Verify tenant exists in user's tenants
	tenants, resp, err := suite.client.GetMyTenants()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	found := false
	for _, t := range tenants {
		if t.ID == tenant.ID {
			found = true
			break
		}
	}
	suite.True(found, "Created tenant should be in user's tenants list")

	// 3. Get tenant by ID
	retrievedTenant, resp, err := suite.client.GetTenant(tenant.ID)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	suite.Equal(tenant.ID, retrievedTenant.ID)
	suite.Equal(tenant.Name, retrievedTenant.Name)
	suite.Equal(tenant.Slug, retrievedTenant.Slug)
}

// TestTenantFieldValidation tests tenant field validation
func (suite *TenantManagementTestSuite) TestTenantFieldValidation() {
	// Create valid tenant
	tenantData := suite.dataGen.GenerateTestTenant()
	tenant, resp, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()

	// Validate required fields are present
	suite.NotEmpty(tenant.ID)
	suite.NotEmpty(tenant.Name)
	suite.NotEmpty(tenant.Slug)
	suite.NotEmpty(tenant.Plan)
	suite.NotEmpty(tenant.Status)
	suite.NotEmpty(tenant.CreatedAt)

	// Validate field types and formats
	_, err = time.Parse(time.RFC3339, tenant.CreatedAt)
	suite.NoError(err, "CreatedAt should be valid RFC3339 timestamp")

	// Status should be valid
	validStatuses := []string{"active", "inactive", "suspended", "deleted", "trial"}
	suite.Contains(validStatuses, tenant.Status)

	// Plan should be valid
	validPlans := []string{"basic", "premium", "enterprise"}
	suite.Contains(validPlans, tenant.Plan)
}

// TestTenantIsolation tests tenant data isolation
func (suite *TenantManagementTestSuite) TestTenantIsolation() {
	// Create tenant with first user
	tenantData1 := suite.dataGen.GenerateTestTenant()
	tenant1, _, err := suite.client.CreateTenant(tenantData1)
	suite.Require().NoError(err)

	// Create second user and tenant
	userData := suite.dataGen.GenerateTestUser()
	otherClient := helpers.NewAPIClient(suite.config)
	_, _, err := otherClient.Register(userData)
	suite.Require().NoError(err)

	tenantData2 := suite.dataGen.GenerateTestTenant()
	tenant2, _, err := otherClient.CreateTenant(tenantData2)
	suite.Require().NoError(err)

	// Get first user's tenants - should not include second user's tenant
	tenants1, _, err := suite.client.GetMyTenants()
	suite.Require().NoError(err)

	found := false
	for _, t := range tenants1 {
		if t.ID == tenant2.ID {
			found = true
			break
		}
	}
	suite.False(found, "User should not see other user's tenants")

	// Get second user's tenants - should not include first user's tenant
	tenants2, _, err := otherClient.GetMyTenants()
	suite.Require().NoError(err)

	found = false
	for _, t := range tenants2 {
		if t.ID == tenant1.ID {
			found = true
			break
		}
	}
	suite.False(found, "User should not see other user's tenants")

	// Cleanup
	otherClient.Logout()
}

// TestE2ETenantManagement runs the TenantManagementTestSuite
func TestE2ETenantManagement(t *testing.T) {
	suite.Run(t, new(TenantManagementTestSuite))
}