package scenarios

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

// UserManagementTestSuite tests user management flows
type UserManagementTestSuite struct {
	suite.Suite
	config      *config.E2EConfig
	client      *helpers.APIClient
	adminClient *helpers.APIClient
	dataGen     *helpers.TestDataGenerator
	predefined  *helpers.PredefinedTestData
	testUser    *helpers.AuthResponse
	adminUser   *helpers.AuthResponse
}

// SetupSuite runs once before all tests in the suite
func (suite *UserManagementTestSuite) SetupSuite() {
	suite.config = config.NewE2EConfig()
	suite.client = helpers.NewAPIClient(suite.config)
	suite.adminClient = helpers.NewAPIClient(suite.config)
	suite.dataGen = helpers.NewTestDataGenerator()
	suite.predefined = helpers.NewPredefinedTestData()

	// Wait for server to be ready
	err := suite.client.WaitForServer(30 * time.Second)
	suite.Require().NoError(err, "Server should be ready")

	// Create test user
	userData := suite.dataGen.GenerateTestUser()
	authResp, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	suite.testUser = authResp

	// Create admin user
	adminData := suite.dataGen.GenerateAdminUser()
	adminResp, _, err := suite.adminClient.Register(adminData)
	suite.Require().NoError(err)
	suite.adminUser = adminResp
}

// SetupTest runs before each test
func (suite *UserManagementTestSuite) SetupTest() {
	// Set auth tokens for both clients
	suite.client.SetAuthToken(suite.testUser.AccessToken)
	suite.adminClient.SetAuthToken(suite.adminUser.AccessToken)
}

// TearDownTest runs after each test
func (suite *UserManagementTestSuite) TearDownTest() {
	// Keep authentication for reuse between tests
}

// TearDownSuite runs once after all tests in the suite
func (suite *UserManagementTestSuite) TearDownSuite() {
	// Cleanup test users
	suite.client.Logout()
	suite.adminClient.Logout()
}

// TestGetUserProfile tests retrieving user profile
func (suite *UserManagementTestSuite) TestGetUserProfile() {
	user, resp, err := suite.client.GetProfile()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second)

	// Verify user data
	suite.NotEmpty(user.ID)
	suite.Equal(suite.testUser.User.Email, user.Email)
	suite.Equal(suite.testUser.User.FirstName, user.FirstName)
	suite.Equal(suite.testUser.User.LastName, user.LastName)
	suite.Equal("active", user.Status)
	suite.NotEmpty(user.CreatedAt)
}

// TestGetUserById tests retrieving user by ID
func (suite *UserManagementTestSuite) TestGetUserById() {
	// Get user by ID using admin client
	userID := suite.testUser.User.ID
	user, resp, err := suite.adminClient.GetUser(userID)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	suite.Equal(userID, user.ID)
	suite.Equal(suite.testUser.User.Email, user.Email)
}

// TestGetNonExistentUser tests retrieving non-existent user
func (suite *UserManagementTestSuite) TestGetNonExistentUser() {
	_, resp, err := suite.adminClient.GetUser("non-existent-user-id")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusNotFound().
		JSONContentType()
}

// TestUnauthorizedUserAccess tests access without authentication
func (suite *UserManagementTestSuite) TestUnauthorizedUserAccess() {
	// Create unauthenticated client
	unauthClient := helpers.NewAPIClient(suite.config)

	_, resp, err := unauthClient.GetProfile()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusUnauthorized().
		JSONContentType()
}

// TestSearchUsers tests user search functionality
func (suite *UserManagementTestSuite) TestSearchUsers() {
	// Search for users using admin client
	searchQuery := suite.testUser.User.FirstName
	users, resp, err := suite.adminClient.SearchUsers(searchQuery)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(3 * time.Second)

	// Should find at least the test user
	suite.GreaterOrEqual(len(users), 1)

	// Check if test user is in results
	found := false
	for _, user := range users {
		if user.ID == suite.testUser.User.ID {
			found = true
			break
		}
	}
	suite.True(found, "Test user should be found in search results")
}

// TestSearchUsersEmpty tests search with no results
func (suite *UserManagementTestSuite) TestSearchUsersEmpty() {
	// Search for non-existent user
	users, resp, err := suite.adminClient.SearchUsers("non-existent-user-xyz123")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	suite.Empty(users, "Should return empty results for non-existent user")
}

// TestSearchUsersInvalidQuery tests search with invalid query
func (suite *UserManagementTestSuite) TestSearchUsersInvalidQuery() {
	// Search with empty query
	_, resp, err := suite.adminClient.SearchUsers("")
	suite.Require().NoError(err)

	// Should return bad request or all users depending on implementation
	suite.True(resp.StatusCode() == http.StatusBadRequest || resp.StatusCode() == http.StatusOK)
}

// TestUserPermissions tests user access permissions
func (suite *UserManagementTestSuite) TestUserPermissions() {
	// Regular user should not be able to get other users
	otherUserID := suite.adminUser.User.ID
	_, resp, err := suite.client.GetUser(otherUserID)
	suite.Require().NoError(err)

	// Should be forbidden for regular users
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusForbidden().
		JSONContentType()
}

// TestAdminPermissions tests admin access permissions
func (suite *UserManagementTestSuite) TestAdminPermissions() {
	// Admin should be able to get any user
	userID := suite.testUser.User.ID
	user, resp, err := suite.adminClient.GetUser(userID)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	suite.Equal(userID, user.ID)
}

// TestConcurrentUserOperations tests concurrent user operations
func (suite *UserManagementTestSuite) TestConcurrentUserOperations() {
	const numConcurrentOps = 5

	// Test concurrent profile access
	results := make(chan error, numConcurrentOps)

	for i := 0; i < numConcurrentOps; i++ {
		go func() {
			client := helpers.NewAPIClient(suite.config)
			client.SetAuthToken(suite.testUser.AccessToken)

			_, resp, err := client.GetProfile()
			if err != nil {
				results <- err
				return
			}

			if resp.StatusCode() != http.StatusOK {
				results <- fmt.Errorf("Expected 200 but got %d", resp.StatusCode())
				return
			}

			results <- nil
		}()
	}

	// Wait for all operations to complete
	for i := 0; i < numConcurrentOps; i++ {
		err := <-results
		suite.NoError(err, "Concurrent user operations should succeed")
	}
}

// TestUserDataConsistency tests data consistency across operations
func (suite *UserManagementTestSuite) TestUserDataConsistency() {
	// Get profile multiple times and verify consistency
	var users []*helpers.User

	for i := 0; i < 3; i++ {
		user, resp, err := suite.client.GetProfile()
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()
		users = append(users, user)

		// Small delay between requests
		time.Sleep(100 * time.Millisecond)
	}

	// All responses should be identical
	for i := 1; i < len(users); i++ {
		suite.Equal(users[0].ID, users[i].ID)
		suite.Equal(users[0].Email, users[i].Email)
		suite.Equal(users[0].FirstName, users[i].FirstName)
		suite.Equal(users[0].LastName, users[i].LastName)
		suite.Equal(users[0].Status, users[i].Status)
		suite.Equal(users[0].CreatedAt, users[i].CreatedAt)
	}
}

// TestUserProfilePerformance tests user profile access performance
func (suite *UserManagementTestSuite) TestUserProfilePerformance() {
	// Test profile access performance
	helpers.AssertResponseTime(suite.T(), 2*time.Second, func() {
		_, resp, err := suite.client.GetProfile()
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})

	// Test user search performance
	helpers.AssertResponseTime(suite.T(), 3*time.Second, func() {
		_, resp, err := suite.adminClient.SearchUsers("test")
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})
}

// TestMultipleUserRegistrations tests creating multiple users
func (suite *UserManagementTestSuite) TestMultipleUserRegistrations() {
	const numUsers = 3
	var createdUsers []*helpers.AuthResponse

	// Create multiple users
	for i := 0; i < numUsers; i++ {
		userData := suite.dataGen.GenerateTestUser()
		client := helpers.NewAPIClient(suite.config)

		authResp, resp, err := client.Register(userData)
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()

		createdUsers = append(createdUsers, authResp)

		// Verify each user can access their profile
		user, profileResp, err := client.GetProfile()
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), profileResp).StatusOK()

		suite.Equal(userData.Email, user.Email)
		suite.Equal(userData.FirstName, user.FirstName)
	}

	// Search for all created users
	for _, createdUser := range createdUsers {
		users, resp, err := suite.adminClient.SearchUsers(createdUser.User.FirstName)
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

		// Should find the user
		found := false
		for _, user := range users {
			if user.ID == createdUser.User.ID {
				found = true
				break
			}
		}
		suite.True(found, "Created user should be found in search")
	}

	// Cleanup created users
	for _, createdUser := range createdUsers {
		client := helpers.NewAPIClient(suite.config)
		client.SetAuthToken(createdUser.AccessToken)
		client.Logout()
	}
}

// TestUserIsolation tests that users can only access their own data
func (suite *UserManagementTestSuite) TestUserIsolation() {
	// Create another regular user
	userData := suite.dataGen.GenerateTestUser()
	otherClient := helpers.NewAPIClient(suite.config)
	authResp, _, err := otherClient.Register(userData)
	suite.Require().NoError(err)

	// First user should not be able to access second user's data by ID
	_, resp, err := suite.client.GetUser(authResp.User.ID)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusForbidden()

	// Second user should not be able to access first user's data by ID
	_, resp, err = otherClient.GetUser(suite.testUser.User.ID)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusForbidden()

	// Cleanup
	otherClient.Logout()
}

// TestUserStatusValidation tests user status field validation
func (suite *UserManagementTestSuite) TestUserStatusValidation() {
	user, resp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	// Status should be one of valid values
	validStatuses := []string{"active", "inactive", "suspended", "pending", "deleted"}
	suite.Contains(validStatuses, user.Status)
}

// TestUserEmailValidation tests email format validation
func (suite *UserManagementTestSuite) TestUserEmailValidation() {
	user, resp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	// Email should contain @ symbol
	suite.Contains(user.Email, "@")
	// Email should not be empty
	suite.NotEmpty(user.Email)
}

// TestUserTimestampValidation tests timestamp field validation
func (suite *UserManagementTestSuite) TestUserTimestampValidation() {
	user, resp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	// CreatedAt should not be empty
	suite.NotEmpty(user.CreatedAt)

	// CreatedAt should be valid timestamp
	_, err = time.Parse(time.RFC3339, user.CreatedAt)
	suite.NoError(err, "CreatedAt should be valid RFC3339 timestamp")
}

// TestUserSearchPerformanceWithLargeDataset tests search performance
func (suite *UserManagementTestSuite) TestUserSearchPerformanceWithLargeDataset() {
	// This test simulates searching through many users
	// In a real scenario, you might have thousands of users

	searchQueries := []string{
		"test",
		"admin",
		"user",
		"nonexistent",
		"",
	}

	for _, query := range searchQueries {
		suite.Run("Search query: "+query, func() {
			start := time.Now()
			_, resp, err := suite.adminClient.SearchUsers(query)
			elapsed := time.Since(start)

			suite.Require().NoError(err)
			suite.True(resp.StatusCode() == http.StatusOK || resp.StatusCode() == http.StatusBadRequest)

			// Search should complete within reasonable time
			suite.LessOrEqual(elapsed, 5*time.Second, "Search should complete within 5 seconds")
		})
	}
}

// TestUserResponseFieldsConsistency tests that all user responses have consistent fields
func (suite *UserManagementTestSuite) TestUserResponseFieldsConsistency() {
	// Get user via profile endpoint
	profileUser, resp1, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp1).StatusOK()

	// Get same user via admin endpoint
	adminUser, resp2, err := suite.adminClient.GetUser(profileUser.ID)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp2).StatusOK()

	// Both responses should have same structure and data
	suite.Equal(profileUser.ID, adminUser.ID)
	suite.Equal(profileUser.Email, adminUser.Email)
	suite.Equal(profileUser.FirstName, adminUser.FirstName)
	suite.Equal(profileUser.LastName, adminUser.LastName)
	suite.Equal(profileUser.Status, adminUser.Status)
	suite.Equal(profileUser.CreatedAt, adminUser.CreatedAt)
}

// TestE2EUserManagement runs the UserManagementTestSuite
func TestE2EUserManagement(t *testing.T) {
	suite.Run(t, new(UserManagementTestSuite))
}