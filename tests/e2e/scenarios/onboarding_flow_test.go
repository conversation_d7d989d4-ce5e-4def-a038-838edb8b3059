package scenarios

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

// OnboardingFlowTestSuite tests onboarding flow functionality
type OnboardingFlowTestSuite struct {
	suite.Suite
	config      *config.E2EConfig
	client      *helpers.APIClient
	dataGen     *helpers.TestDataGenerator
	predefined  *helpers.PredefinedTestData
	testUser    *helpers.AuthResponse
	testTenant  *helpers.Tenant
}

// SetupSuite runs once before all tests in the suite
func (suite *OnboardingFlowTestSuite) SetupSuite() {
	suite.config = config.NewE2EConfig()
	suite.client = helpers.NewAPIClient(suite.config)
	suite.dataGen = helpers.NewTestDataGenerator()
	suite.predefined = helpers.NewPredefinedTestData()

	// Wait for server to be ready
	err := suite.client.WaitForServer(30 * time.Second)
	suite.Require().NoError(err, "Server should be ready")

	// Create test user
	userData := suite.dataGen.GenerateTestUser()
	authResp, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	suite.testUser = authResp

	// Create test tenant
	tenantData := suite.dataGen.GenerateTestTenant()
	tenant, _, err := suite.client.CreateTenant(tenantData)
	suite.Require().NoError(err)
	suite.testTenant = tenant
}

// SetupTest runs before each test
func (suite *OnboardingFlowTestSuite) SetupTest() {
	// Set auth token
	suite.client.SetAuthToken(suite.testUser.AccessToken)
}

// TearDownTest runs after each test
func (suite *OnboardingFlowTestSuite) TearDownTest() {
	// Keep authentication for reuse between tests
}

// TearDownSuite runs once after all tests in the suite
func (suite *OnboardingFlowTestSuite) TearDownSuite() {
	// Cleanup
	suite.client.Logout()
}

// TestStartBasicOnboardingFlow tests starting basic onboarding flow
func (suite *OnboardingFlowTestSuite) TestStartBasicOnboardingFlow() {
	flow, resp, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusCreated().
		JSONContentType().
		ResponseTime(3 * time.Second)

	// Assert onboarding flow response
	helpers.NewOnboardingAssertion(suite.T(), flow).
		HasID().
		HasTenantID(suite.testTenant.ID).
		HasFlowType("basic").
		AtStep(1).
		HasTotalSteps(3).
		IsNotCompleted()

	suite.Equal(suite.testUser.User.ID, flow.UserID)
	suite.NotEmpty(flow.CreatedAt)
}

// TestStartAdvancedOnboardingFlow tests starting advanced onboarding flow
func (suite *OnboardingFlowTestSuite) TestStartAdvancedOnboardingFlow() {
	flow, resp, err := suite.client.StartOnboarding(suite.testTenant.ID, "advanced")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusCreated().
		JSONContentType()

	// Assert onboarding flow response
	helpers.NewOnboardingAssertion(suite.T(), flow).
		HasID().
		HasTenantID(suite.testTenant.ID).
		HasFlowType("advanced").
		AtStep(1).
		HasTotalSteps(5).
		IsNotCompleted()
}

// TestGetOnboardingFlow tests retrieving onboarding flow
func (suite *OnboardingFlowTestSuite) TestGetOnboardingFlow() {
	// Start onboarding flow first
	createdFlow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Get onboarding flow
	flow, resp, err := suite.client.GetOnboardingFlow(createdFlow.ID)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second)

	// Assert flow data
	helpers.NewOnboardingAssertion(suite.T(), flow).
		HasID().
		HasTenantID(suite.testTenant.ID).
		HasFlowType("basic").
		AtStep(1).
		IsNotCompleted()

	suite.Equal(createdFlow.ID, flow.ID)
	suite.Equal(createdFlow.UserID, flow.UserID)
}

// TestGetNonExistentOnboardingFlow tests retrieving non-existent flow
func (suite *OnboardingFlowTestSuite) TestGetNonExistentOnboardingFlow() {
	_, resp, err := suite.client.GetOnboardingFlow("non-existent-flow-id")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusNotFound().
		JSONContentType()
}

// TestCompleteBasicOnboardingFlow tests completing entire basic flow
func (suite *OnboardingFlowTestSuite) TestCompleteBasicOnboardingFlow() {
	// Start basic onboarding flow
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Step 1: Profile Setup
	profileData := suite.dataGen.GenerateOnboardingStepData("profile_setup")
	updatedFlow, resp, err := suite.client.UpdateOnboardingStep(flow.ID, profileData)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
		AtStep(2).
		IsNotCompleted()

	// Verify step data was saved
	suite.NotEmpty(updatedFlow.Data)

	// Step 2: Tenant Configuration
	configData := suite.dataGen.GenerateOnboardingStepData("tenant_configuration")
	updatedFlow, resp, err = suite.client.UpdateOnboardingStep(updatedFlow.ID, configData)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()
	helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
		AtStep(3).
		IsNotCompleted()

	// Step 3: Integration Setup (final step)
	integrationData := suite.dataGen.GenerateOnboardingStepData("integration_setup")
	updatedFlow, resp, err = suite.client.UpdateOnboardingStep(updatedFlow.ID, integrationData)
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()
	helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
		AtStep(3). // Should remain at final step
		IsCompleted()

	// Verify all step data is preserved
	suite.Contains(updatedFlow.Data, "company_size")
	suite.Contains(updatedFlow.Data, "timezone")
	suite.Contains(updatedFlow.Data, "email_provider")
}

// TestCompleteAdvancedOnboardingFlow tests completing entire advanced flow
func (suite *OnboardingFlowTestSuite) TestCompleteAdvancedOnboardingFlow() {
	// Start advanced onboarding flow
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "advanced")
	suite.Require().NoError(err)

	stepTypes := []string{
		"profile_setup",
		"tenant_configuration", 
		"team_invitation",
		"integration_setup",
		"billing_setup",
	}

	currentFlow := flow
	for i, stepType := range stepTypes {
		stepData := suite.dataGen.GenerateOnboardingStepData(stepType)
		updatedFlow, resp, err := suite.client.UpdateOnboardingStep(currentFlow.ID, stepData)
		suite.Require().NoError(err)

		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

		if i == len(stepTypes)-1 {
			// Last step should complete the flow
			helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
				AtStep(5).
				IsCompleted()
		} else {
			// Intermediate steps should advance but not complete
			helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
				AtStep(i + 2).
				IsNotCompleted()
		}

		currentFlow = updatedFlow
	}

	// Verify all step data is preserved
	suite.Contains(currentFlow.Data, "company_size")
	suite.Contains(currentFlow.Data, "timezone")
	suite.Contains(currentFlow.Data, "team_members")
	suite.Contains(currentFlow.Data, "email_provider")
	suite.Contains(currentFlow.Data, "billing_plan")
}

// TestInvalidOnboardingStepData tests updating with invalid step data
func (suite *OnboardingFlowTestSuite) TestInvalidOnboardingStepData() {
	// Start onboarding flow
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Test various invalid data scenarios
	invalidDataCases := []struct {
		name string
		data map[string]interface{}
	}{
		{
			name: "Empty data",
			data: map[string]interface{}{},
		},
		{
			name: "Invalid field types",
			data: map[string]interface{}{
				"company_size": 123, // Should be string
				"industry":     []string{"tech"}, // Should be string
			},
		},
		{
			name: "Invalid enum values",
			data: map[string]interface{}{
				"company_size": "invalid_size",
				"industry":     "invalid_industry",
			},
		},
	}

	for _, tc := range invalidDataCases {
		suite.Run(tc.name, func() {
			_, resp, err := suite.client.UpdateOnboardingStep(flow.ID, tc.data)
			suite.Require().NoError(err)

			// Should return bad request for invalid data
			helpers.NewHTTPAssertion(suite.T(), resp).
				StatusBadRequest().
				JSONContentType()
		})
	}
}

// TestOnboardingFlowValidation tests onboarding flow validation
func (suite *OnboardingFlowTestSuite) TestOnboardingFlowValidation() {
	// Test invalid flow types
	invalidFlowTypes := []string{
		"",
		"invalid_flow",
		"BASIC", // Case sensitive
		"123",
	}

	for _, flowType := range invalidFlowTypes {
		suite.Run("Invalid flow type: "+flowType, func() {
			_, resp, err := suite.client.StartOnboarding(suite.testTenant.ID, flowType)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).
				StatusBadRequest().
				JSONContentType()
		})
	}

	// Test invalid tenant ID
	_, resp, err := suite.client.StartOnboarding("invalid-tenant-id", "basic")
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusCode(http.StatusNotFound).
		JSONContentType()
}

// TestOnboardingFlowAccess tests onboarding flow access control
func (suite *OnboardingFlowTestSuite) TestOnboardingFlowAccess() {
	// Create onboarding flow with first user
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Create another user
	userData := suite.dataGen.GenerateTestUser()
	otherClient := helpers.NewAPIClient(suite.config)
	_, _, err := otherClient.Register(userData)
	suite.Require().NoError(err)

	// Other user should not be able to access the flow
	_, resp, err := otherClient.GetOnboardingFlow(flow.ID)
	suite.Require().NoError(err)

	// Should be forbidden or not found
	suite.True(resp.StatusCode() == http.StatusForbidden || resp.StatusCode() == http.StatusNotFound)

	// Other user should not be able to update the flow
	stepData := suite.dataGen.GenerateOnboardingStepData("profile_setup")
	_, resp, err = otherClient.UpdateOnboardingStep(flow.ID, stepData)
	suite.Require().NoError(err)

	suite.True(resp.StatusCode() == http.StatusForbidden || resp.StatusCode() == http.StatusNotFound)

	// Cleanup
	otherClient.Logout()
}

// TestOnboardingFlowPerformance tests onboarding flow performance
func (suite *OnboardingFlowTestSuite) TestOnboardingFlowPerformance() {
	// Test flow creation performance
	helpers.AssertResponseTime(suite.T(), 3*time.Second, func() {
		_, resp, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
		suite.Require().NoError(err)
		suite.Equal(http.StatusCreated, resp.StatusCode())
	})

	// Create flow for further testing
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Test flow retrieval performance
	helpers.AssertResponseTime(suite.T(), 2*time.Second, func() {
		_, resp, err := suite.client.GetOnboardingFlow(flow.ID)
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})

	// Test step update performance
	stepData := suite.dataGen.GenerateOnboardingStepData("profile_setup")
	helpers.AssertResponseTime(suite.T(), 2*time.Second, func() {
		_, resp, err := suite.client.UpdateOnboardingStep(flow.ID, stepData)
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})
}

// TestConcurrentOnboardingOperations tests concurrent onboarding operations
func (suite *OnboardingFlowTestSuite) TestConcurrentOnboardingOperations() {
	const numConcurrentOps = 3

	// Test concurrent flow creation for different tenants
	results := make(chan error, numConcurrentOps)

	for i := 0; i < numConcurrentOps; i++ {
		go func(index int) {
			client := helpers.NewAPIClient(suite.config)
			client.SetAuthToken(suite.testUser.AccessToken)

			// Create separate tenant for each flow
			tenantData := suite.dataGen.GenerateTestTenant()
			tenant, _, err := client.CreateTenant(tenantData)
			if err != nil {
				results <- err
				return
			}

			// Start onboarding flow
			_, resp, err := client.StartOnboarding(tenant.ID, "basic")
			if err != nil {
				results <- err
				return
			}

			if resp.StatusCode() != http.StatusCreated {
				results <- fmt.Errorf("Expected 201 but got %d", resp.StatusCode())
				return
			}

			results <- nil
		}(i)
	}

	// Wait for all operations to complete
	for i := 0; i < numConcurrentOps; i++ {
		err := <-results
		suite.NoError(err, "Concurrent onboarding operations should succeed")
	}
}

// TestOnboardingDataPersistence tests that onboarding data persists correctly
func (suite *OnboardingFlowTestSuite) TestOnboardingDataPersistence() {
	// Start onboarding flow
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Update first step
	stepData := map[string]interface{}{
		"company_size": "10-50",
		"industry":     "technology",
		"use_case":     "content_management",
		"goals":        "improve_productivity",
	}

	updatedFlow, _, err := suite.client.UpdateOnboardingStep(flow.ID, stepData)
	suite.Require().NoError(err)

	// Retrieve flow and verify data persistence
	retrievedFlow, resp, err := suite.client.GetOnboardingFlow(updatedFlow.ID)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	// Data should be preserved
	suite.Equal("10-50", retrievedFlow.Data["company_size"])
	suite.Equal("technology", retrievedFlow.Data["industry"])
	suite.Equal("content_management", retrievedFlow.Data["use_case"])
	suite.Equal("improve_productivity", retrievedFlow.Data["goals"])

	// Update second step
	configData := map[string]interface{}{
		"timezone":    "UTC",
		"language":    "en",
		"currency":    "USD",
		"date_format": "YYYY-MM-DD",
	}

	updatedFlow, _, err = suite.client.UpdateOnboardingStep(updatedFlow.ID, configData)
	suite.Require().NoError(err)

	// Retrieve again and verify both steps data are preserved
	retrievedFlow, _, err = suite.client.GetOnboardingFlow(updatedFlow.ID)
	suite.Require().NoError(err)

	// First step data should still be there
	suite.Equal("10-50", retrievedFlow.Data["company_size"])
	suite.Equal("technology", retrievedFlow.Data["industry"])

	// Second step data should be added
	suite.Equal("UTC", retrievedFlow.Data["timezone"])
	suite.Equal("en", retrievedFlow.Data["language"])
	suite.Equal("USD", retrievedFlow.Data["currency"])
}

// TestOnboardingStepProgression tests proper step progression
func (suite *OnboardingFlowTestSuite) TestOnboardingStepProgression() {
	// Start basic flow (3 steps)
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	helpers.NewOnboardingAssertion(suite.T(), flow).AtStep(1)

	// Progress through each step
	for step := 1; step <= 3; step++ {
		stepType := []string{"profile_setup", "tenant_configuration", "integration_setup"}[step-1]
		stepData := suite.dataGen.GenerateOnboardingStepData(stepType)

		updatedFlow, resp, err := suite.client.UpdateOnboardingStep(flow.ID, stepData)
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

		if step < 3 {
			// Should advance to next step
			helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
				AtStep(step + 1).
				IsNotCompleted()
		} else {
			// Final step should complete the flow
			helpers.NewOnboardingAssertion(suite.T(), updatedFlow).
				AtStep(3).
				IsCompleted()
		}

		flow = updatedFlow
	}
}

// TestOnboardingFlowTypes tests different onboarding flow types
func (suite *OnboardingFlowTestSuite) TestOnboardingFlowTypes() {
	flowTypes := []struct {
		name       string
		totalSteps int
	}{
		{"basic", 3},
		{"advanced", 5},
	}

	for _, ft := range flowTypes {
		suite.Run("Flow type: "+ft.name, func() {
			flow, resp, err := suite.client.StartOnboarding(suite.testTenant.ID, ft.name)
			suite.Require().NoError(err)

			helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()
			helpers.NewOnboardingAssertion(suite.T(), flow).
				HasFlowType(ft.name).
				HasTotalSteps(ft.totalSteps).
				AtStep(1).
				IsNotCompleted()
		})
	}
}

// TestOnboardingFieldValidation tests onboarding field validation
func (suite *OnboardingFlowTestSuite) TestOnboardingFieldValidation() {
	// Start onboarding flow
	flow, _, err := suite.client.StartOnboarding(suite.testTenant.ID, "basic")
	suite.Require().NoError(err)

	// Verify flow has required fields
	suite.NotEmpty(flow.ID)
	suite.NotEmpty(flow.TenantID)
	suite.NotEmpty(flow.UserID)
	suite.NotEmpty(flow.FlowType)
	suite.Greater(flow.TotalSteps, 0)
	suite.Greater(flow.CurrentStep, 0)
	suite.NotEmpty(flow.CreatedAt)

	// Verify field types
	_, err = time.Parse(time.RFC3339, flow.CreatedAt)
	suite.NoError(err, "CreatedAt should be valid RFC3339 timestamp")

	// Verify data field is initialized
	suite.NotNil(flow.Data)
}

// TestE2EOnboardingFlow runs the OnboardingFlowTestSuite
func TestE2EOnboardingFlow(t *testing.T) {
	suite.Run(t, new(OnboardingFlowTestSuite))
}