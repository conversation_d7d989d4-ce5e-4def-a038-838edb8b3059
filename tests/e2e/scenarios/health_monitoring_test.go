package scenarios

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

// HealthMonitoringTestSuite tests health check and monitoring endpoints
type HealthMonitoringTestSuite struct {
	suite.Suite
	config      *config.E2EConfig
	client      *helpers.APIClient
}

// SetupSuite runs once before all tests in the suite
func (suite *HealthMonitoringTestSuite) SetupSuite() {
	suite.config = config.NewE2EConfig()
	suite.client = helpers.NewAPIClient(suite.config)

	// Wait for server to be ready
	err := suite.client.WaitForServer(30 * time.Second)
	suite.Require().NoError(err, "Server should be ready")
}

// SetupTest runs before each test
func (suite *HealthMonitoringTestSuite) SetupTest() {
	// No authentication needed for health endpoints
}

// TearDownTest runs after each test
func (suite *HealthMonitoringTestSuite) TearDownTest() {
	// Nothing to cleanup
}

// TearDownSuite runs once after all tests in the suite
func (suite *HealthMonitoringTestSuite) TearDownSuite() {
	// Nothing to cleanup
}

// TestHealthCheckEndpoint tests the main health check endpoint
func (suite *HealthMonitoringTestSuite) TestHealthCheckEndpoint() {
	resp, err := suite.client.GetHealth()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second).
		BodyNotEmpty()

	// Parse response and verify structure
	var healthResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &healthResp)
	suite.Require().NoError(err)

	// Health response should contain status
	suite.Contains(healthResp, "status")
	suite.Equal("ok", healthResp["status"])

	// Should contain timestamp
	suite.Contains(healthResp, "timestamp")
	timestamp, ok := healthResp["timestamp"].(string)
	suite.True(ok, "Timestamp should be a string")
	suite.NotEmpty(timestamp)

	// Verify timestamp format
	_, err = time.Parse(time.RFC3339, timestamp)
	suite.NoError(err, "Timestamp should be valid RFC3339 format")
}

// TestReadinessEndpoint tests the readiness probe endpoint
func (suite *HealthMonitoringTestSuite) TestReadinessEndpoint() {
	resp, err := suite.client.GetHealthReady()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second).
		BodyNotEmpty()

	// Parse response
	var readyResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &readyResp)
	suite.Require().NoError(err)

	// Readiness response should indicate ready state
	suite.Contains(readyResp, "ready")
	suite.Equal(true, readyResp["ready"])

	// Should contain service checks
	if checks, exists := readyResp["checks"]; exists {
		checksMap, ok := checks.(map[string]interface{})
		suite.True(ok, "Checks should be a map")

		// Verify individual service checks if present
		for serviceName, status := range checksMap {
			suite.NotEmpty(serviceName)
			suite.Equal("ok", status, "Service %s should be healthy", serviceName)
		}
	}
}

// TestLivenessEndpoint tests the liveness probe endpoint
func (suite *HealthMonitoringTestSuite) TestLivenessEndpoint() {
	// Note: We're using the health endpoint as liveness check
	// Some implementations might have a separate /health/live endpoint
	resp, err := suite.client.GetHealth()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(1 * time.Second)

	// Liveness should be fast and simple
	var healthResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &healthResp)
	suite.Require().NoError(err)

	suite.Contains(healthResp, "status")
	suite.Equal("ok", healthResp["status"])
}

// TestHealthEndpointPerformance tests health endpoint performance
func (suite *HealthMonitoringTestSuite) TestHealthEndpointPerformance() {
	// Health checks should be very fast
	maxResponseTime := 1 * time.Second

	// Test health endpoint performance
	helpers.AssertResponseTime(suite.T(), maxResponseTime, func() {
		resp, err := suite.client.GetHealth()
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})

	// Test readiness endpoint performance
	helpers.AssertResponseTime(suite.T(), maxResponseTime, func() {
		resp, err := suite.client.GetHealthReady()
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})
}

// TestConcurrentHealthChecks tests concurrent health check requests
func (suite *HealthMonitoringTestSuite) TestConcurrentHealthChecks() {
	const numConcurrentRequests = 10

	// Test concurrent health checks
	results := make(chan error, numConcurrentRequests)

	for i := 0; i < numConcurrentRequests; i++ {
		go func() {
			client := helpers.NewAPIClient(suite.config)
			resp, err := client.GetHealth()
			if err != nil {
				results <- err
				return
			}

			if resp.StatusCode() != http.StatusOK {
				results <- fmt.Errorf("Expected 200 but got %d", resp.StatusCode())
				return
			}

			results <- nil
		}()
	}

	// Wait for all requests to complete
	for i := 0; i < numConcurrentRequests; i++ {
		err := <-results
		suite.NoError(err, "Concurrent health checks should succeed")
	}
}

// TestHealthCheckConsistency tests health check response consistency
func (suite *HealthMonitoringTestSuite) TestHealthCheckConsistency() {
	const numChecks = 5
	var responses []map[string]interface{}

	// Make multiple health check requests
	for i := 0; i < numChecks; i++ {
		resp, err := suite.client.GetHealth()
		suite.Require().NoError(err)
		helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

		var healthResp map[string]interface{}
		err = json.Unmarshal(resp.Body(), &healthResp)
		suite.Require().NoError(err)
		responses = append(responses, healthResp)

		// Small delay between requests
		time.Sleep(100 * time.Millisecond)
	}

	// All responses should have consistent structure
	for i := 1; i < len(responses); i++ {
		// Status should be consistent
		suite.Equal(responses[0]["status"], responses[i]["status"])

		// Both should have timestamp field
		suite.Contains(responses[0], "timestamp")
		suite.Contains(responses[i], "timestamp")

		// Timestamps should be different (server is running)
		suite.NotEqual(responses[0]["timestamp"], responses[i]["timestamp"])
	}
}

// TestHealthCheckResponseFormat tests health check response format
func (suite *HealthMonitoringTestSuite) TestHealthCheckResponseFormat() {
	resp, err := suite.client.GetHealth()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	var healthResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &healthResp)
	suite.Require().NoError(err)

	// Required fields
	requiredFields := []string{"status", "timestamp"}
	for _, field := range requiredFields {
		suite.Contains(healthResp, field, "Health response should contain %s field", field)
		suite.NotEmpty(healthResp[field], "Field %s should not be empty", field)
	}

	// Status should be a valid value
	status, ok := healthResp["status"].(string)
	suite.True(ok, "Status should be a string")
	validStatuses := []string{"ok", "error", "degraded"}
	suite.Contains(validStatuses, status, "Status should be one of: %v", validStatuses)

	// Optional fields validation
	if version, exists := healthResp["version"]; exists {
		suite.IsType("", version, "Version should be a string")
	}

	if uptime, exists := healthResp["uptime"]; exists {
		// Uptime can be string or number
		suite.True(
			suite.IsType("", uptime) || suite.IsType(float64(0), uptime),
			"Uptime should be string or number",
		)
	}
}

// TestReadinessCheckResponseFormat tests readiness check response format
func (suite *HealthMonitoringTestSuite) TestReadinessCheckResponseFormat() {
	resp, err := suite.client.GetHealthReady()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()

	var readyResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &readyResp)
	suite.Require().NoError(err)

	// Required fields
	suite.Contains(readyResp, "ready")
	ready, ok := readyResp["ready"].(bool)
	suite.True(ok, "Ready field should be boolean")
	suite.True(ready, "Service should be ready")

	// Optional checks field
	if checks, exists := readyResp["checks"]; exists {
		checksMap, ok := checks.(map[string]interface{})
		suite.True(ok, "Checks should be a map")

		// Each check should have a status
		for serviceName, status := range checksMap {
			suite.NotEmpty(serviceName, "Service name should not be empty")
			statusStr, ok := status.(string)
			suite.True(ok, "Check status should be a string for service %s", serviceName)
			suite.NotEmpty(statusStr, "Check status should not be empty for service %s", serviceName)
		}
	}
}

// TestHealthCheckUnderLoad tests health checks under load
func (suite *HealthMonitoringTestSuite) TestHealthCheckUnderLoad() {
	const numRequests = 50
	const concurrency = 10

	results := make(chan error, numRequests)
	semaphore := make(chan struct{}, concurrency)

	startTime := time.Now()

	// Send multiple requests with controlled concurrency
	for i := 0; i < numRequests; i++ {
		go func() {
			semaphore <- struct{}{} // Acquire semaphore
			defer func() { <-semaphore }() // Release semaphore

			client := helpers.NewAPIClient(suite.config)
			resp, err := client.GetHealth()
			if err != nil {
				results <- err
				return
			}

			if resp.StatusCode() != http.StatusOK {
				results <- fmt.Errorf("Expected 200 but got %d", resp.StatusCode())
				return
			}

			// Response should be fast even under load
			if resp.Time() > 2*time.Second {
				results <- fmt.Errorf("Response too slow: %v", resp.Time())
				return
			}

			results <- nil
		}()
	}

	// Wait for all requests to complete
	var errors []error
	for i := 0; i < numRequests; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	totalTime := time.Since(startTime)
	suite.T().Logf("Completed %d health check requests in %v", numRequests, totalTime)

	// Should complete within reasonable time
	suite.LessOrEqual(totalTime, 30*time.Second, "All requests should complete within 30 seconds")

	// Most requests should succeed (allow for some failures under extreme load)
	failureRate := float64(len(errors)) / float64(numRequests)
	suite.LessOrEqual(failureRate, 0.1, "Failure rate should be less than 10%% under load")

	if len(errors) > 0 {
		suite.T().Logf("Health check failures under load: %d/%d (%.1f%%)", len(errors), numRequests, failureRate*100)
	}
}

// TestHealthCheckAvailability tests health check availability over time
func (suite *HealthMonitoringTestSuite) TestHealthCheckAvailability() {
	const checkInterval = 1 * time.Second
	const totalChecks = 5

	var successCount int
	var totalResponseTime time.Duration

	for i := 0; i < totalChecks; i++ {
		startTime := time.Now()
		resp, err := suite.client.GetHealth()
		responseTime := time.Since(startTime)

		if err == nil && resp.StatusCode() == http.StatusOK {
			successCount++
			totalResponseTime += responseTime
		}

		if i < totalChecks-1 {
			time.Sleep(checkInterval)
		}
	}

	// Calculate availability
	availability := float64(successCount) / float64(totalChecks)
	avgResponseTime := totalResponseTime / time.Duration(successCount)

	suite.T().Logf("Health check availability: %.1f%% (%d/%d)", availability*100, successCount, totalChecks)
	suite.T().Logf("Average response time: %v", avgResponseTime)

	// Should have high availability
	suite.GreaterOrEqual(availability, 0.8, "Health check availability should be at least 80%%")

	// Average response time should be reasonable
	if successCount > 0 {
		suite.LessOrEqual(avgResponseTime, 2*time.Second, "Average response time should be under 2 seconds")
	}
}

// TestHealthEndpointsSecurity tests that health endpoints don't expose sensitive information
func (suite *HealthMonitoringTestSuite) TestHealthEndpointsSecurity() {
	resp, err := suite.client.GetHealth()
	suite.Require().NoError(err)

	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()

	var healthResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &healthResp)
	suite.Require().NoError(err)

	// Should not contain sensitive information
	sensitiveFields := []string{
		"password", "secret", "key", "token", "credential",
		"database_url", "connection_string", "api_key",
	}

	responseStr := string(resp.Body())
	for _, sensitiveField := range sensitiveFields {
		suite.NotContains(responseStr, sensitiveField,
			"Health response should not contain sensitive field: %s", sensitiveField)
	}

	// Should not expose internal paths or system information
	internalInfo := []string{
		"/etc/", "/var/", "/usr/", "/home/",
		"localhost", "127.0.0.1", "192.168.",
	}

	for _, info := range internalInfo {
		suite.NotContains(responseStr, info,
			"Health response should not expose internal info: %s", info)
	}
}

// TestHealthCheckCaching tests health check response caching behavior
func (suite *HealthMonitoringTestSuite) TestHealthCheckCaching() {
	// Make first request
	resp1, err := suite.client.GetHealth()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp1).StatusOK()

	// Check caching headers
	cacheControl := resp1.Header().Get("Cache-Control")
	if cacheControl != "" {
		// If cache control is set, health checks should typically be no-cache
		suite.Contains(cacheControl, "no-cache", "Health checks should not be cached")
	}

	// Make second request immediately
	resp2, err := suite.client.GetHealth()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp2).StatusOK()

	// Responses should be independent (timestamps should be different)
	var health1, health2 map[string]interface{}
	json.Unmarshal(resp1.Body(), &health1)
	json.Unmarshal(resp2.Body(), &health2)

	if timestamp1, ok1 := health1["timestamp"]; ok1 {
		if timestamp2, ok2 := health2["timestamp"]; ok2 {
			suite.NotEqual(timestamp1, timestamp2, "Health check timestamps should be different")
		}
	}
}

// TestE2EHealthMonitoring runs the HealthMonitoringTestSuite
func TestE2EHealthMonitoring(t *testing.T) {
	suite.Run(t, new(HealthMonitoringTestSuite))
}