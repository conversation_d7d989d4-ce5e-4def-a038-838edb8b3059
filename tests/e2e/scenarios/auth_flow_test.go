package scenarios

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

// AuthFlowTestSuite tests authentication-related flows
type AuthFlowTestSuite struct {
	suite.Suite
	config      *config.E2EConfig
	client      *helpers.APIClient
	dataGen     *helpers.TestDataGenerator
	predefined  *helpers.PredefinedTestData
}

// SetupSuite runs once before all tests in the suite
func (suite *AuthFlowTestSuite) SetupSuite() {
	suite.config = config.NewE2EConfig()
	suite.client = helpers.NewAPIClient(suite.config)
	suite.dataGen = helpers.NewTestDataGenerator()
	suite.predefined = helpers.NewPredefinedTestData()

	// Wait for server to be ready
	err := suite.client.WaitForServer(30 * time.Second)
	suite.Require().NoError(err, "Server should be ready")
}

// SetupTest runs before each test
func (suite *AuthFlowTestSuite) SetupTest() {
	// Clear any existing authentication
	suite.client.ClearAuth()
}

// TearDownTest runs after each test
func (suite *AuthFlowTestSuite) TearDownTest() {
	// Logout if authenticated
	suite.client.Logout()
}

// TestHealthCheck verifies the health endpoint is working
func (suite *AuthFlowTestSuite) TestHealthCheck() {
	resp, err := suite.client.GetHealth()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		BodyNotEmpty()
}

// TestServerReadiness verifies the server is ready
func (suite *AuthFlowTestSuite) TestServerReadiness() {
	resp, err := suite.client.GetHealthReady()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		ResponseTime(5 * time.Second)
}

// TestSuccessfulUserRegistration tests complete user registration flow
func (suite *AuthFlowTestSuite) TestSuccessfulUserRegistration() {
	// Generate test user data
	userData := suite.dataGen.GenerateTestUser()
	
	// Register user
	authResp, resp, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	// Assert registration response
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusCreated().
		JSONContentType().
		ResponseTime(3 * time.Second)
	
	// Assert auth response structure
	helpers.NewAuthAssertion(suite.T(), authResp).
		HasAccessToken().
		HasRefreshToken().
		TokenExpiresIn(3600, 86400). // 1 hour to 24 hours
		UserHasID().
		UserHasEmail(userData.Email).
		UserHasStatus("active")
	
	// Verify user can access protected endpoint
	user, profileResp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), profileResp).
		StatusOK().
		JSONContentType()
	
	suite.Equal(userData.Email, user.Email)
	suite.Equal(userData.FirstName, user.FirstName)
	suite.Equal(userData.LastName, user.LastName)
}

// TestSuccessfulUserLogin tests user login flow
func (suite *AuthFlowTestSuite) TestSuccessfulUserLogin() {
	// First register a user
	userData := suite.dataGen.GenerateTestUser()
	_, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	// Clear auth and login
	suite.client.ClearAuth()
	authResp, resp, err := suite.client.Login(userData.Email, userData.Password)
	suite.Require().NoError(err)
	
	// Assert login response
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType().
		ResponseTime(2 * time.Second)
	
	// Assert auth response
	helpers.NewAuthAssertion(suite.T(), authResp).
		HasAccessToken().
		HasRefreshToken().
		UserHasEmail(userData.Email)
	
	// Verify authenticated access
	_, profileResp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), profileResp).StatusOK()
}

// TestInvalidCredentialsLogin tests login with invalid credentials
func (suite *AuthFlowTestSuite) TestInvalidCredentialsLogin() {
	// Try to login with invalid credentials
	_, resp, err := suite.client.Login("<EMAIL>", "wrongpassword")
	suite.Require().NoError(err)
	
	// Should return unauthorized
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusUnauthorized().
		JSONContentType()
	
	// Should contain error message
	errorResp := suite.client.ParseErrorResponse(resp)
	suite.Contains(errorResp, "error")
}

// TestEmptyCredentialsLogin tests login with empty credentials
func (suite *AuthFlowTestSuite) TestEmptyCredentialsLogin() {
	_, resp, err := suite.client.Login("", "")
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusBadRequest().
		JSONContentType()
}

// TestTokenRefresh tests JWT token refresh functionality
func (suite *AuthFlowTestSuite) TestTokenRefresh() {
	// Register and login user
	userData := suite.dataGen.GenerateTestUser()
	authResp, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	originalToken := authResp.AccessToken
	refreshToken := authResp.RefreshToken
	
	// Wait a moment to ensure different timestamps
	time.Sleep(1 * time.Second)
	
	// Refresh token
	newAuthResp, resp, err := suite.client.RefreshToken(refreshToken)
	suite.Require().NoError(err)
	
	// Assert refresh response
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()
	
	// Assert new tokens
	helpers.NewAuthAssertion(suite.T(), newAuthResp).
		HasAccessToken().
		HasRefreshToken()
	
	// New access token should be different
	suite.NotEqual(originalToken, newAuthResp.AccessToken)
	
	// Should be able to use new token
	_, profileResp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), profileResp).StatusOK()
}

// TestInvalidTokenRefresh tests refresh with invalid token
func (suite *AuthFlowTestSuite) TestInvalidTokenRefresh() {
	_, resp, err := suite.client.RefreshToken("invalid-refresh-token")
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusUnauthorized().
		JSONContentType()
}

// TestUserLogout tests user logout functionality
func (suite *AuthFlowTestSuite) TestUserLogout() {
	// Register and login user
	userData := suite.dataGen.GenerateTestUser()
	_, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	// Verify authenticated access works
	_, profileResp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), profileResp).StatusOK()
	
	// Logout
	resp, err := suite.client.Logout()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusOK().
		JSONContentType()
	
	// Should not be able to access protected endpoints after logout
	_, profileResp, err = suite.client.GetProfile()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), profileResp).StatusUnauthorized()
}

// TestUnauthenticatedAccess tests access to protected endpoints without auth
func (suite *AuthFlowTestSuite) TestUnauthenticatedAccess() {
	// Try to access protected endpoint without authentication
	_, resp, err := suite.client.GetProfile()
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).
		StatusUnauthorized().
		JSONContentType()
}

// TestInvalidUserRegistration tests registration with invalid data
func (suite *AuthFlowTestSuite) TestInvalidUserRegistration() {
	invalidUsers := suite.predefined.GetInvalidTestData()["invalid_users"]
	
	// Test various invalid registration scenarios
	testCases := []struct {
		name string
		data helpers.RegisterRequest
	}{
		{
			name: "Invalid email format",
			data: helpers.RegisterRequest{
				Email:     "invalid-email",
				Password:  "ValidPassword123!",
				FirstName: "Test",
				LastName:  "User",
			},
		},
		{
			name: "Weak password",
			data: helpers.RegisterRequest{
				Email:     "<EMAIL>",
				Password:  "123",
				FirstName: "Test",
				LastName:  "User",
			},
		},
		{
			name: "Missing first name",
			data: helpers.RegisterRequest{
				Email:    "<EMAIL>",
				Password: "ValidPassword123!",
				LastName: "User",
			},
		},
		{
			name: "Empty email",
			data: helpers.RegisterRequest{
				Email:     "",
				Password:  "ValidPassword123!",
				FirstName: "Test",
				LastName:  "User",
			},
		},
	}
	
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			_, resp, err := suite.client.Register(tc.data)
			suite.Require().NoError(err)
			
			helpers.NewHTTPAssertion(suite.T(), resp).
				StatusBadRequest().
				JSONContentType()
			
			// Should contain validation error
			errorResp := suite.client.ParseErrorResponse(resp)
			suite.Contains(errorResp, "error")
		})
	}
	
	// Suppress unused variable warning
	_ = invalidUsers
}

// TestDuplicateUserRegistration tests registration with duplicate email
func (suite *AuthFlowTestSuite) TestDuplicateUserRegistration() {
	// Register first user
	userData := suite.dataGen.GenerateTestUser()
	_, resp1, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	helpers.NewHTTPAssertion(suite.T(), resp1).StatusCreated()
	
	// Try to register second user with same email
	userData2 := userData // Same email
	userData2.FirstName = "Different"
	userData2.LastName = "Name"
	
	_, resp2, err := suite.client.Register(userData2)
	suite.Require().NoError(err)
	
	// Should return conflict error
	helpers.NewHTTPAssertion(suite.T(), resp2).
		StatusCode(http.StatusConflict).
		JSONContentType()
}

// TestPasswordSecurity tests password security requirements
func (suite *AuthFlowTestSuite) TestPasswordSecurity() {
	weakPasswords := []string{
		"123",           // Too short
		"password",      // No numbers or special chars
		"12345678",      // No letters or special chars
		"Password",      // No numbers or special chars
		"Password123",   // No special chars
	}
	
	for _, weakPassword := range weakPasswords {
		suite.Run("Weak password: "+weakPassword, func() {
			userData := suite.dataGen.GenerateTestUser()
			userData.Password = weakPassword
			
			_, resp, err := suite.client.Register(userData)
			suite.Require().NoError(err)
			
			helpers.NewHTTPAssertion(suite.T(), resp).
				StatusBadRequest().
				JSONContentType()
		})
	}
}

// TestConcurrentAuthentication tests concurrent auth operations
func (suite *AuthFlowTestSuite) TestConcurrentAuthentication() {
	const numConcurrentUsers = 5
	
	// Create multiple users concurrently
	results := make(chan error, numConcurrentUsers)
	
	for i := 0; i < numConcurrentUsers; i++ {
		go func(index int) {
			userData := suite.dataGen.GenerateTestUser()
			client := helpers.NewAPIClient(suite.config)
			
			// Register
			_, resp, err := client.Register(userData)
			if err != nil {
				results <- err
				return
			}
			
			if resp.StatusCode() != http.StatusCreated {
				results <- fmt.Errorf("Expected 201 but got %d", resp.StatusCode())
				return
			}
			
			// Login
			_, resp, err = client.Login(userData.Email, userData.Password)
			if err != nil {
				results <- err
				return
			}
			
			if resp.StatusCode() != http.StatusOK {
				results <- fmt.Errorf("Expected 200 but got %d", resp.StatusCode())
				return
			}
			
			results <- nil
		}(i)
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numConcurrentUsers; i++ {
		err := <-results
		suite.NoError(err, "Concurrent authentication should succeed")
	}
}

// TestAuthPerformance tests authentication performance
func (suite *AuthFlowTestSuite) TestAuthPerformance() {
	userData := suite.dataGen.GenerateTestUser()
	
	// Test registration performance
	helpers.AssertResponseTime(suite.T(), 3*time.Second, func() {
		_, resp, err := suite.client.Register(userData)
		suite.Require().NoError(err)
		suite.Equal(http.StatusCreated, resp.StatusCode())
	})
	
	// Test login performance
	suite.client.ClearAuth()
	helpers.AssertResponseTime(suite.T(), 2*time.Second, func() {
		_, resp, err := suite.client.Login(userData.Email, userData.Password)
		suite.Require().NoError(err)
		suite.Equal(http.StatusOK, resp.StatusCode())
	})
}

// TestAuthenticationWithSpecialCharacters tests auth with special characters
func (suite *AuthFlowTestSuite) TestAuthenticationWithSpecialCharacters() {
	// Test with special characters in email and names
	userData := helpers.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "TestPassword123!@#$%",
		FirstName: "José",
		LastName:  "García-López",
	}
	
	// Register
	authResp, resp, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).StatusCreated()
	helpers.NewAuthAssertion(suite.T(), authResp).
		UserHasEmail(userData.Email)
	
	// Login
	suite.client.ClearAuth()
	authResp, resp, err = suite.client.Login(userData.Email, userData.Password)
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).StatusOK()
	helpers.NewAuthAssertion(suite.T(), authResp).
		UserHasEmail(userData.Email)
}

// TestAuthenticationErrorMessages tests that error messages are informative
func (suite *AuthFlowTestSuite) TestAuthenticationErrorMessages() {
	// Test login with wrong password
	userData := suite.dataGen.GenerateTestUser()
	_, _, err := suite.client.Register(userData)
	suite.Require().NoError(err)
	
	suite.client.ClearAuth()
	_, resp, err := suite.client.Login(userData.Email, "wrongpassword")
	suite.Require().NoError(err)
	
	helpers.NewHTTPAssertion(suite.T(), resp).StatusUnauthorized()
	
	// Check error message structure
	var errorResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &errorResp)
	suite.NoError(err)
	
	suite.Contains(errorResp, "error")
	suite.IsType("", errorResp["error"]) // Should be string
}

// TestE2EAuthFlow runs the AuthFlowTestSuite
func TestE2EAuthFlow(t *testing.T) {
	suite.Run(t, new(AuthFlowTestSuite))
}