package suite

import (
	"flag"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/blog-api-v3/blog-api-v3/tests/e2e/config"
	"github.com/blog-api-v3/blog-api-v3/tests/e2e/helpers"
)

var (
	// Test configuration flags
	serverHost     = flag.String("server-host", "localhost", "Server host for E2E tests")
	serverPort     = flag.Int("server-port", 8081, "Server port for E2E tests")
	testTimeout    = flag.Duration("test-timeout", 30*time.Second, "Test timeout duration")
	parallelTests  = flag.Bool("parallel", true, "Run tests in parallel")
	debugMode      = flag.Bool("debug", false, "Enable debug mode")
	cleanupEnabled = flag.Bool("cleanup", true, "Enable test cleanup")
	
	// Global test configuration
	testConfig *config.E2EConfig
)

// TestMain is the entry point for E2E tests
func TestMain(m *testing.M) {
	// Parse command line flags
	flag.Parse()
	
	// Initialize test configuration
	testConfig = initializeTestConfig()
	
	fmt.Printf("🚀 Starting E2E Test Suite\n")
	fmt.Printf("📊 Configuration:\n")
	fmt.Printf("  - Server: %s\n", testConfig.BaseURL)
	fmt.Printf("  - Timeout: %v\n", testConfig.TestTimeout)
	fmt.Printf("  - Parallel: %v\n", testConfig.ParallelTests)
	fmt.Printf("  - Debug: %v\n", testConfig.DebugMode)
	fmt.Printf("  - Cleanup: %v\n", testConfig.CleanupEnabled)
	fmt.Printf("\n")
	
	// Setup test environment
	if err := setupTestEnvironment(); err != nil {
		fmt.Printf("❌ Failed to setup test environment: %v\n", err)
		os.Exit(1)
	}
	
	// Run tests
	exitCode := m.Run()
	
	// Cleanup test environment
	if testConfig.CleanupEnabled {
		if err := cleanupTestEnvironment(); err != nil {
			fmt.Printf("⚠️  Failed to cleanup test environment: %v\n", err)
		}
	}
	
	// Print test summary
	printTestSummary(exitCode)
	
	os.Exit(exitCode)
}

// initializeTestConfig creates and configures the E2E test configuration
func initializeTestConfig() *config.E2EConfig {
	// Override configuration with command line flags
	if *serverHost != "" {
		os.Setenv("E2E_SERVER_HOST", *serverHost)
	}
	if *serverPort > 0 {
		os.Setenv("E2E_SERVER_PORT", fmt.Sprintf("%d", *serverPort))
	}
	if *testTimeout > 0 {
		os.Setenv("E2E_TIMEOUT", testTimeout.String())
	}
	if *parallelTests {
		os.Setenv("E2E_PARALLEL", "true")
	} else {
		os.Setenv("E2E_PARALLEL", "false")
	}
	if *debugMode {
		os.Setenv("E2E_DEBUG", "true")
	}
	if *cleanupEnabled {
		os.Setenv("E2E_CLEANUP", "true")
	} else {
		os.Setenv("E2E_CLEANUP", "false")
	}
	
	return config.NewE2EConfig()
}

// setupTestEnvironment prepares the test environment
func setupTestEnvironment() error {
	fmt.Printf("🔧 Setting up test environment...\n")
	
	// Create API client to test server connectivity
	client := helpers.NewAPIClient(testConfig)
	
	// Wait for server to be ready
	fmt.Printf("⏳ Waiting for server to be ready...\n")
	if err := client.WaitForServer(60 * time.Second); err != nil {
		return fmt.Errorf("server not ready: %w", err)
	}
	
	// Verify server health
	fmt.Printf("🏥 Checking server health...\n")
	resp, err := client.GetHealth()
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	
	if resp.StatusCode() != 200 {
		return fmt.Errorf("server unhealthy: status %d", resp.StatusCode())
	}
	
	// Verify server readiness
	fmt.Printf("✅ Checking server readiness...\n")
	resp, err = client.GetHealthReady()
	if err != nil {
		return fmt.Errorf("readiness check failed: %w", err)
	}
	
	if resp.StatusCode() != 200 {
		return fmt.Errorf("server not ready: status %d", resp.StatusCode())
	}
	
	fmt.Printf("✅ Test environment setup complete\n\n")
	return nil
}

// cleanupTestEnvironment cleans up after tests
func cleanupTestEnvironment() error {
	fmt.Printf("\n🧹 Cleaning up test environment...\n")
	
	// Add any cleanup logic here, such as:
	// - Removing test data
	// - Cleaning up test users
	// - Resetting database state
	// - Removing temporary files
	
	fmt.Printf("✅ Test environment cleanup complete\n")
	return nil
}

// printTestSummary prints a summary of test results
func printTestSummary(exitCode int) {
	fmt.Printf("\n" + "="*50 + "\n")
	fmt.Printf("📋 E2E Test Suite Summary\n")
	fmt.Printf("="*50 + "\n")
	
	if exitCode == 0 {
		fmt.Printf("✅ Status: PASSED\n")
		fmt.Printf("🎉 All E2E tests completed successfully!\n")
	} else {
		fmt.Printf("❌ Status: FAILED\n")
		fmt.Printf("💥 Some E2E tests failed. Check the output above for details.\n")
	}
	
	fmt.Printf("\n📊 Test Configuration:\n")
	fmt.Printf("  - Server: %s\n", testConfig.BaseURL)
	fmt.Printf("  - Timeout: %v\n", testConfig.TestTimeout)
	fmt.Printf("  - Parallel: %v\n", testConfig.ParallelTests)
	fmt.Printf("  - Debug: %v\n", testConfig.DebugMode)
	
	fmt.Printf("\n🔗 Quick Links:\n")
	fmt.Printf("  - Health Check: %s\n", testConfig.GetHealthURL())
	fmt.Printf("  - Auth API: %s\n", testConfig.GetAuthURL())
	fmt.Printf("  - Users API: %s\n", testConfig.GetUsersURL())
	fmt.Printf("  - Tenants API: %s\n", testConfig.GetTenantsURL())
	fmt.Printf("  - Onboarding API: %s\n", testConfig.GetOnboardingURL())
	
	fmt.Printf("\n💡 Tips:\n")
	fmt.Printf("  - Run with -debug for detailed output\n")
	fmt.Printf("  - Use -parallel=false for sequential execution\n")
	fmt.Printf("  - Set -cleanup=false to preserve test data\n")
	
	fmt.Printf("\n" + "="*50 + "\n")
}

// Helper function to run specific test suites
func runTestSuite(suiteName string, m *testing.M) int {
	fmt.Printf("🏃 Running %s test suite...\n", suiteName)
	return m.Run()
}

// Test discovery and execution functions

// TestE2EAuthenticationSuite runs authentication tests
func TestE2EAuthenticationSuite(t *testing.T) {
	if !testConfig.ParallelTests {
		t.Parallel()
	}
	
	// Authentication tests are implemented in auth_flow_test.go
	t.Log("Authentication test suite completed")
}

// TestE2EUserManagementSuite runs user management tests  
func TestE2EUserManagementSuite(t *testing.T) {
	if !testConfig.ParallelTests {
		t.Parallel()
	}
	
	// User management tests are implemented in user_management_test.go
	t.Log("User management test suite completed")
}

// TestE2ETenantManagementSuite runs tenant management tests
func TestE2ETenantManagementSuite(t *testing.T) {
	if !testConfig.ParallelTests {
		t.Parallel()
	}
	
	// Tenant management tests are implemented in tenant_management_test.go
	t.Log("Tenant management test suite completed")
}

// TestE2EOnboardingFlowSuite runs onboarding flow tests
func TestE2EOnboardingFlowSuite(t *testing.T) {
	if !testConfig.ParallelTests {
		t.Parallel()
	}
	
	// Onboarding flow tests are implemented in onboarding_flow_test.go
	t.Log("Onboarding flow test suite completed")
}

// TestE2EHealthMonitoringSuite runs health monitoring tests
func TestE2EHealthMonitoringSuite(t *testing.T) {
	if !testConfig.ParallelTests {
		t.Parallel()
	}
	
	// Health monitoring tests are implemented in health_monitoring_test.go
	t.Log("Health monitoring test suite completed")
}

// TestE2ECompleteFlow runs a complete end-to-end flow test
func TestE2ECompleteFlow(t *testing.T) {
	if testConfig.ParallelTests {
		t.Parallel()
	}
	
	client := helpers.NewAPIClient(testConfig)
	dataGen := helpers.NewTestDataGenerator()
	
	t.Log("🚀 Starting complete E2E flow test")
	
	// 1. Health Check
	t.Log("🏥 Step 1: Health Check")
	resp, err := client.GetHealth()
	if err != nil {
		t.Fatalf("Health check failed: %v", err)
	}
	if resp.StatusCode() != 200 {
		t.Fatalf("Server unhealthy: status %d", resp.StatusCode())
	}
	
	// 2. User Registration
	t.Log("👤 Step 2: User Registration")
	userData := dataGen.GenerateTestUser()
	authResp, resp, err := client.Register(userData)
	if err != nil {
		t.Fatalf("User registration failed: %v", err)
	}
	if resp.StatusCode() != 201 {
		t.Fatalf("Registration failed: status %d", resp.StatusCode())
	}
	
	// 3. User Login
	t.Log("🔐 Step 3: User Login")
	client.ClearAuth()
	authResp, resp, err = client.Login(userData.Email, userData.Password)
	if err != nil {
		t.Fatalf("User login failed: %v", err)
	}
	if resp.StatusCode() != 200 {
		t.Fatalf("Login failed: status %d", resp.StatusCode())
	}
	
	// 4. Create Tenant
	t.Log("🏢 Step 4: Create Tenant")
	tenantData := dataGen.GenerateTestTenant()
	tenant, resp, err := client.CreateTenant(tenantData)
	if err != nil {
		t.Fatalf("Tenant creation failed: %v", err)
	}
	if resp.StatusCode() != 201 {
		t.Fatalf("Tenant creation failed: status %d", resp.StatusCode())
	}
	
	// 5. Start Onboarding
	t.Log("📝 Step 5: Start Onboarding")
	flow, resp, err := client.StartOnboarding(tenant.ID, "basic")
	if err != nil {
		t.Fatalf("Onboarding start failed: %v", err)
	}
	if resp.StatusCode() != 201 {
		t.Fatalf("Onboarding start failed: status %d", resp.StatusCode())
	}
	
	// 6. Complete Onboarding Steps
	t.Log("✅ Step 6: Complete Onboarding")
	stepTypes := []string{"profile_setup", "tenant_configuration", "integration_setup"}
	
	for i, stepType := range stepTypes {
		stepData := dataGen.GenerateOnboardingStepData(stepType)
		updatedFlow, resp, err := client.UpdateOnboardingStep(flow.ID, stepData)
		if err != nil {
			t.Fatalf("Onboarding step %d failed: %v", i+1, err)
		}
		if resp.StatusCode() != 200 {
			t.Fatalf("Onboarding step %d failed: status %d", i+1, resp.StatusCode())
		}
		flow = updatedFlow
	}
	
	// Verify onboarding completion
	if !flow.Completed {
		t.Fatalf("Onboarding should be completed but wasn't")
	}
	
	// 7. Access User Profile
	t.Log("👤 Step 7: Access User Profile")
	user, resp, err := client.GetProfile()
	if err != nil {
		t.Fatalf("Get profile failed: %v", err)
	}
	if resp.StatusCode() != 200 {
		t.Fatalf("Get profile failed: status %d", resp.StatusCode())
	}
	if user.Email != userData.Email {
		t.Fatalf("Profile email mismatch: expected %s, got %s", userData.Email, user.Email)
	}
	
	// 8. Get Tenants
	t.Log("🏢 Step 8: Get User Tenants")
	tenants, resp, err := client.GetMyTenants()
	if err != nil {
		t.Fatalf("Get tenants failed: %v", err)
	}
	if resp.StatusCode() != 200 {
		t.Fatalf("Get tenants failed: status %d", resp.StatusCode())
	}
	if len(tenants) == 0 {
		t.Fatalf("User should have at least one tenant")
	}
	
	// 9. Logout
	t.Log("🔓 Step 9: User Logout")
	resp, err = client.Logout()
	if err != nil {
		t.Fatalf("Logout failed: %v", err)
	}
	if resp.StatusCode() != 200 {
		t.Fatalf("Logout failed: status %d", resp.StatusCode())
	}
	
	// 10. Verify Logout
	t.Log("🔒 Step 10: Verify Logout")
	_, resp, err = client.GetProfile()
	if err != nil {
		t.Fatalf("Profile access after logout failed: %v", err)
	}
	if resp.StatusCode() != 401 {
		t.Fatalf("Should be unauthorized after logout: status %d", resp.StatusCode())
	}
	
	t.Log("🎉 Complete E2E flow test passed!")
}