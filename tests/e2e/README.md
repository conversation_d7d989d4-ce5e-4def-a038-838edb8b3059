# E2E Test Suite Documentation

## Tổng quan

E2E (End-to-End) test suite đư<PERSON>c thiết kế để kiểm tra toàn bộ flow của ứng dụng Blog API V3 từ góc độ người dùng cuối.

## Kiến trúc Test Suite

### 1. Test Structure
```
tests/e2e/
├── README.md
├── config/
│   ├── test_config.go
│   └── environment.go
├── fixtures/
│   ├── users.json
│   ├── tenants.json
│   └── onboarding.json
├── helpers/
│   ├── api_client.go
│   ├── assertions.go
│   └── test_data.go
├── scenarios/
│   ├── auth_flow_test.go
│   ├── user_management_test.go
│   ├── tenant_management_test.go
│   ├── onboarding_flow_test.go
│   └── health_monitoring_test.go
└── suite/
    ├── main_test.go
    └── setup.go
```

### 2. Test Categories

#### 🔐 Authentication Flow Tests
- User registration với email verification
- User login với các scenarios khác nhau
- JWT token refresh mechanism
- Password reset flow
- Session management

#### 👤 User Management Tests
- User profile management
- User search và filtering
- User roles và permissions
- User deactivation/activation

#### 🏢 Tenant Management Tests
- Tenant creation và setup
- Multi-tenant isolation
- Tenant member management
- Tenant subscription plans
- Tenant admin operations

#### 📝 Onboarding Flow Tests
- Complete onboarding journey
- Step-by-step progress tracking
- Onboarding customization
- Flow completion validation

#### 🏥 Health & Monitoring Tests
- Health check endpoints
- Readiness và liveness probes
- Metrics collection
- Error handling

### 3. Test Data Management

#### Fixtures
- **users.json**: Test user data với các roles khác nhau
- **tenants.json**: Test tenant configurations
- **onboarding.json**: Onboarding flow definitions

#### Test Data Strategy
- Sử dụng deterministic test data
- Clean up data sau mỗi test
- Isolated test environments
- Mock external dependencies

### 4. Test Execution Flow

#### Setup Phase
1. Start test server với test configuration
2. Initialize database với test data
3. Setup mock services
4. Prepare test fixtures

#### Execution Phase
1. Run authentication tests
2. Run user management tests  
3. Run tenant management tests
4. Run onboarding flow tests
5. Run health monitoring tests

#### Teardown Phase
1. Clean up test data
2. Stop test server
3. Generate test reports
4. Archive test artifacts

### 5. Assertions & Validations

#### API Response Validation
- HTTP status codes
- Response body structure
- Response time performance
- Error message formats

#### Business Logic Validation
- Data consistency checks
- State transitions
- Authorization rules
- Multi-tenant isolation

#### Integration Validation
- Module interactions
- Event propagation
- Database transactions
- External service calls

### 6. Test Configuration

#### Environment Variables
```bash
E2E_TEST_ENV=test
E2E_DB_HOST=localhost
E2E_DB_NAME=blog_api_v3_e2e_test
E2E_SERVER_PORT=8081
E2E_TIMEOUT=30s
E2E_PARALLEL=true
```

#### Test Flags
```bash
# Chạy tất cả E2E tests
go test ./tests/e2e/... -v

# Chạy specific test category
go test ./tests/e2e/scenarios -run TestAuthFlow -v

# Chạy với parallel execution
go test ./tests/e2e/... -v -parallel 4

# Chạy với timeout
go test ./tests/e2e/... -v -timeout 5m
```

### 7. CI/CD Integration

#### GitHub Actions Workflow
- Trigger trên pull requests
- Run trên multiple environments
- Generate test coverage reports
- Upload test artifacts

#### Test Reporting
- JUnit XML format cho CI
- HTML reports cho developers
- Performance metrics tracking
- Test failure notifications

### 8. Best Practices

#### Test Design
- Tests phải độc lập và có thể chạy parallel
- Sử dụng descriptive test names
- Implement proper test cleanup
- Mock external dependencies

#### Performance
- Set reasonable timeouts
- Use efficient test data
- Minimize test setup overhead
- Monitor test execution time

#### Maintenance
- Regular test data updates
- Review test coverage
- Update tests khi API changes
- Monitor test flakiness

### 9. Troubleshooting

#### Common Issues
- Port conflicts khi chạy parallel
- Database connection limits
- Test data conflicts
- Timing issues với async operations

#### Debug Mode
```bash
# Enable debug logging
E2E_DEBUG=true go test ./tests/e2e/... -v

# Run single test với verbose output
go test ./tests/e2e/scenarios -run TestSpecificScenario -v -count=1
```

### 10. Dependencies

#### Required Packages
- `github.com/stretchr/testify` - Assertions và test utilities
- `github.com/go-resty/resty/v2` - HTTP client cho API calls
- `github.com/gofrs/uuid` - UUID generation cho test data
- `github.com/DATA-DOG/go-sqlmock` - Database mocking