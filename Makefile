# Blog API v3 Makefile

# Variables
BINARY_NAME=blog-api
GO=go
GOCOVER=$(GO) tool cover
GOVET=$(GO) vet
GOLINT=golangci-lint
GOTEST=$(GO) test
MAIN_PATH=./cmd/server
BUILD_DIR=./build
COVERAGE_FILE=coverage.out

# Build flags
LDFLAGS=-ldflags "-s -w"

# Colors
GREEN=\033[0;32m
YELLOW=\033[0;33m
RED=\033[0;31m
NC=\033[0m # No Color

.PHONY: help build run test test-coverage lint clean deps dev migrate-up migrate-down migrate-create test-e2e test-e2e-auth test-e2e-debug test-all

## help: <PERSON><PERSON><PERSON> thị help
help:
	@echo "Usage:"
	@echo "  make [target]"
	@echo ""
	@echo "Environment:"
	@echo "  .env file is automatically loaded for database connection"
	@echo "  MODULE=tenant    - Run migrations for specific module"
	@echo "  SEEDERS=name1,name2 - Run specific seeders"
	@echo ""
	@echo "Targets:"
	@grep -E '^##' Makefile | sed 's/## /  /'

## build: Build ứng dụng
build:
	@echo "$(GREEN)Building application...$(NC)"
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)Build completed: $(BUILD_DIR)/$(BINARY_NAME)$(NC)"

## run: Chạy ứng dụng
run:
	@echo "$(GREEN)Running application...$(NC)"
	$(GO) run $(MAIN_PATH)

## test: Chạy unit tests
test:
	@echo "$(GREEN)Running tests...$(NC)"
	$(GOTEST) -v -race ./...

## test-coverage: Chạy tests với coverage report
test-coverage:
	@echo "$(GREEN)Running tests with coverage...$(NC)"
	$(GOTEST) -v -race -coverprofile=$(COVERAGE_FILE) -covermode=atomic ./...
	$(GOCOVER) -html=$(COVERAGE_FILE) -o coverage.html
	@echo "$(GREEN)Coverage report: coverage.html$(NC)"

## lint: Chạy linter
lint:
	@echo "$(GREEN)Running linter...$(NC)"
	@if command -v $(GOLINT) >/dev/null; then \
		$(GOLINT) run ./...; \
	else \
		echo "$(YELLOW)golangci-lint not installed. Installing...$(NC)"; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
		$(GOLINT) run ./...; \
	fi

## fmt: Format code
fmt:
	@echo "$(GREEN)Formatting code...$(NC)"
	$(GO) fmt ./...

## vet: Run go vet
vet:
	@echo "$(GREEN)Running go vet...$(NC)"
	$(GOVET) ./...

## clean: Clean build artifacts
clean:
	@echo "$(GREEN)Cleaning...$(NC)"
	@rm -rf $(BUILD_DIR)
	@rm -f $(COVERAGE_FILE) coverage.html
	@echo "$(GREEN)Clean completed$(NC)"

## deps: Download dependencies
deps:
	@echo "$(GREEN)Downloading dependencies...$(NC)"
	$(GO) mod download
	$(GO) mod tidy
	@echo "$(GREEN)Dependencies downloaded$(NC)"

## dev: Chạy ở chế độ development với hot reload
dev:
	@if command -v air >/dev/null; then \
		air; \
	else \
		echo "$(YELLOW)Air not installed. Installing...$(NC)"; \
		go install github.com/cosmtrek/air@latest; \
		air init; \
		air; \
	fi

## docker-build: Build Docker image
docker-build:
	@echo "$(GREEN)Building Docker image...$(NC)"
	docker build -t $(BINARY_NAME):latest .

## docker-run: Run Docker container
docker-run:
	@echo "$(GREEN)Running Docker container...$(NC)"
	docker run -p 9077:9077 $(BINARY_NAME):latest

## migrate-create: Tạo migration mới
migrate-create:
	@read -p "Module name (e.g., tenant, website, user): " module; \
	read -p "Migration name: " name; \
	mkdir -p internal/database/migrations/$$module; \
	next_seq=$$(find internal/database/migrations/$$module -name "*.sql" 2>/dev/null | sed 's/.*\/\([0-9]\{3\}\).*/\1/' | sort -n | tail -1 | awk '{printf "%03d", $$1 + 1}'); \
	[ -z "$$next_seq" ] && next_seq="001"; \
	touch internal/database/migrations/$$module/$${next_seq}_$${name}.sql

## migrate-up: Chạy migrations
migrate-up:
	@echo "$(GREEN)Running migrations...$(NC)"
	@if [ -z "$(MODULE)" ]; then \
		$(GO) run ./cmd/migrate -command up; \
	else \
		$(GO) run ./cmd/migrate -command up -module $(MODULE); \
	fi

## migrate-down: Rollback migration
migrate-down:
	@echo "$(YELLOW)Rolling back migration...$(NC)"
	@if [ -z "$(MODULE)" ]; then \
		$(GO) run ./cmd/migrate -command down; \
	else \
		$(GO) run ./cmd/migrate -command down -module $(MODULE); \
	fi

## migrate-version: Xem version hiện tại
migrate-version:
	@echo "$(GREEN)Current migration version:$(NC)"
	@if [ -z "$(MODULE)" ]; then \
		$(GO) run ./cmd/migrate -command version; \
	else \
		$(GO) run ./cmd/migrate -command version -module $(MODULE); \
	fi

## migrate-status: Xem migration status
migrate-status:
	@echo "$(GREEN)Migration status:$(NC)"
	@if [ -z "$(MODULE)" ]; then \
		$(GO) run ./cmd/migrate -command status; \
	else \
		$(GO) run ./cmd/migrate -command status -module $(MODULE); \
	fi

## seed-run: Chạy database seeders
seed-run:
	@echo "$(GREEN)Running database seeders...$(NC)"
	@if [ -z "$(SEEDERS)" ]; then \
		$(GO) run ./cmd/seed -command run; \
	else \
		$(GO) run ./cmd/seed -command run -seeders $(SEEDERS); \
	fi

## seed-rollback: Rollback seeders
seed-rollback:
	@echo "$(YELLOW)Rolling back seeders...$(NC)"
	$(GO) run ./cmd/seed -command rollback

## seed-list: List available seeders
seed-list:
	@echo "$(GREEN)Available seeders:$(NC)"
	@$(GO) run ./cmd/seed -command list

## swagger: Generate Swagger documentation
swagger:
	@echo "$(GREEN)Generating Swagger documentation...$(NC)"
	@if command -v swag >/dev/null; then \
		swag init -g $(MAIN_PATH)/main.go -o ./docs/swagger; \
	else \
		echo "$(YELLOW)Swag not installed. Installing...$(NC)"; \
		go install github.com/swaggo/swag/cmd/swag@latest; \
		swag init -g $(MAIN_PATH)/main.go -o ./docs/swagger; \
	fi

## test-e2e: Chạy E2E tests
test-e2e: build
	@echo "$(GREEN)Running E2E tests...$(NC)"
	./scripts/run-e2e-tests.sh --build

## test-e2e-auth: Chạy E2E authentication tests
test-e2e-auth: build
	@echo "$(GREEN)Running E2E authentication tests...$(NC)"
	./scripts/run-e2e-tests.sh --build -s auth

## test-e2e-debug: Chạy E2E tests với debug mode
test-e2e-debug: build
	@echo "$(GREEN)Running E2E tests in debug mode...$(NC)"
	./scripts/run-e2e-tests.sh --build --debug

## test-all: Chạy tất cả tests (unit + e2e)
test-all: test test-e2e
	@echo "$(GREEN)All tests completed!$(NC)"

## check: Run all checks (fmt, vet, lint, test)
check: fmt vet lint test
	@echo "$(GREEN)All checks passed!$(NC)"